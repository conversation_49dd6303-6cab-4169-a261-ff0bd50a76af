<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pinshang.qingyun</groupId>
		<artifactId>qingyun-parent</artifactId>
		<version>5.0.0-UP-SNAPSHOT</version>
		<relativePath/>
	</parent>
	<artifactId>qingyun-report-parent</artifactId>
	<version>4.0.7-UP-SNAPSHOT</version>
	<packaging>pom</packaging>

	<properties>
		<!-- 解决文件拷贝时的编码 -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<!-- 解决编译时中文乱码-->
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<java.version>1.8</java.version>

		<qingyun.box.version>3.0.4-UP-SNAPSHOT</qingyun.box.version>
		<qingyun.base.db.version>1.0.3-SNAPSHOT</qingyun.base.db.version>
		<qingyun.base.mvc.version>3.4.2-SNAPSHOT</qingyun.base.mvc.version>
		<qingyun.basic.version>1.4.6-SNAPSHOT</qingyun.basic.version>
		<qingyun.cache.version>3.1.1-UP-SNAPSHOT</qingyun.cache.version>
		<qingyun.shop.version>4.1.2-UP-SNAPSHOT</qingyun.shop.version>
        <qingyun.product.version>3.6.2-UP-SNAPSHOT</qingyun.product.version>
        <qingyun.price.version>1.4.8-UP-SNAPSHOT</qingyun.price.version>
		<qingyun.storage.version>3.6.7-UP-SNAPSHOT</qingyun.storage.version>
		<qingyun.xs.user.version>3.4.3-UP-SNAPSHOT</qingyun.xs.user.version>
		<qingyun.mq.version>3.8.9-UP-SNAPSHOT</qingyun.mq.version>
		<qingyun.common.version>3.6.7-UP-SNAPSHOT</qingyun.common.version>
		<qingyun.sync.version>3.0.9-UP-SNAPSHOT</qingyun.sync.version>
		<qingyun.order.version>3.5.5-UP-SNAPSHOT</qingyun.order.version>
		<qingyun.xd.version>3.5.9-UP-SNAPSHOT</qingyun.xd.version>
		<qingyun.smm.version>3.2.8-UP-SNAPSHOT</qingyun.smm.version>
		<qingyun.infrastructure.version>2.2.5-SNAPSHOT</qingyun.infrastructure.version>
		<qingyun.renderer.version>2.2.6-UP-SNAPSHOT</qingyun.renderer.version>
        <qingyun.weixin.version>1.2.1-UP-SNAPSHOT</qingyun.weixin.version>
		<qingyun.report.search.version>4.0.5-UP-SNAPSHOT</qingyun.report.search.version>
		<qingyun.import.plugin.version>1.1.1-UP-SNAPSHOT</qingyun.import.plugin.version>
		<qingyun.upload.version>3.1.8-UP-SNAPSHOT</qingyun.upload.version>
		<qingyun.bigdata.version>3.0.9-UP-SNAPSHOT</qingyun.bigdata.version>
		<qingyun.marketing.version>1.2.1-UP-SNAPSHOT</qingyun.marketing.version>
	</properties>
	<modules>
		<module>qingyun-report-service</module>
		<module>qingyun-report-client</module>
		<module>qingyun-report-search-service</module>
		<module>qingyun-report-search-client</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-basic</artifactId>
				<version>${qingyun.basic.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-marketing-client</artifactId>
				<version>${qingyun.marketing.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-bigdata-client</artifactId>
				<version>${qingyun.bigdata.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-upload-client</artifactId>
				<version>${qingyun.upload.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-import-plugin</artifactId>
				<version>${qingyun.import.plugin.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-health-check</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-data-query</artifactId>
				<version>${qingyun.infrastructure.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.baomidou</groupId>
						<artifactId>mybatis-plus-boot-starter</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-switch</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-loadBalancer</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-metrics-client</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-file-export-cache</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-mq</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-components-inventory</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-common</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-springcloud-common</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-cache</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-renderer</artifactId>
                <version>${qingyun.renderer.version}</version>
            </dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-smm-client</artifactId>
				<version>${qingyun.smm.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-xd-promotion-client</artifactId>
				<version>${qingyun.xd.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-xd-wms-client</artifactId>
				<version>${qingyun.xd.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-xd-order-client</artifactId>
				<version>${qingyun.xd.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-xd-report-client</artifactId>
				<version>${qingyun.xd.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-box</artifactId>
				<version>${qingyun.box.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-base-db</artifactId>
				<version>${qingyun.base.db.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-base-mvc</artifactId>
				<version>${qingyun.base.mvc.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-basic</artifactId>
				<version>${qingyun.basic.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-cache</artifactId>
				<version>${qingyun.cache.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-shop-client</artifactId>
				<version>${qingyun.shop.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-product-client</artifactId>
				<version>${qingyun.product.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-price-client</artifactId>
				<version>${qingyun.price.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-storage-client</artifactId>
				<version>${qingyun.storage.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-xs-user-client</artifactId>
				<version>${qingyun.xs.user.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-mq</artifactId>
				<version>${qingyun.mq.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-common-client</artifactId>
				<version>${qingyun.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-sync</artifactId>
				<version>${qingyun.sync.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-order-client</artifactId>
				<version>${qingyun.order.version}</version>
			</dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-weixin-client</artifactId>
                <version>${qingyun.weixin.version}</version>
            </dependency>
			<dependency>
				<groupId>com.pinshang.qingyun</groupId>
				<artifactId>qingyun-report-search-client</artifactId>
				<version>${qingyun.report.search.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-test</artifactId>
				<version>${qingyun.infrastructure.version}</version>
				<scope>test</scope>
			</dependency>
		</dependencies>

	</dependencyManagement>
	<distributionManagement>
		<repository>
			<id>nexus-releases</id>
			<name>Nexus Release Repository</name>
			<url>http://192.168.0.31:9001/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>nexus-snapshots</id>
			<name>Nexus Snapshot Repository</name>
			<url>http://192.168.0.31:9001/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
