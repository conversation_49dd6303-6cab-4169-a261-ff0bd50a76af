mybatis:
  type-aliases-package: com.pinshang.qingyun.report.search.model
  mapper-locations: classpath*:mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
mapper:
  mappers:
  - com.pinshang.qingyun.base.mybatis.MyMapper
  not-empty: false
  identity: MYSQL
pagehelper:
  helperDialect: mysql
  reasonable: false
  supportMethodsArguments: true
  params: count=countSql
spring:
  session:
    store-type=none
  jackson:
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: true
feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
eureka:
  client:
    healthcheck:
      enabled: true
  instance:
    metadata-map:
      instanceVersion: 20250623-1
      appProfile: ${spring.profiles.active}
      appSwitch: ${application.name.switch}
      appCode: ${pinshang.application-name}
ribbon:
  eureka:
    enabled: true

management:
  endpoints:
    web:
      exposure:
        include: '*'

etl:
  filepath: C:/Users/<USER>/Pictures/spoon