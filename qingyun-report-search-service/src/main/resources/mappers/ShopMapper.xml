<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.search.mapper.ShopMapper">

    <select id="getShopByIdList" resultType="com.pinshang.qingyun.report.search.dto.ShopODTO">
        SELECT
            t.id shopId,
            t.shop_code,
            t.shop_name,
            t.shop_type,
            (CASE WHEN t.management_mode=1 THEN '直营' WHEN t.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
        from t_md_shop t
        where 1=1
        <if test="shopIdList != null and shopIdList.size >0 ">
            and t.id in
            <foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>

    </select>

    <select id = "shopByManagementMode" resultType="Long">
        SELECT id
        FROM t_md_shop ms
        WHERE ms.management_mode = #{managementMode}
    </select>


    <select id = "queryShopIdListByParam" resultType="java.lang.Long">
        select t.id from t_md_shop t
        where 1 = 1
        <if test="shopTypeList != null and shopTypeList.size > 0">
            AND t.shop_type in
            <foreach collection="shopTypeList" index="index" item="shopType" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>

        <if test="managementModeList != null and managementModeList.size > 0">
            AND t.management_mode in
            <foreach collection="managementModeList" index="index" item="managementMode" open="(" separator="," close=")">
                #{managementMode}
            </foreach>
        </if>
    </select>
</mapper>