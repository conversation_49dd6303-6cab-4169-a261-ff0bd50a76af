<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.search.mapper.EmployeeUserMapper">

    <select id="getUserIdsByCode" resultType="Long">
        SELECT user_id FROM t_employee_user WHERE employee_code IN
        <foreach collection="codes" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>

    <select id="selectCodeByUserId" resultType="String">
        SELECT employee_code FROM t_employee_user WHERE user_id = #{userId}
    </select>
</mapper>