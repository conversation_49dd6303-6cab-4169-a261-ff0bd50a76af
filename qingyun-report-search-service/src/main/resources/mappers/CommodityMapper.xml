<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.search.mapper.CommodityMapper">

    <select id="findAllFirstCategory" resultType="com.pinshang.qingyun.report.search.dto.CategoryODTO">
        select id,cate_name
        from t_category
        where cate_level = 1
    </select>

    <select id="findCommodityByCommodityIdList" resultType="com.pinshang.qingyun.report.search.dto.CommodityODTO">
        select
            t.id commodityId,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_unit_name commodityUnit,
            GROUP_CONCAT(tb.bar_code ORDER BY tb.default_state desc) barCodes,
            t.commodity_first_kind_id commodityFirstId,
            t.commodity_first_kind_name commodityFirstName,
            t.commodity_second_kind_id commoditySecondId,
            t.commodity_second_kind_name commoditySecondName,
            t.commodity_third_kind_id commodityThirdId,
            t.commodity_third_kind_name commodityThirdName,
            t.commodity_factory_code factoryCode,
            t.commodity_factory_name factoryName,
            t.commodity_workshop_code workshopCode,
            t.commodity_workshop_name workshopName,
            t.commodity_flowshop_name flowshopName,
            t.tax_rate
        from t_commodity t
        LEFT JOIN t_commodity_bar_code tb ON tb.commodity_id = t.id
        <if test="commodityIdList != null and commodityIdList.size >0 ">
            and t.id in
            <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="commodityCodeList != null and commodityCodeList.size >0 ">
            and t.commodity_code in
            <foreach collection="commodityCodeList" index="index" item="commodityCode" open="(" separator="," close=")">
                #{commodityCode}
            </foreach>
        </if>
        GROUP BY t.id
    </select>

    <select id="getCommodityByComodityId" resultType="com.pinshang.qingyun.report.search.dto.CommodityODTO">
        SELECT
        t.id commodityId,
        t.commodity_code,
        t.commodity_name,
        t.commodity_spec,
        t.commodity_unit_name,
        t.bar_code
        from t_commodity t
        where  t.id = #{commodityId}
    </select>

    <select id="findCommodityByBarcode" resultType="com.pinshang.qingyun.report.search.dto.CommodityODTO">
        SELECT
            t.id AS commodityId,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_unit_name AS commodityUnit,
            t.bar_code,
            t.is_weight,
            t.commodity_package_spec
        FROM t_commodity t
        WHERE t.id = (SELECT tc.commodity_id FROM t_commodity_bar_code tc where tc.bar_code = #{barCode})
    </select>

    <select id="findCommodityBarCodeByParam" resultType="com.pinshang.qingyun.report.search.model.Commodity">
        SELECT
            t.commodity_id commodityId,
            GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCode
        from t_commodity_bar_code t
        where 1=1
        <if test="commodityIdList != null and commodityIdList.size >0 ">
            and t.commodity_id in
            <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        GROUP BY t.commodity_id
    </select>
</mapper>