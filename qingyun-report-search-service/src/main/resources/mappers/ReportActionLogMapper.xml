<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.search.mapper.ReportActionLogMapper">
    <select id="selectMatchInfoByDescription" resultType="com.pinshang.qingyun.report.search.model.ReportActionLog">
        SELECT
        id,
        product_name productName,
        first_level_menu firstLevelMenu,
        second_level_menu secondLevelMenu,
        menu_name menuName,
        opera_type operaType
        FROM t_report_action_log
        WHERE uri = #{uri}
        and application_name = #{applicationName}
    </select>

    <select id="selectReportMenuList" resultType="com.pinshang.qingyun.report.search.dto.ReportMenuItem">
        SELECT
        id as id,
        first_level_menu_id as firstLevelMenuId,
        second_level_menu_id as secondLevelMenuId,
        third_level_menu_id as thirdLevelMenuId
        FROM
        t_report_action_log
        <where>
            <if test="systemId != null ">
                system_id = #{systemId}
            </if>
        </where>
        GROUP BY first_level_menu_id,
        second_level_menu_id,
        third_level_menu_id,
        system_id
        ORDER BY system_id,
        first_level_menu_id,
        second_level_menu_id,
        third_level_menu_id
    </select>

    <select id="selectMenuName" resultType="java.lang.String">
        SELECT
        <if test='type == "10"'>first_level_menu_id</if>
        <if test='type == "20"'>second_level_menu_id</if>
        <if test='type == "30"'>third_level_menu_id</if>
        FROM t_report_action_log WHERE id = #{id};
    </select>

    <select id="selectDistinctMenu" resultType="com.pinshang.qingyun.report.search.dto.ReportMenuItemVO">
        SELECT
        first_level_menu AS firstLevelMenu,
        second_level_menu AS secondLevelMenu,
        menu_name AS menuName,
        product_name AS productName
        FROM
        t_report_action_log
        GROUP BY first_level_menu,
        second_level_menu,
        menu_name,
        product_name
    </select>
</mapper>
