<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.search.mapper.ShortDeliveryReportMapper">

    <!-- 短交数据 to ES -->
    <select id="getShortDeliveryReportByOrderTime" resultType="com.pinshang.qingyun.report.search.elasticsearch.document.ESShortDeliveryReport">
        select
            o.id id,
            ms.id shopId,
            ms.shop_code shopCode,
            ms.shop_name shopName,
            ms.shop_type shopType,
            DATE_FORMAT(o.order_time,'%Y-%m-%d') orderTime,
            o.order_code orderCode,
            c.`commodity_first_kind_id` commodityFirstKindId,
            c.`commodity_first_kind_name` commodityFirstKindName,
            c.`commodity_second_kind_id` commoditySecondKindId,
            c.`commodity_second_kind_name` commoditySecondKindName,
            c.`commodity_third_kind_id` commodityThirdKindId,
            c.`commodity_third_kind_name` commodityThirdKindName,
            c.`id` commodityId,
            c.`commodity_code` commodityCode,
            c.`commodity_name` commodityName,
            c.`commodity_spec` commoditySpec,
            o.`commodity_unit_name` commodityUnitName,
            o.quantity orderNum,
            o.`price` price,
            IFNULL(o.real_delivery_quantity,0) deliveryNum,
            IFNULL(o.real_receive_quantity,0) receiveNum,
            (case when o.real_delivery_quantity is null then o.quantity else concat(o.quantity-o.real_delivery_quantity,'') end) as differNum,
            (case when o.real_delivery_quantity is null then '100.00%' else concat(FORMAT((o.quantity-o.real_delivery_quantity)/o.quantity*100,2),'%') end) as rate,
            o.factory_code factoryCode,
            o.factory_name factoryName,
            o.workshop_code workshopCode,
            o.workshop_name workshopName,
            ts.store_code storeCode,
            o.real_name createName,
            ts.store_line_group_name storeLineGroupName,
            ts.store_type_id storeTypeId,
            ts.store_type_name storeTypeName,
            o.status receiveStatus,
            c.commodity_flowshop_name flowshopName,
            IFNULL(o.consignment_id,-1) consignmentId
        from t_day_order_report o
        LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
        LEFT join t_store ts on ts.id=o.`store_id`
        LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
        where 1=1
        and o.order_status = 0
        and o.order_time = #{orderTime}


        UNION ALL

        select
            CONCAT(so.id,'99999') id,
            ms.id shopId,
            ms.shop_code shopCode,
            ms.shop_name shopName,
            ms.shop_type shopType,
            DATE_FORMAT(so.order_time,'%Y-%m-%d') orderTime,
            so.order_code orderCode,
            c.`commodity_first_kind_id` commodityFirstKindId,
            c.`commodity_first_kind_name` commodityFirstKindName,
            c.`commodity_second_kind_id` commoditySecondKindId,
            c.`commodity_second_kind_name` commoditySecondKindName,
            c.`commodity_third_kind_id` commodityThirdKindId,
            c.`commodity_third_kind_name` commodityThirdKindName,
            c.`id` commodityId,
            c.`commodity_code` commodityCode,
            c.`commodity_name` commodityName,
            c.`commodity_spec` commoditySpec,
            so.`commodity_unit_name` commodityUnitName,
            so.require_quantity orderNum,
            so.`price` price,
            IFNULL(so.real_receive_quantity,0) deliveryNum,
            IFNULL(so.real_receive_quantity,0) receiveNum,
            (case when so.real_receive_quantity is null then so.require_quantity else concat(so.require_quantity-so.real_receive_quantity,'') end) as differNum,
            (case when so.real_receive_quantity is null then '100.00%' else concat(FORMAT((so.require_quantity-so.real_receive_quantity)/so.require_quantity*100,2),'%') end) as rate,
            so.factory_code factoryCode,
            so.factory_name factoryName,
            so.workshop_code workshopCode,
            so.workshop_name workshopName,
            ts.store_code storeCode,
            so.real_name createName,
            ts.store_line_group_name storeLineGroupName,
            ts.store_type_id storeTypeId,
            ts.store_type_name storeTypeName,
            so.receive_status receiveStatus,
            c.commodity_flowshop_name flowshopName,
            -1
        from t_day_preorder_report so
        LEFT join `t_md_shop` ms on ms.`store_id` =  so.`store_id`
        LEFT join t_store ts on ts.id=so.`store_id`
        LEFT join `t_commodity` c on c.`id` = so.`commodity_id`
        where 1=1
        and so.order_status in (1,2)
        and so.order_time = #{orderTime}

    </select>


    <select id="getShortDeliveryReportByOrderCodeList" resultType="com.pinshang.qingyun.report.search.elasticsearch.document.ESShortDeliveryReport">
        select
            o.id id,
            ms.id shopId,
            ms.shop_code shopCode,
            ms.shop_name shopName,
            ms.shop_type shopType,
            DATE_FORMAT(o.order_time,'%Y-%m-%d') orderTime,
            o.order_code orderCode,
            c.`commodity_first_kind_id` commodityFirstKindId,
            c.`commodity_first_kind_name` commodityFirstKindName,
            c.`commodity_second_kind_id` commoditySecondKindId,
            c.`commodity_second_kind_name` commoditySecondKindName,
            c.`commodity_third_kind_id` commodityThirdKindId,
            c.`commodity_third_kind_name` commodityThirdKindName,
            c.`id` commodityId,
            c.`commodity_code` commodityCode,
            c.`commodity_name` commodityName,
            c.`commodity_spec` commoditySpec,
            o.`commodity_unit_name` commodityUnitName,
            o.quantity orderNum,
            o.`price` price,
            IFNULL(o.real_delivery_quantity,0) deliveryNum,
            IFNULL(o.real_receive_quantity,0) receiveNum,
            (case when o.real_delivery_quantity is null then o.quantity else concat(o.quantity-o.real_delivery_quantity,'') end) as differNum,
            (case when o.real_delivery_quantity is null then '100.00%' else concat(FORMAT((o.quantity-o.real_delivery_quantity)/o.quantity*100,2),'%') end) as rate,
            o.factory_code factoryCode,
            o.factory_name factoryName,
            o.workshop_code workshopCode,
            o.workshop_name workshopName,
            ts.store_code storeCode,
            o.real_name createName,
            ts.store_line_group_name storeLineGroupName,
            ts.store_type_id storeTypeId,
            ts.store_type_name storeTypeName,
            o.status receiveStatus,
            c.commodity_flowshop_name flowshopName,
            IFNULL(o.consignment_id,-1) consignmentId
        from t_day_order_report o
                 LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
                 LEFT join t_store ts on ts.id=o.`store_id`
                 LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
        where 1=1
          and o.order_status = 0
          and o.order_code in
        <foreach collection="orderCodeList" index="index" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
    </select>

    <!--  门店订货汇总表 -->
    <select id="shopOrderGoodReport" resultType="com.pinshang.qingyun.report.search.dto.ShopOrderGoodReportODto" parameterType="com.pinshang.qingyun.report.search.dto.ShopOrderGoodReportIDto" >
        SELECT
            t.shop_id,
            t.shop_type,
            t.shop_code,
            t.shop_name,

            t.commodity_id,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_first_name,
            t.commodity_second_name,
            t.commodity_third_name,
            t.commodity_unit_name commodityUnit,

            sum(t.quantity) orderNum,
            sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
            sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
            sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum,
            t.factory_name,
            t.workshop_name
        FROM (

                 select
                     ms.id shop_id,
                     ms.shop_type,
                     ms.shop_code,
                     ms.shop_name,

                     c.`id` commodity_id,
                     c.`commodity_code`,
                     c.`commodity_name`,
                     c.`commodity_spec`,
                     o.`commodity_unit_name`,
                     c.commodity_first_kind_name commodity_first_name,
                     c.commodity_second_kind_name commodity_second_name,
                     c.commodity_third_kind_name commodity_third_name,

                     o.quantity quantity,
                     IFNULL(o.real_delivery_quantity,0) real_delivery_quantity,
                     (o.price*IFNULL(o.real_delivery_quantity,0)) real_delivery_amount,
                     o.quantity-IFNULL(o.real_delivery_quantity,0) differ_num,

                     o.factory_name,
                     o.workshop_name
                 from t_day_order_report o
                          LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
                          LEFT join t_store ts on ts.id=o.`store_id`
                          LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
                 where 1=1
                   and o.order_status = 0
                    <if test="vo.shopType != null ">
                        AND ms.shop_type = #{vo.shopType}
                    </if>
                    <if test="vo.shopId != null">
                        AND ms.id =  #{vo.shopId}
                    </if>
                    <if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
                        and o.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
                    </if>
                    <if test="vo.cate1 != null">
                        AND c.commodity_first_kind_id =  #{vo.cate1}
                    </if>
                    <if test="vo.cate2 != null">
                        AND c.commodity_second_kind_id =  #{vo.cate2}
                    </if>
                    <if test="vo.cate3 != null">
                        AND c.commodity_third_kind_id =  #{vo.cate3}
                    </if>

                    <if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
                        and o.`commodity_id` in
                        <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                            #{commodityId}
                        </foreach>
                    </if>
                    <if test="vo.barCode != null and vo.barCode !=''">
                        AND o.`commodity_id` =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
                    </if>

                    <if test="vo.factoryCode != null and vo.factoryCode != ''">
                        AND o.factory_code = #{vo.factoryCode}
                    </if>

                    <if test="vo.differ != null and vo.differ == true">
                        and o.quantity != o.real_delivery_quantity
                    </if>

                 UNION ALL

                 select
                     ms.id shop_id,
                     ms.shop_type,
                     ms.shop_code,
                     ms.shop_name,

                     c.`id` commodity_id,
                     c.`commodity_code`,
                     c.`commodity_name`,
                     c.`commodity_spec`,
                     so.`commodity_unit_name`,
                     c.commodity_first_kind_name commodity_first_name,
                     c.commodity_second_kind_name commodity_second_name,
                     c.commodity_third_kind_name commodity_third_name,

                     so.require_quantity quantity,
                     so.real_receive_quantity real_delivery_quantity,
                     (so.price*IFNULL(so.real_receive_quantity,0)) real_delivery_amount,
                     so.require_quantity-IFNULL(so.real_receive_quantity,0) differ_num,

                     so.factory_name,
                     so.workshop_name
                 from t_day_preorder_report so
                          LEFT join `t_md_shop` ms on ms.`store_id` =  so.`store_id`
                          LEFT join t_store ts on ts.id= so.`store_id`
                          LEFT join `t_commodity` c on c.`id` = so.`commodity_id`
                 where 1=1
                   and so.order_status in (1,2)
                    <if test="vo.shopType != null ">
                        AND ms.shop_type = #{vo.shopType}
                    </if>
                    <if test="vo.shopId != null">
                        AND ms.id =  #{vo.shopId}
                    </if>
                    <if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
                        and so.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
                    </if>
                    <if test="vo.cate1 != null">
                        AND c.commodity_first_kind_id =  #{vo.cate1}
                    </if>
                    <if test="vo.cate2 != null">
                        AND c.commodity_second_kind_id =  #{vo.cate2}
                    </if>
                    <if test="vo.cate3 != null">
                        AND c.commodity_third_kind_id =  #{vo.cate3}
                    </if>

                    <if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
                        and so.`commodity_id` in
                        <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                            #{commodityId}
                        </foreach>
                    </if>
                    <if test="vo.barCode != null and vo.barCode !=''">
                        AND so.`commodity_id` =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
                    </if>

                    <if test="vo.factoryCode != null and vo.factoryCode != '' ">
                        AND so.factory_code = #{vo.factoryCode}
                    </if>

                    <if test="vo.differ != null and vo.differ == true">
                        and so.require_quantity != so.real_receive_quantity
                    </if>
             ) t GROUP BY t.shop_id,t.commodity_id
                ORDER  BY  t.shop_id,t.commodity_id
    </select>



    <!--  商品实发汇总表 公用-->
    <sql id="realDeliveryReportWhere">
        select
            c.`id` commodity_id,
            c.`commodity_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            o.`commodity_unit_name`,
            c.commodity_first_kind_name commodity_first_name,

            o.quantity quantity,
            IFNULL(o.real_delivery_quantity,0) real_delivery_quantity,
            (o.price*IFNULL(o.real_delivery_quantity,0)) real_delivery_amount,
            o.quantity-IFNULL(o.real_delivery_quantity,0) differ_num,

            o.factory_name,
            o.workshop_name,
            c.commodity_flowshop_name flowshop_name
        from t_day_order_report o
        LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
        LEFT join t_store ts on ts.id=o.`store_id`
        LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
        where 1=1
        and o.order_status = 0 and o.real_delivery_quantity >= 0
        <if test="vo.commodityKey != null and vo.commodityKey !='' ">
            AND (c.`commodity_code` like concat('%',#{vo.commodityKey},'%') or c.`commodity_name` like concat('%',#{vo.commodityKey},'%')  )
        </if>
        <if test="vo.commodityId != null">
            AND o.commodity_id =  #{vo.commodityId}
        </if>
        <if test="vo.barCode != null and vo.barCode !=''">
            AND o.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
        </if>
        <if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
            and o.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
        </if>
        <if test="vo.categoryId != null">
            AND c.commodity_first_kind_id = #{vo.categoryId}
        </if>
        <if test="vo.factoryCode != null and vo.factoryCode != ''">
            AND o.factory_code = #{vo.factoryCode}
        </if>
        <if test="vo.workshopCodeOrName != null and vo.workshopCodeOrName != ''">
            AND (o.`workshop_code` like concat('%',#{vo.workshopCodeOrName},'%') or o.`workshop_name` like concat('%',#{vo.workshopCodeOrName},'%')  )
        </if>
        <if test="vo.flowshopCodeOrName != null and vo.flowshopCodeOrName !=''">
            AND c.`commodity_flowshop_name` like concat('%',#{vo.flowshopCodeOrName},'%')
        </if>
        <if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
            and o.commodity_id in
            <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="vo.differ != null and vo.differ == true">
            and (o.real_delivery_quantity is null or o.quantity != o.real_delivery_quantity)
        </if>

        UNION ALL

        select
            c.`id` commodity_id,
            c.`commodity_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            so.`commodity_unit_name`,
            c.commodity_first_kind_name commodity_first_name,

            so.require_quantity quantity,
            so.real_receive_quantity real_delivery_quantity,
            (so.price*IFNULL(so.real_receive_quantity,0)) real_delivery_amount,
            so.require_quantity-IFNULL(so.real_receive_quantity,0) differ_num,

            so.factory_name,
            so.workshop_name,
            c.commodity_flowshop_name flowshop_name
        from t_day_preorder_report so
        LEFT join `t_md_shop` ms on ms.`store_id` =  so.`store_id`
        LEFT join t_store ts on ts.id= so.`store_id`
        LEFT join `t_commodity` c on c.`id` = so.`commodity_id`
        where 1=1
        and so.order_status in (1,2) and so.real_receive_quantity >= 0
        <if test="vo.commodityKey != null and vo.commodityKey !='' ">
            AND (c.`commodity_code` like concat('%',#{vo.commodityKey},'%') or c.`commodity_name` like concat('%',#{vo.commodityKey},'%')  )
        </if>
        <if test="vo.commodityId != null">
            AND so.commodity_id =  #{vo.commodityId}
        </if>
        <if test="vo.barCode != null and vo.barCode !=''">
            AND so.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
        </if>
        <if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
            and so.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
        </if>
        <if test="vo.categoryId != null">
            AND c.commodity_first_kind_id = #{vo.categoryId}
        </if>
        <if test="vo.factoryCode != null and vo.factoryCode != ''">
            AND so.factory_code = #{vo.factoryCode}
        </if>
        <if test="vo.workshopCodeOrName != null and vo.workshopCodeOrName != ''">
            AND (so.`workshop_code` like concat('%',#{vo.workshopCodeOrName},'%') or so.`workshop_name` like concat('%',#{vo.workshopCodeOrName},'%')  )
        </if>
        <if test="vo.flowshopCodeOrName != null and vo.flowshopCodeOrName !=''">
             AND c.`commodity_flowshop_name` like concat('%',#{vo.flowshopCodeOrName},'%')
         </if>
        <if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
            and so.commodity_id in
            <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="vo.differ != null and vo.differ == true">
            and (so.real_receive_quantity is null or so.require_quantity != so.real_receive_quantity)
        </if>

    </sql>

    <!--  商品实发汇总表 -->
    <select id="realDeliveryReportCurrentDay" resultType="com.pinshang.qingyun.report.search.dto.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.search.dto.RealDeliveryReportIDto" >
        SELECT
            t.commodity_id,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_first_name,
            t.commodity_unit_name commodityUnit,

            sum(t.quantity) orderNum,
            sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
            sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
            sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum,
            t.factory_name,
            t.workshop_name,
            t.flowshop_name
        FROM (
          <include refid="realDeliveryReportWhere"></include>
        ) t GROUP BY t.commodity_id
        ORDER  BY  t.commodity_id
    </select>
    <!--  商品实发汇总表  合计 -->
    <select id="realTotalDeliveryReportCurrentDay" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.report.search.dto.RealDeliveryReportIDto" >
        SELECT
        sum(IFNULL(t.real_delivery_amount,0))
        FROM
        (
          <include refid="realDeliveryReportWhere"></include>
        ) t
    </select>




    <!--  商品实发汇总表(按客户类型) 公用 -->
    <sql id="realDeliveryStoreTypeReportWhere">
        select
            ts.store_type_id store_type_id,
            ts.store_type_name store_type_name,
            c.`id` commodity_id,
            c.`commodity_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            o.`commodity_unit_name`,
            c.commodity_first_kind_name commodity_first_name,

            o.quantity quantity,
            IFNULL(o.real_delivery_quantity,0) real_delivery_quantity,
            (o.price*IFNULL(o.real_delivery_quantity,0)) real_delivery_amount,
            o.quantity-IFNULL(o.real_delivery_quantity,0) differ_num,

            o.factory_name,
            o.workshop_name,
            c.commodity_flowshop_name flowshop_name
        from t_day_order_report o
        LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
        LEFT join t_store ts on ts.id=o.`store_id`
        LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
        where 1=1
        and o.order_status = 0 and o.real_delivery_quantity >= 0
        <if test="vo.commodityKey != null and vo.commodityKey !='' ">
            AND (c.`commodity_code` like concat('%',#{vo.commodityKey},'%') or c.`commodity_name` like concat('%',#{vo.commodityKey},'%')  )
        </if>
        <if test="vo.commodityId != null">
            AND o.commodity_id =  #{vo.commodityId}
        </if>
        <if test="vo.barCode != null and vo.barCode !=''">
            AND o.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
        </if>
        <if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
            and o.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
        </if>
        <if test="vo.categoryId != null">
            AND c.commodity_first_kind_id = #{vo.categoryId}
        </if>
        <if test="vo.factoryCode != null and vo.factoryCode != ''">
            AND o.factory_code = #{vo.factoryCode}
        </if>
        <if test="vo.workshopCodeOrName != null and vo.workshopCodeOrName != ''">
            AND (o.`workshop_code` like concat('%',#{vo.workshopCodeOrName},'%') or o.`workshop_name` like concat('%',#{vo.workshopCodeOrName},'%')  )
        </if>
        <if test="vo.flowshopCodeOrName != null and vo.flowshopCodeOrName !=''">
            AND c.`commodity_flowshop_name` like concat('%',#{vo.flowshopCodeOrName},'%')
        </if>
        <if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
            and o.commodity_id in
            <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="vo.commodityIdList2 != null and vo.commodityIdList2.size >0 ">
            and o.commodity_id in
            <foreach collection="vo.commodityIdList2" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="vo.differ != null and vo.differ == true">
            and (o.real_delivery_quantity is null or o.quantity != o.real_delivery_quantity)
        </if>
        <if test="vo.storeTypeId != null">
            AND ts.store_type_id =  #{vo.storeTypeId}
        </if>

        UNION ALL

        select
            ts.store_type_id store_type_id,
            ts.store_type_name store_type_name,
            c.`id` commodity_id,
            c.`commodity_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            so.`commodity_unit_name`,
            c.commodity_first_kind_name commodity_first_name,

            so.require_quantity quantity,
            so.real_receive_quantity real_delivery_quantity,
            (so.price*IFNULL(so.real_receive_quantity,0)) real_delivery_amount,
            so.require_quantity-IFNULL(so.real_receive_quantity,0) differ_num,

            so.factory_name,
            so.workshop_name,
            c.commodity_flowshop_name flowshop_name
        from t_day_preorder_report so
        LEFT join `t_md_shop` ms on ms.`store_id` =  so.`store_id`
        LEFT join t_store ts on ts.id= so.`store_id`
        LEFT join `t_commodity` c on c.`id` = so.`commodity_id`
        where 1=1
        and so.order_status in (1,2) and so.real_receive_quantity >= 0
        <if test="vo.commodityKey != null and vo.commodityKey !='' ">
            AND (c.`commodity_code` like concat('%',#{vo.commodityKey},'%') or c.`commodity_name` like concat('%',#{vo.commodityKey},'%')  )
        </if>
        <if test="vo.commodityId != null">
            AND so.commodity_id =  #{vo.commodityId}
        </if>
        <if test="vo.barCode != null and vo.barCode !=''">
            AND so.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
        </if>
        <if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
            and so.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
        </if>
        <if test="vo.categoryId != null">
            AND c.commodity_first_kind_id = #{vo.categoryId}
        </if>
        <if test="vo.factoryCode != null and vo.factoryCode != ''">
            AND so.factory_code = #{vo.factoryCode}
        </if>
        <if test="vo.workshopCodeOrName != null and vo.workshopCodeOrName != ''">
            AND (so.`workshop_code` like concat('%',#{vo.workshopCodeOrName},'%') or so.`workshop_name` like concat('%',#{vo.workshopCodeOrName},'%')  )
        </if>
        <if test="vo.flowshopCodeOrName != null and vo.flowshopCodeOrName !=''">
            AND c.`commodity_flowshop_name` like concat('%',#{vo.flowshopCodeOrName},'%')
        </if>
        <if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
            and so.commodity_id in
            <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="vo.commodityIdList2 != null and vo.commodityIdList2.size >0 ">
            and so.commodity_id in
            <foreach collection="vo.commodityIdList2" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        <if test="vo.differ != null and vo.differ == true">
            and (so.real_receive_quantity is null or so.require_quantity != so.real_receive_quantity)
        </if>
        <if test="vo.storeTypeId != null">
            AND ts.store_type_id =  #{vo.storeTypeId}
        </if>
    </sql>

    <!--  商品实发汇总表 (按客户类型)-->
    <select id="realDeliveryStoreTypeReportCurrentDay" resultType="com.pinshang.qingyun.report.search.dto.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.search.dto.RealDeliveryReportIDto" >
        SELECT
            t.store_type_name storeTypeName,
            t.commodity_id,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_first_name,
            t.commodity_unit_name commodityUnit,

            sum(t.quantity) orderNum,
            sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
            sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
            sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum,
            t.factory_name,
            t.workshop_name,
            t.flowshop_name
        FROM (
          <include refid="realDeliveryStoreTypeReportWhere"></include>
        ) t GROUP BY t.store_type_id,t.commodity_id
        ORDER  BY  t.store_type_id,t.commodity_id
    </select>

    <!--  商品实发汇总表 (按客户类型) 合计 -->
    <select id="realTotalDeliveryStoreTypeReportCurrentDay" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.report.search.dto.RealDeliveryReportIDto" >
        SELECT
        sum(IFNULL(t.real_delivery_amount,0))
        FROM
        (
          <include refid="realDeliveryStoreTypeReportWhere"></include>
        ) t
    </select>
</mapper>