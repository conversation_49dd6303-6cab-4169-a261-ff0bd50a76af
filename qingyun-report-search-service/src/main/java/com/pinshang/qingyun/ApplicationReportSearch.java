package com.pinshang.qingyun;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.base.spring.MainArgsPreHandler;
import com.pinshang.qingyun.infrastructure.apm.cat.springboot.EnableCatMetrics;
import com.pinshang.qingyun.infrastructure.loadBalancer.starter.EnableQyLoadBalancer;
import com.pinshang.qingyun.infrastructure.mq.starter.EnableMqComponent;
import com.pinshang.qingyun.infrastructure.switcher.starter.EnableOnlineSwitchComponent;
import com.pinshang.qinyun.cache.service.RedisServiceDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.List;

@Controller
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan(basePackages = {"com.pinshang.qingyun.report.search.mapper", "com.pinshang.qingyun.sync.mapper"})
@ComponentScan(basePackages = "com.pinshang.qingyun")
@EnableCircuitBreaker
@EnableKafka
@EnableAsync
@EnableScheduling
@Import(value = {RedisServiceDefinition.class})
@EnableApolloConfig
@EnableMqComponent
@EnableQyLoadBalancer
@EnableOnlineSwitchComponent
@EnableCatMetrics
public class ApplicationReportSearch extends WebMvcConfigurerAdapter {
	private Logger logger = LoggerFactory.getLogger(getClass());

	public static void main(String[] args) {
		SpringApplication.run(ApplicationReportSearch.class, MainArgsPreHandler.argsHandle(args));
	}

	@GetMapping(value={"","/"})
	public String index(){
		return "redirect:/chk.html";
	}

	@RequestMapping(value = "/test", method = RequestMethod.GET)
	@ResponseBody
	public String testLogLevel() {
		logger.debug("Logger Level ：DEBUG************************************");
		logger.info("Logger Level ：INFO************************************");
		logger.error("Logger Level ：ERROR************************************");
		return "success";
	}

	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
		MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
		ObjectMapper objectMapper = new ObjectMapper();
		SimpleModule simpleModule = new SimpleModule();
		simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
//        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
		objectMapper.registerModule(simpleModule);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		jackson2HttpMessageConverter.setObjectMapper(objectMapper);
		converters.add(jackson2HttpMessageConverter);
	}
}
