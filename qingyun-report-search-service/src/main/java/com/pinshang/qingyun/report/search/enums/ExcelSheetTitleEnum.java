package com.pinshang.qingyun.report.search.enums;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ExcelSheetTitleEnum {

    SHOP_SHORT_DELIVERY_HQ("shop_short_delivery","短交报表", new String[] {"门店类型","门店名称","送货日期","订单号","大类","中类","小类","条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","发货数量","差异数量","短交比例","工厂","生产组", "客户编号","下单人","线路组","发货仓库","经营模式"}),
   SHOP_SHORT_DELIVERY("shop_short_delivery","短交报表", new String[] {"门店类型","门店名称","送货日期","订单号","大类","中类","小类","条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","发货数量","差异数量","短交比例","工厂","生产组", "客户编号","下单人"}),

    ACTUAL_RECEIPT_ANALYSIS_REPORT("actual_receipt_analysis_report","门店实收商品分析表",new String[] {"门店名称","一级分类","条形码","商品编码","商品名称","规格","订货数量","实发数量","实收数量","实收差异数量","供货价","供货金额","供应商","采购员"}),

    SHOP_REAL_DELIVERY("shop_real_delivery","商品实发汇总报表", new String[] {"商品编码","商品名称","规格","条码","一级品类","计量单位","订货数量","实发数量","实发金额","差异数量","工厂","生产组","车间","税率"}),
    SHOP_REAL_DELIVERY_STORE_TYPE("shop_real_delivery_store_type","商品实发汇总报表(客户类型)", new String[] {"客户类型","商品编码","商品名称","规格","条码","一级品类","计量单位","订货数","实发数量","实发金额","默认仓库","工厂","生产组","车间"}),

    // 注释 POS----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    COMMODITY_SALES_SUMMARY_WITHSHOP("commodity_sales_summary_withshop","商品销售汇总",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","零售价","销售数量","退货数量","赠送数量","销售金额","退货金额","赠送金额","让利金额","数量小计","金额小计","不含税金额","税额"}),
    COMMODITY_SALES_SUMMARY_NOSHOP("commodity_sales_summary_noshop","商品销售汇总",new String[] {"大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","零售价","销售数量","退货数量","赠送数量","销售金额","退货金额","赠送金额","让利金额","数量小计","金额小计","税率","不含税金额","税额"}),


    COMMODITY_SALES_SUMMARY_ANALYSIS_WITHSHOP("commodity_sales_summary_analysis_withshop","商品销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),
    COMMODITY_SALES_SUMMARY_ANALYSIS_NOSHOP("commodity_sales_summary_analysis_noshop","商品销售汇总分析",new String[] {"大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),


    CATE_FIRST_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","大类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_FIRST_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"大类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","大类名称","中类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"大类名称","中类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"大类名称","中类名称","小类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),

    CATE_FIRST_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_FIRST_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"大类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","中类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"大类名称","中类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"大类名称","中类名称","小类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),

    BEST_SELLS_REPORT("best_sells_report","畅销品排行",new String[] {"一级分类","商品编码","条形码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","平均售价"}),

    SHOP_SALES_SUMMARY_ANALYSIS_WITHSHOP("shop_sales_summary_analysis_withshop","门店销售汇总分析",new String[] {"部门","门店编码","门店","销售金额","销售数量","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),
    SHOPDAY_SUMMARY_ANALYSIS_NOSHOP("shopday_sales_summary_analysis_noshop","门店销售汇总分析",new String[] {"销售日期","部门","门店编码","门店","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),
    ACTION_LOG_DETAIL("search_logs_details","访问日志明细", new String[]{"一级菜单", "二级菜单", "菜单名称","访问动作", "访问时间","账号编码","账号名称", "账号所属机构","所属系统"}),
    ACTION_LOG_LIST("search_logs_list","访问日志汇总", new String[]{ "一级菜单", "二级菜单", "菜单名称","访问次数","所属系统"})
    ;
    private String code;
    private String name;
    private String[] titles;
    /** 属性名 */
    private String[] props;

    private ExcelSheetTitleEnum(String code, String name, String[] titles) {
        this.code = code;
        this.name = name;
        this.titles = titles;
    }

    ExcelSheetTitleEnum(String code, String name, String[] titles, String[] props) {
        this.code = code;
        this.name = name;
        this.titles = titles;
        this.props = props;
    }


    /**
     * 动态设置title
     * @param code
     * @param titles
     */
    public static void setTitles(String code, String[] titles) {
        for (ExcelSheetTitleEnum est : ExcelSheetTitleEnum.values()) {
            if (code.equals(est.getCode())) {
                est.setTitles(titles);
                break;
            }
        }
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String[] getTitles() {
        return titles;
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
    }

    public String[] getProps() {
        return props;
    }

    public void setProps(String[] props) {
        this.props = props;
    }

    public static List<List<String>> getExcelHead(ExcelSheetTitleEnum sheetTitle){
        String[] head = sheetTitle.getTitles();
        return Arrays.stream(head).map(title -> {
            List<String> head0 = new ArrayList<String>();
            head0.add(title);
            return head0;
        }).collect(Collectors.toList());
    }
}
