package com.pinshang.qingyun.report.search.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.search.dto.*;
import com.pinshang.qingyun.report.search.elasticsearch.document.ESShortDeliveryReport;
import com.pinshang.qingyun.report.search.elasticsearch.document.EsDaySaleSummary;
import com.pinshang.qingyun.report.search.elasticsearch.document.EsDayThirdSaleSummary;
import com.pinshang.qingyun.report.search.elasticsearch.repository.EsDaySaleSummaryRepository;
import com.pinshang.qingyun.report.search.elasticsearch.repository.EsDayThirdSaleSummaryRepository;
import com.pinshang.qingyun.report.search.elasticsearch.repository.EsShortDeliveryReportRepository;
import com.pinshang.qingyun.report.search.enums.PosReportSummaryTypeEnum;
import com.pinshang.qingyun.report.search.enums.PosReportWithShopEnum;
import com.pinshang.qingyun.report.search.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.search.mapper.*;
import com.pinshang.qingyun.report.search.model.Category;
import com.pinshang.qingyun.report.search.model.DaySaleSummary;
import com.pinshang.qingyun.report.search.model.ThirdSummary;
import com.pinshang.qingyun.report.search.util.PageUtil;
import com.pinshang.qingyun.report.search.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.ConsignmentIDTO;
import com.pinshang.qingyun.shop.dto.ConsignmentODTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.consignment.ConsignmentClient;
import com.pinshang.qingyun.smm.dto.org.SelectShopParentOrgInfoIDTO;
import com.pinshang.qingyun.smm.dto.org.ShopAndShopParentOrgInfoODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.ScriptQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregator;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Avg;
import org.elasticsearch.search.aggregations.metrics.AvgAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.aggregations.metrics.SumAggregationBuilder;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.clients.elasticsearch7.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SalesSummaryReportService  {

    @Autowired
    private DaySaleSummaryMapper daySaleSummaryMapper;

    @Autowired
    private EsDaySaleSummaryRepository esDaySaleSummaryRepository;

    @Autowired
    private ThirdSummaryMapper thirdSummaryMapper;

    @Autowired
    private EsDayThirdSaleSummaryRepository esDayThirdSaleSummaryRepository;

    @Autowired
    private EsShortDeliveryReportRepository esShortDeliveryReportRepository;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private ShortDeliveryReportMapper shortDeliveryReportMapper;
    @Autowired
    private CommodityService commodityService;

    @Autowired
    private ConsignmentClient consignmentClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private OrgClient orgClient;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SMMUserClient smmUserClient;

    private Integer deleteNum = 10000000;

    public static final int BATCH_INSERT_ES_SIZE = 500;
    /**
     * 注释
     * 设置查询条件
     * @param idto
     * @param shopIdList
     * @param queryBuilder
     * @return
     */
    private boolean setQueryBuilder(SalesSummaryReportOrderIDTO idto, List<Long> shopIdList, NativeSearchQueryBuilder queryBuilder) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", shopIdList));

        if(idto.getShopId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", idto.getShopId()));
        }

        if(idto.getConsignmentId() != null){
            //-1 表示没有代销商的商品，es里面需要兼容为null的老数据
            if (idto.getConsignmentId() > 0) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("consignmentId", idto.getConsignmentId()));
            } else {
                List<ConsignmentODTO> consignmentList = consignmentClient.selectConsignmentList(new ConsignmentIDTO());
                if(!CollectionUtils.isEmpty(consignmentList)){
                    List<Long> consignmentIdList = consignmentList.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
                    boolQueryBuilder.mustNot(QueryBuilders.termsQuery("consignmentId", consignmentIdList));
                }
            }
        }

        if(StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate())){
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("saleTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));
        }

        if(idto.getCateId1() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstId", idto.getCateId1()));
        }
        if(idto.getCateId2() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commoditySecondId", idto.getCateId2()));
        }
        if(idto.getCateId3() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityThirdId", idto.getCateId3()));
        }

        BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
        if(StringUtils.isNotBlank(idto.getCommodityKey())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("commodityCode", String.format("*%s*", idto.getCommodityKey() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("commodityName", String.format("*%s*", idto.getCommodityKey() , "*%s*"))));
        }

        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return true;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }

        if(idto.getCommodityId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", idto.getCommodityId()));
        }

        queryBuilder.withQuery(boolQueryBuilder);

        // 结果过滤：通过sourceFilter设置返回的结果字段(聚合不查询字段)
        queryBuilder.withSourceFilter(new FetchSourceFilter(
                new String[]{/*"id", "shopId", "shopCode", "shopName" ,"commodityFirstId","commodityFirstName",
                        "commodityId","commodityCode","commodityName","commoditySpec","commodityUnit","commodityPrice","taxRate",
                        "saleTime", "saleQuantity","returnQuantity","giveQuantity","saleAmount","weightAmount","returnAmount",
                        "giveAmount","discountAmount","tatalAuantity","tatalAmount","noTaxRateAmount","taxRateAmount"*/}, null));
        return false;
    }

    /**
     * 设置聚合
     * @param idto
     * @param queryBuilder
     */
    private void setAggration(SalesSummaryReportOrderIDTO idto, NativeSearchQueryBuilder queryBuilder) {
        TermsAggregationBuilder shopIdAgg = AggregationBuilders.terms("group_shopId").field("shopId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.key(true)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder commodityAgg = AggregationBuilders.terms("group_commodity").field("commodityId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.key(true)).size(Integer.MAX_VALUE);

        TermsAggregationBuilder commodityFirstIdAgg = AggregationBuilders.terms("group_commodityFirstId").field("commodityFirstId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.key(true)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder commoditySecondIdAgg = AggregationBuilders.terms("group_commoditySecondId").field("commoditySecondId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.key(true)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder commodityThirdIdAgg = AggregationBuilders.terms("group_commodityThirdId").field("commodityThirdId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.key(true)).size(Integer.MAX_VALUE);

        TermsAggregationBuilder shopTatalAmountAgg = AggregationBuilders.terms("group_tatalAmount_shopId").field("shopId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.aggregation("sum_tatal_amount ", false)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder saleTimeAgg = AggregationBuilders.terms("group_saleTime").field("saleTime").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).order(BucketOrder.key(true)).size(Integer.MAX_VALUE);


        SumAggregationBuilder sum_sale_quantity = AggregationBuilders.sum("sum_sale_quantity").field("saleQuantity");
        SumAggregationBuilder sum_return_quantity = AggregationBuilders.sum("sum_return_quantity").field("returnQuantity");
        SumAggregationBuilder sum_give_quantity = AggregationBuilders.sum("sum_give_quantity").field("giveQuantity");
        SumAggregationBuilder sum_sale_amount = AggregationBuilders.sum("sum_sale_amount").field("saleAmount");
        SumAggregationBuilder sum_weight_amount = AggregationBuilders.sum("sum_weight_amount").field("weightAmount");
        SumAggregationBuilder sum_return_amount = AggregationBuilders.sum("sum_return_amount").field("returnAmount");
        SumAggregationBuilder sum_give_amount = AggregationBuilders.sum("sum_give_amount").field("giveAmount");
        SumAggregationBuilder sum_discount_amount = AggregationBuilders.sum("sum_discount_amount").field("discountAmount");
        SumAggregationBuilder sum_tatal_quantity = AggregationBuilders.sum("sum_tatal_quantity").field("tatalQuantity");
        SumAggregationBuilder sum_tatal_amount = AggregationBuilders.sum("sum_tatal_amount").field("tatalAmount");
        SumAggregationBuilder sum_no_tax_rate_amount = AggregationBuilders.sum("sum_no_tax_rate_amount").field("noTaxRateAmount");
        SumAggregationBuilder sum_tax_rate_amount = AggregationBuilders.sum("sum_tax_rate_amount").field("taxRateAmount");

        AvgAggregationBuilder avg_commodityPrice = AggregationBuilders.avg("avg_commodityPrice").field("commodityPrice");
        AvgAggregationBuilder avg_taxRate = AggregationBuilders.avg("avg_taxRate").field("taxRate");

        SumAggregationBuilder sum_no_tax_weight_amount = AggregationBuilders.sum("sum_no_tax_weight_amount").field("noTaxWeightAmount");

        // 1：商品汇总（区分门店）
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode()){
            queryBuilder.addAggregation(shopIdAgg.subAggregation(commodityAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(avg_commodityPrice).subAggregation(avg_taxRate)));
        }
        // 2：商品汇总（不区分门店）
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.TWO.getCode()){
            queryBuilder.addAggregation(commodityAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(avg_commodityPrice).subAggregation(avg_taxRate));
        }

        // 大类汇总,区分门店
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.THREE.getCode() && idto.getIsWithShop() == 1){
            queryBuilder.addAggregation(shopIdAgg.subAggregation(commodityFirstIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount)));
        }
        // 大类汇总,不区分门店
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.THREE.getCode() && idto.getIsWithShop() == 0){
            queryBuilder.addAggregation(commodityFirstIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount));
        }

        // 中类汇总,区分门店
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.FOUR.getCode() && idto.getIsWithShop() == 1){
            queryBuilder.addAggregation(shopIdAgg.subAggregation(commoditySecondIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount)));
        }
        // 中类汇总,不区分门店
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.FOUR.getCode() && idto.getIsWithShop() == 0){
            queryBuilder.addAggregation(commoditySecondIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount));
        }

        // 小类汇总,区分门店
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.FIVE.getCode() && idto.getIsWithShop() == 1){
            queryBuilder.addAggregation(shopIdAgg.subAggregation(commodityThirdIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount)));
        }
        // 小类汇总,不区分门店
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.FIVE.getCode() && idto.getIsWithShop() == 0){
            queryBuilder.addAggregation(commodityThirdIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount));
        }

        // 门店汇总,按销售额排序
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() && idto.getIsSortByAmount() == 1){
            queryBuilder.addAggregation(shopTatalAmountAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount));
        }
        // 门店汇总,不按销售额排序
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() && idto.getIsSortByAmount() == 0){
            queryBuilder.addAggregation(shopIdAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount));
        }
        // 门店汇总:日毛利率
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.SEVEN.getCode()){
            queryBuilder.addAggregation(shopIdAgg.subAggregation(saleTimeAgg.subAggregation(sum_sale_quantity).subAggregation(sum_return_quantity).subAggregation(sum_give_quantity).subAggregation(sum_sale_amount)
                    .subAggregation(sum_weight_amount).subAggregation(sum_return_amount).subAggregation(sum_give_amount).subAggregation(sum_discount_amount)
                    .subAggregation(sum_tatal_quantity).subAggregation(sum_tatal_amount).subAggregation(sum_no_tax_rate_amount).subAggregation(sum_tax_rate_amount)
                    .subAggregation(sum_no_tax_weight_amount)));
        }
    }

    private Map<Long, ShopAndShopParentOrgInfoODTO> getOrgInformation(List<Long> shopIdList) {
        Map<Long, ShopAndShopParentOrgInfoODTO> map = null;
        SelectShopParentOrgInfoIDTO shopParentOrgInfoIDTO = new SelectShopParentOrgInfoIDTO();
        shopParentOrgInfoIDTO.setShopIdList(shopIdList);
        List<ShopAndShopParentOrgInfoODTO> orgInfoList = orgClient.selectShopAndShopParentOrgInfoList(shopParentOrgInfoIDTO);
        if (null != orgInfoList && orgInfoList.size() > 0) {
            map = orgInfoList.stream().collect(Collectors.toMap(ShopAndShopParentOrgInfoODTO::getShopId, e -> e));
        }
        return map;
    }

    /**
     * 商品销售汇总,类别销售汇总
     * 门店，类别，商品销售汇总分析
     * @param idto
     * @return
     */
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(SalesSummaryReportOrderIDTO idto, Integer type){
        TablePageInfo info = new TablePageInfo();
        info.setList(null);
        info.setHeader(new TotalSalesSummaryODTO());

        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), idto.getOrgCode(), shopTypeList, idto.getManagementMode(), idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            return info;
        }

        if(!CollectionUtils.isEmpty(idto.getShopTypeList()) || !CollectionUtils.isEmpty(idto.getManagementModeList())) {
            List<Long> idList = shopMapper.queryShopIdListByParam(idto.getShopTypeList(), idto.getManagementModeList());
            if(CollectionUtils.isEmpty(idList)) {
                return info;
            }else {
                shopIdList.retainAll(idList);
                if(CollectionUtils.isEmpty(shopIdList)) {
                    return info;
                }
            }
        }

        // 商品销售汇总 、商品销售汇总分析
        // （汇总类型选择商品汇总（区分门店）时，选择全部门店时，必须输入商品；选择门店时，可以查询全部商品的销售数据）
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode()){
            if(idto.getShopId() == null && idto.getCommodityId() == null && StringUtils.isBlank(idto.getBarCode())){
                QYAssert.isTrue(false,"门店和商品不能全部为空");
            }
        }

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        Boolean isNull = setQueryBuilder(idto, shopIdList, queryBuilder);
        if (isNull) return info;


        // 设置聚合
        setAggration(idto, queryBuilder);
        queryBuilder.withTrackTotalHits(true);
        List<SalesSummaryReportOrderODTO> saleSummaryList = new ArrayList<>();
        //查询，获取结果(商品汇总)
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode() || idto.getSummaryType() == PosReportSummaryTypeEnum.TWO.getCode()){
            SearchHits<EsDaySaleSummary> search = elasticsearchTemplate.search(queryBuilder.build(),EsDaySaleSummary.class);
            if(CollectionUtils.isEmpty(search.getSearchHits())){
                return info;
            }

            Map<String, Aggregation> aggMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode()){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();

                    Map<String, Aggregation> commodityMap = shopIdBucket.getAggregations().getAsMap();
                    ParsedLongTerms commodityIdTerms = (ParsedLongTerms)commodityMap.get("group_commodity");
                    for(Terms.Bucket commodityBucket:commodityIdTerms.getBuckets()){
                        Long commodityId = (Long)commodityBucket.getKey();
                        setCommoditySummaryReturnBucket(saleSummaryList, commodityBucket, commodityId, shopId,null,null,null,null);
                    }
                }
            }

            if(idto.getSummaryType() == PosReportSummaryTypeEnum.TWO.getCode()){
                ParsedLongTerms commodityTerms = (ParsedLongTerms)aggMap.get("group_commodity");
                for(Terms.Bucket commodityBucket : commodityTerms.getBuckets()){
                    Long commodityId = (Long)commodityBucket.getKey();
                    setCommoditySummaryReturnBucket(saleSummaryList, commodityBucket, commodityId, null,null,null,null,null);
                }
            }
        }else {
            // 类别、门店汇总
            SearchHits<EsDayThirdSaleSummary> search = elasticsearchTemplate.search(queryBuilder.build(),EsDayThirdSaleSummary.class);
            if(CollectionUtils.isEmpty(search.getSearchHits())){
                return info;
            }

            Map<String, Aggregation> aggMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
            // 大类汇总,区分门店
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.THREE.getCode() && idto.getIsWithShop() == 1){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();

                    Map<String, Aggregation> commodityFirstIdMap = shopIdBucket.getAggregations().getAsMap();
                    ParsedLongTerms commodityFirstIdTerms = (ParsedLongTerms)commodityFirstIdMap.get("group_commodityFirstId");
                    for(Terms.Bucket commodityFirstIdBucket:commodityFirstIdTerms.getBuckets()){
                        Long commodityFirstId = (Long)commodityFirstIdBucket.getKey();
                        setCommoditySummaryReturnBucket(saleSummaryList, commodityFirstIdBucket, null, shopId,commodityFirstId,null,null,null);
                    }
                }
            }
            // 大类汇总,不区分门店
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.THREE.getCode() && idto.getIsWithShop() == 0){
                ParsedLongTerms commodityFirstIdTerms = (ParsedLongTerms)aggMap.get("group_commodityFirstId");
                for(Terms.Bucket commodityFirstIdBucket : commodityFirstIdTerms.getBuckets()){
                    Long commodityFirstId = (Long)commodityFirstIdBucket.getKey();
                    setCommoditySummaryReturnBucket(saleSummaryList, commodityFirstIdBucket, null, null,commodityFirstId,null,null,null);
                }
            }

            // 中类汇总,区分门店
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.FOUR.getCode() && idto.getIsWithShop() == 1){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();

                    Map<String, Aggregation> commodityFirstIdMap = shopIdBucket.getAggregations().getAsMap();
                    ParsedLongTerms commoditySecondIdTerms = (ParsedLongTerms)commodityFirstIdMap.get("group_commoditySecondId");
                    for(Terms.Bucket commoditySecondIdBucket:commoditySecondIdTerms.getBuckets()){
                        Long commoditySecondId = (Long)commoditySecondIdBucket.getKey();
                        setCommoditySummaryReturnBucket(saleSummaryList, commoditySecondIdBucket, null, shopId,null,commoditySecondId,null,null);
                    }
                }
            }
            // 中类汇总,不区分门店
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.FOUR.getCode() && idto.getIsWithShop() == 0){
                ParsedLongTerms commoditySecondIdTerms = (ParsedLongTerms)aggMap.get("group_commoditySecondId");
                for(Terms.Bucket commoditySecondIdBucket : commoditySecondIdTerms.getBuckets()){
                    Long commoditySecondId = (Long)commoditySecondIdBucket.getKey();
                    setCommoditySummaryReturnBucket(saleSummaryList, commoditySecondIdBucket, null, null,null,commoditySecondId,null,null);
                }
            }

            // 小类汇总,区分门店
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.FIVE.getCode() && idto.getIsWithShop() == 1){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();

                    Map<String, Aggregation> commodityFirstIdMap = shopIdBucket.getAggregations().getAsMap();
                    ParsedLongTerms commodityThirdIdTerms = (ParsedLongTerms)commodityFirstIdMap.get("group_commodityThirdId");
                    for(Terms.Bucket commodityThirdIdBucket:commodityThirdIdTerms.getBuckets()){
                        Long commodityThirdId = (Long)commodityThirdIdBucket.getKey();
                        setCommoditySummaryReturnBucket(saleSummaryList, commodityThirdIdBucket, null, shopId,null,null,commodityThirdId,null);
                    }
                }
            }
            // 小类汇总,不区分门店
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.FIVE.getCode() && idto.getIsWithShop() == 0){
                ParsedLongTerms commodityThirdIdTerms = (ParsedLongTerms)aggMap.get("group_commodityThirdId");
                for(Terms.Bucket commodityThirdIdBucket : commodityThirdIdTerms.getBuckets()){
                    Long commodityThirdId = (Long)commodityThirdIdBucket.getKey();
                    setCommoditySummaryReturnBucket(saleSummaryList, commodityThirdIdBucket, null, null,null,null,commodityThirdId,null);
                }
            }

            // 门店汇总,按销售额排序
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() && idto.getIsSortByAmount() == 1){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_tatalAmount_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();
                    setCommoditySummaryReturnBucket(saleSummaryList, shopIdBucket, null, shopId,null,null,null,null);
                }
            }
            // 门店汇总,不按销售额排序
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() && idto.getIsSortByAmount() == 0){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();
                    setCommoditySummaryReturnBucket(saleSummaryList, shopIdBucket, null, shopId,null,null,null,null);
                }
            }
            // 门店汇总:日毛利率
            if(idto.getSummaryType() == PosReportSummaryTypeEnum.SEVEN.getCode()){
                ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
                for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
                    Long shopId = (Long)shopIdBucket.getKey();

                    Map<String, Aggregation> saleTimeMap = shopIdBucket.getAggregations().getAsMap();
                    ParsedLongTerms saleTimeTerms = (ParsedLongTerms)saleTimeMap.get("group_saleTime");
                    for(Terms.Bucket saleTimeBucket:saleTimeTerms.getBuckets()){
                        String saleTime = DateUtil.getDateFormate(new Date((Long)saleTimeBucket.getKey()),"yyyy-MM-dd");
                        setCommoditySummaryReturnBucket(saleSummaryList, saleTimeBucket, null, shopId,null,null,null,saleTime);
                    }
                }
            }
        }

        // 设置分页、合计
        if(!CollectionUtils.isEmpty(saleSummaryList)){
            setReturnPageData(idto, type, info, saleSummaryList, shopIdList);
        }
        return info;
    }


    /**
     * 商品汇总 返回桶设置
     * @param saleSummaryList
     * @param commodityBucket
     * @param commodityId
     * @param shopId
     */
    private void setCommoditySummaryReturnBucket(List<SalesSummaryReportOrderODTO> saleSummaryList, Terms.Bucket commodityBucket, Long commodityId, Long shopId, Long commodityFirstId, Long commoditySecondId, Long commodityThirdId,String saleTime) {
        double sale_quantity = ((Sum) commodityBucket.getAggregations().asMap().get("sum_sale_quantity")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_sale_quantity")).getValue() : 0;
        double return_quantity = ((Sum) commodityBucket.getAggregations().asMap().get("sum_return_quantity")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_return_quantity")).getValue() : 0;
        double give_quantity = ((Sum) commodityBucket.getAggregations().asMap().get("sum_give_quantity")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_give_quantity")).getValue() : 0;
        double sale_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_sale_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_sale_amount")).getValue() : 0;
        double weight_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_weight_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_weight_amount")).getValue() : 0;
        double return_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_return_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_return_amount")).getValue() : 0;
        double give_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_give_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_give_amount")).getValue() : 0;
        double discount_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_discount_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_discount_amount")).getValue() : 0;
        double tatal_quantity = ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_quantity")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_quantity")).getValue() : 0;
        double tatal_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_amount")).getValue() : 0;
        double no_tax_rate_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_no_tax_rate_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_no_tax_rate_amount")).getValue() : 0;
        double tax_rate_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_tax_rate_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_tax_rate_amount")).getValue() : 0;
        double commodityPrice = ((Avg) commodityBucket.getAggregations().asMap().get("avg_commodityPrice")) != null ? ((Avg) commodityBucket.getAggregations().asMap().get("avg_commodityPrice")).getValue() : 0;
        double taxRate = ((Avg) commodityBucket.getAggregations().asMap().get("avg_taxRate")) != null ? ((Avg) commodityBucket.getAggregations().asMap().get("avg_taxRate")).getValue() : 0;

        double no_tax_weight_amount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_no_tax_weight_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_no_tax_weight_amount")).getValue() : 0;


        SalesSummaryReportOrderODTO saleSummaryDTO = new SalesSummaryReportOrderODTO();
        saleSummaryDTO.setShopId(shopId);
        saleSummaryDTO.setCommodityId(commodityId);

        saleSummaryDTO.setSaleTime(saleTime);

        saleSummaryDTO.setCommodityFirstId(commodityFirstId);
        saleSummaryDTO.setCommoditySecondId(commoditySecondId);
        saleSummaryDTO.setCommodityThirdId(commodityThirdId);

        saleSummaryDTO.setSaleQuantity(new BigDecimal(sale_quantity));
        saleSummaryDTO.setReturnQuantity(new BigDecimal(return_quantity));
        saleSummaryDTO.setGiveQuantity(new BigDecimal(give_quantity));
        saleSummaryDTO.setSaleAmount(new BigDecimal(sale_amount));
        saleSummaryDTO.setWeightAmount(new BigDecimal(weight_amount));
        saleSummaryDTO.setReturnAmount(new BigDecimal(return_amount));
        saleSummaryDTO.setGiveAmount(new BigDecimal(give_amount));
        saleSummaryDTO.setDiscountAmount(new BigDecimal(discount_amount));
        saleSummaryDTO.setTatalQuantity(new BigDecimal(tatal_quantity));
        saleSummaryDTO.setTatalAmount(new BigDecimal(tatal_amount));
        saleSummaryDTO.setNoTaxRateAmount(new BigDecimal(no_tax_rate_amount));
        saleSummaryDTO.setTaxRateAmount(new BigDecimal(tax_rate_amount));
        saleSummaryDTO.setCommodityPrice(new BigDecimal(commodityPrice));
        saleSummaryDTO.setTaxRate(new BigDecimal(taxRate));
        saleSummaryDTO.setNoTaxWeightAmount(new BigDecimal(no_tax_weight_amount));
        saleSummaryList.add(saleSummaryDTO);
    }

    /**
     * 设置分页、合计
     * @param idto
     * @param type
     * @param info
     * @param saleSummaryList
     */
    private void setReturnPageData(SalesSummaryReportOrderIDTO idto, Integer type, TablePageInfo info, List<SalesSummaryReportOrderODTO> saleSummaryList, List<Long> shopIdList) {
        Boolean isCommodity = idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode() || idto.getSummaryType() == PosReportSummaryTypeEnum.TWO.getCode();
        BigDecimal totalSalesAmount = BigDecimal.ZERO;
        if(type == 0){
            totalSalesAmount = new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getSaleAmount().doubleValue()).sum());
        }else if(type == 1){
            totalSalesAmount = new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getTatalAmount().doubleValue()).sum());
        }
        totalSalesAmount = totalSalesAmount.setScale(2,BigDecimal.ROUND_HALF_UP);

        //分页
        Map<String,Integer> getPageMap = PageUtil.getPageMap(saleSummaryList.size(),idto.getPageNo(),idto.getPageSize(),info);
        int startPoint = getPageMap.get("startPoint");
        int endPoint = getPageMap.get("endPoint");
        List<SalesSummaryReportOrderODTO> pageList = saleSummaryList.subList(startPoint, endPoint);

        Map<Long, CommodityODTO> commodityMap = new HashMap<>();
        if(isCommodity){
            List<Long> commodityIdList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            commodityMap = commodityService.getCommodityMapByIdList(commodityIdList);
        }
        // 获取门店map
        Map<Long, ShopODTO> shopMap = getShopMap();
        //获取类别 map
        Map<Long,Category> cateMap = getCateMap();

        Map<Long, ShopAndShopParentOrgInfoODTO> orgMap = null;

        if ((idto.getSummaryType().equals(PosReportSummaryTypeEnum.ONE.getCode()) || (null != idto.getIsWithShop() && idto.getIsWithShop().equals(PosReportWithShopEnum.ONE.getCode()))
                || idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType() == PosReportSummaryTypeEnum.SEVEN.getCode())) {
            List<Long> shopIds = new ArrayList<>();
            if (null != idto.getShopId()) {
                shopIds.add(idto.getShopId());
            } else {
                shopIds = shopIdList;
            }
            orgMap = getOrgInformation(shopIds);
        }

        for(SalesSummaryReportOrderODTO odto : pageList){
            odto.setSaleQuantity(odto.getSaleQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setReturnQuantity(odto.getReturnQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setGiveQuantity(odto.getGiveQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setSaleAmount(odto.getSaleAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setWeightAmount(odto.getWeightAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setReturnAmount(odto.getReturnAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setGiveAmount(odto.getGiveAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setDiscountAmount(odto.getDiscountAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setTatalQuantity(odto.getTatalQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setTatalAmount(odto.getTatalAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setNoTaxRateAmount(odto.getNoTaxRateAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setTaxRateAmount(odto.getTaxRateAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setCommodityPrice(odto.getCommodityPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setTaxRate(odto.getTaxRate().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setNoTaxWeightAmount(odto.getNoTaxWeightAmount().setScale(2, BigDecimal.ROUND_HALF_UP));

            CommodityODTO commodityODTO = commodityMap.get(odto.getCommodityId());
            if(commodityODTO != null){
                String barCodes = commodityODTO.getBarCodes();
                BeanUtils.copyProperties(commodityODTO,odto);
                odto.setBarCodeList(barCodes);
                odto.setBarCode(barCodes != null ? barCodes.split(",")[0]:"");
            }

            ShopODTO shopODTO = shopMap.get(odto.getShopId());
            if(shopODTO != null){
                BeanUtils.copyProperties(shopODTO,odto);
            }

            // 设置类别
            setCateName(idto, cateMap, odto);

            odto.setGrossprofitmarginAmount(odto.getTatalAmount().subtract(odto.getWeightAmount()));
            if(null !=odto.getTatalAmount() && odto.getTatalAmount().compareTo(BigDecimal.ZERO) != 0){
                odto.setGrossprofitrate(odto.getGrossprofitmarginAmount().divide(odto.getTatalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }

            if(isCommodity){
                odto.setNoTaxSaleAmount(odto.getTatalAmount().divide(new BigDecimal(1).add(odto.getTaxRate()),2,BigDecimal.ROUND_HALF_UP));
                odto.setNoTaxWeightAmount(odto.getWeightAmount().divide(new BigDecimal(1).add(odto.getTaxRate()),2,BigDecimal.ROUND_HALF_UP));
            }else {
                odto.setNoTaxSaleAmount(odto.getNoTaxRateAmount());
            }

           if(null !=totalSalesAmount && totalSalesAmount.compareTo(BigDecimal.ZERO) != 0){
                if(type ==0){
                    odto.setSalesAmountPercent(odto.getSaleAmount().divide(totalSalesAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }else if(type ==1){
                    odto.setSalesAmountPercent(odto.getTatalAmount().divide(totalSalesAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }
            }
            if (null != orgMap && null != orgMap.get(odto.getShopId())) {
                odto.setOrgName(orgMap.get(odto.getShopId()).getParentOrgName());
            }
        }
        info.setList(pageList);

        // 计算合计
        TotalSalesSummaryODTO total = new TotalSalesSummaryODTO();
        if(type ==0){
            total.setTotalSaleQuantity(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getSaleQuantity().doubleValue()).sum()).setScale(3,BigDecimal.ROUND_HALF_UP));
            total.setTotalReturnQuantity(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getReturnQuantity().doubleValue()).sum()).setScale(3,BigDecimal.ROUND_HALF_UP));
            total.setTotalGiveQuantity(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getGiveQuantity().doubleValue()).sum()).setScale(3,BigDecimal.ROUND_HALF_UP));
            total.setTotalSaleAmount(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getSaleAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            total.setTotalReturnAmount(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getReturnAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            total.setTotalGiveAmount(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getGiveAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            total.setTotalDiscountAmount(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getDiscountAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            total.setTotalQuantity(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getTatalQuantity().doubleValue()).sum()).setScale(3,BigDecimal.ROUND_HALF_UP));
            total.setTotalAmount(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getTatalAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }else if (type ==1){
            total.setTotalSaleQuantity(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getTatalQuantity().doubleValue()).sum()).setScale(3,BigDecimal.ROUND_HALF_UP));
            total.setTotalSaleAmount(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getTatalAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            total.setTotalSaleWeight(new BigDecimal(saleSummaryList.stream().mapToDouble(item -> item.getWeightAmount().doubleValue()).sum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            total.setTotalGrossprofitmarginAmount(total.getTotalSaleAmount().subtract(total.getTotalSaleWeight()));
        }

        info.setHeader(total);
    }

    /**
     * 设置类别
     * @param idto
     * @param cateMap
     * @param odto
     */
    private void setCateName(SalesSummaryReportOrderIDTO idto, Map<Long, Category> cateMap, SalesSummaryReportOrderODTO odto) {
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.THREE.getCode()){
            Category firstCate = cateMap.get(odto.getCommodityFirstId());
            if(null != firstCate){
                odto.setCommodityFirstName(firstCate.getCateName());
            }
        }


        if(idto.getSummaryType() == PosReportSummaryTypeEnum.FOUR.getCode()){
            Category secondCate = cateMap.get(odto.getCommoditySecondId());
            if(null != secondCate){
                odto.setCommoditySecondName(secondCate.getCateName());

                Category firstCate = cateMap.get(secondCate.getParentId());
                if(null != firstCate){
                    odto.setCommodityFirstName(firstCate.getCateName());
                }
            }
        }

        if(idto.getSummaryType() == PosReportSummaryTypeEnum.FIVE.getCode()){
            Category thirdCate = cateMap.get(odto.getCommodityThirdId());
            if(null != thirdCate){
                odto.setCommodityThirdName(thirdCate.getCateName());

                Category secondCate = cateMap.get(thirdCate.getParentId());
                if(null != secondCate){
                    odto.setCommoditySecondName(secondCate.getCateName());

                    Category firstCate = cateMap.get(secondCate.getParentId());
                    if(null != firstCate){
                        odto.setCommodityFirstName(firstCate.getCateName());
                    }
                }
            }
        }

    }

    /**
     * 短交数据保存进ES
     * @param saleTime
     * @return
     */
    @Async
    public Boolean shortDeliveryToES(String saleTime) {
        try {
            long deleteStart = System.currentTimeMillis();

            // 删除短交报表
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderTime", saleTime));
            queryBuilder.withQuery(boolQueryBuilder);
            queryBuilder.withPageable(PageRequest.of(0, deleteNum));
            queryBuilder.withTrackTotalHits(true);
            elasticsearchTemplate.delete(queryBuilder.build(), ESShortDeliveryReport.class);

            long deleteEnd = System.currentTimeMillis();
            log.info("短交数据保存进ES 删除耗时" + (deleteEnd - deleteStart) + "毫秒");

            // 短交日汇总 to ES
            List<ESShortDeliveryReport> shortReportList = shortDeliveryReportMapper.getShortDeliveryReportByOrderTime(saleTime);
            if(!CollectionUtils.isEmpty(shortReportList)){
                batchInsertEsShortDeliveryReport(shortReportList);
            }
            log.info("短交数据保存进ES 新增耗时" + (System.currentTimeMillis() - deleteEnd) + "毫秒");
        } catch (Exception e) {
            log.error("短交数据进ES异常: ", e);
            StringBuffer sb = new StringBuffer();
            sb.append("短交数据保存进ES异常");

            //发送短信消息
            commodityService.sendMessage(sb.toString());
        }
        return true;
    }

    /**
     * 商品销售汇总报表保存进ES
     * @param saleTime
     * @return
     */
    @Async
    public Boolean daySalesSummaryToES(String saleTime) {
        try {
            long deleteStart = System.currentTimeMillis();

            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.filter(QueryBuilders.termQuery("saleTime", saleTime));
            queryBuilder.withQuery(boolQueryBuilder);
            queryBuilder.withPageable(PageRequest.of(0, deleteNum));
            queryBuilder.withTrackTotalHits(true);

            // 删除商品汇总
            elasticsearchTemplate.delete(queryBuilder.build(), EsDaySaleSummary.class);

            // 删除类别汇总
            elasticsearchTemplate.delete(queryBuilder.build(), EsDayThirdSaleSummary.class);

            long deleteEnd = System.currentTimeMillis();
            log.info("商品销售汇总报表保存进ES 删除耗时" + (deleteEnd - deleteStart) + "毫秒");

            // 商品日汇总 to ES
            Example exampleCashier = new Example(DaySaleSummary.class);
            exampleCashier.createCriteria().andEqualTo("saleTime", saleTime);
            List<DaySaleSummary> daySaleSummaries = daySaleSummaryMapper.selectByExample(exampleCashier);
            if(!CollectionUtils.isEmpty(daySaleSummaries)){
                List<EsDaySaleSummary> esList = new ArrayList<>();
                // 转换es对象
                for (int i = 0; i < daySaleSummaries.size(); i++) {
                    EsDaySaleSummary esSale = new EsDaySaleSummary();
                    BeanUtils.copyProperties(daySaleSummaries.get(i) ,esSale);
                    esList.add(esSale);
                }
                batchInsertEsDaySaleSummary(esList);
            }

            // 类别日汇总 to ES
            Example thirdSummaryExample = new Example(ThirdSummary.class);
            thirdSummaryExample.createCriteria().andEqualTo("saleTime", saleTime);
            List<ThirdSummary> thirdSummaryList = thirdSummaryMapper.selectByExample(thirdSummaryExample);
            if(!CollectionUtils.isEmpty(thirdSummaryList)){
                List<EsDayThirdSaleSummary> esList = new ArrayList<>();
                // 转换es对象
                for (int i = 0; i < thirdSummaryList.size(); i++) {
                    EsDayThirdSaleSummary esSale = new EsDayThirdSaleSummary();
                    BeanUtils.copyProperties(thirdSummaryList.get(i) ,esSale);
                    esList.add(esSale);
                }
                batchInsertEsDayThirdSaleSummary(esList);
            }

            log.info("商品销售汇总报表保存进ES 新增耗时" + (System.currentTimeMillis() - deleteEnd) + "毫秒");
        } catch (Exception e) {
            log.error("商品销售汇总进ES异常: ", e);
            StringBuffer sb = new StringBuffer();
            sb.append("商品销售汇总报表保存进ES异常");

            //发送短信消息
            commodityService.sendMessage(sb.toString());
        }
        return true;
    }


    /**
     * B订单实发补偿进ES
     * @param orderCodeList
     * @return
     */
    public Boolean bOrderFixToES(List<String> orderCodeList) {
        long deleteStart = System.currentTimeMillis();

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("orderCode", orderCodeList));
        queryBuilder.withQuery(boolQueryBuilder);
        queryBuilder.withPageable(PageRequest.of(0, deleteNum));
        queryBuilder.withTrackTotalHits(true);
        elasticsearchTemplate.delete(queryBuilder.build(), ESShortDeliveryReport.class);

        long deleteEnd = System.currentTimeMillis();
        log.info("B订单实发补偿进ES 删除耗时" + (deleteEnd - deleteStart) + "毫秒");


        // B订单实发补偿进ES
        List<ESShortDeliveryReport> shortReportList = shortDeliveryReportMapper.getShortDeliveryReportByOrderCodeList(orderCodeList);
        if(!CollectionUtils.isEmpty(shortReportList)){
            batchInsertEsShortDeliveryReport(shortReportList);
        }
        log.info("B订单实发补偿进ES 新增耗时" + (System.currentTimeMillis() - deleteEnd) + "毫秒");
        return true;
    }

    /**
     * 批量新增ES 短交报表
     * @param cs
     */
    public void batchInsertEsShortDeliveryReport(List<ESShortDeliveryReport> cs) {
        int index = 0;
        int count = BATCH_INSERT_ES_SIZE;
        while (true) {
            List<ESShortDeliveryReport> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                try{
                    // 一次插入500条
                    elasticsearchTemplate.save(items);
                }catch (Exception e){
                    e.printStackTrace();
                    toEsErrorSendMessage("shortDeliveryToEs" + items.get(0).getOrderTime(),"短交报表保存进ES异常,日期" + items.get(0).getOrderTime());
                }
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 批量新增ES 类别销售汇总表
     * @param cs
     */
    public void batchInsertEsDayThirdSaleSummary(List<EsDayThirdSaleSummary> cs) {
        int index = 0;
        int count = BATCH_INSERT_ES_SIZE;
        while (true) {
            List<EsDayThirdSaleSummary> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                try{
                    // 一次插入500条
                    elasticsearchTemplate.save(items);
                }catch (Exception e){
                    toEsErrorSendMessage("thirdSaleSummaryToEs" + items.get(0).getSaleTime(),"类别销售汇总表保存进ES异常,日期 " + items.get(0).getSaleTime());
                }
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 批量新增ES 商品销售汇总表
     * @param cs
     */
    public void batchInsertEsDaySaleSummary(List<EsDaySaleSummary> cs) {
        int index = 0;
        int count = BATCH_INSERT_ES_SIZE;
        while (true) {
            List<EsDaySaleSummary> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                try{
                    // 一次插入500条
                    elasticsearchTemplate.save(items);
                }catch (Exception e){
                    toEsErrorSendMessage("daySaleSummaryToEs" + items.get(0).getSaleTime(),"商品销售汇总表保存进ES异常,日期 " + items.get(0).getSaleTime());
                }
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 保存近es 异常发送短信(同种类型，一天只发送一次短信)
     */
    public void toEsErrorSendMessage(String redissonKey,String content){
        RBucket<String> bucket = redissonClient.getBucket(redissonKey);
        String data = bucket.get();
        if(StringUtils.isBlank(data)){
            StringBuffer sb = new StringBuffer();
            sb.append(content);

            //发送短信消息
            commodityService.sendMessage(sb.toString());
            bucket.set(redissonKey, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);
        }
    }

    /**
     * 获取门店map
     * @return
     */
    public Map<Long,ShopODTO> getShopMap(){
        Map<Long,ShopODTO> shopMap = new HashMap<>();
        List<ShopODTO> shopList = shopMapper.getShopByIdList(null);
        if(!CollectionUtils.isEmpty(shopList)){
            shopMap = shopList.stream().collect(Collectors.toMap(ShopODTO::getShopId, Function.identity()));
        }
        return shopMap;
    }

    /**
     * 获取类别 map
     * @return
     */
    public Map<Long,Category> getCateMap(){
        List <Category> cateList = categoryMapper.selectAll();
        Map<Long,Category> cateMap = new HashMap<>();
        for(Category dto:cateList){
            cateMap.put(dto.getId(),dto);
        }
        return cateMap;
    }

    /**
     * 畅销品排行报表
     * @param idto
     * @return
     */
    public TablePageInfo<SalesSummaryReportOrderODTO> bestSellerReport(SalesSummaryReportOrderIDTO idto) {
        TablePageInfo info = new TablePageInfo();
        info.setList(null);
        info.setHeader(new TotalSalesSummaryODTO());
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), idto.getOrgCode(), shopTypeList, idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            return info;
        }

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", shopIdList));

        if(idto.getShopId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", idto.getShopId()));
        }

        if(StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate())){
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("saleTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));
        }

        if(idto.getCateId1() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstId", idto.getCateId1()));
        }
        if(idto.getCateId2() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commoditySecondId", idto.getCateId2()));
        }
        if(idto.getCateId3() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityThirdId", idto.getCateId3()));
        }

        BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
        if(StringUtils.isNotBlank(idto.getCommodityKey())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("commodityCode", String.format("*%s*", idto.getCommodityKey() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("commodityName", String.format("*%s*", idto.getCommodityKey() , "*%s*"))));
        }

        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return info;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }

        if(idto.getCommodityId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", idto.getCommodityId()));
        }

        queryBuilder.withQuery(boolQueryBuilder);

        // 结果过滤：通过sourceFilter设置返回的结果字段(聚合不查询字段)
        queryBuilder.withSourceFilter(new FetchSourceFilter(
                new String[]{}, null));

        TermsAggregationBuilder commodityAgg = AggregationBuilders.terms("group_commodity").field("commodityId")
                .collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).size(Integer.MAX_VALUE);

        SumAggregationBuilder sum_tatal_quantity = AggregationBuilders.sum("sum_tatal_quantity").field("tatalQuantity");
        SumAggregationBuilder sum_tatal_amount = AggregationBuilders.sum("sum_tatal_amount").field("tatalAmount");
        SumAggregationBuilder sum_weight_amount = AggregationBuilders.sum("sum_weight_amount").field("weightAmount");
        SumAggregationBuilder sum_grossprofitmarginAmount = AggregationBuilders.sum("sum_grossprofitmarginAmount")
                       .script(new Script("doc['tatalAmount'].value - doc['weightAmount'].value"));

        if(idto.getSummaryType() == PosReportSummaryTypeEnum.EIGHT.getCode()){
            queryBuilder.addAggregation(commodityAgg.subAggregation(sum_tatal_quantity)
                    .subAggregation(sum_tatal_amount)
                    .subAggregation(sum_weight_amount)
                    .subAggregation(sum_grossprofitmarginAmount).order(BucketOrder.compound(
                            BucketOrder.aggregation("sum_tatal_quantity",false),
                            BucketOrder.key(true)
                    ))
                   );
            // Terms.Order.aggregation("sum_tatal_quantity ", false)
        }

        if(idto.getSummaryType() == PosReportSummaryTypeEnum.NINE.getCode()){
            queryBuilder.addAggregation(commodityAgg.subAggregation(sum_tatal_quantity)
                    .subAggregation(sum_tatal_amount)
                    .subAggregation(sum_weight_amount)
                    .subAggregation(sum_grossprofitmarginAmount)
                    .order(BucketOrder.compound(
                            BucketOrder.aggregation("sum_tatal_amount",false),
                            BucketOrder.key(true)
                    ))
            );
        }

        if(idto.getSummaryType() == PosReportSummaryTypeEnum.TEN.getCode()){
            queryBuilder.addAggregation(commodityAgg.subAggregation(sum_tatal_quantity)
                    .subAggregation(sum_tatal_amount)
                    .subAggregation(sum_weight_amount)
                    .subAggregation(sum_grossprofitmarginAmount)
                    .order(BucketOrder.compound(
                            BucketOrder.aggregation("sum_grossprofitmarginAmount",false),
                            BucketOrder.key(true)
                    ))
            );
        }

        List<SalesSummaryReportOrderODTO> list = new ArrayList<>();
        List<Long> commodityIdList = new ArrayList<>();
        queryBuilder.withTrackTotalHits(true);
        SearchHits<EsDaySaleSummary> search = elasticsearchTemplate.search(queryBuilder.build(),EsDaySaleSummary.class);
        if(CollectionUtils.isEmpty(search.getSearchHits())){
            return info;
        }

        Map<String, Aggregation> aggMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().getAsMap();
        ParsedLongTerms commodityIdTerms = (ParsedLongTerms)aggMap.get("group_commodity");
        for(Terms.Bucket commodityBucket : commodityIdTerms.getBuckets()){
            Long commodityId = (Long)commodityBucket.getKey();
            double tatalQuantity = ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_quantity")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_quantity")).getValue() : 0;
            double tatalAmount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_tatal_amount")).getValue() : 0;
            double weightAmount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_weight_amount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_weight_amount")).getValue() : 0;
            double grossprofitmarginAmount = ((Sum) commodityBucket.getAggregations().asMap().get("sum_grossprofitmarginAmount")) != null ? ((Sum) commodityBucket.getAggregations().asMap().get("sum_grossprofitmarginAmount")).getValue() : 0;

            SalesSummaryReportOrderODTO odto = new SalesSummaryReportOrderODTO();
            odto.setCommodityId(commodityId);
            odto.setTatalQuantity(new BigDecimal(tatalQuantity).setScale(3,BigDecimal.ROUND_HALF_UP));
            odto.setTatalAmount(new BigDecimal(tatalAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
            odto.setWeightAmount(new BigDecimal(weightAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
            odto.setGrossprofitmarginAmount(new BigDecimal(grossprofitmarginAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
            commodityIdList.add(commodityId);
            list.add(odto);
        }
        list = list.subList(0,list.size() >= 100 ? 100 : list.size());
        Map<Long, CommodityODTO> commodityMap = commodityService.getCommodityMapByIdList(commodityIdList);
        TotalSalesSummaryODTO total=new TotalSalesSummaryODTO();
        BigDecimal tatalQuantity=BigDecimal.ZERO;
        BigDecimal tatalAmount=BigDecimal.ZERO;
        BigDecimal weightAmount=BigDecimal.ZERO;
        for(SalesSummaryReportOrderODTO odto:list){
            CommodityODTO commodityODTO = commodityMap.get(odto.getCommodityId());
            if(commodityODTO != null){
                String barCodes = commodityODTO.getBarCodes();
                BeanUtils.copyProperties(commodityODTO,odto);
                odto.setBarCodeList(barCodes);
                odto.setBarCode(barCodes != null ? barCodes.split(",")[0]:"");
            }

            odto.setGrossprofitmarginAmount(odto.getTatalAmount().subtract(odto.getWeightAmount()));
            if(null !=odto.getTatalAmount() && odto.getTatalAmount().compareTo(BigDecimal.ZERO) != 0){
                odto.setGrossprofitrate(odto.getGrossprofitmarginAmount().divide(odto.getTatalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }
            if(null !=odto.getTatalQuantity() && odto.getTatalQuantity().compareTo(BigDecimal.ZERO) != 0){
                odto.setAvgSalePrice(odto.getTatalAmount().divide(odto.getTatalQuantity(),2,BigDecimal.ROUND_HALF_UP));
            }
            tatalQuantity=tatalQuantity.add(odto.getTatalQuantity());
            tatalAmount=tatalAmount.add(odto.getTatalAmount());
            weightAmount=weightAmount.add(odto.getWeightAmount());
        }
        //设置总值
        total.setTotalSaleQuantity(tatalQuantity);
        total.setTotalSaleAmount(tatalAmount);
        total.setTotalSaleWeight(weightAmount);
        total.setTotalGrossprofitmarginAmount(tatalAmount.subtract(weightAmount));
        info.setHeader(total);
        info.setList(list);
        info.setTotal(list.size());
        return info;
    }
}
