package com.pinshang.qingyun.report.search.model;

import lombok.Data;

@Data
public class EmployeeUser {

    /*** 职员ID */
    private Long employeeId;

    /*** 工号 */
    private String employeeCode;

    /*** 真实姓名 */
    private String employeeName;

    /*** 职员状态：1-在职、4-离职 */
    private Integer employeeState;

    /*** 账号状态：0-未开通、1-已开通、2-已关闭 */
    private Integer employeeAccountState;

    /*** 用户ID */
    private Long userId;

    /*** 用户是否删除 */
    private Integer isDeleted;
}
