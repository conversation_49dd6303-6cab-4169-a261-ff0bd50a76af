package com.pinshang.qingyun.report.search.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName DemoDTO
 * <AUTHOR>
 * @Date 2021/4/29 15:35
 * @Description DemoDTO
 * @Version 1.0
 */
@Data
public class LogActionDetailsODTO {
    /**
     * 所属系统
     */
    @ApiModelProperty("系统名称code: SystemIdNewEnums")
    private String systemName;
    /**
     * 一级菜单
     */
    @ApiModelProperty("一级菜单")
    private String firstLevelMenu;

    /**
     * 二级菜单
     */
    @ApiModelProperty("二级菜单")
    private String secondLevelMenu;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String menuName;

    /**
     * 账户编号
     */
    @ApiModelProperty("账户编号")
    private String userCode;
    /**
     * 账户名称
     */
    @ApiModelProperty("账户名称")
    private String userName;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private String startTime;
    /**
     * 访问账户所属部门
     */
    @ApiModelProperty("访问账户所属部门")
    private String userDept;
    /**
     * 访问动作, 0-查询, 1-导出
     */
    @ApiModelProperty("访问动作, 0-查询, 1-导出")
    private Integer operaType;
}
