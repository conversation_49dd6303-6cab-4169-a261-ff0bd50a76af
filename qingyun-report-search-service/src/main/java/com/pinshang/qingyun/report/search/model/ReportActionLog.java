package com.pinshang.qingyun.report.search.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ClassName ActionLOg
 * <AUTHOR>
 * @Date 2021/4/25 14:45
 * @Description ActionLOg
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name="t_report_action_log")
public class ReportActionLog implements Serializable {
    @Id
    private Long id;

    /**
     * 一级菜单id
     */
    @Field(type = FieldType.Long)
    private Long firstLevelMenuId;

    /**
     * 二级菜单id
     */
    @Field(type = FieldType.Long)
    private Long secondLevelMenuId;

    /**
     * 三级菜单id
     */
    @Field(type = FieldType.Long)
    private Long thirdLevelMenuId;

    /**
     * 系统id
     */
    @Field(type = FieldType.Integer)
    private Integer systemId;

    private String operaType;

    private String description;

    private String uri;

    private String applicationName;
}
