package com.pinshang.qingyun.report.search.enums;

/**
 * @ClassName ReportActionMenuTypeEnum
 * <AUTHOR>
 * @Date 2021/6/1 18:29
 * @Description ReportActionMenuTypeEnum
 * @Version 1.0
 */
public enum ReportActionMenuTypeEnum {
    /**
     * 菜单一级路径
     */
    FIRST_LEVEL_MENU("10", "菜单一级路径", "firstLevelMenuId"),
    /**
     * 菜单二级路径
     */
    SECOND_LEVEL_MENU("20", "菜单二级路径", "secondLevelMenuId"),
    /**
     * 菜单名称
     */
    MENU_NAME("30", "菜单三级路径", "thirdLevelMenuId");

    ReportActionMenuTypeEnum(String code, String desc, String menuNameContent) {
        this.code = code;
        this.desc = desc;
        this.menuNameContent = menuNameContent;
    }

    private String code;
    private String desc;
    /**
     * 在action_log_v2中的properties名称
     */
    private String menuNameContent;


    public static String getMenuNameContentByCode(String code){
        for(ReportActionMenuTypeEnum  typeEnum : ReportActionMenuTypeEnum.values()){
            if(typeEnum.getCode().equals(code)){
                return typeEnum.getMenuNameContent();
            }
        }
        return MENU_NAME.getMenuNameContent();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getMenuNameContent() {
        return menuNameContent;
    }
}
