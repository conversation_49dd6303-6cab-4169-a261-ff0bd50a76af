package com.pinshang.qingyun.report.search.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class ActualReceiptAnalysisIDto extends Pagination{
	private static final long serialVersionUID = 5167881220777885090L;
	private Long shopId;
	private Long cateId1;
	private Long cateId2;
	private Long cateId3;
	private String searchWord;
	private String barCode;
	@ApiModelProperty("开始时间 yyyy-MM-dd")
	private String beginDate;
	@ApiModelProperty("结束时间 yyyy-MM-dd")
	private String endDate;

	private Long commodityId;

}
