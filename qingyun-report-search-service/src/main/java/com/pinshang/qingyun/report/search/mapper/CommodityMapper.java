package com.pinshang.qingyun.report.search.mapper;

import com.pinshang.qingyun.report.search.dto.CategoryODTO;
import com.pinshang.qingyun.report.search.dto.CommodityODTO;
import com.pinshang.qingyun.report.search.model.Commodity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommodityMapper {

    List<CategoryODTO> findAllFirstCategory();

    List<CommodityODTO> findCommodityByCommodityIdList(@Param("commodityIdList") List<Long> commodityIdList,@Param("commodityCodeList") List<String> commodityCodeList);

    CommodityODTO getCommodityByComodityId(@Param("commodityId") Long commodityId);

    CommodityODTO findCommodityByBarcode(@Param("barCode") String barcode);

    List<Commodity> findCommodityBarCodeByParam(@Param("commodityIdList") List<Long> commodityIdList);

}
