package com.pinshang.qingyun.report.search.controller;

import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.search.dto.PosToEsODTO;
import com.pinshang.qingyun.report.search.dto.SalesSummaryReportOrderIDTO;
import com.pinshang.qingyun.report.search.dto.SalesSummaryReportOrderODTO;
import com.pinshang.qingyun.report.search.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.search.enums.PosReportSummaryTypeEnum;
import com.pinshang.qingyun.report.search.enums.PosReportWithShopEnum;
import com.pinshang.qingyun.report.search.service.PosReportService;
import com.pinshang.qingyun.report.search.service.SalesSummaryReportService;
import com.pinshang.qingyun.report.search.util.ViewExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: sk
 * @Date: 2020/12/3
 */
@Slf4j
@RestController
@RequestMapping("/posReports")
@Api(value = "pos报表相关接口", tags = "posReport", description = "pos报表相关接口")
public class PosReportController {

    @Autowired
    private SalesSummaryReportService salesSummaryReportService;
    @Autowired
    private PosReportService posReportService;


    /**
     * 注释
     * pos非当日数据，直接保存进es
     * @return
     */
    @ApiOperation(value = "pos非当日数据，直接保存进es", notes = "pos非当日数据，直接保存进es")
    @RequestMapping(value = "/posNotCurrentDaySaveES", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean posNotCurrentDaySaveES(@RequestBody PosToEsODTO posToEsODTO){
        return posReportService.posNotCurrentDaySaveES(posToEsODTO);
    }

    /**
     * 获取ES短交报表 实发总金额
     * @param orderTime
     * @return
     */
    @ApiOperation(value = "获取短交报表 ES sum值", notes = "获取短交报表 ES sum值")
    @RequestMapping(value = "/getShortReportESSum", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BigDecimal getShortReportESSum(@RequestParam(value = "orderTime") String orderTime){
        return posReportService.getShortReportESSum(orderTime);
    }

    /**
     * 获取ES商品日汇总、商品类别汇总 sum金额 用于和原始金额对比
     * key: dayAmount   商品日汇总
     * key: thirdAmount  商品类别汇总
     * @param saleTime
     * @return
     */
    @ApiOperation(value = "获取商品日汇总,类别日汇总 ES sum值", notes = "获取商品日汇总,类别日汇总 ES sum值")
    @RequestMapping(value = "/getPosReportESSum", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Map<String, BigDecimal> getPosReportESSum(@RequestParam(value = "saleTime") String saleTime){
        return posReportService.getPosReportESSum(saleTime);
    }

    /**
     * 注意：今日之前的数据走这个接口
     * @param salesSummaryReportOrderIDTO
     * @return
     */
    @ApiOperation(value = "商品销售汇总报表", notes = "商品销售汇总报表")
    @RequestMapping(value = "/commoditySalesSummaryReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,0);
    }

    @ApiOperation(value = "商品销售汇总分析报表", notes = "商品销售汇总分析报表")
    @RequestMapping(value = "/commoditySalesSummary2Report", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummary2Report(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,1);
    }

    @ApiOperation(value = "商品销售汇总报表保存进ES", notes = "商品销售汇总报表保存进ES")
    @RequestMapping(value = "/daySalesSummaryToES", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean daySalesSummaryToES(@RequestParam(value = "saleTime") String saleTime){
        return salesSummaryReportService.daySalesSummaryToES(saleTime);
    }

    @ApiOperation(value = "短交数据保存进ES", notes = "短交数据保存进ES")
    @RequestMapping(value = "/shortDeliveryToES", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean shortDeliveryToES(@RequestParam(value = "saleTime") String saleTime){
        return salesSummaryReportService.shortDeliveryToES(saleTime);
    }

    @ApiOperation(value = "B订单实发补偿进ES", notes = "B订单实发补偿进ES")
    @RequestMapping(value = "/bOrderFixToES", method = RequestMethod.POST)
    public Boolean bOrderFixToES(@RequestBody List<String> orderCodeList){
        return salesSummaryReportService.bOrderFixToES(orderCodeList);
    }

    @ApiOperation(value = "商品销售汇总报表导出", notes = "商品销售汇总报表导出")
    @RequestMapping(value = "/exportInfo/commoditySalesSummaryReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_SR_COMMODITY_ES")
    public void commoditySalesSummaryReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {

        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<SalesSummaryReportOrderODTO> result=salesSummaryReportService.commoditySalesSummaryReport(idto,0);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            //设置总行
            setCommodityTotalRow(idto.getSummaryType(), idto.getIsWithShop(), result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setDataLst(idto.getSummaryType(), idto.getIsWithShop(), dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.TWO.getCode()){
            fileName="商品销售汇总报表";
        }
        if(idto.getSummaryType() ==PosReportSummaryTypeEnum.THREE.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FOUR.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FIVE.getCode()){
            fileName="类别销售汇总报表";
        }
        ExcelSheetTitleEnum sheetTitle = getExcelSheetTitle(idto.getSummaryType(), idto.getIsWithShop());

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        fileName = String.format("%s_%s", fileName, new DateTime().toString("yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

    }



    public void commoditySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<SalesSummaryReportOrderODTO> result=salesSummaryReportService.commoditySalesSummaryReport(idto,1);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            //设置总行
            setCommodityAnalysisTotalRow(idto.getSummaryType(), idto.getIsWithShop(), result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setDataLstAnalysis(idto.getSummaryType(), idto.getIsWithShop(), dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        if(idto.getSummaryType() ==PosReportSummaryTypeEnum.ONE.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.TWO.getCode()){
            fileName="商品销售汇总分析报表";
        }
        if(idto.getSummaryType() ==PosReportSummaryTypeEnum.THREE.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FOUR.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FIVE.getCode()){
            fileName="类别销售汇总分析报表";
        }
        if(idto.getSummaryType() ==PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            fileName="门店销售汇总分析报表";
        }
        ExcelSheetTitleEnum sheetTitle = getAnalysisExcelSheetTitle(idto.getSummaryType(), idto.getIsWithShop());

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        fileName = String.format("%s_%s", fileName, new DateTime().toString("yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

    }

    @ApiOperation(value = "商品销售汇总分析报表导出", notes = "商品销售汇总分析报表导出")
    @RequestMapping(value = "/exportInfo/commoditySalesSummaryAnalysisReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_SR_COMMODITY_SUMMARY_ES")
    public void commoditySalesSummaryAnalysisReportByEs(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {
        /* 暂不加限制, 为兼容前端旧版本
        QYAssert.isTrue(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType ==PosReportSummaryTypeEnum.TWO.getCode(),
                "商品销售汇总, 汇总类型不存在");
        */
        commoditySalesSummaryAnalysisReport(idto, response);
    }

    @ApiOperation(value = "类别销售汇总分析报表导出", notes = "")
    @RequestMapping(value = "/exportInfo/category/commoditySalesSummaryAnalysisReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_SR_CATEGORY_SUMMARY_ES")
    public void categoryCommoditySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {

        QYAssert.isTrue(idto.getSummaryType() ==PosReportSummaryTypeEnum.THREE.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FOUR.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FIVE.getCode(),
                "类别销售汇总, 汇总类型不存在");

        commoditySalesSummaryAnalysisReport(idto, response);
    }

    @ApiOperation(value = "门店销售汇总分析报表导出", notes = "")
    @RequestMapping(value = "/exportInfo/shop/commoditySalesSummaryAnalysisReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_SR_SHOP_SUMMARY_ES")
    public void shopCommoditySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {

        QYAssert.isTrue(idto.getSummaryType() ==PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.SEVEN.getCode(),
                "门店销售汇总, 汇总类型不存在");

        commoditySalesSummaryAnalysisReport(idto, response);
    }

    //门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  导出合计行设置
    private void setCommodityAnalysisTotalRow(Integer summaryType,Integer isWithShop, TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(12,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,15);
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(9,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,13);
        }

        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()  && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(2,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(3,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(6,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()   && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(3,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,9);
        }
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
    }


    private void setDataLstAnalysis(Integer summaryType,Integer isWithShop, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            dataLst.add(dto.getSaleTime());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()  || summaryType==PosReportSummaryTypeEnum.SIX.getCode() || summaryType==PosReportSummaryTypeEnum.SEVEN.getCode() || ( null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
        }

        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
        }else{
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
        }
        dataLst.add(dto.getWeightAmount()+"");
        dataLst.add(dto.getGrossprofitmarginAmount()+"");
        dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
        dataLst.add(dto.getNoTaxSaleAmount()+"");
        dataLst.add(dto.getNoTaxWeightAmount()+"");
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getSalesAmountPercent());
        }
    }

    private ExcelSheetTitleEnum getAnalysisExcelSheetTitle(Integer summaryType,Integer isWithShop) {
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()  && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            sheetTitle=ExcelSheetTitleEnum.SHOP_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            sheetTitle=ExcelSheetTitleEnum.SHOPDAY_SUMMARY_ANALYSIS_NOSHOP;
        }
        return sheetTitle;
    }

    private void setSalesSummaryParam(Long shopId,String beginDate,String endDate,Long cateId1, Long cateId2,Long cateId3,String commodityKey,String barCode,Integer summaryType,Integer isWithShop, Integer isSortByAmount,Long commodityId,String orgCode, Integer shopType,Integer managementMode, Long provinceId,List<Integer> shopTypeList, List<Integer> managementModeList,SalesSummaryReportOrderIDTO idto) {
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(null !=cateId1) {
            idto.setCateId1(cateId1);
        }
        if(null !=cateId2) {
            idto.setCateId2(cateId2);
        }
        if(null !=cateId3) {
            idto.setCateId3(cateId3);
        }
        if(!StringUtils.isEmpty(commodityKey)) {
            idto.setCommodityKey(commodityKey);
        }
        if(!StringUtils.isEmpty(barCode)) {
            idto.setBarCode(barCode);
        }
        if(!StringUtils.isEmpty(orgCode)) {
            idto.setOrgCode(orgCode);
        }
        if(null !=summaryType) {
            idto.setSummaryType(summaryType);
        }
        if(null !=isWithShop) {
            idto.setIsWithShop(isWithShop);
        }
        if(null !=isSortByAmount) {
            idto.setIsSortByAmount(isSortByAmount);
        }
        if(null !=commodityId) {
            idto.setCommodityId(commodityId);
        }
        if(null != shopType) {
            idto.setShopType(shopType);
        }
        if(null != managementMode) {
            idto.setManagementMode(managementMode);
        }
        if (null != provinceId) {
            idto.setProvinceId(provinceId);
        }

        if(CollectionUtils.isNotEmpty(shopTypeList)) {
            idto.setShopTypeList(shopTypeList);
        }

        if(CollectionUtils.isNotEmpty(managementModeList)) {
            idto.setManagementModeList(managementModeList);
        }

        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
    }

    private void setCommodityTotalRow(Integer summaryType, Integer isWithShop, TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(13,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(17,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(18,getFieldValueByName("totalGiveAmount",result.getHeader()));
            totalMap.put(19,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(20,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(21,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,21);
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(10,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGiveAmount",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(17,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(18,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,18);
        }
        if( summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(2,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(3,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }

        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(6,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(3,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if( summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
    }

    private String getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value==null?"":value.toString();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return null;
        }
    }
    private void setTotalRow(Map<String, List<String>> data, Map<Integer, String> totalMap,Integer length) {
        List<String> dataLst = new ArrayList<>();
        totalMap.put(1,"合计");
        for(int j=1;j<=length;j++){
            dataLst.add(totalMap.get(j));
        }
        data.put("key_0", dataLst);
    }

    private void setDataLst(Integer summaryType,Integer isWithShop, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
            dataLst.add(dto.getCommodityPrice()+"");
            dataLst.add(dto.getSaleQuantity()+"");
            dataLst.add(dto.getReturnQuantity()+"");
            dataLst.add(dto.getGiveQuantity()+"");
            dataLst.add(dto.getSaleAmount()+"");
            dataLst.add(dto.getReturnAmount()+"");
            dataLst.add(dto.getGiveAmount()+"");
            dataLst.add(dto.getDiscountAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getTaxRate()+"");
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getSaleQuantity()+"");
            dataLst.add(dto.getSaleAmount()+"");
            dataLst.add(dto.getReturnQuantity()+"");
            dataLst.add(dto.getReturnAmount()+"");
            dataLst.add(dto.getGiveQuantity()+"");
            dataLst.add(dto.getDiscountAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
            dataLst.add(dto.getSalesAmountPercent());
        }
    }

    private ExcelSheetTitleEnum getExcelSheetTitle(Integer summaryType,Integer isWithShop) {
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_NOSHOP;
        }
        return sheetTitle;
    }


    @ApiOperation(value = "畅销品排行报表", notes = "畅销品排行报表")
    @RequestMapping(value = "/bestSellerReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<SalesSummaryReportOrderODTO> bestSellerReport(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.bestSellerReport(salesSummaryReportOrderIDTO);
    }

    @ApiOperation(value = "畅销品排行报表导出", notes = "畅销品排行报表导出")
    @RequestMapping(value = "/exportInfo/bestSellerReport", method = RequestMethod.GET)
    public ModelAndView bestSellerReport(@RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="beginDate", required=false) String beginDate, @RequestParam(value="endDate", required=false) String endDate,
                                         @RequestParam(value="cateId1", required=false) Long cateId1, @RequestParam(value="cateId2", required=false) Long cateId2, @RequestParam(value="cateId3", required=false) Long cateId3,
                                         @RequestParam(value="commodityKey", required=false) String commodityKey, @RequestParam(value="barCode", required=false) String barCode,
                                         @RequestParam(value="summaryType", required=false) Integer summaryType,@RequestParam(value="isWithShop", required=false) Integer isWithShop,
                                         @RequestParam(value="commodityId", required=false) Long commodityId, @RequestParam(value="shopType", required=false) Integer shopType,
                                         @RequestParam(value="provinceId", required=false) Long provinceId, @RequestParam(value = "orgCode", required = false) String orgCode){

        SalesSummaryReportOrderIDTO idto=new SalesSummaryReportOrderIDTO();
        //设置参数
        setSalesSummaryParam1(shopId, beginDate, endDate, cateId1, cateId2, cateId3, commodityKey, barCode, summaryType, isWithShop,null, commodityId, shopType, provinceId, orgCode, idto);
        TablePageInfo<SalesSummaryReportOrderODTO> pageDate=salesSummaryReportService.bestSellerReport(idto);
        List<SalesSummaryReportOrderODTO> list= pageDate.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",pageDate.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",pageDate.getHeader()));
            totalMap.put(9,getFieldValueByName("totalSaleWeight",pageDate.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGrossprofitmarginAmount",pageDate.getHeader()));
            setTotalRow(data, totalMap,12);

            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnit());
                dataLst.add(dto.getTatalQuantity()+"");
                dataLst.add(dto.getTatalAmount()+"");
                dataLst.add(dto.getWeightAmount()+"");
                dataLst.add(dto.getGrossprofitmarginAmount()+"");
                dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
                dataLst.add(dto.getAvgSalePrice() == null ? "" : (dto.getAvgSalePrice()+""));
                data.put("key_"+ i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "畅销品排行报表"+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.BEST_SELLS_REPORT);
        map.put("data", data);
        map.put("title", "畅销品排行报表");
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    private void setSalesSummaryParam1(Long shopId,String beginDate,String endDate,Long cateId1, Long cateId2,Long cateId3,String commodityKey,String barCode,Integer summaryType,Integer isWithShop, Integer isSortByAmount,Long commodityId, Integer shopType, Long provinceId, String orgCode, SalesSummaryReportOrderIDTO idto) {
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(null !=cateId1) {
            idto.setCateId1(cateId1);
        }
        if(null !=cateId2) {
            idto.setCateId2(cateId2);
        }
        if(null !=cateId3) {
            idto.setCateId3(cateId3);
        }
        if(!StringUtils.isEmpty(commodityKey)) {
            idto.setCommodityKey(commodityKey);
        }
        if(!StringUtils.isEmpty(barCode)) {
            idto.setBarCode(barCode);
        }
        if(null !=summaryType) {
            idto.setSummaryType(summaryType);
        }
        if(null !=isWithShop) {
            idto.setIsWithShop(isWithShop);
        }
        if(null !=isSortByAmount) {
            idto.setIsSortByAmount(isSortByAmount);
        }
        if(null !=commodityId) {
            idto.setCommodityId(commodityId);
        }
        if(null != shopType){
            idto.setShopType(shopType);
        }
        if(null != provinceId){
            idto.setProvinceId(provinceId);
        }
        if(null != orgCode) {
            idto.setOrgCode(orgCode);
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
    }
}
