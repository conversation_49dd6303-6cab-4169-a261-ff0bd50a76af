package com.pinshang.qingyun.report.search.elasticsearch.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "day_third_sales_summary")
public class EsDayThirdSaleSummary {

    @Id
    private Long id;

    /** 门店ID*/
    @Field(type = FieldType.Long)
    private Long shopId;

    /** 门店code*/
    @Field(type = FieldType.Keyword)
    private String shopCode;

    /** 门店名称*/
    @Field(type = FieldType.Keyword)
    private String shopName;

    /** 一级大类ID*/
    @Field(type = FieldType.Long)
    private Long commodityFirstId;
    @Field(type = FieldType.Keyword)
    private String commodityFirstCode;
    @Field(type = FieldType.Keyword)
    private String commodityFirstName;

    /** 二级大类ID*/
    @Field(type = FieldType.Long)
    private Long commoditySecondId;
    @Field(type = FieldType.Keyword)
    private String commoditySecondCode;
    @Field(type = FieldType.Keyword)
    private String commoditySecondName;

    /** 三级大类ID*/
    @Field(type = FieldType.Long)
    private Long commodityThirdId;
    @Field(type = FieldType.Keyword)
    private String commodityThirdCode;
    @Field(type = FieldType.Keyword)
    private String commodityThirdName;

    /** 销售日期*/
    @Field(type = FieldType.Date)
    private String saleTime;

    /** 销售数量*/
    @Field(type = FieldType.Double)
    private Double saleQuantity = Double.valueOf(0);

    /** 退货数量*/
    @Field(type = FieldType.Double)
    private Double returnQuantity = Double.valueOf(0);

    /** 赠送数量*/
    @Field(type = FieldType.Double)
    private Double giveQuantity = Double.valueOf(0);

    /** 销售金额*/
    @Field(type = FieldType.Double)
    private Double saleAmount = Double.valueOf(0);

    /** 销售成本*/
    @Field(type = FieldType.Double)
    private Double weightAmount = Double.valueOf(0);

    /** 退货金额*/
    @Field(type = FieldType.Double)
    private Double returnAmount = Double.valueOf(0);

    /** 赠送金额*/
    @Field(type = FieldType.Double)
    private Double giveAmount = Double.valueOf(0);

    /** 让利金额*/
    @Field(type = FieldType.Double)
    private Double discountAmount = Double.valueOf(0);


    /** 数量小计*/
    @Field(type = FieldType.Double)
    private Double tatalQuantity = Double.valueOf(0);

    /** 金额小计*/
    @Field(type = FieldType.Double)
    private Double tatalAmount = Double.valueOf(0);

    /** 不含税金额*/
    @Field(type = FieldType.Double)
    private Double noTaxRateAmount = Double.valueOf(0);

    /** 税额*/
    @Field(type = FieldType.Double)
    private Double taxRateAmount = Double.valueOf(0);

    /** 不含税成本 */
    @Field(type = FieldType.Double)
    private Double noTaxWeightAmount = Double.valueOf(0);
}
