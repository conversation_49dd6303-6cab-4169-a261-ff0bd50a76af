package com.pinshang.qingyun.report.search.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName DemoDTO
 * <AUTHOR>
 * @Date 2021/4/29 15:35
 * @Description DemoDTO
 * @Version 1.0
 */
@Data
public class LogActionListODTO {
    /**
     * 所属系统
     */
    @ApiModelProperty("系统名称code: SystemIdNewEnums")
    private String systemName;
    /**
     * 一级菜单
     */
    @ApiModelProperty("一级菜单")
    private String firstLevelMenu;

    /**
     * 二级菜单
     */
    @ApiModelProperty("二级菜单")
    private String secondLevelMenu;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String menuName;

    /**
     * 菜单名称
     */
    @ApiModelProperty("操作次数")
    private Long actionCount;


}
