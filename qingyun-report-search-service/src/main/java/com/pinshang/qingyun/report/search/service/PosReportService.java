package com.pinshang.qingyun.report.search.service;

import com.pinshang.qingyun.report.search.dto.PosToEsODTO;
import com.pinshang.qingyun.report.search.elasticsearch.document.ESShortDeliveryReport;
import com.pinshang.qingyun.report.search.elasticsearch.document.EsDaySaleSummary;
import com.pinshang.qingyun.report.search.elasticsearch.document.EsDayThirdSaleSummary;
import com.pinshang.qingyun.report.search.elasticsearch.repository.EsDaySaleSummaryRepository;
import com.pinshang.qingyun.report.search.elasticsearch.repository.EsDayThirdSaleSummaryRepository;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.clients.elasticsearch7.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2021/7/7
 */
@Service
public class PosReportService {

    @Autowired
    private EsDaySaleSummaryRepository esDaySaleSummaryRepository;

    @Autowired
    private EsDayThirdSaleSummaryRepository esDayThirdSaleSummaryRepository;
    @Autowired
    private SalesSummaryReportService salesSummaryReportService;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    /**
     * 获取商品日汇总,类别日汇总 ES sum值
     * @return
     */
    public Map<String, BigDecimal> getPosReportESSum(String saleTime){
        Map<String, BigDecimal> sumMap = new HashMap<>();
        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("saleTime", saleTime));
        queryBuilder.withQuery(boolQueryBuilder);

        // 设置聚合属性
        queryBuilder.addAggregation(AggregationBuilders.sum("sum_tatalAmount").field("tatalAmount"));

        double dayAmount = 0;
        double thirdAmount = 0;
        queryBuilder.withTrackTotalHits(true);
        SearchHits<EsDaySaleSummary> searchDay = elasticsearchTemplate.search(queryBuilder.build(),EsDaySaleSummary.class);
        if(CollectionUtils.isNotEmpty(searchDay.getSearchHits())){
            Map<String, Aggregation> map = ((ElasticsearchAggregations)searchDay.getAggregations()).aggregations().asMap();
            dayAmount = (Sum)map.get("sum_tatalAmount") != null ? ((Sum) map.get("sum_tatalAmount")).getValue() : 0;
        }

        SearchHits<EsDayThirdSaleSummary> searchThirdDay = elasticsearchTemplate.search(queryBuilder.build(),EsDayThirdSaleSummary.class);
        if(CollectionUtils.isNotEmpty(searchThirdDay.getSearchHits())){
            Map<String, Aggregation> map = ((ElasticsearchAggregations)searchThirdDay.getAggregations()).aggregations().asMap();
            thirdAmount = (Sum)map.get("sum_tatalAmount") != null ? ((Sum) map.get("sum_tatalAmount")).getValue() : 0;
        }

        sumMap.put("dayAmount",new BigDecimal(dayAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
        sumMap.put("thirdAmount",new BigDecimal(thirdAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
        return sumMap;
    }

    /**
     * pos非当日数据，直接保存进es
     * @return
     */
    public Boolean posNotCurrentDaySaveES(PosToEsODTO posToEsODTO){

        List<EsDaySaleSummary> daySaleSummaryList = posToEsODTO.getDaySaleSummaryList();
        List<EsDayThirdSaleSummary> dayThirdSaleSummaryList = posToEsODTO.getDayThirdSaleSummaryList();
        String saleTime = daySaleSummaryList.get(0).getSaleTime();
        try{
            esDaySaleSummaryRepository.saveAll(daySaleSummaryList);
            esDayThirdSaleSummaryRepository.saveAll(dayThirdSaleSummaryList);

        }catch (Exception e){
            salesSummaryReportService.toEsErrorSendMessage("posNotCurrent" + saleTime,"pos非当日数据保存进ES异常,日期" + saleTime);
        }
        return Boolean.TRUE;
    }


    /**
     * 获取ES短交报表 实发总金额
     * @return
     */
    public BigDecimal getShortReportESSum(String orderTime){
        Map<String, BigDecimal> sumMap = new HashMap<>();
        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("orderTime", orderTime));
        queryBuilder.withQuery(boolQueryBuilder);

        // 设置聚合属性
        queryBuilder.addAggregation(AggregationBuilders.sum("sum_deliveryAmount").script(new Script("params._source.deliveryNum * params._source.price ")));

        double deliveryAmount = 0;
        queryBuilder.withTrackTotalHits(true);
        SearchHits<ESShortDeliveryReport> search = elasticsearchTemplate.search(queryBuilder.build(),ESShortDeliveryReport.class);
        if(CollectionUtils.isNotEmpty(search.getSearchHits())){
            Map<String, Aggregation> map = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
            deliveryAmount = (Sum)map.get("sum_deliveryAmount") != null ? ((Sum) map.get("sum_deliveryAmount")).getValue() : 0;
        }

        return new BigDecimal(deliveryAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
    }
}
