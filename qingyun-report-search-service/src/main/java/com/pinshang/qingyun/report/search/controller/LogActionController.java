package com.pinshang.qingyun.report.search.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.search.dto.*;
import com.pinshang.qingyun.report.search.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.search.enums.ReportActionOperaTypeEnum;
import com.pinshang.qingyun.report.search.service.LogActionService;
import com.pinshang.qingyun.report.search.util.ViewExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName DemoController
 * <AUTHOR>
 * @Date 2021/4/29 14:54
 * @Description DemoController
 * @Version 1.0
 */
@RestController
@RequestMapping("/logAction")
@Api(value = "访问日志相关接口", tags = "logAction", description = "访问日志相关接口")
public class LogActionController {

    @Autowired
    private LogActionService logActionService;

    @ApiOperation(value = "访问日志明细", notes = "访问日志详情查询", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/searchLogsDetails", method = RequestMethod.POST)
    public PageInfo<LogActionDetailsODTO> searchLogsDetails(@RequestBody LogActionDetailsIDTO idto){
        return logActionService.searchLogsDetails(idto);
    }

    @ApiOperation(value = "访问日志汇总", notes = "访问日志分页查询", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/searchLogsList", method = RequestMethod.POST)
    public PageInfo<LogActionListODTO> searchLogsList(@RequestBody LogActionListIDTO idto){
        return logActionService.searchLogsList(idto);
    }

    @ApiOperation(value = "访问日志明细", notes = "访问日志详情查询-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/searchLogsDetails/export", method = RequestMethod.GET)
    public ModelAndView searchLogsDetailsExport(LogActionDetailsIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(65536);
        TablePageInfo<LogActionDetailsODTO> result = logActionService.searchLogsDetails(idto);
        List<LogActionDetailsODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataList = new ArrayList<>();
        int i = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (null != list && !list.isEmpty()) {
            for (LogActionDetailsODTO dto : list) {
                dataList = new ArrayList<>();
                dataList.add(dto.getFirstLevelMenu());
                dataList.add(dto.getSecondLevelMenu());
                dataList.add(dto.getMenuName());
                dataList.add(ReportActionOperaTypeEnum.getDescByCode(dto.getOperaType()));
                dataList.add(dto.getStartTime());
                dataList.add(dto.getUserCode());
                dataList.add(dto.getUserName());
                dataList.add(dto.getUserDept());
                dataList.add(dto.getSystemName());
                data.put("key_" + i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名

        String filename = "访问日志明细" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.ACTION_LOG_DETAIL);
        map.put("data", data);
        map.put("tableHeader", "访问日志明细");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "访问日志汇总", notes = "访问日志详情查询-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/searchLogsList/export", method = RequestMethod.GET)
    public ModelAndView searchLogsListExport(LogActionListIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(65536);
        PageInfo<LogActionListODTO> result = logActionService.searchLogsList(idto);
        List<LogActionListODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataList = new ArrayList<>();
        int i = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (null != list && !list.isEmpty()) {
            for (LogActionListODTO dto : list) {
                dataList = new ArrayList<>();
                dataList.add(dto.getFirstLevelMenu());
                dataList.add(dto.getSecondLevelMenu());
                dataList.add(dto.getMenuName());
                dataList.add(dto.getActionCount()+"");
                dataList.add(dto.getSystemName());
                data.put("key_" + i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名

        String filename = "访问日志汇总" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.ACTION_LOG_LIST);
        map.put("data", data);
        map.put("tableHeader", "访问日志汇总");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @GetMapping("/refreshActionLogMatchInfo")
    @ApiOperation(value = "访问日志缓存刷新", notes = "访问日志缓存刷新", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public boolean refreshActionLogMatchInfo(){
        return logActionService.refreshActionLogMatchInfo();
    }

    /**
     * 查询访问日志菜单列表
     * @param idto
     * @return
     */
    @GetMapping("/findReportMenuList")
    @ApiOperation(value = "查询菜单列表", notes = "查询菜单列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ReportMenuItemODTO> findReportMenuList(ReportMenuItemIDTO idto){
        return logActionService.findReportMenuList(idto);
    }
}
