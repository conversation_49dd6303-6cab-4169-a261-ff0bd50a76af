package com.pinshang.qingyun.report.search.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * http://localhost:9077/swagger-ui.html
 */
@Configuration
@EnableSwagger2
@Profile({"me","dev","test"})
public class ReportSearchSwagger2Configuration {

    @Bean
    public Docket shopRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.pinshang.qingyun.report.search"))
                // 下面这句代码是只生成被ApiOperation这个注解注解过的api接口
                // 以 及最后一定要执行build()方法,不然不起作用
                //.apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build();


    }

    private ApiInfo apiInfo() {
        Contact contact = new Contact("pinshang", "http://qiye.tramy.com", "<EMAIL>");
        return new ApiInfoBuilder()
                .title("品上生活API")
                .description("报表API接口")
                .termsOfServiceUrl("http://localhost:9029/swagger-ui.html")
                .contact(contact)
                .version("1.0")
                .build();
    }
}

