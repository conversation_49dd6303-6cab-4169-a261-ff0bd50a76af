package com.pinshang.qingyun.report.dto.pos;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.report.search.dto.TableColumnODTO;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/6/21
 */
public class TablePageInfo<T> extends PageInfo<T> {
    /** 表头 */
    private Object header;
    /** 表尾 */
    private Object footer;

    /** 表列名 */
    private List<TableColumnODTO> tableColumns;


    public Object getHeader() {
        return header;
    }

    public void setHeader(Object header) {
        this.header = header;
    }

    public Object getFooter() {
        return footer;
    }

    public void setFooter(Object footer) {
        this.footer = footer;
    }

    public List<TableColumnODTO> getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(List<TableColumnODTO> tableColumns) {
        this.tableColumns = tableColumns;
    }
}
