package com.pinshang.qingyun.report.search.elasticsearch.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName EsUtilizationTrack
 * <AUTHOR>
 * @Date 2021/4/25 11:26
 * @Description EsUtilizationTrack
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "action_log_v2")
public class EsActionLogV2 {
    @Id
    private Long id;

    /**
     * 系统名称
     */
    @Field(type = FieldType.Keyword)
    private String applicationName;
    /**
     * 操作描述
     */
    @Field(type = FieldType.Keyword)
    private String description;

    /**
     * 操作用户
     */
    @Field(type = FieldType.Keyword)
    private String userCode;

    /**
     * 操作用户名
     */
    @Field(type = FieldType.Keyword)
    private String userName;

    /**
     * 操作时间
     */
    @Field(type = FieldType.Long)
    private Long startTime;

    /**
     * 消耗时间
     */
    @Field(type = FieldType.Integer)
    private Integer spendTime;

    /**
     * URI
     */
    @Field(type = FieldType.Keyword)
    private String uri;

    /**
     * URL
     */
    @Field(type = FieldType.Keyword)
    private String url;

    /**
     * 请求类型
     */
    @Field(type = FieldType.Keyword)
    private String method;

    /**
     * IP地址
     */
    @Field(type = FieldType.Keyword)
    private String ip;

    /**
     * 请求参数
     */
    @Field(type = FieldType.Text)
    private String parameterStr;

    /**
     * 请求返回的结果
     */
    @Field(type = FieldType.Text)
    private String resultStr;

    /**
     * 一级菜单id
     */
    @Field(type = FieldType.Long)
    private Long firstLevelMenuId;

    /**
     * 二级菜单id
     */
    @Field(type = FieldType.Long)
    private Long secondLevelMenuId;

    /**
     * 三级菜单id
     */
    @Field(type = FieldType.Long)
    private Long thirdLevelMenuId;

    /**
     * 访问账户所属部门
     */
    @Field(type = FieldType.Keyword)
    private String userDept;

    /**
     * 访问动作, 0-查询, 1-导出
     */
    @Field(type = FieldType.Integer)
    private Integer operaType;

    /**
     * 系统id
     */
    @Field(type = FieldType.Integer)
    private Integer systemId;
}
