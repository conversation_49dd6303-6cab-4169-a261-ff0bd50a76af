package com.pinshang.qingyun.report.search.mapper;

import com.pinshang.qingyun.report.search.dto.ReportMenuItem;
import com.pinshang.qingyun.report.search.dto.ReportMenuItemVO;
import com.pinshang.qingyun.report.search.model.ReportActionLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * @ClassName ActionLogMapper
 * <AUTHOR>
 * @Date 2021/4/25 14:52
 * @Description ActionLogMapper
 * @Version 1.0
 */
@Repository
public interface ReportActionLogMapper extends Mapper<ReportActionLog> {
    ReportActionLog selectMatchInfoByDescription(@Param("applicationName") String applicationName, @Param("uri") String uri);

    /**
     * 按第一第二路径排序后的菜单list
     * @return
     */
    List<ReportMenuItem> selectReportMenuList(@Param("systemId") Long systemId);

    String selectMenuName(@Param("id") Long id, @Param("type") String type);

    List<ReportMenuItemVO> selectDistinctMenu();
}
