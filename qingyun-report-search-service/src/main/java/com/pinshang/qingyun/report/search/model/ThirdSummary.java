package com.pinshang.qingyun.report.search.model;

import lombok.Data;

import javax.persistence.Table;


@Table(name = "t_pos_report_third_summary")
@Data
public class ThirdSummary extends BaseModel<ThirdSummary> {

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    /** 一级大类ID*/
    private Long commodityFirstId;
    private String commodityFirstCode;
    private String commodityFirstName;

    /** 二级大类ID*/
    private Long commoditySecondId;
    private String commoditySecondCode;
    private String commoditySecondName;

    /** 三级大类ID*/
    private Long commodityThirdId;
    private String commodityThirdCode;
    private String commodityThirdName;

    /** 销售数量*/
    private Double saleQuantity;

    /** 退货数量*/
    private Double returnQuantity;

    /** 赠送数量*/
    private Double giveQuantity;

    /** 销售金额*/
    private Double saleAmount;

    /** 销售成本*/
    private Double weightAmount;

    /** 退货金额*/
    private Double returnAmount;

    /** 赠送金额*/
    private Double giveAmount;

    /** 让利金额*/
    private Double discountAmount;


    /** 数量小计*/
    private Double tatalQuantity;

    /** 金额小计*/
    private Double tatalAmount;

    /** 不含税金额*/
    private Double noTaxRateAmount;

    /** 税额*/
    private Double taxRateAmount;

    /** 不含税成本*/
    private Double noTaxWeightAmount;

    /** 销售日期*/
    private String saleTime;
}
