package com.pinshang.qingyun.report.search.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.search.dto.*;
import com.pinshang.qingyun.report.search.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.search.service.ShopReportService;
import com.pinshang.qingyun.report.search.util.ViewExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/shopReport")
@Api(value = "门店报表接口", tags = "shopReport", description = "门店报表相关接口")
@Slf4j
public class ShopReportController {

    @Autowired
    private ShopReportService shopReportService;


    @ApiOperation(value = "门店短交报表", notes = "门店短交报表",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shortDeliveryReport", method = RequestMethod.GET)
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        return shopReportService.shortDeliveryReport(shortDeliveryReportIDto);
    }
    @ApiOperation(value = "门店短交报表-导出", notes = "门店短交报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/shortDeliveryReport", method = RequestMethod.GET)
    public ModelAndView exportInfoShortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        QYAssert.isTrue(shortDeliveryReportIDto.getShopId() != null, "权限有问题或未选择门店");
        QYAssert.isTrue(shortDeliveryReportIDto.getBeginDate() != null, "请选择送货日期");
        QYAssert.isTrue(shortDeliveryReportIDto.getEndDate() != null, "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(shortDeliveryReportIDto.getBeginDate(), "yyyy-MM-dd"), 1), DateTimeUtil.parse(shortDeliveryReportIDto.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        shortDeliveryReportIDto.setPageNo(1);
        shortDeliveryReportIDto.setPageSize(65536);

        PageInfo<ShortDeliveryReportODto> result = shopReportService.shortDeliveryReport(shortDeliveryReportIDto);
        List<ShortDeliveryReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (null != list && !list.isEmpty()) {
            for (ShortDeliveryReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getShopTypeName());
                dataLst.add(dto.getShopName());
                dataLst.add(dateFormat.format(dto.getDeliveryDate()));
                dataLst.add(dto.getOrderCode());
                dataLst.add(dto.getCategoryName());
                dataLst.add(dto.getSecondCategoryName());
                dataLst.add(dto.getThirdCategoryName());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(dto.getPrice().toString());
                dataLst.add(dto.getOrderNum().toString());
                dataLst.add(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                dataLst.add(dto.getDifferNum() == null ? "" : dto.getDifferNum().toString());
                dataLst.add(dto.getRate() == null ? "" : dto.getRate().toString());
                dataLst.add(dto.getFactoryName());
                dataLst.add(dto.getWorkshopName());
                dataLst.add(dto.getStoreCode());
                dataLst.add(dto.getCreateName());
                if(shortDeliveryReportIDto.getIsHq() != null && 1 == shortDeliveryReportIDto.getIsHq()) {
                    dataLst.add(dto.getStoreLineGroupName());
                    dataLst.add(dto.getWarehouseName());
                    dataLst.add(dto.getManagementModeName());
                }
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "短交报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        if(shortDeliveryReportIDto.getIsHq() != null && 1 == shortDeliveryReportIDto.getIsHq()){
            map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_SHORT_DELIVERY_HQ);
        }else{
            map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_SHORT_DELIVERY);
        }
        map.put("data", data);
//        map.put("title", "短交报表");
//        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    @ApiOperation(value = "门店实收商品分析表", notes = "门店实收商品分析表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public TablePageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto idto) throws Exception{
        return shopReportService.actualReceiptAnalysisReport(idto);
    }

    @ApiOperation(value = "门店实收商品分析表-导出", notes = "门店实收商品分析表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public ModelAndView exportInfoActualReceiptAnalysisReport(ActualReceiptAnalysisIDto actualReceiptAnalysisIDto)throws Exception {
        actualReceiptAnalysisIDto.setPageNo(1);
        actualReceiptAnalysisIDto.setPageSize(65536);

        TablePageInfo<ActualReceiptAnalysisODto> result = actualReceiptAnalysisReport(actualReceiptAnalysisIDto);
        List<ActualReceiptAnalysisODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        int i = 0;
        String shopName = "";
        if (null != list && !list.isEmpty()) {
            for (ActualReceiptAnalysisODto dto : list) {
                List<String> dataList = new ArrayList<>();
                if (i == 0) {
                    if (actualReceiptAnalysisIDto.getShopId() != null) {
                        shopName = dto.getShopName();
                    } else {
                        shopName = "全部";
                    }
                }
                dataList.add(dto.getShopName());
                dataList.add(dto.getCateName());
                dataList.add(dto.getBarCode());
                dataList.add(dto.getCommodityCode());
                dataList.add(dto.getCommodityName());
                dataList.add(dto.getCommoditySpec());
                dataList.add(toStr(dto.getTotalQuantity()));
                dataList.add(toStr(dto.getTotalRealDeliveryQuantity()));
                dataList.add(toStr(dto.getTotalRealReceiveQuantity()));
                dataList.add(toStr(dto.getQuantityDifference()));
                //dataList.add(toStr(dto.getStockQuantity()));
                dataList.add(toStr(dto.getSupplyPrice()));
                dataList.add(toStr(dto.getTotalSupplyPrice()));
                dataList.add(dto.getSupplierName());
                dataList.add(dto.getRealName());
                data.put("key_" + i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "门店实收商品分析表" + "_" + sdf.format(new Date());

        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle",ExcelSheetTitleEnum.ACTUAL_RECEIPT_ANALYSIS_REPORT);
        map.put("data", data);
        map.put("title", "门店实收商品分析表[" + shopName + "]");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    private String toStr(Object o) {
        if(o == null) {
            return "";
        }else if(o instanceof Date) {
            return DateFormatUtils.format((Date)o, "yyyy-MM-dd HH:mm:ss");
        }else {
            return o.toString();
        }
    }


    @ApiOperation(value = "门店订货汇总表(当日)", notes = "门店订货汇总表(当日)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shopOrderGoodReportCurrentDay", method = RequestMethod.POST)
    public TablePageInfo<ShopOrderGoodReportODto> shopOrderGoodReportCurrentDay(@RequestBody ShopOrderGoodReportIDto idto) {
        return shopReportService.shopOrderGoodReportCurrentDay(idto);
    }

    @ApiOperation(value = "门店订货汇总表(ES)", notes = "门店订货汇总表(ES)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shopOrderGoodReportES", method = RequestMethod.POST)
    public TablePageInfo<ShopOrderGoodReportODto> shopOrderGoodReportES(@RequestBody ShopOrderGoodReportIDto idto) {
        return shopReportService.shopOrderGoodReportES(idto);
    }

    /**
     * 已废弃,功能迁移至qingyun-order
     */
    @ApiOperation(value = "商品实发汇总表(当日)", notes = "商品实发汇总表(当日)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryReportCurrentDay", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.realDeliveryReportCurrentDay(realDeliveryReportIDto);
    }
    @ApiOperation(value = "商品实发汇总表(ES)", notes = "商品实发汇总表(ES)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryReportES", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportES(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.realDeliveryReportES(realDeliveryReportIDto);
    }

    /**
     * 已废弃,功能迁移至qingyun-order
     */
    @ApiOperation(value = "商品实发汇总表-导出(当日)", notes = "商品实发汇总表-导出(当日)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryReportCurrentDay", method = RequestMethod.GET)
    public void exportInfoRealDeliveryReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto,HttpServletResponse response) throws IOException {
        exportInfoRealDeliveryReport(realDeliveryReportIDto,true, response);
    }
    @ApiOperation(value = "商品实发汇总表-导出(ES)", notes = "商品实发汇总表-导出(ES)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryReportES", method = RequestMethod.GET)
    public void exportInfoRealDeliveryReportES(RealDeliveryReportIDto realDeliveryReportIDto,HttpServletResponse response) throws IOException {
        exportInfoRealDeliveryReport(realDeliveryReportIDto,false, response);
    }
    public void exportInfoRealDeliveryReport(RealDeliveryReportIDto realDeliveryReportIDto, Boolean currentDate, HttpServletResponse response) throws IOException {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(65536);

        TablePageInfo<RealDeliveryReportODto> result ;
        if(currentDate){
            result = shopReportService.realDeliveryReportCurrentDay(realDeliveryReportIDto);
        }else {
            result = shopReportService.realDeliveryReportES(realDeliveryReportIDto);
        }
        List<RealDeliveryReportODto> list = result.getList();
        BigDecimal realTotalAmount ;
        List<RealDeliveryReportODto> dataList = new ArrayList<>();
        if (null != list && !list.isEmpty()) {
            realTotalAmount = (BigDecimal) result.getHeader();
            RealDeliveryReportODto head = new RealDeliveryReportODto();
            head.setCommodityCode("合计");
            head.setRealDeliveryAmount(realTotalAmount);
            dataList.add(head);
            dataList.addAll(result.getList());
        }
        try {
            ExcelUtil.setFileNameAndHead( response,"商品实发汇总报表");
            EasyExcel.write(response.getOutputStream(), RealDeliveryReportODto.class).autoCloseStream(Boolean.FALSE).sheet("商品实发汇总报表")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("商品实发汇总报表导出",e);
        }
    }


    /**
     * 已废弃,功能迁移至qingyun-order
     */
    @ApiOperation(value = "商品实发汇总表(按客户类型) 当日", notes = "商品实发汇总表(按客户类型) 当日",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryStoreTypeReportCurrentDay", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.realDeliveryStoreTypeReportCurrentDay(realDeliveryReportIDto);
    }
    @ApiOperation(value = "商品实发汇总表(按客户类型) ES", notes = "商品实发汇总表(按客户类型) ES",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryStoreTypeReportES", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportES(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.realDeliveryStoreTypeReportES(realDeliveryReportIDto);
    }

    /**
     * 已废弃,功能迁移至qingyun-order
     */
    @ApiOperation(value = "商品实发汇总表-导出(按客户类型) 当日", notes = "商品实发汇总表-导出(按客户类型) 当日", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryStoreTypeReportCurrentDay", method = RequestMethod.GET)
    public ModelAndView exportInfoRealDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return exportInfoRealDeliveryStoreTypeReport(realDeliveryReportIDto,true);
    }
    @ApiOperation(value = "商品实发汇总表-导出(按客户类型) ES", notes = "商品实发汇总表-导出(按客户类型) ES", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryStoreTypeReportES", method = RequestMethod.GET)
    public ModelAndView exportInfoRealDeliveryStoreTypeReportES(RealDeliveryReportIDto realDeliveryReportIDto) {
        return exportInfoRealDeliveryStoreTypeReport(realDeliveryReportIDto,false);
    }

    public ModelAndView exportInfoRealDeliveryStoreTypeReport(RealDeliveryReportIDto realDeliveryReportIDto,Boolean currentDay) {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(65536);

        TablePageInfo<RealDeliveryReportODto> result = new TablePageInfo<>();
        if(currentDay){
            result = shopReportService.realDeliveryStoreTypeReportCurrentDay(realDeliveryReportIDto);
        }else {
            result = shopReportService.realDeliveryStoreTypeReportES(realDeliveryReportIDto);
        }
        List<RealDeliveryReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if (null != list && !list.isEmpty()) {
            realTotalAmount = (BigDecimal) result.getHeader();
            for (RealDeliveryReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getStoreTypeName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getCommodityUnit());
                dataLst.add(dto.getOrderNum() == null ? "" : dto.getOrderNum().toString());
                dataLst.add(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                dataLst.add(dto.getRealDeliveryAmount() == null ? "" : dto.getRealDeliveryAmount().toString());
                dataLst.add(dto.getWarehouseName());
                dataLst.add(dto.getFactoryName());
                dataLst.add(dto.getWorkshopName());
                dataLst.add(dto.getFlowshopName());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品实发汇总报表(客户类型)" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_REAL_DELIVERY_STORE_TYPE);
        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        map.put("tableHeader", "实发金额总计:" + realTotalAmount+"元");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }
}
