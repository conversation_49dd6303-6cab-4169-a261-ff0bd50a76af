package com.pinshang.qingyun.report.search.enums;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * @ClassName ReportActionOperaTypeEnum
 * <AUTHOR>
 * @Date 2021/6/3 18:10
 * @Description ReportActionOperaTypeEnum
 * @Version 1.0
 */
public enum ReportActionOperaTypeEnum {
    /**
     * 导出
     */
    DOWNLOAD(1, "导出"),
    /**
     * 查询
     */
    SEARCH(2,"查询"),
    /**
     * 查询
     */
    DOWNLOAD_OR_SEARCH(3,"查询或导出");

    private int code;
    private String desc;

    ReportActionOperaTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(int code){
        for(ReportActionOperaTypeEnum  typeEnum : ReportActionOperaTypeEnum.values()){
            if(code == typeEnum.getCode()){
                return typeEnum.getDesc();
            }
        }
        return DOWNLOAD_OR_SEARCH.getDesc();
    }
}
