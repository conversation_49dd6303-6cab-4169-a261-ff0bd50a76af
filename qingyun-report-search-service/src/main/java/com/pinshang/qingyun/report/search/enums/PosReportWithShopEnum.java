package com.pinshang.qingyun.report.search.enums;

/**
 * <AUTHOR> @Date
 */
public enum PosReportWithShopEnum {
    ZERO(0, "是否区分门店:不区分"),
    ONE(1, "是否区分门店:区分"),
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;

    PosReportWithShopEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
