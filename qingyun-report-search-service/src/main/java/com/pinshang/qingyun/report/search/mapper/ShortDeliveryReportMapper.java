package com.pinshang.qingyun.report.search.mapper;

import com.pinshang.qingyun.report.search.dto.RealDeliveryReportIDto;
import com.pinshang.qingyun.report.search.dto.RealDeliveryReportODto;
import com.pinshang.qingyun.report.search.dto.ShopOrderGoodReportIDto;
import com.pinshang.qingyun.report.search.dto.ShopOrderGoodReportODto;
import com.pinshang.qingyun.report.search.elasticsearch.document.ESShortDeliveryReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface ShortDeliveryReportMapper{

    List<ESShortDeliveryReport> getShortDeliveryReportByOrderTime(@Param("orderTime") String orderTime);

    List<ESShortDeliveryReport> getShortDeliveryReportByOrderCodeList(@Param("orderCodeList") List<String> orderCodeList);

    List<ShopOrderGoodReportODto> shopOrderGoodReport(@Param("vo") ShopOrderGoodReportIDto idto);

    List<RealDeliveryReportODto> realDeliveryReportCurrentDay(@Param("vo") RealDeliveryReportIDto vo);

    BigDecimal realTotalDeliveryReportCurrentDay(@Param("vo") RealDeliveryReportIDto vo);

    List<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(@Param("vo") RealDeliveryReportIDto vo);

    BigDecimal realTotalDeliveryStoreTypeReportCurrentDay(@Param("vo") RealDeliveryReportIDto vo);
}
