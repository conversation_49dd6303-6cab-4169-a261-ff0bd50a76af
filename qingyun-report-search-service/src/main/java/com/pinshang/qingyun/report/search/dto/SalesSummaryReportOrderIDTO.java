package com.pinshang.qingyun.report.search.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SalesSummaryReportOrderIDTO extends Pagination {

    @ApiModelProperty("组织code")
    private String orgCode;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("销售日期开始时间yyyy-MM-dd HH:mm:ss")
    private String beginDate;//销售日期开始

    @ApiModelProperty("销售日期结束时间yyyy-MM-dd HH:mm:ss")
    private String endDate;//销售日期结束

    @ApiModelProperty("大类ID")
    private Long cateId1;

    @ApiModelProperty("中类ID")
    private Long cateId2;

    @ApiModelProperty("小类ID")
    private Long cateId3;

    @ApiModelProperty("商品编码/名称/助记码")
    private String commodityKey;

    @ApiModelProperty("条形码")
    private String barCode;//条形码
    /**商品销售汇总       *     1：商品汇总（区分门店） 2：商品汇总（不区分门店）
     * 类别销售汇总    3：大类汇总  4：中类汇总  5：小类汇总
     *  门店销售汇总分析  6：门店汇总   7：日毛利率
     * 类别销售汇总分析   同类别销售汇总
     * 商品销售汇总分析   同商品销售汇总
     *畅销品排行  8：按销量  9：按售额   10：按毛利
     */
    @ApiModelProperty("汇总类型1：商品汇总（区分门店） 2：商品汇总（不区分门店）3：大类汇总  4：种类汇总  5：小类汇总6：门店汇总   7：日毛利率8：按销量  9：按售额   10：按毛利")
    private Integer summaryType;//汇总类型

    @ApiModelProperty("是否区分门店(1:区分   0:不区分)")
    private Integer isWithShop;//是否区分门店(1:区分   0:不区分)

    /** 特价类型*/
    @ApiModelProperty("特价类型")
    private String promotionType;

    @ApiModelProperty("促销方案：编码，名称")
    private String promotionKey;
    private Integer isTotal;//0:不查询总和  1：查询总和

    private List<Long> shopIdList;

    @ApiModelProperty("按销售额排序:1排序0不排序")
    private Integer isSortByAmount;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("代销商id")
    private Long consignmentId;

    @ApiModelProperty("门店类型")
    private Integer shopType;

    @ApiModelProperty("省id")
    private Long provinceId;

    @ApiModelProperty("经营模式：1-直营、2-外包")
    private Integer managementMode;

    @ApiModelProperty("档口id")
    private Integer stallId;

    @ApiModelProperty("门店类型List")
    private List<Integer> shopTypeList;

    @ApiModelProperty("经营模式List：1-直营、2-外包")
    private List<Integer> managementModeList;
}
