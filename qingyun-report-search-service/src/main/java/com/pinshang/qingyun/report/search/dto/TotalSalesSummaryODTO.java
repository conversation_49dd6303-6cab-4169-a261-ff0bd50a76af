package com.pinshang.qingyun.report.search.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TotalSalesSummaryODTO {

      @ApiModelProperty("销售数量合计")
      private BigDecimal totalSaleQuantity;

      @ApiModelProperty("退货数量合计")
      private BigDecimal totalReturnQuantity;

      @ApiModelProperty("赠送数量合计")
      private BigDecimal totalGiveQuantity;

      @ApiModelProperty("销售金额合计")
      private BigDecimal totalSaleAmount;

      @ApiModelProperty("退货金额合计")
      private BigDecimal totalReturnAmount;

      @ApiModelProperty("赠送金额合计")
      private BigDecimal totalGiveAmount;

      @ApiModelProperty("让利金额合计")
      private BigDecimal totalDiscountAmount;

      @ApiModelProperty("数量小计合计")
      private BigDecimal totalQuantity;

      @ApiModelProperty("金额小计合计")
      private BigDecimal totalAmount;



      @ApiModelProperty("销售成本合计")
      private BigDecimal totalSaleWeight;

      @ApiModelProperty("毛利额合计")
      private BigDecimal totalGrossprofitmarginAmount;
}
