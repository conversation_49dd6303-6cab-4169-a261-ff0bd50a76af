/*
package com.pinshang.qingyun.report.search.configuration;

import org.elasticsearch.client.Client;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.transport.InetSocketTransportAddress;
import org.elasticsearch.transport.client.PreBuiltTransportClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;

import javax.annotation.PostConstruct;
import java.net.InetAddress;

*/
/**
 * @Author: sk
 * @Date: 2022/8/15
 *由于springboot2.6.6版本远高于 spring-data-elasticsearch3.0.5.RELEASE
 * 导致elasticsearchTemplate加载丢失，故从新延迟加载配置
 *//*

@Lazy
@Configuration
public class ElasticConfiguration {

    private Logger logger = LoggerFactory.getLogger(ElasticConfiguration.class);

    @Value("${spring.data.elasticsearch.cluster-nodes}")
    private String esClusterNodes;

    @Value("${spring.data.elasticsearch.cluster-name}")
    private String esClusterName;

    @Lazy
    @Bean
    public Client client() throws Exception {
        String [] esClusterNodesStr = esClusterNodes.split(":");
        String esHost = esClusterNodesStr[0];
        int esPort = Integer.valueOf(esClusterNodesStr[1]);

        Settings.Builder builder = Settings.builder();
        // builder.put("client.transport.sniff", true);
        Settings settings = builder.put("cluster.name", esClusterName).build();
        TransportClient client = new PreBuiltTransportClient(settings);
        InetAddress address = InetAddress.getByName(esHost);
        client.addTransportAddress(new InetSocketTransportAddress(address, esPort));
        logger.info(
                "ES Client built with cluster.name;" + esClusterName +";host;" + esHost +";port;" + esPort +";client.transport.sniff;"
                        + builder.get("client.transport.sniff"));
        return client;
    }

    @Lazy
    @Bean(name = {"elasticsearchTemplate"})
    public ElasticsearchOperations elasticsearchTemplate() throws Exception{
        return new ElasticsearchTemplate(client());
    }

    */
/**
     * 解决netty冲突后初始化client时还会抛出异常
     *//*

    @PostConstruct
    void init() {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
    }
}
*/
