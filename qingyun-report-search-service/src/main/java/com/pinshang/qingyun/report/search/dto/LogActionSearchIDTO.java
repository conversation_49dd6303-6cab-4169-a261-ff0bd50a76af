package com.pinshang.qingyun.report.search.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName LogActionSearchIDTO
 * <AUTHOR>
 * @Date 2022/3/17 15:22
 * @Description LogActionSearchIDTO
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LogActionSearchIDTO extends Pagination {
    /**
     * 系统名称
     */
    @ApiModelProperty("系统名称code: SystemIdNewEnums")
    private String systemCode;

    /**
     * 操作账户
     */
    @ApiModelProperty("操作账户, 取userId")
    private Long userId;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称 层级编码+action_log表id")
    private String menuCode;

    @ApiModelProperty("开始时间 yyyy-MM-dd HH:mm:ss")
    private String beginDate;

    @ApiModelProperty("结束时间 yyyy-MM-dd HH:mm:ss")
    private String endDate;
}
