package com.pinshang.qingyun.report.search.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品
 */
@Data
public class Commodity {

    private Long commodityId;

    /*** 商品编码 */
    private String commodityCode;

    /*** 商品名称 */
    private String commodityName;

    /*** 商品规格 */
    private String commoditySpec;

    /*** 商品计量单位 */
    private String commodityUnitName;


    /*** 主条形码 */
    private String barCode;

    /** 零售价 */
    private BigDecimal retailPrice;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 小类 */
    private Long commodityFirstKindId;

    /** 小类 */
    private Long commoditySecondKindId;

    /** 小类 */
    private Long commodityThirdKindId;

    //包装规格
    private BigDecimal commodityPackageSpec;

    //是否称重0-不称量,1-称重
    private Integer isWeight;

}
