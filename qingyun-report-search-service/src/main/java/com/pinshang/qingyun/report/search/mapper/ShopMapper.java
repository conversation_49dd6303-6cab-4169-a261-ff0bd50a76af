package com.pinshang.qingyun.report.search.mapper;

import com.pinshang.qingyun.report.search.dto.ShopODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopMapper {

    List<ShopODTO> getShopByIdList(@Param("shopIdList") List<Long> shopIdList);

    /**
     * 根据经营模式查询门店信息
     * @param managementMode
     * @return
     */
    List<Long> shopByManagementMode(@Param("managementMode") Integer managementMode);

    List<Long> queryShopIdListByParam(@Param("shopTypeList") List<Integer> shopTypeList, @Param("managementModeList") List<Integer> managementModeList);


}
