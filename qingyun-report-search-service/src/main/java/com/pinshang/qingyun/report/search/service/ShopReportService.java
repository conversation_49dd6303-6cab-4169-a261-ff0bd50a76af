package com.pinshang.qingyun.report.search.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.search.dto.*;
import com.pinshang.qingyun.report.search.elasticsearch.document.ESShortDeliveryReport;
import com.pinshang.qingyun.report.search.mapper.CommodityMapper;
import com.pinshang.qingyun.report.search.mapper.ShopMapper;
import com.pinshang.qingyun.report.search.mapper.ShortDeliveryReportMapper;
import com.pinshang.qingyun.report.search.util.PageUtil;
import com.pinshang.qingyun.shop.dto.ConsignmentIDTO;
import com.pinshang.qingyun.shop.dto.ConsignmentODTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.consignment.ConsignmentClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.ScriptQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.aggregations.metrics.SumAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.clients.elasticsearch7.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopReportService {

    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private SalesSummaryReportService salesSummaryReportService;

    @Autowired
    private CommodityPriceClient commodityPriceClient;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private ShortDeliveryReportMapper shortDeliveryReportMapper;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private ConsignmentClient consignmentClient;

    /**
     * 门店短交报表
     * @param idto
     * @return
     */
    public TablePageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto idto) {
        TablePageInfo<ShortDeliveryReportODto> pageDate = new TablePageInfo<>();

        QYAssert.isTrue(!StringUtil.isBlank(idto.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(idto.getEndDate()), "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(idto.getBeginDate(), "yyyy-MM-dd"),1), DateTimeUtil.parse(idto.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        boolQueryBuilder.filter(QueryBuilders.rangeQuery("orderTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));

        if(idto.getShopId() != null && idto.getShopId() != 0){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", idto.getShopId()));
        }

        List<Long> orgShopIdList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(idto.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(idto.getOrgCode());
            if (!org.springframework.util.CollectionUtils.isEmpty(shopDtos)) {
                orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(orgShopIdList)){
                    boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", orgShopIdList));
                }else {
                    TablePageInfo info = new TablePageInfo();
                    info.setList(null);
                    return info;
                }
            }else {
                TablePageInfo info = new TablePageInfo();
                info.setList(null);
                return info;
            }
        }

        if (null != idto.getManagementMode()) {
            List<Long> shopIds = shopMapper.shopByManagementMode(idto.getManagementMode());
            if (CollectionUtils.isEmpty(shopIds)) {
                TablePageInfo info = new TablePageInfo();
                info.setList(null);
                return info;
            } else {
                if (!CollectionUtils.isEmpty(orgShopIdList)) {
                    shopIds.retainAll(orgShopIdList);
                }
                boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", shopIds));
            }
        }

        if(idto.getShopType() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopType", idto.getShopType()));
        }

        if(idto.getConsignmentId() != null){
            if(idto.getConsignmentId() > 0){
                boolQueryBuilder.filter(QueryBuilders.termQuery("consignmentId", idto.getConsignmentId()));
            }else {
                List<ConsignmentODTO> consignmentList = consignmentClient.selectConsignmentList(new ConsignmentIDTO());
                if(CollectionUtils.isNotEmpty(consignmentList)){
                    List<Long> consignmentIdList = consignmentList.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
                    boolQueryBuilder.mustNot(QueryBuilders.termsQuery("consignmentId", consignmentIdList));
                }
            }
        }

        BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
        if(StringUtils.isNotBlank(idto.getCommodityKey())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("commodityCode", String.format("*%s*", idto.getCommodityKey() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("commodityName", String.format("*%s*", idto.getCommodityKey() , "*%s*"))));
        }

        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return null;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }

        if(idto.getCateId1() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstKindId", idto.getCateId1()));
        }
        if(idto.getCateId2() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commoditySecondKindId", idto.getCateId2()));
        }
        if(idto.getCateId3() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityThirdKindId", idto.getCateId3()));
        }

        if(StringUtils.isNotBlank(idto.getOrderCode())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderCode", idto.getOrderCode()));
        }

        if(StringUtils.isNotBlank(idto.getFactoryCode())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("factoryCode", idto.getFactoryCode()));
        }

        if(idto.isDiffer()){
            Script script = new Script("doc['deliveryNum'].value == null");
            Script script1 = new Script("doc['orderNum'].value != doc['deliveryNum'].value ");
            ScriptQueryBuilder filterBuilder = QueryBuilders.scriptQuery(script);
            ScriptQueryBuilder filterBuilder1 = QueryBuilders.scriptQuery(script1);

            QueryBuilder query = QueryBuilders.boolQuery()
                    .should(filterBuilder)
                    .should(filterBuilder1);

            boolQueryBuilder.must(query);
        }

        queryBuilder.withQuery(boolQueryBuilder);

        // 结果过滤：通过sourceFilter设置返回的结果字段
        queryBuilder.withSourceFilter(new FetchSourceFilter(
                new String[]{"shopId","shopCode", "shopName", "orderTime", "orderCode","commodityFirstKindName"
                        ,"commoditySecondKindName","commodityThirdKindName","commodityId",
                        "commodityCode", "commodityName","commoditySpec","commodityUnitName","orderNum","price","deliveryNum",
                        "differNum","rate","factoryName","workshopName","storeCode","createName","shopType","storeLineGroupName"}, null));

        // 分页
        int page = idto.getPageNo();
        int size = idto.getPageSize();
        queryBuilder.withPageable(PageRequest.of(page - 1, size));

        // 排序
        FieldSortBuilder shopCodeSortBuilder = SortBuilders.fieldSort("shopCode").order(SortOrder.ASC);
        FieldSortBuilder deliveryDateSortBuilder = SortBuilders.fieldSort("orderTime").order(SortOrder.DESC);
        FieldSortBuilder commodityCodeSortBuilder = SortBuilders.fieldSort("commodityCode").order(SortOrder.ASC);
        queryBuilder.withSort(shopCodeSortBuilder).withSort(deliveryDateSortBuilder).withSort(commodityCodeSortBuilder);
        queryBuilder.withTrackTotalHits(true);
        SearchHits<ESShortDeliveryReport> search = elasticsearchTemplate.search(queryBuilder.build(),ESShortDeliveryReport.class);
        List<SearchHit<ESShortDeliveryReport>> searchHits = search.getSearchHits();
        if(CollectionUtils.isEmpty(searchHits)){
            return new TablePageInfo<>();
        }

        // 设置返回分页信息
        PageUtil.getPageMap(Integer.valueOf(search.getTotalHits() + ""),idto.getPageNo(),idto.getPageSize(),pageDate);

        List<Long> commodityIdList = searchHits.stream().map(item -> item.getContent().getCommodityId()).collect(Collectors.toList());

        Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
        Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);

        List<Long> shopIds = searchHits.stream().map(item -> item.getContent().getShopId()).collect(Collectors.toList());
        List<ShopODTO> shopODTOList = shopMapper.getShopByIdList(shopIds);
        Map<Long,String> shopMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(shopODTOList)) {
            shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getManagementModeName));
        }

        List<ShortDeliveryReportODto> list = new ArrayList<>();
        for(SearchHit<ESShortDeliveryReport> searchHit : searchHits){
            ESShortDeliveryReport es = searchHit.getContent();
            ShortDeliveryReportODto odto = new ShortDeliveryReportODto();
            BeanUtils.copyProperties(es,odto);

            odto.setCommodityId(es.getCommodityId() + "");
            odto.setDeliveryDate(DateUtil.parseDate(es.getOrderTime(),"yyyy-MM-dd"));
            odto.setCategoryName(es.getCommodityFirstKindName());
            odto.setSecondCategoryName(es.getCommoditySecondKindName());
            odto.setThirdCategoryName(es.getCommodityThirdKindName());
            odto.setOrderNum(null != es.getOrderNum() ? new BigDecimal(es.getOrderNum()).setScale(3,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            odto.setDeliveryNum(null != es.getDeliveryNum() ? new BigDecimal(es.getDeliveryNum()).setScale(3,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            odto.setDifferNum(null != es.getDifferNum() ? new BigDecimal(es.getDifferNum()).setScale(3,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            odto.setPrice(null != es.getPrice() ? new BigDecimal(es.getPrice()).setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            odto.setShopType(Integer.valueOf(es.getShopType() + ""));

            String barCodes = barCodeMap.get(Long.valueOf(odto.getCommodityId()));
            odto.setBarCodes(barCodes);
            odto.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");

            CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(Long.valueOf(odto.getCommodityId()));
            if(commodityWarehouseODto != null){
                odto.setWarehouseName(commodityWarehouseODto.getWarehouseName());
            }
            odto.setManagementModeName(shopMap.get(es.getShopId()));
            list.add(odto);
        }
        pageDate.setList(list);

        return pageDate ;
    }


    /**
     * 门店实收商品分析表
     * @param idto
     * @return
     */
    public TablePageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto idto) {
        TablePageInfo<ActualReceiptAnalysisODto> pageDate = new TablePageInfo<>();

        QYAssert.isTrue(!StringUtil.isBlank(idto.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(idto.getEndDate()), "请选择送货日期");

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        if(idto.getShopId() != null && idto.getShopId() != 0){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", idto.getShopId()));
        }

        boolQueryBuilder.filter(QueryBuilders.rangeQuery("orderTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));

        Script script = new Script("doc['receiveStatus'].value != 0 ");
        Script script1 = new Script("doc['receiveStatus'].value != 4 ");
        ScriptQueryBuilder filterBuilder = QueryBuilders.scriptQuery(script);
        ScriptQueryBuilder filterBuilder1 = QueryBuilders.scriptQuery(script1);
        QueryBuilder query = QueryBuilders.boolQuery()
                .must(filterBuilder)
                .must(filterBuilder1);
        boolQueryBuilder.must(query);

        BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
        if(idto.getCateId1() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstKindId", idto.getCateId1()));
        }
        if(idto.getCateId2() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commoditySecondKindId", idto.getCateId2()));
        }
        if(idto.getCateId3() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityThirdKindId", idto.getCateId3()));
        }
        if(StringUtils.isNotBlank(idto.getSearchWord())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("commodityCode", String.format("*%s*", idto.getSearchWord() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("commodityName", String.format("*%s*", idto.getSearchWord() , "*%s*"))));
        }

        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return pageDate;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }

        queryBuilder.withQuery(boolQueryBuilder);

        // 设置聚合
        TermsAggregationBuilder shopIdAgg = AggregationBuilders.terms("group_shopId").field("shopId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder commodityAgg = AggregationBuilders.terms("group_commodity").field("commodityId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);

        SumAggregationBuilder sum_orderNum = AggregationBuilders.sum("sum_orderNum").field("orderNum");
        SumAggregationBuilder sum_receiveNum = AggregationBuilders.sum("sum_receiveNum").field("receiveNum");
        SumAggregationBuilder sum_deliveryNum = AggregationBuilders.sum("sum_deliveryNum").field("deliveryNum");

        // 设置聚合属性
        queryBuilder.addAggregation(shopIdAgg.subAggregation(commodityAgg.subAggregation(sum_orderNum)
                .subAggregation(sum_receiveNum).subAggregation(sum_deliveryNum)));
        queryBuilder.withTrackTotalHits(true);
        SearchHits<ESShortDeliveryReport> search = elasticsearchTemplate.search(queryBuilder.build(),ESShortDeliveryReport.class);
        if(CollectionUtils.isEmpty(search.getSearchHits())){
            return new TablePageInfo<>();
        }

        Map<String, Aggregation> aggMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
        ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
        List<ActualReceiptAnalysisODto> actualReceiptlist = new ArrayList<>();
        for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
            Long shopId = (Long)shopIdBucket.getKey();

            Map<String, Aggregation> commodityMap = shopIdBucket.getAggregations().getAsMap();
            ParsedLongTerms commodityIdTerms = (ParsedLongTerms)commodityMap.get("group_commodity");
            for(Terms.Bucket commodityBucket:commodityIdTerms.getBuckets()){
                Long commodityId = (Long)commodityBucket.getKey();

                double orderNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")) != null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")).getValue() : 0;
                double receiveNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_receiveNum")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_receiveNum")).getValue() : 0;
                double deliveryNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")).getValue() : 0;

                ActualReceiptAnalysisODto oDto = new ActualReceiptAnalysisODto();
                oDto.setShopId(shopId);
                oDto.setCommodityId(commodityId);
                oDto.setTotalQuantity(new BigDecimal(orderNum));
                oDto.setTotalRealReceiveQuantity(new BigDecimal(receiveNum));
                oDto.setTotalRealDeliveryQuantity(new BigDecimal(deliveryNum));
                actualReceiptlist.add(oDto);
            }
        }

        // 设置分页、合计
        if(CollectionUtils.isNotEmpty(actualReceiptlist)){
            setActualReceiptReturnPage(idto,pageDate,actualReceiptlist);
        }
        return pageDate;
    }

    // 门店实收商品分析表  设置返回pageData
    public void setActualReceiptReturnPage(ActualReceiptAnalysisIDto idto,TablePageInfo info,List<ActualReceiptAnalysisODto> actualReceiptlist){
        //分页
        Map<String,Integer> getPageMap = PageUtil.getPageMap(actualReceiptlist.size(),idto.getPageNo(),idto.getPageSize(),info);
        int startPoint = getPageMap.get("startPoint");
        int endPoint = getPageMap.get("endPoint");
        List<ActualReceiptAnalysisODto> pageList = actualReceiptlist.subList(startPoint, endPoint);

        // 获取门店map
        Map<Long, ShopODTO> shopMap = salesSummaryReportService.getShopMap();

        List<Long> commodityIdList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, CommodityODTO> commodityMap = commodityService.getCommodityMapByIdList(commodityIdList);

        Map<Long,BigDecimal> priceMap = commodityPriceClient.getCommoditySupplyPrice();
        Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);

        for(ActualReceiptAnalysisODto odto : pageList){
            odto.setTotalQuantity(odto.getTotalQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setTotalRealReceiveQuantity(odto.getTotalRealReceiveQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setTotalRealDeliveryQuantity(odto.getTotalRealDeliveryQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));

            ShopODTO shopODTO = shopMap.get(odto.getShopId());
            if(shopODTO != null){
                odto.setShopName(shopODTO.getShopName());
            }

            CommodityODTO commodityODTO = commodityMap.get(odto.getCommodityId());
            if(commodityODTO != null){
                String barCodes = commodityODTO.getBarCodes();
                BeanUtils.copyProperties(commodityODTO,odto);
                odto.setBarCodes(barCodes);
                odto.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
                odto.setCateName(commodityODTO.getCommodityFirstName());
            }

            if(null != priceMap){
                odto.setSupplyPrice(priceMap.get(odto.getCommodityId()));
            }

            BigDecimal supplyPrice = odto.getSupplyPrice();
            BigDecimal totalRealReceiveQuantity = odto.getTotalRealReceiveQuantity();
            if(null != supplyPrice) {
                odto.setTotalSupplyPrice(supplyPrice.multiply(totalRealReceiveQuantity).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            BigDecimal totalRealDeliveryQuantity = odto.getTotalRealDeliveryQuantity();
            odto.setQuantityDifference(totalRealDeliveryQuantity.subtract(totalRealReceiveQuantity));

            CommoditySupplierODto commoditySupplierODto = commodityIdAndSupplierODTOMap.get(odto.getCommodityId());
            if(commoditySupplierODto != null){
                odto.setSupplierName(commoditySupplierODto.getSupplierName());
                odto.setRealName(commoditySupplierODto.getRealName());
            }
        }
        info.setList(pageList);
    }


    /**
     * 门店订货汇总表(当日)
     * @param idto
     * @return
     */
    public TablePageInfo<ShopOrderGoodReportODto> shopOrderGoodReportCurrentDay(ShopOrderGoodReportIDto idto) {
        idto.check();
        PageInfo<ShopOrderGoodReportODto> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            shortDeliveryReportMapper.shopOrderGoodReport(idto);
        });

        List<ShopOrderGoodReportODto> list = pageDate.getList();
        if(null !=list && !list.isEmpty()){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);
            List<Long> shopIdList = list.stream().map(item -> item.getShopId()).collect(Collectors.toList());
            Map<Long, OrgAndParentInfoODTO> orgMap = getOrgMap(shopIdList);

            for(ShopOrderGoodReportODto entry : list){
                String[] barCode = barCodeMap.get(Long.valueOf(entry.getCommodityId())).split(",");
                entry.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                entry.setBarCodeList(barCodeList);
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

                OrgAndParentInfoODTO orgODTO = orgMap.get(entry.getShopId());
                if(orgODTO != null){
                    entry.setOrgName(orgODTO.getParentOrgName());
                }
            }
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        return tablePageInfo;
    }

    /**
     * 门店订货汇总表(ES)
     * @param idto
     * @return
     */
    public TablePageInfo<ShopOrderGoodReportODto> shopOrderGoodReportES(ShopOrderGoodReportIDto idto) {
        idto.check();
        TablePageInfo<ShopOrderGoodReportODto> pageDate = new TablePageInfo<>();

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        if(idto.getShopType() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopType", idto.getShopType()));
        }
        if(idto.getShopId() != null && idto.getShopId() != 0){
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", idto.getShopId()));
        }
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("orderTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));

        if(idto.getCate1() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstKindId", idto.getCate1()));
        }
        if(idto.getCate2() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commoditySecondKindId", idto.getCate2()));
        }
        if(idto.getCate3() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityThirdKindId", idto.getCate3()));
        }

        if(CollectionUtils.isNotEmpty(idto.getCommodityIdList())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("commodityId", idto.getCommodityIdList()));
        }
        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return pageDate;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }

        if(StringUtils.isNotBlank(idto.getFactoryCode())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("factoryCode", idto.getFactoryCode()));
        }

        if(idto.getDiffer()){
            Script script = new Script("doc['orderNum'].value != doc['deliveryNum'].value ");
            ScriptQueryBuilder filterBuilder = QueryBuilders.scriptQuery(script);
            QueryBuilder query = QueryBuilders.boolQuery()
                    .should(filterBuilder);
            boolQueryBuilder.must(query);
        }

        queryBuilder.withQuery(boolQueryBuilder);

        // 设置聚合
        TermsAggregationBuilder shopIdAgg = AggregationBuilders.terms("group_shopId").field("shopId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder commodityAgg = AggregationBuilders.terms("group_commodity").field("commodityId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);

        SumAggregationBuilder sum_orderNum = AggregationBuilders.sum("sum_orderNum").field("orderNum");
        SumAggregationBuilder sum_deliveryNum = AggregationBuilders.sum("sum_deliveryNum").field("deliveryNum");
        SumAggregationBuilder sum_deliveryAmount =
                AggregationBuilders.sum("sum_orderAmount").script(new Script("params._source.deliveryNum * params._source.price "));


        // 设置聚合属性
        queryBuilder.addAggregation(shopIdAgg.subAggregation(commodityAgg.subAggregation(sum_orderNum)
                .subAggregation(sum_deliveryNum).subAggregation(sum_deliveryAmount)));

        queryBuilder.withTrackTotalHits(true);
        SearchHits<ESShortDeliveryReport> search = elasticsearchTemplate.search(queryBuilder.build(),ESShortDeliveryReport.class);
        if(CollectionUtils.isEmpty(search.getSearchHits())){
            return new TablePageInfo<>();
        }

        List<ShopOrderGoodReportODto> shopOrderGoodlist = new ArrayList<>();
        // 5、解析聚合结果
        Map<String, Aggregation> aggMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
        ParsedLongTerms shopIdTerms = (ParsedLongTerms)aggMap.get("group_shopId");
        for(Terms.Bucket shopIdBucket : shopIdTerms.getBuckets()){
            Long shopId = (Long)shopIdBucket.getKey();

            Map<String, Aggregation> commodityMap = shopIdBucket.getAggregations().getAsMap();
            ParsedLongTerms commodityIdTerms = (ParsedLongTerms)commodityMap.get("group_commodity");
            for(Terms.Bucket commodityBucket:commodityIdTerms.getBuckets()){
                Long commodityId= (Long)commodityBucket.getKey();

                double orderNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")) != null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")).getValue() : 0;
                double receiveNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")).getValue() : 0;
                double deliveryAmount = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderAmount")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderAmount")).getValue() : 0;

                ShopOrderGoodReportODto oDto = new ShopOrderGoodReportODto();
                oDto.setShopId(shopId);
                oDto.setCommodityId(commodityId);
                oDto.setOrderNum(new BigDecimal(orderNum));
                oDto.setDeliveryNum(new BigDecimal(receiveNum));
                oDto.setRealDeliveryAmount(new BigDecimal(deliveryAmount));
                shopOrderGoodlist.add(oDto);
            }
        }
        // 设置分页、合计
        if(CollectionUtils.isNotEmpty(shopOrderGoodlist)){
            setShopOrderGoodReturnPage(idto,pageDate,shopOrderGoodlist);
        }
        return pageDate;
    }


    // 门店订货汇总表(ES) 返回值
    public void setShopOrderGoodReturnPage(ShopOrderGoodReportIDto idto,TablePageInfo<ShopOrderGoodReportODto> info,List<ShopOrderGoodReportODto> shopOrderGoodlist){
        //分页
        Map<String,Integer> getPageMap = PageUtil.getPageMap(shopOrderGoodlist.size(),idto.getPageNo(),idto.getPageSize(),info);
        int startPoint = getPageMap.get("startPoint");
        int endPoint = getPageMap.get("endPoint");
        List<ShopOrderGoodReportODto> pageList = shopOrderGoodlist.subList(startPoint, endPoint);

        // 获取门店map
        Map<Long, ShopODTO> shopMap = salesSummaryReportService.getShopMap();
        List<Long> shopIdList = pageList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        List<Long> commodityIdList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, CommodityODTO> commodityMap = commodityService.getCommodityMapByIdList(commodityIdList);
        Map<Long, OrgAndParentInfoODTO> orgMap = getOrgMap(shopIdList);

        for(ShopOrderGoodReportODto odto : pageList){
            odto.setOrderNum(odto.getOrderNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setDeliveryNum(odto.getDeliveryNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setRealDeliveryAmount(odto.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setDifferNum(odto.getOrderNum().subtract(odto.getDeliveryNum()));

            ShopODTO shopODTO = shopMap.get(odto.getShopId());
            if(shopODTO != null){
                BeanUtils.copyProperties(shopODTO,odto);
            }

            OrgAndParentInfoODTO orgODTO = orgMap.get(odto.getShopId());
            if(orgODTO != null){
                odto.setOrgName(orgODTO.getParentOrgName());
            }

            CommodityODTO commodityODTO = commodityMap.get(odto.getCommodityId());
            if(commodityODTO != null){
                String barCodes = commodityODTO.getBarCodes();
                BeanUtils.copyProperties(commodityODTO,odto);
                if(StringUtils.isNotBlank(barCodes)){
                    String[] barCode = barCodes.split(",");
                    odto.setBarCode(barCode[0]);
                    List barCodeList = java.util.Arrays.asList(barCode);
                    odto.setBarCodeList(barCodeList);
                }
            }

        }
        info.setList(pageList);
    }

    // 根据门店idList 获取上级组织
    public Map<Long, OrgAndParentInfoODTO> getOrgMap(List<Long> shopIdList){
        Map<Long, OrgAndParentInfoODTO> orgMap = new HashMap<>();
        SelectShopOrgInfoListIDTO selectShopOrgInfoListIDTO = new SelectShopOrgInfoListIDTO();
        selectShopOrgInfoListIDTO.setShopIdList(shopIdList);
        List<OrgAndParentInfoODTO> orgList = orgClient.selectShopOrgInfoList(selectShopOrgInfoListIDTO);
        if(CollectionUtils.isNotEmpty(orgList)){
            orgMap = orgList.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId, Function.identity()));
        }
        return orgMap;
    }


    /**
     * 商品实发汇总表(当日)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportIDto vo) {
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");

        PageInfo<RealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            shortDeliveryReportMapper.realDeliveryReportCurrentDay(vo);
        });

        List<RealDeliveryReportODto> list = pageDate.getList();
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(null !=list && !list.isEmpty()){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);

            for(RealDeliveryReportODto entry:list){
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

            }
            //获取实发总金额
            realTotalAmount = shortDeliveryReportMapper.realTotalDeliveryReportCurrentDay(vo);
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount);
        return tablePageInfo;
    }

    /**
     * 商品实发汇总表(ES)
     * @param idto
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportES(RealDeliveryReportIDto idto) {
        QYAssert.isTrue(!StringUtil.isBlank(idto.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(idto.getEndDate()), "请选择送货日期");
        TablePageInfo<RealDeliveryReportODto> pageDate = new TablePageInfo<>();

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
        if(StringUtils.isNotBlank(idto.getCommodityKey())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("commodityCode", String.format("*%s*", idto.getCommodityKey() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("commodityName", String.format("*%s*", idto.getCommodityKey() , "*%s*"))));
        }
        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return pageDate;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }
        if(idto.getConsignmentId() != null){
            if(idto.getConsignmentId() > 0){
                boolQueryBuilder.filter(QueryBuilders.termQuery("consignmentId", idto.getConsignmentId()));
            }else {
                List<ConsignmentODTO> consignmentList = consignmentClient.selectConsignmentList(new ConsignmentIDTO());
                if(CollectionUtils.isNotEmpty(consignmentList)){
                    List<Long> consignmentIdList = consignmentList.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
                    boolQueryBuilder.mustNot(QueryBuilders.termsQuery("consignmentId", consignmentIdList));
                }
            }
        }
        if(idto.getCategoryId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstKindId", idto.getCategoryId()));
        }
        if(StringUtils.isNotBlank(idto.getFactoryCode())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("factoryCode", idto.getFactoryCode()));
        }
        if(StringUtils.isNotBlank(idto.getWorkshopCodeOrName())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("workshopCode", String.format("*%s*", idto.getWorkshopCodeOrName() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("workshopName", String.format("*%s*", idto.getWorkshopCodeOrName() , "*%s*"))));
        }
        if(StringUtils.isNotBlank(idto.getFlowshopCodeOrName())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("flowshopName", String.format("*%s*", idto.getFlowshopCodeOrName())));
        }
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("orderTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));
        if(idto.getDiffer()){
            Script script = new Script("doc['deliveryNum'].value == null");
            Script script1 = new Script("doc['orderNum'].value != doc['deliveryNum'].value ");
            ScriptQueryBuilder filterBuilder = QueryBuilders.scriptQuery(script);
            ScriptQueryBuilder filterBuilder1 = QueryBuilders.scriptQuery(script1);
            QueryBuilder query = QueryBuilders.boolQuery()
                    .should(filterBuilder)
                    .should(filterBuilder1);
            boolQueryBuilder.must(query);
        }
        queryBuilder.withQuery(boolQueryBuilder);

        // 设置聚合
        TermsAggregationBuilder commodityAgg = AggregationBuilders.terms("group_commodity").field("commodityId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);

        SumAggregationBuilder sum_orderNum = AggregationBuilders.sum("sum_orderNum").field("orderNum");
        SumAggregationBuilder sum_deliveryNum = AggregationBuilders.sum("sum_deliveryNum").field("deliveryNum");
        SumAggregationBuilder sum_deliveryAmount =
                AggregationBuilders.sum("sum_orderAmount").script(new Script("params._source.deliveryNum * params._source.price "));


        // 设置聚合属性
        queryBuilder.addAggregation(commodityAgg.subAggregation(sum_orderNum)
                .subAggregation(sum_deliveryNum).subAggregation(sum_deliveryAmount));
        queryBuilder.withTrackTotalHits(true);
        SearchHits<ESShortDeliveryReport> search = elasticsearchTemplate.search(queryBuilder.build(),ESShortDeliveryReport.class);
        if(CollectionUtils.isEmpty(search.getSearchHits())){
            return new TablePageInfo<>();
        }

        List<RealDeliveryReportODto> realDeliverylist = new ArrayList<>();
        // 5、解析聚合结果
        Map<String, Aggregation> commodityMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
        ParsedLongTerms commodityIdTerms = (ParsedLongTerms)commodityMap.get("group_commodity");
        for(Terms.Bucket commodityBucket:commodityIdTerms.getBuckets()){
            Long commodityId = (Long)commodityBucket.getKey();

            double orderNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")) != null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")).getValue() : 0;
            double receiveNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")).getValue() : 0;
            double deliveryAmount = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderAmount")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderAmount")).getValue() : 0;

            RealDeliveryReportODto oDto = new RealDeliveryReportODto();
            oDto.setCommodityId(commodityId);
            oDto.setOrderNum(new BigDecimal(orderNum));
            oDto.setDeliveryNum(new BigDecimal(receiveNum));
            oDto.setRealDeliveryAmount(new BigDecimal(deliveryAmount));
            realDeliverylist.add(oDto);
        }

        // 设置分页、合计
        if(CollectionUtils.isNotEmpty(realDeliverylist)){
            setRealDeliveryReportReturnPage(idto,pageDate,realDeliverylist);
        }
        return pageDate;
    }

    // 商品实发汇总表(ES) 返回值
    public void setRealDeliveryReportReturnPage(RealDeliveryReportIDto idto,TablePageInfo<RealDeliveryReportODto> info,List<RealDeliveryReportODto> realDeliverylist){
        BigDecimal totalDeliveryAmount = new BigDecimal(realDeliverylist.stream().mapToDouble(item -> item.getRealDeliveryAmount().doubleValue()).sum());
        //分页
        Map<String,Integer> getPageMap = PageUtil.getPageMap(realDeliverylist.size(),idto.getPageNo(),idto.getPageSize(),info);
        int startPoint = getPageMap.get("startPoint");
        int endPoint = getPageMap.get("endPoint");
        List<RealDeliveryReportODto> pageList = realDeliverylist.subList(startPoint, endPoint);

        List<Long> commodityIdList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, CommodityODTO> commodityMap = commodityService.getCommodityMapByIdList(commodityIdList);

        for(RealDeliveryReportODto odto : pageList){
            odto.setOrderNum(odto.getOrderNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setDeliveryNum(odto.getDeliveryNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setRealDeliveryAmount(odto.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setDifferNum(odto.getOrderNum().subtract(odto.getDeliveryNum()));

            CommodityODTO commodityODTO = commodityMap.get(odto.getCommodityId());
            if(commodityODTO != null){
                String barCodes = commodityODTO.getBarCodes();
                BeanUtils.copyProperties(commodityODTO,odto);
                if(StringUtils.isNotBlank(barCodes)){
                    String[] barCode = barCodes.split(",");
                    odto.setBarCode(barCode[0]);
                    odto.setBarCodes(barCodes);
                }
            }
        }
        info.setHeader(totalDeliveryAmount.setScale(2,BigDecimal.ROUND_HALF_UP));
        info.setList(pageList);
    }



    /**
     * 商品实发汇总表(当日)---------------(按客户类型)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo) {
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");

        if(vo.getWarehouseId() != null){
            List<Long> commodityIdList = commodityWarehouseClient.queryCommodityIdsByDefaultWarehouseid(vo.getWarehouseId());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                vo.setCommodityIdList2(commodityIdList);
            }else {
                TablePageInfo tablePageInfo = new TablePageInfo();
                tablePageInfo.setList(new ArrayList());
                return tablePageInfo;
            }
        }

        PageInfo<RealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            shortDeliveryReportMapper.realDeliveryStoreTypeReportCurrentDay(vo);
        });
        List<RealDeliveryReportODto> list = pageDate.getList();
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(null !=list && !list.isEmpty()){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
            Map<Long, CommodityWarehouseODto> wareHouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);

            for(RealDeliveryReportODto entry:list){
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

                CommodityWarehouseODto warehouseODto = wareHouseMap.get(entry.getCommodityId());
                if(warehouseODto != null){
                    entry.setWarehouseName(warehouseODto.getWarehouseName());
                }
            }
            //获取实发总金额
            realTotalAmount = shortDeliveryReportMapper.realTotalDeliveryStoreTypeReportCurrentDay(vo);
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount);
        return tablePageInfo;
    }

    /**
     * 商品实发汇总表(ES)---------------(按客户类型)
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportES(RealDeliveryReportIDto idto) {
        TablePageInfo<RealDeliveryReportODto> pageDate = new TablePageInfo<>();

        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
        if(StringUtils.isNotBlank(idto.getCommodityKey())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("commodityCode", String.format("*%s*", idto.getCommodityKey() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("commodityName", String.format("*%s*", idto.getCommodityKey() , "*%s*"))));
        }
        if(StringUtils.isNotBlank(idto.getFactoryCode())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("factoryCode", idto.getFactoryCode()));
        }
        if(idto.getWarehouseId() != null){
            List<Long> commodityIdList = commodityWarehouseClient.queryCommodityIdsByDefaultWarehouseid(idto.getWarehouseId());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                boolQueryBuilder.filter(QueryBuilders.termsQuery("commodityId", commodityIdList));
            }else {
                return pageDate;
            }
        }
        if(StringUtils.isNotBlank(idto.getBarCode())){
            CommodityODTO commodityODTO = commodityMapper.findCommodityByBarcode(idto.getBarCode());
            if(commodityODTO == null){
                return pageDate;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityId", commodityODTO.getCommodityId()));
        }
        if(StringUtils.isNotBlank(idto.getWorkshopCodeOrName())){
            boolQueryBuilder.must(fuzzyQueryBuilder.should(QueryBuilders.wildcardQuery("workshopCode", String.format("*%s*", idto.getWorkshopCodeOrName() ,"*%s*")))
                    .should(QueryBuilders.wildcardQuery("workshopName", String.format("*%s*", idto.getWorkshopCodeOrName() , "*%s*"))));
        }
        if(idto.getCategoryId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("commodityFirstKindId", idto.getCategoryId()));
        }
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("orderTime").gte(idto.getBeginDate()).lte(idto.getEndDate()));
        if(StringUtils.isNotBlank(idto.getFlowshopCodeOrName())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("flowshopName", String.format("*%s*", idto.getFlowshopCodeOrName())));
        }
        if(idto.getStoreTypeId() != null){
            boolQueryBuilder.filter(QueryBuilders.termQuery("storeTypeId", idto.getStoreTypeId()));
        }
        if(idto.getConsignmentId() != null){
            if(idto.getConsignmentId() > 0){
                boolQueryBuilder.filter(QueryBuilders.termQuery("consignmentId", idto.getConsignmentId()));
            }else {
                List<ConsignmentODTO> consignmentList = consignmentClient.selectConsignmentList(new ConsignmentIDTO());
                if(CollectionUtils.isNotEmpty(consignmentList)){
                    List<Long> consignmentIdList = consignmentList.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
                    boolQueryBuilder.mustNot(QueryBuilders.termsQuery("consignmentId", consignmentIdList));
                }
            }
        }
        queryBuilder.withQuery(boolQueryBuilder);

        // 设置聚合
        TermsAggregationBuilder storeTypeIdAgg = AggregationBuilders.terms("group_storeTypeId").field("storeTypeId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);
        TermsAggregationBuilder commodityAgg = AggregationBuilders.terms("group_commodity").field("commodityId").order(BucketOrder.key(true)).size(Integer.MAX_VALUE);

        SumAggregationBuilder sum_orderNum = AggregationBuilders.sum("sum_orderNum").field("orderNum");
        SumAggregationBuilder sum_deliveryNum = AggregationBuilders.sum("sum_deliveryNum").field("deliveryNum");
        SumAggregationBuilder sum_deliveryAmount =
                AggregationBuilders.sum("sum_orderAmount").script(new Script("params._source.deliveryNum * params._source.price "));


        // 设置聚合属性
        queryBuilder.addAggregation(storeTypeIdAgg.subAggregation(commodityAgg.subAggregation(sum_orderNum)
                .subAggregation(sum_deliveryNum).subAggregation(sum_deliveryAmount)));
        queryBuilder.withTrackTotalHits(true);
        SearchHits<ESShortDeliveryReport> search = elasticsearchTemplate.search(queryBuilder.build(),ESShortDeliveryReport.class);
        if(CollectionUtils.isEmpty(search.getSearchHits())){
            return new TablePageInfo<>();
        }

        List<RealDeliveryReportODto> realDeliverylist = new ArrayList<>();
        // 5、解析聚合结果
        Map<String, Aggregation> aggMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
        ParsedLongTerms storeTypeIdTerms = (ParsedLongTerms)aggMap.get("group_storeTypeId");
        for(Terms.Bucket storeTypeIdTermsBucket : storeTypeIdTerms.getBuckets()){
            Long storeTypeId = (Long)storeTypeIdTermsBucket.getKey();

            Map<String, Aggregation> commodityMap = storeTypeIdTermsBucket.getAggregations().getAsMap();
            ParsedLongTerms commodityIdTerms = (ParsedLongTerms)commodityMap.get("group_commodity");
            for(Terms.Bucket commodityBucket:commodityIdTerms.getBuckets()){
                Long commodityId = (Long)commodityBucket.getKey();

                double orderNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")) != null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderNum")).getValue() : 0;
                double receiveNum = ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_deliveryNum")).getValue() : 0;
                double deliveryAmount = ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderAmount")) !=null ? ((Sum)commodityBucket.getAggregations().asMap().get("sum_orderAmount")).getValue() : 0;

                RealDeliveryReportODto oDto = new RealDeliveryReportODto();
                oDto.setStoreTypeId(storeTypeId);
                oDto.setCommodityId(commodityId);
                oDto.setOrderNum(new BigDecimal(orderNum));
                oDto.setDeliveryNum(new BigDecimal(receiveNum));
                oDto.setRealDeliveryAmount(new BigDecimal(deliveryAmount));
                realDeliverylist.add(oDto);
            }
        }

        // 设置分页、合计
        if(CollectionUtils.isNotEmpty(realDeliverylist)){
            setRealDeliveryStoreTypeReturnPage(idto,pageDate,realDeliverylist);
        }
        return pageDate;
    }

    // 商品实发汇总表(ES)---------------(按客户类型) 返回值
    public void setRealDeliveryStoreTypeReturnPage(RealDeliveryReportIDto idto,TablePageInfo<RealDeliveryReportODto> info,List<RealDeliveryReportODto> realDeliverylist){
        BigDecimal totalDeliveryAmount = new BigDecimal(realDeliverylist.stream().mapToDouble(item -> item.getRealDeliveryAmount().doubleValue()).sum());
        //分页
        Map<String,Integer> getPageMap = PageUtil.getPageMap(realDeliverylist.size(),idto.getPageNo(),idto.getPageSize(),info);
        int startPoint = getPageMap.get("startPoint");
        int endPoint = getPageMap.get("endPoint");
        List<RealDeliveryReportODto> pageList = realDeliverylist.subList(startPoint, endPoint);

        List<Long> commodityIdList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<Long> storeTypeIdList = pageList.stream().map(item -> item.getStoreTypeId()).collect(Collectors.toList());
        Map<Long, CommodityODTO> commodityMap = commodityService.getCommodityMapByIdList(commodityIdList);
        Map<Long, CommodityWarehouseODto> wareHouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
        Map<String,String> storeTypeMap = getStoreTypeMap(storeTypeIdList);

        for(RealDeliveryReportODto odto : pageList){
            odto.setOrderNum(odto.getOrderNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setDeliveryNum(odto.getDeliveryNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            odto.setRealDeliveryAmount(odto.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setDifferNum(odto.getOrderNum().subtract(odto.getDeliveryNum()));

            CommodityODTO commodityODTO = commodityMap.get(odto.getCommodityId());
            if(commodityODTO != null){
                String barCodes = commodityODTO.getBarCodes();
                BeanUtils.copyProperties(commodityODTO,odto);
                if(StringUtils.isNotBlank(barCodes)){
                    String[] barCode = barCodes.split(",");
                    odto.setBarCode(barCode[0]);
                    odto.setBarCodes(barCodes);
                }
            }

            CommodityWarehouseODto warehouseODto = wareHouseMap.get(odto.getCommodityId());
            if(warehouseODto != null){
                odto.setWarehouseName(warehouseODto.getWarehouseName());
            }
            odto.setStoreTypeName(storeTypeMap.get(odto.getStoreTypeId() + ""));
        }
        info.setHeader(totalDeliveryAmount.setScale(2,BigDecimal.ROUND_HALF_UP));
        info.setList(pageList);
    }

    // 获取客户类型map
    public Map<String,String> getStoreTypeMap(List<Long> storeTypeIdList){
        Map<String,String> storeTypeMap = new HashMap<>();
        List<DictionaryODTO> dicList = dictionaryClient.listDictionaryByOptionName("QM001");
        if(CollectionUtils.isNotEmpty(dicList)){
            storeTypeMap = dicList.stream().collect(Collectors.toMap(DictionaryODTO::getId,DictionaryODTO::getOptionName,(key1 , key2)-> key2));
        }
        return storeTypeMap;
    }
}
