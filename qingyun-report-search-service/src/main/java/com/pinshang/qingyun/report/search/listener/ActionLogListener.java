
package com.pinshang.qingyun.report.search.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.log.ActionLog;
import com.pinshang.qingyun.report.search.service.LogActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * @ClassName ReportSearchListen
 * <AUTHOR>
 * @Date 2021/4/25 10:54
 * @Description ReportSearchListen
 * @Version 1.0
 */

@Component
@Slf4j
@OnlineSwitchWatcher
public class ActionLogListener {
    @Autowired
    private LogActionService logActionService;

/**
     * 报表查询操作监听
     * @param message
     */

    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.QINGYUN_ACTION_LOG_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.QINGYUN_ACTION_LOG_TOPIC})
    public void insertEsActionLog(String message) {
        log.info("topic:{}, message:{}", KafkaTopicConstant.QINGYUN_ACTION_LOG_TOPIC, message);
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        ActionLog actionLog = JsonUtil.json2java(messageWrapper.getData().toString(), ActionLog.class);
        if(null != actionLog) {
            logActionService.saveActionLog(actionLog);
        }
    }
}

