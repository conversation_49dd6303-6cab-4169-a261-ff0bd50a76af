package com.pinshang.qingyun.report.search.enums;

/**
 * <AUTHOR> @Date
 */
public enum PosReportSummaryTypeEnum {
    ONE(1, "商品汇总（区分门店）"),
    TWO(2, "商品汇总（不区分门店）"),
    THREE(3, "大类汇总"),
    FOUR(4, "中类汇总"),
    FIVE(5, "小类汇总"),
    SIX(6, "门店汇总"),
    SEVEN(7, "日毛利率"),
    EIGHT(8, "按销量"),
    NINE(9, "按售额"),
    TEN(10, "按毛利"),
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;

    PosReportSummaryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
