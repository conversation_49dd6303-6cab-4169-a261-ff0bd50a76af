package com.pinshang.qingyun.report.search.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品
 */
@Data
public class CommodityODTO {

    private Long commodityId;

    /*** 商品编码 */
    private String commodityCode;

    /*** 商品名称 */
    private String commodityName;

    /*** 商品规格 */
    private String commoditySpec;

    /*** 商品计量单位 */
    private String commodityUnit;


    /*** 主条形码 */
    private String barCode;

    private String barCodes;



    private Long commodityFirstId;
    private String commodityFirstName;
    private Long commoditySecondId;
    private String commoditySecondName;
    private Long commodityThirdId;
    private String commodityThirdName;


    //包装规格
    private BigDecimal commodityPackageSpec;

    //是否称重0-不称量,1-称重
    private Integer isWeight;

    // 商品工厂
    private String factoryCode;
    private String factoryName;

    // 商品生产组
    private String workshopCode;
    private String workshopName;

    // 商品车间
    private String flowshopCode;
    private String flowshopName;
    // 税率
    private BigDecimal taxRate;
}
