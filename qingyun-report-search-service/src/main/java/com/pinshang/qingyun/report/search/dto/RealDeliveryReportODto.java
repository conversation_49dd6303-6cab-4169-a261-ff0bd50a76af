package com.pinshang.qingyun.report.search.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RealDeliveryReportODto {
	@ExcelIgnore
	private Long storeTypeId;
	@ExcelIgnore
	private Long commodityId;
	@ExcelProperty("商品编码")
	private String commodityCode;
	@ExcelProperty("商品名称")
	private String commodityName;
	@ExcelProperty("规格")
	private String commoditySpec;
	@ExcelProperty("条码")
	private String barCode;
	@ExcelIgnore
	private String barCodes;	// 子码列表
	@ExcelProperty("一级品类")
	private String commodityFirstName;
	@ExcelProperty("计量单位")
	private String commodityUnit;
	@ExcelIgnore
	private BigDecimal price;
    @ExcelProperty("订货数量")
	private BigDecimal orderNum;
	@ExcelProperty("实发数量")
	private BigDecimal deliveryNum;
	@ExcelProperty("实发金额")
	private BigDecimal realDeliveryAmount;
	@ExcelProperty("差异数量")
	private BigDecimal differNum;
	@ExcelProperty("工厂")
	private String factoryName;
	@ExcelProperty("生产组")
	private String workshopName;
	@ExcelProperty("车间")
	private String flowshopName;
	@ExcelIgnore
	private String warehouseName;
	@ExcelIgnore
	private String storeTypeName;
	// 税率
	@ExcelProperty("税率")
	private BigDecimal taxRate;
}
