package com.pinshang.qingyun.report.search.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;

import java.util.List;

@Data
public class ShopOrderGoodReportIDto extends Pagination{

	@ApiModelProperty("门店类型")
	private Integer shopType;

	@ApiModelProperty("门店id")
	private Long shopId;

	@ApiModelProperty("送货日期开始 yyyy-MM-dd")
	private String beginDate;

	@ApiModelProperty("送货日期结束 yyyy-MM-dd")
	private String endDate;

	@ApiModelProperty("大类id")
	private Long cate1;

	@ApiModelProperty("中类id")
	private Long cate2;

	@ApiModelProperty("小类id")
	private Long cate3;

	@ApiModelProperty("商品idlist")
	private List<String> commodityIdList;

	@ApiModelProperty("条形码")
	private String barCode;

	@ApiModelProperty("工厂code")
	private String factoryCode;

	@ApiModelProperty("true:查询差异数据")
	private Boolean differ = false;

	public void check(){
		QYAssert.isTrue(!StringUtil.isBlank(beginDate), "请选择送货日期");
		QYAssert.isTrue(!StringUtil.isBlank(endDate), "请选择送货日期");
		QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityIdList), "商品不能为空");

		int diff = DateUtil.getDayDif(DateUtil.parseDate(endDate, DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(beginDate, DateUtil.DEFAULT_DATE_FORMAT));
		QYAssert.isTrue(diff <= 9, "送货日期的跨度不能超过10天");
	}
}
