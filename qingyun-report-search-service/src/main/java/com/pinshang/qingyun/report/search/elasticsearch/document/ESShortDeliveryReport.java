package com.pinshang.qingyun.report.search.elasticsearch.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @Author: sk
 * @Date: 2021/1/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "short_delivery_report_basic")
public class ESShortDeliveryReport {

    @Id
    private Long id;

    /** 门店ID*/
    @Field(type = FieldType.Long)
    private Long shopId;

    /** 门店code*/
    @Field(type = FieldType.Keyword)
    private String shopCode;

    /** 门店名称*/
    @Field(type = FieldType.Keyword)
    private String shopName;

    @Field(type = FieldType.Long)
    private Long shopType;

    @Field(type = FieldType.Date)
    private String orderTime;

    @Field(type = FieldType.Keyword)
    private String orderCode;

    @Field(type = FieldType.Long)
    private Long commodityFirstKindId;

    @Field(type = FieldType.Keyword)
    private String commodityFirstKindName;

    @Field(type = FieldType.Long)
    private Long commoditySecondKindId;

    @Field(type = FieldType.Keyword)
    private String commoditySecondKindName;

    @Field(type = FieldType.Long)
    private Long commodityThirdKindId;

    @Field(type = FieldType.Keyword)
    private String commodityThirdKindName;

    @Field(type = FieldType.Long)
    private Long commodityId;

    @Field(type = FieldType.Keyword)
    private String commodityCode;

    @Field(type = FieldType.Keyword)
    private String commodityName;

    @Field(type = FieldType.Keyword)
    private String commoditySpec;

    @Field(type = FieldType.Keyword)
    private String commodityUnitName;

    @Field(type = FieldType.Double)
    private Double orderNum;

    @Field(type = FieldType.Double)
    private Double price;

    @Field(type = FieldType.Double)
    private Double deliveryNum;

    @Field(type = FieldType.Double)
    private Double receiveNum;

    @Field(type = FieldType.Double)
    private Double differNum;

    @Field(type = FieldType.Keyword)
    private String rate;

    @Field(type = FieldType.Keyword)
    private String factoryCode;

    @Field(type = FieldType.Keyword)
    private String factoryName;

    @Field(type = FieldType.Keyword)
    private String workshopCode;

    @Field(type = FieldType.Keyword)
    private String workshopName;

    @Field(type = FieldType.Keyword)
    private String storeCode;

    @Field(type = FieldType.Keyword)
    private String createName;

    @Field(type = FieldType.Keyword)
    private String storeLineGroupName;


    @Field(type = FieldType.Long)
    private Long storeTypeId;

    @Field(type = FieldType.Keyword)
    private String storeTypeName;

    @Field(type = FieldType.Long)
    private Long receiveStatus;

    @Field(type = FieldType.Keyword)
    private String flowshopName;

    @Field(type = FieldType.Long)
    private Long consignmentId; // 代销商户id
}
