package com.pinshang.qingyun.report.search.service;

import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.report.search.dto.CommodityODTO;
import com.pinshang.qingyun.report.search.mapper.CommodityMapper;
import com.pinshang.qingyun.report.search.model.Commodity;
import com.pinshang.qingyun.weixin.dto.WeiXinSendMessageIDTO;
import com.pinshang.qingyun.weixin.service.WeixinTemplateClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by chenqi on 2019/5/28 11:24.
 * 商品 service
 */
@Service
@Slf4j
public class CommodityService {

    @Autowired
    private CommodityMapper commodityMapper;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private WeixinTemplateClient weixinTemplateClient;

    private static String REPORT_BARCODE = "REPORTSEARCH:BAR_CODE:";
    /**
     * 获取商品条码map
     * @return
     */
    public Map<Long,String> getCommodityBarCodeMap(List<Long> commodityIdList){
        /*List<Commodity> list = commodityMapper.findCommodityBarCodeByParam(null);
        Map<Long, String> map = list.stream().collect(
                Collectors.toMap(Commodity::getCommodityId, Commodity::getBarCode));
        return map;*/
        List<Long> idList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(commodityIdList)){
            // 去重，排序
            idList = commodityIdList.stream().distinct().collect(Collectors.toList());
            Collections.sort(idList);
        }

        RBucket<Map<Long, String>> bucket = redissonClient.getBucket(REPORT_BARCODE+idList.hashCode());
        Map<Long, String> cacheMap = bucket.get();
        if(null != cacheMap){
            Map<Long, String> map = cacheMap.entrySet().stream()
                    .collect(Collectors.toMap(e -> Long.valueOf(e.getKey()+""), e -> e.getValue()));
            return map;
        }

        List<Commodity> list= commodityMapper.findCommodityBarCodeByParam(idList);
        Map<Long, String> map = list.stream().collect(
                Collectors.toMap(Commodity::getCommodityId, Commodity::getBarCode));
        bucket.set(map, 2, TimeUnit.MINUTES);
        return map;
    }


    /**
     * 获取商品map
     * @param commodityIdList
     * @return
     */
    public Map<Long, CommodityODTO> getCommodityMapByIdList(List<Long> commodityIdList){
        Map<Long,CommodityODTO> commodityMap = new HashMap<>();
        List<CommodityODTO> commodityList = commodityMapper.findCommodityByCommodityIdList(commodityIdList,null);
        if(!org.springframework.util.CollectionUtils.isEmpty(commodityList)){
            commodityMap = commodityList.stream().collect(Collectors.toMap(CommodityODTO::getCommodityId, Function.identity()));
        }
        return commodityMap;
    }


    /**
     * 发送微信消息
     * @param content
     */
    @Async
    public void sendMessage(String content){
        WeiXinSendMessageIDTO idto = new WeiXinSendMessageIDTO();
        idto.setContent(content);
        idto.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
        weixinTemplateClient.sendMessageWarning(idto);
    }
}
