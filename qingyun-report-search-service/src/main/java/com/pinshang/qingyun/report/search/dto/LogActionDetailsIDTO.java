package com.pinshang.qingyun.report.search.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName LogActionListIDTO
 * <AUTHOR>
 * @Date 2021/5/24 13:57
 * @Description LogActionListIDTO
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LogActionDetailsIDTO extends LogActionSearchIDTO {

    /**
     * 操作动作
     */
    @ApiModelProperty("操作动作 1-导出, 2-查询")
    private String operaType;
}
