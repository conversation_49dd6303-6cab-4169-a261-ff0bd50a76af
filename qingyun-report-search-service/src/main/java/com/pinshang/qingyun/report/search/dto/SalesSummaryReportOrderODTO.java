package com.pinshang.qingyun.report.search.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SalesSummaryReportOrderODTO {

    @ApiModelProperty("组织code")
    private String orgName;

    @ApiModelProperty("订单编码")
    private Long orderCode;

    @ApiModelProperty("门店ID")
    private Long shopId;

    /** 门店code*/
    @ApiModelProperty("门店code")
    private String shopCode;

    /** 门店名称*/
    @ApiModelProperty("门店name")
    private String shopName;

    @ApiModelProperty("品类编码")
    private String cateCode;
    /** 一级大类ID*/
    @ApiModelProperty("一级大类ID")
    private Long commodityFirstId;
    @ApiModelProperty("一级大类name")
    private String commodityFirstName;

    /** 二级大类ID*/
    @ApiModelProperty("二级大类ID")
    private Long commoditySecondId;
    @ApiModelProperty("二级大类name")
    private String commoditySecondName;

    /** 三级大类ID*/
    @ApiModelProperty("三级大类ID")
    private Long commodityThirdId;
    @ApiModelProperty("三级大类name")
    private String commodityThirdName;

    /** 商品ID*/
    @ApiModelProperty("商品ID")
    private Long commodityId;

    /** 商品编码*/
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /** 商品名称*/
    @ApiModelProperty("商品名称")
    private String commodityName;

    /** 助记码*/
    @ApiModelProperty("助记码")
    private String commodityAid;

    /** 规格*/
    @ApiModelProperty("规格")
    private String commoditySpec;

    /** 单位*/
    @ApiModelProperty("单位")
    private String commodityUnit;

    @ApiModelProperty("零售价")
    private BigDecimal commodityPrice;

    @ApiModelProperty("售价(最终分摊价格)")
    private BigDecimal salePrice;

    /** 销售成本*/
    @ApiModelProperty("销售成本")
    private BigDecimal weightAmount;

    /** 销售数量*/
    @ApiModelProperty("销售数量")
    private BigDecimal saleQuantity;

    /** 销售金额*/
    @ApiModelProperty("销售金额")
    private BigDecimal saleAmount;

    /** 特价类型*/
    @ApiModelProperty("特价类型")
    private String promotionType;
    /** 税率*/
    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建ID")
    private Long createId;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("更人ID")
    private Long updateId;



    /** 退货数量*/
    @ApiModelProperty("退货数量")
    private BigDecimal returnQuantity;

    /** 赠送数量*/
    @ApiModelProperty("赠送数量")
    private BigDecimal giveQuantity;

    /** 退货金额*/
    @ApiModelProperty("退货金额")
    private BigDecimal returnAmount;

    /** 赠送金额*/
    @ApiModelProperty("赠送金额")
    private BigDecimal giveAmount;

    /** 让利金额*/
    @ApiModelProperty("让利金额")
    private BigDecimal discountAmount;

    /** 数量小计*/
    @ApiModelProperty("数量小计")
    private BigDecimal tatalQuantity;

    /** 金额小计*/
    @ApiModelProperty("金额小计")
    private BigDecimal tatalAmount;


    /** 不含税金额*/
    @ApiModelProperty("不含税金额")
    private BigDecimal noTaxRateAmount;

    /** 税额*/
    @ApiModelProperty("税额")
    private BigDecimal taxRateAmount;

    /** 销售日期*/
    @ApiModelProperty("销售日期")
    private String saleTime;

    @ApiModelProperty("毛利额")
    private BigDecimal grossprofitmarginAmount;//毛利额

    @ApiModelProperty("毛利率")
    private String grossprofitrate;//毛利率

    @ApiModelProperty("不含税销售额")
    private BigDecimal noTaxSaleAmount;//不含税销售额

    @ApiModelProperty("不含税销售成本")
    private BigDecimal noTaxWeightAmount;//不含税销售成本

    @ApiModelProperty("平均售价")
    private BigDecimal avgSalePrice;//平均售价

    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("子码列表")
    private String barCodeList;	// 子码列表
    @ApiModelProperty("销售额占比")
    private String salesAmountPercent;

    @ApiModelProperty("促销方案：编码，名称")
    private String promotionKey;

}
