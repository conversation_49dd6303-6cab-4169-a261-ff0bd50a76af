package com.pinshang.qingyun.report.search.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.log.ActionLog;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.search.dto.*;
import com.pinshang.qingyun.report.search.elasticsearch.document.EsActionLogV2;
import com.pinshang.qingyun.report.search.elasticsearch.repository.EsActionLogV2Repository;
import com.pinshang.qingyun.report.search.enums.ReportActionMenuTypeEnum;
import com.pinshang.qingyun.report.search.mapper.EmployeeUserMapper;
import com.pinshang.qingyun.report.search.mapper.ReportActionLogMapper;
import com.pinshang.qingyun.report.search.model.ReportActionLog;
import com.pinshang.qingyun.report.search.util.PageUtil;
import com.pinshang.qingyun.smm.dto.menu.Level3MenuInfoODTO;
import com.pinshang.qingyun.smm.dto.menu.SelectLevel3MenuInfoListIDTO;
import com.pinshang.qingyun.smm.dto.menu.SystemManagerODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserOrgInfoListIDTO;
import com.pinshang.qingyun.smm.dto.user.UserOrgInfoODTO;
import com.pinshang.qingyun.smm.service.MenuClient;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.smm.service.SystemManagerClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregator;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.clients.elasticsearch7.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName LogActionService
 * <AUTHOR>
 * @Date 2021/5/8 14:13
 * @Description LogActionService
 * @Version 1.0
 */
@Slf4j
@Service
public class LogActionService {
    public static final String MATCH_INFO_REDIS_KEY = "REPORTSEARCH:ACTIONLOG:MATCHINFO";
    @Autowired
    private ReportActionLogMapper reportActionLogMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private EsActionLogV2Repository esActionLogV2Repository;

    @Autowired
    private EmployeeUserMapper employeeUserMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private SystemManagerClient systemManagerClient;

    @Autowired
    private MenuClient menuClient;

    public TablePageInfo<LogActionDetailsODTO> searchLogsDetails(LogActionDetailsIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate()), "查询日期不能为空");
        TablePageInfo<LogActionDetailsODTO> pageDate = new TablePageInfo<>();
        // 查询访问日志
        FieldSortBuilder fieldSortBuilder = SortBuilders.fieldSort("startTime").order(SortOrder.DESC);
        NativeSearchQueryBuilder nativeSearchQuery = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if(StringUtils.isNotBlank(idto.getOperaType())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("operaType", idto.getOperaType()));
        }

        boolean queryFlag = handleLogActionSearchCondition(idto, boolQueryBuilder);
        if(!queryFlag){
            return new TablePageInfo<>();
        }
        nativeSearchQuery.withQuery(boolQueryBuilder);
        nativeSearchQuery.withSort(fieldSortBuilder);
        nativeSearchQuery.withPageable(PageRequest.of(idto.getPageNo()-1, idto.getPageSize()));
        nativeSearchQuery.withSourceFilter(new FetchSourceFilter(new String[]{"thirdLevelMenuId", "startTime"
                , "userCode", "userName", "operaType","systemId"}, null));
        nativeSearchQuery.withTrackTotalHits(true);
        SearchHits<EsActionLogV2> search = elasticsearchTemplate.search(nativeSearchQuery.build(),EsActionLogV2.class);
        List<SearchHit<EsActionLogV2>> searchHits = search.getSearchHits();
        if (CollectionUtils.isEmpty(searchHits)) {
            return pageDate;
        }
        // 获取员工所在部门
        List<String> codes = searchHits.stream().map(item -> item.getContent().getUserCode()).collect(Collectors.toList());
        List<Long> userIds = employeeUserMapper.getUserIdsByCode(codes);
        SelectUserOrgInfoListIDTO serOrgInfoIdto = new SelectUserOrgInfoListIDTO();
        serOrgInfoIdto.setUserIdList(userIds);
        List<UserOrgInfoODTO> orgInfoList = smmUserClient.selectUserOrgInfoList(serOrgInfoIdto);
        Map<String, String> orgMap = orgInfoList.stream().collect(Collectors.toMap(UserOrgInfoODTO::getEmployeeNumber, UserOrgInfoODTO::getOrgName));
        Map<Long, Level3MenuInfoODTO> allMenuMap = getAllMenuMap();
        // list添加所在部门+格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<LogActionDetailsODTO> list = searchHits.stream().map(item -> {
            EsActionLogV2 it = item.getContent();
            LogActionDetailsODTO dto = new LogActionDetailsODTO();

            Level3MenuInfoODTO level3MenuInfo = allMenuMap.get(it.getThirdLevelMenuId());
            if (null != level3MenuInfo){
                dto.setSystemName(level3MenuInfo.getSystemName());
                dto.setFirstLevelMenu(level3MenuInfo.getLevel1MenuName());
                dto.setSecondLevelMenu(level3MenuInfo.getLevel2MenuName());
                dto.setMenuName(level3MenuInfo.getLevel3MenuName());
            }

            dto.setOperaType(it.getOperaType());
            dto.setStartTime(sdf.format(it.getStartTime()));
            dto.setUserCode(it.getUserCode());
            dto.setUserName(it.getUserName());
            dto.setUserDept(orgMap.get(dto.getUserCode()));
            return dto;
        }).collect(Collectors.toList());
        pageDate.setList(list);
        // 设置返回分页信息
        PageUtil.getPageMap(Integer.valueOf(search.getTotalHits()+ ""), idto.getPageNo(), idto.getPageSize(), pageDate);
        return pageDate;
    }

    public PageInfo<LogActionListODTO> searchLogsList(LogActionListIDTO idto){
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate()), "查询日期不能为空");
        TablePageInfo<LogActionListODTO> pageDate = new TablePageInfo<>();
        // 查询访问日志
        NativeSearchQueryBuilder nativeSearchQuery = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolean queryFlag = handleLogActionSearchCondition(idto, boolQueryBuilder);
        if(!queryFlag){
            return new TablePageInfo<>();
        }
        nativeSearchQuery.withQuery(boolQueryBuilder);
        nativeSearchQuery.withPageable(PageRequest.of(idto.getPageNo()-1, idto.getPageSize()));
        // 开始构造聚合条件
        TermsAggregationBuilder thirdMenuIdCountAggre = AggregationBuilders.terms("thirdMenuIdAggre").field("thirdLevelMenuId").collectMode(Aggregator.SubAggCollectionMode.BREADTH_FIRST).size(Integer.MAX_VALUE);
        nativeSearchQuery.addAggregation(thirdMenuIdCountAggre);
        SearchHits<EsActionLogV2> search = elasticsearchTemplate.search(nativeSearchQuery.build(),EsActionLogV2.class);
        if(CollectionUtils.isEmpty(search.getSearchHits())){
            return new TablePageInfo<>();
        }
        List<LogActionListODTO> resultList = new ArrayList<>();

        Map<Long, Level3MenuInfoODTO> allMenuMap = getAllMenuMap();

        // 组装聚合结果
        Map<String, Aggregation> thirdLevelMap = ((ElasticsearchAggregations)search.getAggregations()).aggregations().asMap();
        ParsedLongTerms thirdLevelAggreTerm = (ParsedLongTerms) thirdLevelMap.get("thirdMenuIdAggre");
        for (Terms.Bucket thirdLevelTermBucket : thirdLevelAggreTerm.getBuckets()) {
            String thirdLevelTermKey = thirdLevelTermBucket.getKeyAsString();
            long count = thirdLevelTermBucket.getDocCount();
            LogActionListODTO itemODTO = new LogActionListODTO();
            itemODTO.setActionCount(count);
            Long thirdMenuId = Long.parseLong(thirdLevelTermKey);

            Level3MenuInfoODTO menuInfo = allMenuMap.get(thirdMenuId);
            if(null == menuInfo){
                continue;
            }
            itemODTO.setFirstLevelMenu(menuInfo.getLevel1MenuName());
            itemODTO.setMenuName(menuInfo.getLevel3MenuName());
            itemODTO.setSystemName(menuInfo.getSystemName());
            itemODTO.setSecondLevelMenu(menuInfo.getLevel2MenuName());

            resultList.add(itemODTO);
        }

        if(SpringUtil.isEmpty(resultList)){
            return pageDate;
        }
        resultList = resultList.stream().sorted(Comparator.comparingLong(LogActionListODTO::getActionCount)).collect(Collectors.toList());
        // 设置返回分页信息
        Map<String,Integer> getPageMap = PageUtil.getPageMap(resultList.size(),idto.getPageNo(),idto.getPageSize(),pageDate);
        int startPoint = getPageMap.get("startPoint");
        int endPoint = getPageMap.get("endPoint");
        List<LogActionListODTO> pageList = resultList.subList(startPoint, endPoint);
        pageDate.setList(pageList);
        return pageDate;
    }

    /**
     * 根据前端回传的menuName获取对应查询条件
     * @param menuName
     * @return
     */
    public TermQueryBuilder getMenuFilterContent(String menuName){
        String menuLevel = "";
        String menuId = "";
        if(StringUtils.isNotBlank(menuName)){
            menuLevel = menuName.substring(0,2);
            menuId = menuName.substring(2);
        }else{
            return null;
        }
        if(StringUtils.isBlank(menuLevel) || StringUtils.isBlank(menuId)){
            return null;
        }
        String menuNameValue = reportActionLogMapper.selectMenuName(Long.parseLong(menuId),menuLevel);
        String menuNameContent = ReportActionMenuTypeEnum.getMenuNameContentByCode(menuLevel);
        if(StringUtils.isBlank(menuNameContent) || StringUtils.isBlank(menuNameValue)){
            return null;
        }
        return QueryBuilders.termQuery(menuNameContent, menuNameValue);
    }

    public boolean refreshActionLogMatchInfo() {
        RBucket<Map<String, ReportActionLog>> matchInfoBucket = redissonClient.getBucket(LogActionService.MATCH_INFO_REDIS_KEY);
        matchInfoBucket.delete();
        List<ReportActionLog> matchInfoList = reportActionLogMapper.selectAll();
        Map<String, ReportActionLog> matchInfoMap = matchInfoList.stream()
                .collect(Collectors.toMap(it -> String.format("%s:%s", it.getApplicationName(), it.getUri()), it -> it, (t1, t2) -> t2));
        matchInfoBucket.set(matchInfoMap);
        log.info("actionLog缓存刷新完毕");
        return true;
    }

    /**
     * 向es中保存查询日志
     *
     * @param actionLog
     */
    public void saveActionLog(ActionLog actionLog) {
        ReportActionLog matchInfo = getMatchInfo(actionLog);
        if (null == matchInfo) {
            log.info("不存在对应类型, notes{}", actionLog.getDescription());
            return;
        }
        EsActionLogV2 esActionLog = actionLog2EsActionLog(actionLog);
        esActionLog.setOperaType(Integer.parseInt(matchInfo.getOperaType()));
        esActionLog.setFirstLevelMenuId(matchInfo.getFirstLevelMenuId());
        esActionLog.setSecondLevelMenuId(matchInfo.getSecondLevelMenuId());
        esActionLog.setThirdLevelMenuId(matchInfo.getThirdLevelMenuId());
        esActionLog.setSystemId(matchInfo.getSystemId());
        esActionLog.setId(IdWorker.getId());
        esActionLogV2Repository.save(esActionLog);
    }

    /**
     * 获取匹配的日志类型信息
     *
     * @param actionLog
     * @return
     */
    private ReportActionLog getMatchInfo(ActionLog actionLog) {
        RBucket<Map<String, ReportActionLog>> matchInfoBucket = redissonClient.getBucket(MATCH_INFO_REDIS_KEY);
        Map<String, ReportActionLog> matchInfoMap = matchInfoBucket.get();
        if (SpringUtil.isEmpty(matchInfoMap)) {
            List<ReportActionLog> matchInfoList = reportActionLogMapper.selectAll();
            matchInfoMap = matchInfoList.stream()
                    .collect(Collectors.toMap(it -> String.format("%s:%s", it.getApplicationName(), it.getUri()), it -> it, (t1, t2) -> t2));
            matchInfoBucket.set(matchInfoMap);
        }
        return matchInfoMap.get(String.format("%s:%s", actionLog.getApplicationName(), actionLog.getUri()));
    }

    /**
     * 将ActionLog中的部分值拷贝到EsActionLog中
     *
     * @param actionLog
     * @return
     */
    private EsActionLogV2 actionLog2EsActionLog(ActionLog actionLog) {
        EsActionLogV2 esActionLog = new EsActionLogV2();
        esActionLog.setApplicationName(actionLog.getApplicationName());
        esActionLog.setDescription(actionLog.getDescription());
        esActionLog.setUserCode(actionLog.getUserCode());
        esActionLog.setUserName(actionLog.getUserName());
        esActionLog.setStartTime(actionLog.getStartTime());
        esActionLog.setUri(actionLog.getUri());
        esActionLog.setUrl(actionLog.getUrl());
        esActionLog.setMethod(actionLog.getMethod());
        esActionLog.setIp(actionLog.getIp());
        String parameterStr = null == actionLog.getParameter() ? "" : actionLog.getParameter().toString();
        esActionLog.setParameterStr(parameterStr);
        String resultStr = null == actionLog.getResult() ? "" : actionLog.getResult().toString();
        esActionLog.setResultStr(resultStr);
        return esActionLog;
    }

    public List<ReportMenuItemODTO> findReportMenuList(ReportMenuItemIDTO idto) {
        List<ReportMenuItemODTO> result = new ArrayList<>();
        String systemName = getSystemName(idto.getSystemId());
        if(StringUtils.isBlank(systemName)){
            return result;
        }
        Long systemId = idto.getSystemId().longValue();
        List<ReportMenuItem> items = reportActionLogMapper.selectReportMenuList(systemId);
        if (SpringUtil.isEmpty(items)) {
            return result;
        }
        Long lastFirst = 0L;
        Long lastSecond = 0L;
        ReportMenuItemODTO firstItem = null;
        ReportMenuItemODTO secondItem = null;
        Map<Long, Level3MenuInfoODTO> allMenuMap = getAllMenuMap();
        for (ReportMenuItem item : items) {
            if(null == item.getThirdLevelMenuId() || null == item.getSecondLevelMenuId() || null == item.getFirstLevelMenuId()){
                continue;
            }
            Level3MenuInfoODTO level3MenuInfo = allMenuMap.get(item.getThirdLevelMenuId());
            if(null == level3MenuInfo){
                continue;
            }
            item.setFirstLevelMenu(level3MenuInfo.getLevel1MenuName());
            item.setSecondLevelMenu(level3MenuInfo.getLevel2MenuName());
            item.setMenuName(level3MenuInfo.getLevel3MenuName());

            ReportMenuItemODTO menu = new ReportMenuItemODTO();
            menu.setLabel(item.getMenuName());
            menu.setValue(ReportActionMenuTypeEnum.MENU_NAME.getCode()+ item.getId());
            if (lastSecond.equals(item.getSecondLevelMenuId()) && null != secondItem) {
                secondItem.getChildren().add(menu);
            } else {
                secondItem = new ReportMenuItemODTO();
                secondItem.setValue(ReportActionMenuTypeEnum.SECOND_LEVEL_MENU.getCode() + item.getId());
                secondItem.setLabel(item.getSecondLevelMenu());
                List<ReportMenuItemODTO> secondChildren = new ArrayList<>();
                secondChildren.add(menu);
                secondItem.setChildren(secondChildren);
            }
            if (lastFirst.equals(item.getFirstLevelMenuId()) && null != firstItem) {
                if(!lastSecond.equals(item.getSecondLevelMenuId())) {
                    firstItem.getChildren().add(secondItem);
                }
            } else {
                firstItem = new ReportMenuItemODTO();
                firstItem.setValue(ReportActionMenuTypeEnum.FIRST_LEVEL_MENU.getCode() + item.getId());
                firstItem.setLabel(item.getFirstLevelMenu());
                List<ReportMenuItemODTO> firstChildren = new ArrayList<>();
                firstChildren.add(secondItem);
                firstItem.setChildren(firstChildren);
                result.add(firstItem);
                lastFirst = item.getFirstLevelMenuId();
            }
            if (!lastSecond.equals(item.getSecondLevelMenuId()) ) {
                lastSecond = item.getSecondLevelMenuId();
            }
        }
        return result;
    }

    /**
     * 根据系统id获取系统名称
     * @param systemId
     * @return
     */
    private String getSystemName(Integer systemId){
        List<SystemManagerODTO> systemList = systemManagerClient.queryAllSystemList();
        if(SpringUtil.isEmpty(systemList)){
            return null;
        }
        for (SystemManagerODTO item: systemList){
            if(null != item.getSystemId() && item.getSystemId().equals(systemId)){
                return item.getSystemName();
            }
        }
        return null;
    }

    /**
     * 获取所有三级目录信息
     * @return
     */
    private Map<Long, Level3MenuInfoODTO> getAllMenuMap(){
        SelectLevel3MenuInfoListIDTO idto = new SelectLevel3MenuInfoListIDTO();
        List<Level3MenuInfoODTO> menuList = menuClient.selectLevel3MenuInfoList(idto);
        return menuList.stream().collect(Collectors.toMap(it -> Long.parseLong(it.getLevel3MenuId()), it -> it));
    }


    /**
     * 拼接查询条件boolQueryBuilder
     * @param idto
     * @param boolQueryBuilder
     * @return
     */
    private boolean handleLogActionSearchCondition(LogActionSearchIDTO idto, BoolQueryBuilder boolQueryBuilder){
        if(StringUtils.isNotBlank(idto.getMenuCode())){
            TermQueryBuilder menuFilterContent = getMenuFilterContent(idto.getMenuCode());
            if(null == menuFilterContent){
                return false;
            }
            boolQueryBuilder.filter(menuFilterContent);
        }
        if(StringUtils.isNotBlank(idto.getSystemCode())){
            Integer systemId = Integer.parseInt(idto.getSystemCode());
            boolQueryBuilder.filter(QueryBuilders.termQuery("systemId",systemId));
        }
        if(null != idto.getUserId()){
            String userCode = employeeUserMapper.selectCodeByUserId(idto.getUserId());
            if(StringUtils.isBlank(userCode)){
                return false;
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("userCode", userCode));
        }
        if (StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            long beginTime = 0;
            long endTime = 0;
            try {
                endTime = simpleDateFormat.parse(idto.getEndDate()).getTime();
                beginTime = simpleDateFormat.parse(idto.getBeginDate()).getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("startTime").gte(beginTime).lte(endTime));
        }
        return true;
    }
}
