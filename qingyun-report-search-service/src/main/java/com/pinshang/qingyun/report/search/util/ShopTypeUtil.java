package com.pinshang.qingyun.report.search.util;

import com.pinshang.qingyun.report.search.enums.ShopTypeConditionEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/27
 * @Version 1.0
 */
public class ShopTypeUtil {
    public static List<Integer> getShopType(Integer shopType, Integer code){
        if(null == code){
            return new ArrayList<>();
        }
        if(null == shopType){
            ShopTypeConditionEnum shopTypeEnum = ShopTypeConditionEnum.getShopTypeListByCode(code);
            return null == shopTypeEnum ? new ArrayList<>() : ShopTypeConditionEnum.getShopTypeListByCode(code).getShopTypeList();
        }else{
            return Collections.singletonList(shopType);
        }
    }
}
