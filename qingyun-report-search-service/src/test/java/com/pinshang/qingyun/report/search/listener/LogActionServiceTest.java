package com.pinshang.qingyun.report.search.listener;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.search.service.LogActionService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName LogActionServiceTest
 * <AUTHOR>
 * @Date 2022/10/25 15:07
 * @Description LogActionServiceTest
 * @Version 1.0
 */
public class LogActionServiceTest extends AbstractJunitBase {

    @Autowired
    private LogActionService logActionService;

    @Test
    public void refreshActionLogMatchInfoTest(){
        logActionService.refreshActionLogMatchInfo();
    }
}
