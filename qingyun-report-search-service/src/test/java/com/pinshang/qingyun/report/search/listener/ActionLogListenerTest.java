/*
package com.pinshang.qingyun.report.search.listener;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.log.ActionLog;
import com.pinshang.qingyun.report.search.controller.LogActionController;
import com.pinshang.qingyun.report.search.dto.LogActionListIDTO;
import com.pinshang.qingyun.report.search.mapper.ReportActionLogMapper;
import com.pinshang.qingyun.report.search.service.LogActionService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

*/
/**
 * @ClassName ActionLogListenerTest
 * <AUTHOR>
 * @Date 2021/4/26 10:27
 * @Description ActionLogListenerTest
 * @Version 1.0
 *//*

public class ActionLogListenerTest extends AbstractJunitBase {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private LogActionController logSearchController;

    @Autowired
    private LogActionService logActionService;

    @Autowired
    private ReportActionLogMapper reportActionLogMapper;

    public ActionLogListenerTest() {
    }

    @Test
    public void sendKafka(){
        long startTime = System.currentTimeMillis();
        //记录请求信息
        ActionLog webLog = new ActionLog();
        webLog.setApplicationName("applicationName");
        webLog.setDescription("商品销售汇总");
        long endTime = System.currentTimeMillis();
        webLog.setIp("*************");
        webLog.setMethod("request.getMethod()");
        Map<String, Object> parameter = new HashMap<>(4);
        parameter.put("demo", 1);
        List<Object> argList = new ArrayList<>(4);
        argList.add(parameter);
        webLog.setParameter(argList);
        webLog.setResult(argList);
        webLog.setSpendTime((int) (endTime - startTime));
        webLog.setStartTime(startTime);
        webLog.setUri("/report/posReports/commoditySalesSummaryReport");
        webLog.setUrl("http://*************:9100/report/posReports/commoditySalesSummaryReport");

        webLog.setUserCode("test001");
        webLog.setUserName("测试001");

        //kafka形式
        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.QINGYUN_ACTION_LOG_TYPE
                , webLog, KafkaMessageOperationTypeEnum.INSERT);
        kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.QINGYUN_ACTION_LOG_TOPIC, JsonUtil.java2json(message));
    }

    @Test
    public void findReportMenuList(){
       */
/* String menuName = reportActionLogMapper.selectMenuName(3L, "20");
        System.out.println("======================="+menuName);*//*

        */
/*ReportMenuItemIDTO idto = new ReportMenuItemIDTO();
        idto.setSystemId(300);
        List<ReportMenuItemODTO> result = logActionService.findReportMenuList(idto);
        System.out.println(result.toString());*//*

       */
/* LogActionDetailsIDTO idto = new LogActionDetailsIDTO();
        idto.setBeginDate("2021-04-30 00:00:00");
        idto.setEndDate("2021-05-30 00:00:00");
        idto.setMenuName("103");
        idto.setPageNo(5);
        TablePageInfo<LogActionDetailsODTO> result = logActionService.searchLogsDetails(idto);
        System.out.println(result.getList().toString());*//*


        LogActionListIDTO idto = new LogActionListIDTO();
        idto.setBeginDate("2021-04-30 00:00:00");
        idto.setEndDate("2021-06-30 00:00:00");
        idto.setSystemCode("300");
        //idto.setMenuCode("103");
       // PageInfo<LogActionListODTO> result = logActionService.searchLogsList(idto);
//        /System.out.println(result.getList().toString());
    }

    public void downloadLog(){

    }
}
*/
