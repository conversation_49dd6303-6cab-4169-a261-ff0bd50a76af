<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pinshang.qingyun</groupId>
		<artifactId>qingyun-report-parent</artifactId>
		<version>4.0.7-UP-SNAPSHOT</version>
	</parent>
	<artifactId>qingyun-report-search-service</artifactId>
	<properties>
		<java.version>1.8</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-basic</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-json</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>-->
		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-client</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-health-check</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-file-export-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-mq</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-components-inventory</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-switch</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-loadBalancer</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-metrics-client</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-common</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-springcloud-common</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-joda</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-base-db</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-base-mvc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-box</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-price-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-storage-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-smm-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-product-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-common-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-weixin-client</artifactId>
		</dependency>
		<dependency>
			<groupId>tk.mybatis</groupId>
			<artifactId>mapper-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-boot-starter</artifactId>
			<version>${springfox-swagger.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>

		<!--集成logstash-->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>5.3</version>
		</dependency>

		<!--Elasticsearch相关依赖-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-elasticsearch</artifactId>
		</dependency>

		<!--Elasticsearch相关依赖  开始-->
		<!--<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-elasticsearch</artifactId>
			<version>3.0.5.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>5.6.8</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>transport</artifactId>
			<version>5.6.8</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.plugin</groupId>
			<artifactId>transport-netty4-client</artifactId>
			<version>5.6.8</version>
		</dependency>-->
		<!--Elasticsearch相关依赖  结束-->

		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-mq</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-shop-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>4.0.2</version>
			<scope>compile</scope>
		</dependency>

		<!--Hutool Java工具包-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>4.5.7</version>
		</dependency>
	</dependencies>
	<build>
        <plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.6.6</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
        </plugins>
    </build>
</project>
