package com.pinshang.qingyun.report.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/1 13:15
 */
@Data
@NoArgsConstructor
public class ShopBreakageRecordODTO {
    @ApiModelProperty(value ="1-出库、2-入库")
    private Integer inoutType = 1;

    @ApiModelProperty(value = "业务类型:9-门店报损")
    private Integer businessType = 9;

    @ApiModelProperty(value = "业务单号")
    private String businessCode;

    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务时间：yyyy-MM-dd HH:mm:ss")
    private Date businessTime;

    @ApiModelProperty(value = "业务明细ID")
    private Long businessOrderItemId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("档口")
    private Long stallId;
}
