package com.pinshang.qingyun.report.dto.index;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class HomePageIDTO {
    /**
     * 门店id
     */
    private Long  shopId;

    /**
     * 时间类型:1、今日 2、周 3、月 4、年
     */
    private Integer dateType;

    /**
     * 渠道(1、线上线下 2、线上 3、线下)
     */
    private  Integer sourceType;

    /**
     * 门店id 集合
     */
    private List<Long> shopIdList;
    /**
     * 今天开始时间
     */
    private String beginDate;

    /**
     * 今天结束时间
     */
    private String endDate;

    /**
     * 上周开始时间
     */
    private String lastWeekBeginDate;

    /**
     * 上周结束时间
     */
    private String lastWeekEndDate;

    /**
     * 门店类型 弃用
     */
    private  Integer shopType;

    public HomePageIDTO(Integer shopType,String beginDate, String endDate, String lastWeekBeginDate, String lastWeekEndDate,
                        Long shopId, List<Long> shopIdList) {
        this.shopType = shopType;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.lastWeekBeginDate = lastWeekBeginDate;
        this.lastWeekEndDate = lastWeekEndDate;
        this.shopId = shopId;
        this.shopIdList = shopIdList;

    }
}
