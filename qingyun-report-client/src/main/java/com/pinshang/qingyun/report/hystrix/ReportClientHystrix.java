package com.pinshang.qingyun.report.hystrix;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportIDTO;
import com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportODTO;
import com.pinshang.qingyun.report.dto.pos.PosConsignmentOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.PosConsignmentOrderODTO;
import com.pinshang.qingyun.report.dto.pos.PosSaleWaterODTO;
import com.pinshang.qingyun.report.dto.pos.PosSaleWaterQueryIDTO;
import com.pinshang.qingyun.report.service.ReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class ReportClientHystrix implements FallbackFactory<ReportClient> {
    @Override
    public ReportClient create(Throwable cause) {
        return new ReportClient() {

            @Override
            public Boolean etlrun(String timeStamp, String fileName) {
                return null;
            }

            @Override
            public ApiResponse importMonthlyCashierReport(Long userId, String userName, List<ClearingChargesItemImport> chargesItems) {
                return null;
            }

            @Override
            public Object importDaySaleAim(List<SaleAimIDTO> idtoList) {
                return null;
            }

            @Override
            public Object importMonthSaleAim(List<SaleAimIDTO> idtoList) {
                return null;
            }

            @Override
            public BigDecimal getShortDeliveryRealDeliveryAmount(String timeStamp) {
                return null;
            }

            @Override
            public BigDecimal getCommodityRealDeliveryAmount(String timeStamp) {
                return null;
            }

            @Override
            public Boolean commodityDaySalesSummaryReport(String saleTime) {
                return null;
            }

            @Override
            public Boolean posThirdCateDaySummary(String timeStamp) {
                return null;
            }

            @Override
            public Boolean cashierWaterDayReport(String saleTime) {
                return null;
            }

            @Override
            public List<CashierWaterDayReportODTO> queryCashierWaterDayReport(Long shopId,String beginTime, String endTime, Integer type) {
                return null;
            }

            @Override
            public Boolean orderReportMonth(String beginTime, String endTime) {
                return null;
            }

            @Override
            public Boolean mdCheckReportMonth(String beginTime, String endTime) {
                return null;
            }

            @Override
            public Boolean bestsellerGoodMonth(String beginTime, String endTime) {
                return null;
            }


            @Override
            public Object importBlendCollectReport(List<BlendCollectImportIDTO> collectReportList) {
                return null;
            }

            @Override
            public Boolean replaceByTime(String oneDay) {
                return null;
            }

            // 报损
            @Override
            public Boolean breakageEnteringDay(String dateTime) {
                return null;
            }

            @Override
            public Boolean fixOriginOrderCode(String timeStamp) {
                return null;
            }

            @Override
            public Boolean cloudCommodityReportSummary(String orderTime) {
                return null;
            }

            @Override
            public Boolean cloudCommodityInfoReport(String orderTime) {
                return null;
            }

            @Override
            public Boolean shopNegativeStockReportSummary() {
                return null;
            }

            @Override
            public List<ThirdSummaryODTO> queryPosCateSummary(ThirdSummaryIDTO idto) {
                return null;
            }

            @Override
            public List<PosSaleWaterODTO> queryPosSalesWaterData(PosSaleWaterQueryIDTO idto) {
                return Collections.emptyList();
            }

            @Override
            public BigDecimal queryFirstCateWeekSaleAmount(Long shopId, Date orderTime) {
                return null;
            }

            @Override
            public Boolean comparePosReportAmount(String saleTime) {
                return null;
            }

            @Override
            public List<PosConsignmentOrderODTO> queryConsignmentOrderSettle(PosConsignmentOrderIDTO idto) {
                return null;
            }

            @Override
            public Boolean exceptionHandleReport(String saleTime) {
                return null;
            }

            @Override
            public List<InventoryCompPosReportODTO> queryPosReportShopCommoditySumQuantity(InventoryCompPosReportIDTO idto) {
                return null;
            }
        };
    }
}
