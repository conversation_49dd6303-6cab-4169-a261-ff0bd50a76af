

package com.pinshang.qingyun.report.dto.index;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
public class SalesDataODTO {
    /**
     * 鲜到app销售
     */
    private BigDecimal appSales;

    /**
     * 鲜到app成本
     */
    private BigDecimal appCost;

    /**
     * 鲜到小程序销售
     */
    private BigDecimal appletsSales;

    /**
     * 鲜到小程序成本
     */
    private BigDecimal appletsCost;

    /**
     * pos销售
     */
    private BigDecimal posSales;

    /**
     * pos成本
     */
    private BigDecimal posCost;

    /**
     * elm销售
     */
    private BigDecimal elmSales;

    /**
     * elm成本
     */
    private BigDecimal elmCost;

    /**
     * 京东到家销售
     */
    private BigDecimal jddjSales;

    /**
     * 京东到家成本
     */
    private BigDecimal jddjCost;

    /**
     * 团购销售
     */
    private BigDecimal groupSales;

    /**
     * 团购成本
     */
    private BigDecimal groupCost;

    /**
     * 云超小程序销售
     */
    private BigDecimal cloudSales;

    /**
     * 云超小程序成本
     */
    private BigDecimal cloudCost;

    /**
     * 云超app销售
     */
    private BigDecimal cloudAppSales;

    /**
     * 云超app成本
     */
    private BigDecimal cloudAppCost;

    /**前置仓销售**/
    private BigDecimal warehouseSales;

    /**前置仓销售**/
    private BigDecimal warehouseCost;

    /**前置仓销售**/
    private BigDecimal warehouseAppSales;

    /**前置仓销售**/
    private BigDecimal warehouseAppCost;

    /**清美团团成本**/
    private BigDecimal quickSales;

    /**清美团团成本**/
    private BigDecimal quickCost;
    /**
     * 客单量
     */
    private BigDecimal visitorNumber;
}
