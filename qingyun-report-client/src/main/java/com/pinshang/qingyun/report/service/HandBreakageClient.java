package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.report.BreakageEnteringIDTO;
import com.pinshang.qingyun.report.dto.report.BreakagedCommodityListODTO;
import com.pinshang.qingyun.report.dto.report.CommodityInfoODTO;
import com.pinshang.qingyun.report.hystrix.HandBreakageClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = HandBreakageClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface HandBreakageClient {

    @RequestMapping(value = "/hand/breakage/pda/commodities/{barcode}", method = RequestMethod.GET)
    CommodityInfoODTO handBreakageBarcode(@PathVariable("barcode") String barcode,
                                          @RequestParam(value = "shopId", required = false) Long shopId,
                                          @RequestParam(value = "stallId", required = false) Long stallId);

    @RequestMapping(value = "/hand/breakage/commodities/{barcode}", method = RequestMethod.GET)
    CommodityInfoODTO priceTagBarcode(@PathVariable("barcode") String barcode);

    @RequestMapping(value = "/hand/breakage/entering", method = RequestMethod.POST)
    Boolean entering(BreakageEnteringIDTO idto);

    @RequestMapping(value = "/hand/breakage/breakagedCommodityForPass24Hours", method = RequestMethod.GET)
    BreakagedCommodityListODTO breakagedCommodityForPass24Hours();

}
