package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.LastMonthSaleDTO;
import com.pinshang.qingyun.report.dto.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO;
import com.pinshang.qingyun.report.hystrix.XdCommodityTaxClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = XdCommodityTaxClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdCommodityTaxClient {

    @RequestMapping(value = "/xdCommodityTax/xdShopCommodityTax", method = RequestMethod.POST)
    Boolean xdCommodityTax(@RequestParam(value = "timeStamp") String timeStamp);

    @RequestMapping(value = "/xdCommodityTax/findXdCommodityTaxList", method = RequestMethod.GET)
    List<XdCommodityTaxODTO> findXdCommodityTaxList(@RequestParam(value = "beginTime") String beginTime, @RequestParam(value = "endTime") String endTime, @RequestParam(value = "shopId") Long shopId, @RequestParam(value = "commodityId") Long commodityId);

    @PostMapping("/commodityTax/lastMonthSale")
    List<ShopCommoditySaleStatisticsODTO> lastMonthSale(@RequestBody LastMonthSaleDTO lastMonthSaleDTO);
}
