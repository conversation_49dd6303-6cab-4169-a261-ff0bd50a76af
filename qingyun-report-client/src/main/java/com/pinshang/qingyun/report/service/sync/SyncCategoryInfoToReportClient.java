package com.pinshang.qingyun.report.service.sync;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.hystrix.sync.SyncCategoryInfoToReportClientHystrix;

/**
 * 同步品类相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年05月06日
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = SyncCategoryInfoToReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncCategoryInfoToReportClient {
    
	/**
	 * 同步主库品类相关信息到从库
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/sync/categoryInfoTo/syncCategoryInfo", method = RequestMethod.POST)
    public Integer syncCategoryInfo(@RequestBody SyncInfoByCodesIDTO idto);
    
	/**
	 * 查询主从品类差异信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/categoryInfoTo/selectCategoryDifferenceInfo", method = RequestMethod.POST)
	public DifferenceInfoODTO selectCategoryDifferenceInfo();
	
	/**
	 * 查询从库缺失的品类编码集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/categoryInfoTo/selectMissingCategoryCodeList", method = RequestMethod.POST)
	public List<String> selectMissingCategoryCodeList();
	
	/**
	 * 删除从库多余的品类集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/categoryInfoTo/deleteRedundantCategoryList", method = RequestMethod.POST)
	public Integer deleteRedundantCategoryList();

}
