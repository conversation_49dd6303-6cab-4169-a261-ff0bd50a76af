package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.hystrix.pos.PosHelpCardAccountHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * @ClassName PosHelpCardTradeClient
 * <AUTHOR>
 * @Date 2023/2/28 17:34
 * @Description PosHelpCardTradeClient
 * @Version 1.0
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = PosHelpCardAccountHystrix.class, configuration = FeignClientConfiguration.class)
public interface PosHelpCardAccountClient {

    @ApiOperation(value = "根据 区域 所属公司查询对账汇总", notes = "根据 区域 所属公司 查询对账汇总", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/posHelpCardAccountCheck/selectCheckDetail4InvoiceByCompany", method = RequestMethod.POST)
    PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByCompany(@RequestBody HelpCardCheckDetail4InvoiceIDTO idto);

    @ApiOperation(value = "根据 区域 查询对账汇总", notes = "根据 区域 查询对账汇总", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/posHelpCardAccountCheck/selectCheckDetail4InvoiceByArea", method = RequestMethod.POST)
    PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByArea(@RequestBody HelpCardCheckDetail4InvoiceIDTO idto);

    @ApiOperation(value = "查询对账明细", notes = "查询对账明细", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/posHelpCardAccountCheck/selectCheckDetail4Invoice", method = RequestMethod.POST)
    List<HelpCardCheckDetail4InvoiceItemODTO> selectCheckDetail4Invoice(@RequestBody HelpCardCheckDetail4InvoiceItemIDTO idto);

    @ApiOperation(value = "帮困卡支付金额结算数据", notes = "帮困卡支付金额结算数据", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/posHelpCardAccountCheck/selectPxAmountByStoreIdList", method = RequestMethod.POST)
    List<HelpCardPxAmountByStoreIdListODTO> selectPxAmountByStoreIdList(@RequestBody HelpCardPxAmountByStoreIdListIDTO idto);
}
