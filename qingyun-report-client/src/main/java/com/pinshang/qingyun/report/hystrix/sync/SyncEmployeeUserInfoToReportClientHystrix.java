package com.pinshang.qingyun.report.hystrix.sync;

import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.service.sync.SyncEmployeeUserInfoToReportClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 同步职员用户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年07月12日
 */
@Slf4j
@Component
public class SyncEmployeeUserInfoToReportClientHystrix implements FallbackFactory<SyncEmployeeUserInfoToReportClient> {
    @Override
    public SyncEmployeeUserInfoToReportClient create(Throwable throwable) {
        return new SyncEmployeeUserInfoToReportClient() {

        	@Override
			public Integer syncEmployeeUserInfo(SyncInfoByCodesIDTO idto) {
        		log.error("\n syncEmployeeUserInfo:" + throwable.toString() + "\nidto=" + idto);
				return null;
			}
        	
        	@Override
			public DifferenceInfoODTO selectEmployeeUserDifferenceInfo() {
        		log.error("\n selectEmployeeUserDifferenceInfo:" + throwable.toString());
				return null;
			}

			@Override
			public List<String> selectMissingEmployeeUserCodeList() {
				log.error("\n selectMissingEmployeeUserCodeList:" + throwable.toString());
				return null;
			}

        };
    }

}
