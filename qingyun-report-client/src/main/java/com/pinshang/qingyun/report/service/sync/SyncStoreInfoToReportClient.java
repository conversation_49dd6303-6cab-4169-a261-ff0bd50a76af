package com.pinshang.qingyun.report.service.sync;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.hystrix.sync.SyncStoreInfoToReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 同步客户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = SyncStoreInfoToReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncStoreInfoToReportClient {
    
	/**
	 * 同步主库客户相关信息到从库
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/sync/storeInfoTo/syncStoreInfo", method = RequestMethod.POST)
    public Integer syncStoreInfo(@RequestBody SyncInfoByCodesIDTO idto);
    
	/**
	 * 查询主从客户差异信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/storeInfoTo/selectStoreDifferenceInfo", method = RequestMethod.POST)
	public DifferenceInfoODTO selectStoreDifferenceInfo();
	
	/**
	 * 查询从库缺失的客户编码集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/storeInfoTo/selectMissingStoreCodeList", method = RequestMethod.POST)
	public List<String> selectMissingStoreCodeList();

}
