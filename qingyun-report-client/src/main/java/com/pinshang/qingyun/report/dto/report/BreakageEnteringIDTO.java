package com.pinshang.qingyun.report.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:44
 */
@Data
public class BreakageEnteringIDTO {
    @ApiModelProperty(position = 1, value = "商品id")
    private Long commodityId;
    @ApiModelProperty(position = 2, value = "监控位置")
    private Long monitorLocationId;
    @ApiModelProperty(position = 3, value = "报损原因id")
    private Long reasonId;
    @ApiModelProperty(position = 4, value = "生产日期yyyy-MM-dd")
    private String makeDate;
    @ApiModelProperty(position = 5, value = "到期日期yyyy-MM-dd")
    private String expireDate;
    @ApiModelProperty(position = 6, value = "报损数量")
    private BigDecimal breakageNum;
    @ApiModelProperty(position = 7, value = "报损份数")
    private Integer breakage;
    @ApiModelProperty(position = 8, value = "报损库区（从字典中获取）")
    private Long stockAreaId;

    @ApiModelProperty(hidden = true)
    private Long shopId;
    @ApiModelProperty(hidden = true)
    private Long createId;
    @ApiModelProperty(hidden = true)
    private String createName;
    @ApiModelProperty(hidden = true)
    private String reasonName;
    @ApiModelProperty(hidden = true)
    private String monitorLocationName;

    @ApiModelProperty(value = "是否餐饮, 1-是, 0或空-否")
    private Integer isCatering;

    @ApiModelProperty("货位ID")
    private Long goodsAllocationId;

    @ApiModelProperty("货位号")
    private String goodsAllocationCode;

    @ApiModelProperty("档口ID")
    private Long stallId;
}
