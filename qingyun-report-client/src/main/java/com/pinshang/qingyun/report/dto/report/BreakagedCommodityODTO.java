package com.pinshang.qingyun.report.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-06-03
 */
@Data
public class BreakagedCommodityODTO {
    @ApiModelProperty(position = 1, value = "商品名称")
    private String commodityName;
    @ApiModelProperty(position = 2, value = "商品规格")
    private String commoditySpec;
    @ApiModelProperty(position = 3, value = "报损数量")
    private BigDecimal breakageNum;
    @ApiModelProperty(position = 4, value = "单位")
    private String commodityUnit;
    @ApiModelProperty(position = 5, value = "报损原因")
    private String reasonName;
    @ApiModelProperty(position = 6, value = "操作人")
    private String createName;
    @ApiModelProperty(position = 7, value = "操作时间")
    private String createTime;

    @ApiModelProperty(hidden = true)
    private Date createDate;
}
