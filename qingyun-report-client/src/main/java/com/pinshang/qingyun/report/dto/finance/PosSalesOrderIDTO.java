package com.pinshang.qingyun.report.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/30 12:44
 */
@Data
@NoArgsConstructor
public class PosSalesOrderIDTO {

    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务单明细ID 查询起点比如100表示就从第100条开始查询到limitQuantity")
    private Long businessOrderItemId;

    @ApiModelProperty(value = "查询条数")
    private Integer limitQuantity;

    @ApiModelProperty(value = "业务类型:3-POS销售订单 5-POS销售订单退货")
    private Integer businessType;
    
    public PosSalesOrderIDTO(String businessDate, Integer businessType, Integer limitQuantity, Long businessOrderItemId) {
    	this.businessDate = businessDate;
    	this.businessType = businessType;
    	this.limitQuantity = limitQuantity;
    	this.businessOrderItemId = businessOrderItemId;
    }
    
}
