package com.pinshang.qingyun.report.hystrix.pos;

import com.pinshang.qingyun.report.service.pos.PosHelpCardTradeClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @ClassName PosHelpCardTradeHystrix
 * <AUTHOR>
 * @Date 2023/2/28 17:35
 * @Description PosHelpCardTradeHystrix
 * @Version 1.0
 */
@Component
public class PosHelpCardTradeHystrix implements FallbackFactory<PosHelpCardTradeClient> {
    @Override
    public PosHelpCardTradeClient create(Throwable throwable) {
        return new PosHelpCardTradeClient() {
            @Override
            public Boolean helpCardAutoCheckJob(String date) {
                return null;
            }
        };
    }
}
