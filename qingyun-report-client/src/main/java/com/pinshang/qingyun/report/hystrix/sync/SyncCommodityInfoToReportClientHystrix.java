package com.pinshang.qingyun.report.hystrix.sync;

import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.service.sync.SyncCommodityInfoToReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 同步商品相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@Component
public class SyncCommodityInfoToReportClientHystrix implements FallbackFactory<SyncCommodityInfoToReportClient> {
    @Override
    public SyncCommodityInfoToReportClient create(Throwable throwable) {
        return new SyncCommodityInfoToReportClient() {

        	@Override
			public Integer syncCommodityInfo(SyncInfoByCodesIDTO idto) {
				return null;
			}
        	
        	@Override
			public DifferenceInfoODTO selectCommodityDifferenceInfo() {
				return null;
			}

			@Override
			public List<String> selectMissingCommodityCodeList() {
				return null;
			}

        };
    }

}
