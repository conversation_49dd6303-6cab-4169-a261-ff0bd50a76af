package com.pinshang.qingyun.report.dto.pos;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName HelpCardCheckDetail4InvoiceIDTO
 * <AUTHOR>
 * @Date 2023/3/9 16:35
 * @Description HelpCardCheckDetail4InvoiceIDTO
 * @Version 1.0
 */
@Data
public class HelpCardCheckDetail4InvoiceIDTO {


    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("t_pos_help_card_account_check.id")
    private List<Long> idList;
    @ApiModelProperty("排除 t_pos_help_card_account_check.id")
    private List<Long> notIdList;

    @ApiModelProperty("区域id")
    private List<Long> areaIdList;

    private Long companyId;

    /**
     * 当前页号, 默认第 1 页
     */
    private Integer pageNo = 1;
    /**
     * 每页显示记录数, 默认 10 条
     */
    private Integer pageSize = 10;

    /**
     * 导出时设置的分页参数
     */
    public  void initExportPage(){
        this.pageNo = 1 ;
        this.pageSize = Integer.MAX_VALUE ;
    }

    public Integer getPageNo(){
        return  pageNo==null || pageNo<1 ? 1 : pageNo ;
    }



    private Integer pageCount;
}
