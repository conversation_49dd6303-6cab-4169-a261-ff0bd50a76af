package com.pinshang.qingyun.report.hystrix;

import com.pinshang.qingyun.report.dto.LastMonthSaleDTO;
import com.pinshang.qingyun.report.dto.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO;
import com.pinshang.qingyun.report.service.XdCommodityTaxClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class XdCommodityTaxClientHystrix implements FallbackFactory<XdCommodityTaxClient> {
    @Override
    public XdCommodityTaxClient create(Throwable cause) {
        return new XdCommodityTaxClient() {


            @Override
            public Boolean xdCommodityTax(String timeStamp) {
                return null;
            }

            @Override
            public List<XdCommodityTaxODTO> findXdCommodityTaxList(String beginTime, String endTime, Long shopId, Long commodityId) {
                return null;
            }

            @Override
            public List<ShopCommoditySaleStatisticsODTO> lastMonthSale(LastMonthSaleDTO lastMonthSaleDTO) {
                return null;
            }


        };
    }
}
