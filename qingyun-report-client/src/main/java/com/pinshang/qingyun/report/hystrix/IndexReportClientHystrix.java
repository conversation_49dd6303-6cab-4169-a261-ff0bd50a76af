package com.pinshang.qingyun.report.hystrix;

import com.pinshang.qingyun.report.dto.index.*;
import com.pinshang.qingyun.report.service.IndexReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class IndexReportClientHystrix implements FallbackFactory<IndexReportClient> {
    @Override
    public IndexReportClient create(Throwable cause) {
        return new IndexReportClient() {

            @Override
            public SalesDataODTO selectSalesDataHistory(HomePageIDTO homePageIDTO) {
                return null;
            }

            @Override
            public List<YearOnYearDataODTO> selectSelectDataGraphicsHistory(HomePageIDTO homePageIDTO) {
                return null;
            }

            @Override
            public List<StoreSaleODTO> selectHistoryStoreSale(HomePageIDTO homePageIDTO) {
                return null;
            }

            @Override
            public List<IndexPosSalesODTO> compensateIndex(CompensateIDTO compensateIDTO) {
                return null;
            }

            @Override
            public Boolean monitorXsXdReportDay(String dateTime) {
                return null;
            }

            @Override
            public List<IndexPosSalesODTO> selectOffHistorySaleByTimeSlot(CompensateIDTO compensateIDTO) {
                return null;
            }
        };
    }
}
