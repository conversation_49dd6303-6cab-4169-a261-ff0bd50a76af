package com.pinshang.qingyun.report.hystrix.sync;

import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.service.sync.SyncStoreInfoToReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 同步客户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@Component
public class SyncStoreInfoToReportClientHystrix implements FallbackFactory<SyncStoreInfoToReportClient> {
    @Override
    public SyncStoreInfoToReportClient create(Throwable throwable) {
        return new SyncStoreInfoToReportClient() {

        	@Override
			public Integer syncStoreInfo(SyncInfoByCodesIDTO idto) {
				return null;
			}
        	
        	@Override
			public DifferenceInfoODTO selectStoreDifferenceInfo() {
				return null;
			}

			@Override
			public List<String> selectMissingStoreCodeList() {
				return null;
			}

        };
    }

}
