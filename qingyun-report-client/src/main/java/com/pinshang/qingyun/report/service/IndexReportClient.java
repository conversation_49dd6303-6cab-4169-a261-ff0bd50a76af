package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.index.*;
import com.pinshang.qingyun.report.hystrix.IndexReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = IndexReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface IndexReportClient {
    /**
     * 查询年月周的四大框数据
     * @param homePageIDTO
     * @return
     */
    @RequestMapping(value = "/indexReport/selectSalesDataHistory", method = RequestMethod.POST)
    SalesDataODTO selectSalesDataHistory(@RequestBody HomePageIDTO homePageIDTO);

    /**
     * 曲线图周月年
     * @param homePageIDTO
     * @return
     */
    @RequestMapping(value = "/indexReport/selectSelectDataGraphicsHistory", method = RequestMethod.POST)
    List<YearOnYearDataODTO> selectSelectDataGraphicsHistory(@RequestBody HomePageIDTO homePageIDTO);

    /**
     * 门店排序周月年
     * @param homePageIDTO
     * @return
     */
    @RequestMapping(value = "/indexReport/selectHistoryStoreSale", method = RequestMethod.POST)
    List<StoreSaleODTO> selectHistoryStoreSale(@RequestBody HomePageIDTO homePageIDTO);

    /**
     * 数据补偿
     * @param compensateIDTO
     * @return
     */
    @RequestMapping(value = "/indexReport/compensateIndex", method = RequestMethod.POST)
    List<IndexPosSalesODTO> compensateIndex(@RequestBody CompensateIDTO compensateIDTO);


    /**
     * 监控商品总表(门店)和前置仓日汇总表
     * @param dateTime
     * @return
     */
    @RequestMapping(value = "/indexReport/monitorXsXdReportDay", method = RequestMethod.POST)
    Boolean monitorXsXdReportDay(@RequestParam("dateTime")String  dateTime);

    /**
     * 根据时间段 补偿数据
     * @param compensateIDTO
     * @return
     */
    @RequestMapping(value = "/indexReport/selectOffHistorySaleByTimeSlot", method = RequestMethod.POST)
    List<IndexPosSalesODTO> selectOffHistorySaleByTimeSlot(@RequestBody CompensateIDTO compensateIDTO);
}
