package com.pinshang.qingyun.report.hystrix.pos;

import com.pinshang.qingyun.report.dto.pos.AnalysisBySynthesisDTO;
import com.pinshang.qingyun.report.dto.pos.AnalysisBySynthesisIDTO;
import com.pinshang.qingyun.report.dto.pos.JoinShopSummaryAmountDTO;
import com.pinshang.qingyun.report.dto.pos.JoinShopSummaryAmountIDTO;
import com.pinshang.qingyun.report.service.pos.JoinShopClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class JoinShopClientHystrix implements FallbackFactory<JoinShopClient> {

    @Override
    public JoinShopClient create(Throwable throwable){
        return new JoinShopClient() {


            @Override
            public List<JoinShopSummaryAmountDTO> getSummaryAmount(JoinShopSummaryAmountIDTO dto) {
                return null;
            }

            @Override
            public List<AnalysisBySynthesisDTO> analysisBySynthesis(AnalysisBySynthesisIDTO dto) {
                return null;
            }
        };
    }
}
