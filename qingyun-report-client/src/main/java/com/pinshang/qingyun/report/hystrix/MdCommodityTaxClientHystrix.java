package com.pinshang.qingyun.report.hystrix;

import com.pinshang.qingyun.report.dto.ShopAutoCommodityTaxDTO;
import com.pinshang.qingyun.report.dto.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.service.MdCommodityTaxClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MdCommodityTaxClientHystrix implements FallbackFactory<MdCommodityTaxClient> {
    @Override
    public MdCommodityTaxClient create(Throwable cause) {
        return new MdCommodityTaxClient() {

            @Override
            public List<ShopCommoditySaleStatisticsODTO> queryShopCommoditySaleList(Long shopId) {
                return null;
            }

            @Override
            public Boolean shopCommodityTax(String timeStamp) {
                return null;
            }

            @Override
            public Boolean shopCommodityTaxMonth(String timeStamp) {
                return null;
            }

            @Override
            public List<Long> queryTopShopCommoditySale(ShopAutoCommodityTaxDTO shopAutoCommodityTaxDTO) {
                return null;
            }

            @Override
            public Boolean shopStockInTax(String timeStamp) {
                return null;
            }

            @Override
            public Integer insertOrUpdateOnline(String time) {
                return null;
            }

            @Override
            public Boolean insertOrUpdateCategorySalesOnLine(String time) {
                return null;
            }
        };
    }
}
