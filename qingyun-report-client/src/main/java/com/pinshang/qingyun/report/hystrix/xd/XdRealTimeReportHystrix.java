package com.pinshang.qingyun.report.hystrix.xd;

import com.pinshang.qingyun.report.service.xd.XdRealTimeReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class XdRealTimeReportHystrix implements FallbackFactory<XdRealTimeReportClient> {
    @Override
    public XdRealTimeReportClient create(Throwable throwable) {
        return new XdRealTimeReportClient() {
            @Override
            public boolean report(String dateTime) {
                return false;
            }

            @Override
            public boolean reportByDb(String dateTime) {
                return false;
            }
        };
    }
}
