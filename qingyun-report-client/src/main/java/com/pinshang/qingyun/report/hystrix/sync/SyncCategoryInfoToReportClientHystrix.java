package com.pinshang.qingyun.report.hystrix.sync;

import java.util.List;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.service.sync.SyncCategoryInfoToReportClient;

import feign.hystrix.FallbackFactory;

/**
 * 同步品类相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年05月06日
 */
@Component
public class SyncCategoryInfoToReportClientHystrix implements FallbackFactory<SyncCategoryInfoToReportClient> {
    @Override
    public SyncCategoryInfoToReportClient create(Throwable throwable) {
        return new SyncCategoryInfoToReportClient() {

			@Override
			public Integer syncCategoryInfo(SyncInfoByCodesIDTO idto) {
				return null;
			}
			
			@Override
			public DifferenceInfoODTO selectCategoryDifferenceInfo() {
				return null;
			}

			@Override
			public List<String> selectMissingCategoryCodeList() {
				return null;
			}

			@Override
			public Integer deleteRedundantCategoryList() {
				return null;
			}

        };
    }

}
