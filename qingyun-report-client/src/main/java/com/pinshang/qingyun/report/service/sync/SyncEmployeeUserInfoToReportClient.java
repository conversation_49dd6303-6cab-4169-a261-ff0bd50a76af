package com.pinshang.qingyun.report.service.sync;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.hystrix.sync.SyncEmployeeUserInfoToReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 同步职员用户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年07月12日
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = SyncEmployeeUserInfoToReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncEmployeeUserInfoToReportClient {
    
	/**
	 * 同步主库相关信息到从库
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/sync/employeeUserInfoTo/syncEmployeeUserInfo", method = RequestMethod.POST)
    public Integer syncEmployeeUserInfo(@RequestBody SyncInfoByCodesIDTO idto);
    
	/**
	 * 查询主从差异信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/employeeUserInfoTo/selectEmployeeUserDifferenceInfo", method = RequestMethod.POST)
	public DifferenceInfoODTO selectEmployeeUserDifferenceInfo();
	
	/**
	 * 查询从库缺失的编码集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/employeeUserInfoTo/selectMissingEmployeeUserCodeList", method = RequestMethod.POST)
	public List<String> selectMissingEmployeeUserCodeList();

}
