package com.pinshang.qingyun.report.hystrix;

import com.pinshang.qingyun.report.dto.report.BreakageEnteringIDTO;
import com.pinshang.qingyun.report.dto.report.BreakagedCommodityListODTO;
import com.pinshang.qingyun.report.dto.report.CommodityInfoODTO;
import com.pinshang.qingyun.report.service.HandBreakageClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class HandBreakageClientHystrix implements FallbackFactory<HandBreakageClient> {


    @Override
    public HandBreakageClient create(Throwable cause) {
        return new HandBreakageClient() {
            @Override
            public CommodityInfoODTO handBreakageBarcode(String barcode, Long shopId, Long stallId) {
                return null;
            }

            @Override
            public CommodityInfoODTO priceTagBarcode(String barcode) {
                return null;
            }

            @Override
            public Boolean entering(BreakageEnteringIDTO idto) {
                return false;
            }

            @Override
            public BreakagedCommodityListODTO breakagedCommodityForPass24Hours() {
                return null;
            }
        };
    }
}
