package com.pinshang.qingyun.report.service;


import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.CloudCommodityReportODTO;
import com.pinshang.qingyun.report.dto.CloudCommodityReportQueryIDTO;
import com.pinshang.qingyun.report.hystrix.CloudCommodityReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = CloudCommodityReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface CloudCommodityReportClient {


    @RequestMapping(value = "/commodityReport/client/realDeliveryReport", method = RequestMethod.POST)
    List<CloudCommodityReportODTO> realDeliveryReport(@RequestBody CloudCommodityReportQueryIDTO dto);
}
