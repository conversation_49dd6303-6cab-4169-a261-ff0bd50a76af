package com.pinshang.qingyun.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/2/23
 */
@Data
public class CloudCommodityReportODTO {

    private Long shopId;
    private String shopCode;
    private String shopName;
    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date orderTime;

    private Long commodityId;
    @ApiModelProperty(value = "商品工厂名称")
    private String commodityFactoryName;
    @ApiModelProperty(value = "商品生产组名称")
    private String commodityWorkshopName;
    @ApiModelProperty(value = "商品车间名称")
    private String commodityFlowshopName;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单位")
    private String commodityUnitName;

    private String barCode;
    @ApiModelProperty("商品条形码")
    private List<String> barCodeList;

    @ApiModelProperty(value = "商品一级分类")
    private String commodityFirstKindName;
    @ApiModelProperty(value = "商品二级分类")
    private String commoditySecondKindName;
    @ApiModelProperty(value = "商品三级分类")
    private String commodityThirdKindName;


    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty("订单数量")
    private BigDecimal quantity;
    @ApiModelProperty("商品金额")
    private BigDecimal amount;
    @ApiModelProperty("实收金额")
    private BigDecimal realAmount;
    @ApiModelProperty("销售成本")
    private BigDecimal weightAmount;
    @ApiModelProperty("实发数量")
    private BigDecimal packageQuantity;
    @ApiModelProperty("实发金额")
    private BigDecimal packageAmount;

}
