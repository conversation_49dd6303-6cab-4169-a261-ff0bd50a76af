package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.ShopAutoCommodityTaxDTO;
import com.pinshang.qingyun.report.dto.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.hystrix.MdCommodityTaxClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = MdCommodityTaxClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface MdCommodityTaxClient {

    @RequestMapping(value = "/shopCommodityTax/queryShopCommoditySaleList/{shopId}", method = RequestMethod.GET)
    List<ShopCommoditySaleStatisticsODTO> queryShopCommoditySaleList(@PathVariable("shopId") Long shopId);

    @RequestMapping(value = "/shopCommodityTax/shopCommodityTax", method = RequestMethod.POST)
    Boolean shopCommodityTax(@RequestParam(value = "timeStamp") String timeStamp);


    /**
     * 商品总表汇总job(只保留31天数据，专为PDA订货用)
     * @param timeStamp
     * @return
     */
    @RequestMapping(value = "/shopCommodityTax/shopCommodityTaxMonth", method = RequestMethod.POST)
    Boolean shopCommodityTaxMonth(@RequestParam(value = "timeStamp") String timeStamp);

    /**
     * 上个月的销量top5(自动订货)
     * @return
     */
    @RequestMapping(value = "/shopCommodityTax/queryTopShopCommoditySale", method = RequestMethod.POST)
    List<Long> queryTopShopCommoditySale(@RequestBody ShopAutoCommodityTaxDTO shopAutoCommodityTaxDTO);

    @RequestMapping(value = "/shopStockIn/shopStockInTax", method = RequestMethod.POST)
    Boolean shopStockInTax(@RequestParam(value = "timeStamp") String timeStamp);

    /**
     * 维护线上客单量和销售金额
     * @param time
     * @return
     */
    @GetMapping("shopCommodityTax/insertOrUpdateOnline")
    Integer insertOrUpdateOnline(@RequestParam("time") String time);

    /**
     * 维护线上大类销售额
     * @param time
     * @return
     */
    @GetMapping("/shopCommodityTax/insertOrUpdateCategorySalesOnLine")
    Boolean insertOrUpdateCategorySalesOnLine(@RequestParam("time") String time);
}
