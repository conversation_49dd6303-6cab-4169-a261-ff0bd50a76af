package com.pinshang.qingyun.report.service.xd;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.hystrix.xd.XdRealTimeReportHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = XdRealTimeReportHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdRealTimeReportClient {

    @GetMapping(value = "/xd/xdRealTimeReport/report")
    boolean report(@RequestParam("dateTime") String dateTime);

    @GetMapping(value = "/xd/xdRealTimeReport/reportByDb")
    boolean reportByDb(@RequestParam("dateTime") String dateTime);
}
