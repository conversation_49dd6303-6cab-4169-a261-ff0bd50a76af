package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.pos.AnalysisBySynthesisDTO;
import com.pinshang.qingyun.report.dto.pos.AnalysisBySynthesisIDTO;
import com.pinshang.qingyun.report.hystrix.pos.JoinShopClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.pinshang.qingyun.report.dto.pos.JoinShopSummaryAmountDTO;
import com.pinshang.qingyun.report.dto.pos.JoinShopSummaryAmountIDTO;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = JoinShopClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface JoinShopClient {


    @RequestMapping(value = "/joinShop/getSummaryAmount", method = RequestMethod.POST)
    List<JoinShopSummaryAmountDTO> getSummaryAmount(@RequestBody JoinShopSummaryAmountIDTO dto);

    @PostMapping("/joinShop/analysisBySynthesis")
    List<AnalysisBySynthesisDTO> analysisBySynthesis(@RequestBody AnalysisBySynthesisIDTO dto);



}
