package com.pinshang.qingyun.report.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/30 12:44
 */
@Data
@NoArgsConstructor
public class PosSalesOrderODTO {

    @ApiModelProperty(value ="1-出库、2-入库")
    private Integer inoutType;

    @ApiModelProperty(value = "业务类型:3-POS销售订单 5-POS销售订单退货")
    private Integer businessType;

    @ApiModelProperty(value = "业务单号")
    private String businessCode;

    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务时间：yyyy-MM-dd HH:mm:ss")
    private Date businessTime;

    @ApiModelProperty(value = "业务明细ID")
    private Long businessOrderItemId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("档口")
    private Long stallId;
}
