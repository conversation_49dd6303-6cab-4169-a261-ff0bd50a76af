package com.pinshang.qingyun.report.hystrix.sync;

import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.service.sync.SyncShopInfoToReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 同步门店相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年12月13日
 */
@Component
public class SyncShopInfoToReportClientHystrix implements FallbackFactory<SyncShopInfoToReportClient> {
    @Override
    public SyncShopInfoToReportClient create(Throwable throwable) {
        return new SyncShopInfoToReportClient() {


			@Override
			public Integer syncShopInfo(SyncInfoByCodesIDTO idto) {
				return null;
			}

			@Override
			public DifferenceInfoODTO selectShopDifferenceInfo() {
				return null;
			}

			@Override
			public List<String> selectMissingShopCodeList() {
				return null;
			}
		};
    }

}
