package com.pinshang.qingyun.report.dto.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: mystery
 * @DateTime: 2025/2/18 15:46
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InventoryCompPosReportODTO {
    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * pos总流水变化量(与进销存符号相反)
     */
    private BigDecimal sumQuantity;

}
