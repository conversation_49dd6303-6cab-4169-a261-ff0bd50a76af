package com.pinshang.qingyun.report.dto.xd;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class XdCommodityTaxODTO {
	private Long shopId;
	private String dateTime;
	private Long commodityId;
	private Long commodityFirstId;
	private Long commoditySecondId;
	private Long commodityThirdId;

	/** 总销售额 */
	private BigDecimal totalSales = BigDecimal.ZERO;

	/** 销售总量 */
	private BigDecimal totalQuanty = BigDecimal.ZERO;

	/** 成本金额合计 */
	private BigDecimal costTotal = BigDecimal.ZERO;


	/** 进货金额 */
	private BigDecimal orderTotal = BigDecimal.ZERO;

	/** 进货数量 */
	private BigDecimal orderQuanty = BigDecimal.ZERO;



	/** 损(过保/临保) */
	private BigDecimal normalBreakageQuantity = BigDecimal.ZERO;

	/** 损(非过保/非临保) */
	private BigDecimal overNormalBreakageQuantity = BigDecimal.ZERO;

}
