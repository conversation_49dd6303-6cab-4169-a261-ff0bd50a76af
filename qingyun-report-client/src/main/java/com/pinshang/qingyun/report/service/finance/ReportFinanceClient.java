package com.pinshang.qingyun.report.service.finance;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderIDTO;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderODTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordIDTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordODTO;
import com.pinshang.qingyun.report.hystrix.finance.ReportFinanceClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 12:34
 * 抓取pos销售流水原始数据 供大店财务 内部往来单据使用
 */

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = ReportFinanceClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ReportFinanceClient {

    /**
     * 抓取pos销售流水原始数据 供大店财务 内部往来单据使用
     * @param idto
     * @return
     */
    @PostMapping("/reportFinance/client/posSalesOrder/list")
    List<PosSalesOrderODTO> selectShopPosSalesOrderList(@RequestBody PosSalesOrderIDTO idto);

    /***
     * 抓取门店报损记录 供大店财务 内部往来单据使用
     * @param idto
     * @return
     */
    @PostMapping("/reportFinance/client/breakageRecord/list")
    List<ShopBreakageRecordODTO> selectBreakageRecordList(@RequestBody ShopBreakageRecordIDTO idto);
}
