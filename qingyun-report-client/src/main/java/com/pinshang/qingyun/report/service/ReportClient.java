package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportIDTO;
import com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportODTO;
import com.pinshang.qingyun.report.dto.pos.PosConsignmentOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.PosConsignmentOrderODTO;
import com.pinshang.qingyun.report.dto.pos.PosSaleWaterODTO;
import com.pinshang.qingyun.report.dto.pos.PosSaleWaterQueryIDTO;
import com.pinshang.qingyun.report.hystrix.ReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = ReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ReportClient {

    @RequestMapping(value = "/reports/etlrun", method = RequestMethod.GET)
    Boolean etlrun(@RequestParam(value = "timeStamp",required = true) String timeStamp, @RequestParam(value = "fileName",required = false) String fileName);

    @RequestMapping(value = "/reports/import/monthlyCashierReport", method = RequestMethod.POST)
    ApiResponse importMonthlyCashierReport(@RequestParam("userId") Long userId, @RequestParam("userName") String userName, @RequestBody List<ClearingChargesItemImport> chargesItems);

    @RequestMapping(value = "/daySaleAim/import/importDaySaleAim", method = RequestMethod.POST)
    Object importDaySaleAim(@RequestBody List<SaleAimIDTO> idtoList);

    @RequestMapping(value = "/monthSaleAim/import/importMonthSaleAim", method = RequestMethod.POST)
    Object importMonthSaleAim(@RequestBody List<SaleAimIDTO> idtoList);

    @RequestMapping(value = "/shopReport/getShortDeliveryRealDeliveryAmount", method = RequestMethod.GET)
    BigDecimal getShortDeliveryRealDeliveryAmount(@RequestParam(value = "timeStamp") String timeStamp);

    @RequestMapping(value = "/shopReport/getCommodityRealDeliveryAmount", method = RequestMethod.GET)
    BigDecimal getCommodityRealDeliveryAmount(@RequestParam(value = "timeStamp") String timeStamp);

    /**
     * pos商品日汇总，客单日汇总，收银流水日汇总   qingyun-report --> commodityDaySalesSummaryReport
     *  表 t_pos_report_day_sales_summary，t_pos_report_guest_list_summary_day，t_pos_report_cashier_water_day
     * @param saleTime
     * @return
     */
    @RequestMapping(value = "/posReports/commodityDaySalesSummaryReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Boolean commodityDaySalesSummaryReport(@RequestParam("saleTime") String saleTime);

    /**
     * pos商品类别汇总
     * @param timeStamp
     * @return
     */
    @RequestMapping(value = "/posReportFix/thirdCateDaySummary", method = RequestMethod.POST)
    Boolean posThirdCateDaySummary(@RequestParam("timeStamp") String timeStamp);

    @RequestMapping(value = "/posReports/cashierWaterDayReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Boolean cashierWaterDayReport(@RequestParam("saleTime") String saleTime);

    @RequestMapping(value = "/posReports/queryCashierWaterDayReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<CashierWaterDayReportODTO> queryCashierWaterDayReport(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime, @RequestParam("type") Integer type);

    @RequestMapping(value = "/shopReport/orderReportMonth", method = RequestMethod.GET)
    Boolean orderReportMonth(@RequestParam(value = "beginTime") String beginTime, @RequestParam(value = "endTime") String endTime);

    @RequestMapping(value = "/shopReport/mdCheckReportMonth", method = RequestMethod.GET)
    Boolean mdCheckReportMonth(@RequestParam(value = "beginTime") String beginTime, @RequestParam(value = "endTime") String endTime);

    @RequestMapping(value = "/reports/bestsellerGoodMonth", method = RequestMethod.POST)
    Boolean bestsellerGoodMonth(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime);

    /**
     * 导入混合收款
     * @param collectReportList
     * @return
     */
    @RequestMapping(value = "/reports/import/importBlendCollectReport", method = RequestMethod.POST)
    Object importBlendCollectReport(@RequestBody List<BlendCollectImportIDTO> collectReportList);

    @RequestMapping(value = "/posReportReturn/replaceByTime/{oneDay}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Boolean replaceByTime(@PathVariable("oneDay") String oneDay);

    // 报损
    @RequestMapping(value = "/hand/breakage/breakageEnteringDay", method = RequestMethod.GET)
    Boolean breakageEnteringDay(@RequestParam(value = "dateTime") String dateTime);


    @RequestMapping(value = "/posReportFix/fixOriginOrderCode", method = RequestMethod.POST)
    Boolean fixOriginOrderCode(@RequestParam(value = "timeStamp") String timeStamp);

    /**
     * 云超实发汇总信息
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/commodityReport/cloudCommodityReportSummary", method = RequestMethod.POST)
    Boolean cloudCommodityReportSummary(@RequestParam(value = "orderTime") String orderTime);


    /**
     * 云超商品报表汇总
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/commodityReport/cloudCommodityInfoReport", method = RequestMethod.POST)
    Boolean cloudCommodityInfoReport(@RequestParam(value = "orderTime") String orderTime);

    /**
     * 及时达负库存报表汇总
     * @return
     */
    @RequestMapping(value = "/reports/shopNegativeStockReportSummary", method = RequestMethod.POST)
    Boolean shopNegativeStockReportSummary();

    /**
     * 云超配货>查询大类销售额
     * @param idto
     * @return
     */
    @RequestMapping(value = "/posReports/queryPosCateSummary", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<ThirdSummaryODTO> queryPosCateSummary(@RequestBody ThirdSummaryIDTO idto);

    /**
     * 订货参考(qingyun-order调用)>查询pos销售数量
     * 只查销售的，不查退货的
     * @param idto
     * @return
     */
    @RequestMapping(value = "/posReports/queryPosSalesWaterData", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<PosSaleWaterODTO> queryPosSalesWaterData(@RequestBody PosSaleWaterQueryIDTO idto);

    /**
     * 查询香烟大类一周销售额
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/posReportsCurrent/queryFirstCateWeekSaleAmount", method = RequestMethod.GET)
    BigDecimal queryFirstCateWeekSaleAmount(@RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="orderTime", required=false) Date orderTime);

    /**
     * 商品销售汇总和收银流水金额对比(job)
     * @param saleTime
     * @return
     */
    @RequestMapping(value = "/posReports/comparePosReportAmount", method = RequestMethod.GET)
    Boolean comparePosReportAmount(@RequestParam("saleTime") String saleTime);

    /**
     * 云集-结算数据
     *提供代销商类型订单数据：
     * 3、POS线下订单，成交金额（包含退货，退货成交金额为负）
     */
    @RequestMapping(value = "/posReports/queryConsignmentOrderSettle", method = RequestMethod.POST)
    List<PosConsignmentOrderODTO> queryConsignmentOrderSettle(@RequestBody PosConsignmentOrderIDTO idto);

    /**
     * 会员号异常使用、手动优惠报表(job)
     * @param saleTime
     * @return
     */
    @RequestMapping(value = "/posReports/exceptionHandleReport", method = RequestMethod.GET)
    Boolean exceptionHandleReport(@RequestParam("saleTime") String saleTime);

    /**
     * 查询t_pos_report_sales_water 销售流水报表某天特定类型商品的变动总数量
     * 查询参数，日期、商品特定分类
     */
    @RequestMapping(value = "/posReports/queryPosReportShopCommoditySumQuantity", method = RequestMethod.POST)
    List<InventoryCompPosReportODTO> queryPosReportShopCommoditySumQuantity(@RequestBody InventoryCompPosReportIDTO idto);


}
