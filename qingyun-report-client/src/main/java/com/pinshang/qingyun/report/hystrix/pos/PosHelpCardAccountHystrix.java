package com.pinshang.qingyun.report.hystrix.pos;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.service.pos.PosHelpCardAccountClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName PosHelpCardTradeHystrix
 * <AUTHOR>
 * @Date 2023/2/28 17:35
 * @Description PosHelpCardTradeHystrix
 * @Version 1.0
 */
@Component
public class PosHelpCardAccountHystrix implements FallbackFactory<PosHelpCardAccountClient> {
    @Override
    public PosHelpCardAccountClient create(Throwable throwable) {
        return new PosHelpCardAccountClient() {
            @Override
            public PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByCompany(HelpCardCheckDetail4InvoiceIDTO idto) {
                return null;
            }

            @Override
            public PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByArea(HelpCardCheckDetail4InvoiceIDTO idto) {
                return null;
            }

            @Override
            public List<HelpCardCheckDetail4InvoiceItemODTO> selectCheckDetail4Invoice(HelpCardCheckDetail4InvoiceItemIDTO idto) {
                return null;
            }

            @Override
            public List<HelpCardPxAmountByStoreIdListODTO> selectPxAmountByStoreIdList(HelpCardPxAmountByStoreIdListIDTO idto) {
                return null;
            }
        };
    }
}
