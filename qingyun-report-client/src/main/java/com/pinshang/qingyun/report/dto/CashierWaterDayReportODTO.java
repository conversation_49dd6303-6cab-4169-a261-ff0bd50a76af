package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class CashierWaterDayReportODTO {

    private String shopId;

    @ApiModelProperty(position = 1, value ="门店编码")
    private String shopCode;

    @ApiModelProperty(position = 2, value ="门店名称")
    private String shopName;

    @ApiModelProperty(position = 3, value ="时间")
    private String saleTime;

    @ApiModelProperty(position = 4, value ="支付类型")
    private String payType;

    @ApiModelProperty(position = 5, value ="支付名称")
    private String payName;

    @ApiModelProperty(position = 6, value ="支付金额")
    private BigDecimal payAmount;


}
