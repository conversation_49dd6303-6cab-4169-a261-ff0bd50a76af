package com.pinshang.qingyun.report.dto.tax;

import java.math.BigDecimal;

public class CommoditySecondCateTaxIDTO {
	private Long id;
    private Long shopId;
    private Long commodityFirstId;
    private Long commoditySecondId;
    private String dateTime;
    private BigDecimal onlineSales =BigDecimal.ZERO;//线上销售额
    private BigDecimal offlineSales =BigDecimal.ZERO;//线下销售额
    private BigDecimal totalSales =BigDecimal.ZERO;//总销售额
    private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额
    private BigDecimal costTotal =BigDecimal.ZERO;//成本金额合计

    private BigDecimal onlineCost =BigDecimal.ZERO;//线上成本金额合计

    private BigDecimal offlineCost =BigDecimal.ZERO;//线下成本金额合计
    private BigDecimal saleReturnOrderTotal =BigDecimal.ZERO;//退货金额
    private BigDecimal stockAdjustAmountTotal =BigDecimal.ZERO;//库存更正金额
    private Integer offlineVisitorNumber =0; //线下来客数
    private BigDecimal offlineAverageAmount =BigDecimal.ZERO; //线下客单价
    private BigDecimal totalQuanty =BigDecimal.ZERO;//销售总量
    private BigDecimal orderQuanty =BigDecimal.ZERO;//进货数量

    public BigDecimal getTotalQuanty() {
        return totalQuanty;
    }

    public void setTotalQuanty(BigDecimal totalQuanty) {
        this.totalQuanty = totalQuanty;
    }

    public BigDecimal getOrderQuanty() {
        return orderQuanty;
    }

    public void setOrderQuanty(BigDecimal orderQuanty) {
        this.orderQuanty = orderQuanty;
    }

    public BigDecimal getOnlineCost() {
        return onlineCost;
    }

    public void setOnlineCost(BigDecimal onlineCost) {
        this.onlineCost = onlineCost;
    }

    public BigDecimal getOfflineCost() {
        return offlineCost;
    }

    public void setOfflineCost(BigDecimal offlineCost) {
        this.offlineCost = offlineCost;
    }

    public Integer getOfflineVisitorNumber() {
        return offlineVisitorNumber;
    }

    public void setOfflineVisitorNumber(Integer offlineVisitorNumber) {
        this.offlineVisitorNumber = offlineVisitorNumber;
    }

    public BigDecimal getOfflineAverageAmount() {
        return offlineAverageAmount;
    }

    public void setOfflineAverageAmount(BigDecimal offlineAverageAmount) {
        this.offlineAverageAmount = offlineAverageAmount;
    }

    public Long getCommodityFirstId() {
        return commodityFirstId;
    }

    public void setCommodityFirstId(Long commodityFirstId) {
        this.commodityFirstId = commodityFirstId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCommoditySecondId() {
        return commoditySecondId;
    }

    public void setCommoditySecondId(Long commoditySecondId) {
        this.commoditySecondId = commoditySecondId;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public BigDecimal getOnlineSales() {
        return onlineSales;
    }

    public void setOnlineSales(BigDecimal onlineSales) {
        this.onlineSales = onlineSales;
    }

    public BigDecimal getOfflineSales() {
        return offlineSales;
    }

    public void setOfflineSales(BigDecimal offlineSales) {
        this.offlineSales = offlineSales;
    }

    public BigDecimal getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }

    public BigDecimal getOrderTotal() {
        return orderTotal;
    }

    public void setOrderTotal(BigDecimal orderTotal) {
        this.orderTotal = orderTotal;
    }

    public BigDecimal getCostTotal() {
        return costTotal;
    }

    public void setCostTotal(BigDecimal costTotal) {
        this.costTotal = costTotal;
    }

    public BigDecimal getSaleReturnOrderTotal() {
        return saleReturnOrderTotal;
    }

    public void setSaleReturnOrderTotal(BigDecimal saleReturnOrderTotal) {
        this.saleReturnOrderTotal = saleReturnOrderTotal;
    }

    public BigDecimal getStockAdjustAmountTotal() {
        return stockAdjustAmountTotal;
    }

    public void setStockAdjustAmountTotal(BigDecimal stockAdjustAmountTotal) {
        this.stockAdjustAmountTotal = stockAdjustAmountTotal;
    }

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
}
