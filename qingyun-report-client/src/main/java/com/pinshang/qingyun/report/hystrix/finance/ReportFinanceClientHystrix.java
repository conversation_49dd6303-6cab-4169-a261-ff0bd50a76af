package com.pinshang.qingyun.report.hystrix.finance;

import com.pinshang.qingyun.report.dto.finance.PosSalesOrderIDTO;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderODTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordIDTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordODTO;
import com.pinshang.qingyun.report.service.finance.ReportFinanceClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 14:06
 */
@Component
public class ReportFinanceClientHystrix implements FallbackFactory<ReportFinanceClient> {
    @Override
    public ReportFinanceClient create(Throwable cause) {
        return new ReportFinanceClient() {
            @Override
            public List<PosSalesOrderODTO> selectShopPosSalesOrderList(PosSalesOrderIDTO idto) {
                return null;
            }

            @Override
            public List<ShopBreakageRecordODTO> selectBreakageRecordList(ShopBreakageRecordIDTO idto) {
                return null;
            }
        };
    }
}
