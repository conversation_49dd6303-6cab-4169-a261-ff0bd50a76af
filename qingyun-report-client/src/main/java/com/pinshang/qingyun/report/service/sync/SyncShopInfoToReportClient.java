package com.pinshang.qingyun.report.service.sync;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.report.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.report.hystrix.sync.SyncShopInfoToReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 同步门店相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年12月13日
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = SyncShopInfoToReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncShopInfoToReportClient {
    
	/**
	 * 同步主库门店相关信息到从库
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/sync/shopInfoTo/syncShopInfo", method = RequestMethod.POST)
    public Integer syncShopInfo(@RequestBody SyncInfoByCodesIDTO idto);
    
	/**
	 * 查询主从门店差异信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/shopInfoTo/selectShopDifferenceInfo", method = RequestMethod.POST)
	public DifferenceInfoODTO selectShopDifferenceInfo();
	
	/**
	 * 查询从库缺失的门店编码集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/shopInfoTo/selectMissingShopCodeList", method = RequestMethod.POST)
	public List<String> selectMissingShopCodeList();

}
