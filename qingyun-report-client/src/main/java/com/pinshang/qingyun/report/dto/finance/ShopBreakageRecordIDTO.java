package com.pinshang.qingyun.report.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/1 13:15
 */
@Data
@NoArgsConstructor
public class ShopBreakageRecordIDTO {
    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务单明细ID 查询起点比如100表示就从第100条开始查询到limitQuantity")
    private Long businessOrderItemId;

    @ApiModelProperty(value = "查询条数")
    private Integer limitQuantity;
    
    public ShopBreakageRecordIDTO(String businessDate, Integer limitQuantity, Long businessOrderItemId) {
    	this.businessDate = businessDate;
    	this.limitQuantity = limitQuantity;
    	this.businessOrderItemId = businessOrderItemId;
    }
    
}
