package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.hystrix.pos.PosHelpCardTradeHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName PosHelpCardTradeClient
 * <AUTHOR>
 * @Date 2023/2/28 17:34
 * @Description PosHelpCardTradeClient
 * @Version 1.0
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = PosHelpCardTradeHystrix.class, configuration = FeignClientConfiguration.class)
public interface PosHelpCardTradeClient {

    @RequestMapping(value = "/posHelpCardTrade/helpCardAutoCheckJob", method = RequestMethod.GET)
    Boolean helpCardAutoCheckJob(@RequestParam("date") String date);
}
