package com.pinshang.qingyun.report.hystrix;

import com.pinshang.qingyun.report.dto.CloudCommodityReportODTO;
import com.pinshang.qingyun.report.dto.CloudCommodityReportQueryIDTO;
import com.pinshang.qingyun.report.service.CloudCommodityReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CloudCommodityReportClientHystrix implements FallbackFactory<CloudCommodityReportClient> {


    @Override
    public CloudCommodityReportClient create(Throwable throwable) {
        return new CloudCommodityReportClient() {
            @Override
            public List<CloudCommodityReportODTO> realDeliveryReport(CloudCommodityReportQueryIDTO dto) {
                return null;
            }
        };
    }
}
