#报表服务

####服务简介
报表服务是支撑清美集团报表相关的核心模块。

#### 主要研发人员
ALL

####git 地址
1. http://*************/pinshang/qingyun-report.git
2. git@*************:pinshang/qingyun-report.git

####jenkins 部署

一。服务部署
1. qingyun-report-service build 配置
   > Root POM = pom.xml
   > Goals and options = clean  package -Dmaven.test.skip=true -pl qingyun-report-service -am

二。Client部署
1. qingyun-report-client build 配置
   > Root POM = pom.xml
   > Goals and options = clean  deploy -Dmaven.test.skip=true -pl qingyun-report-client -am
   
####服务架构
1. springboot + springcloud + redis + kafka + db + eureka + apollo

#### 安装要求
1. pinshang_report库
2. jar包运行
3. 2核，4G，20G最低要求
4. 注册中心eureka
5. 配置中心apollo
6. 鲜食redis
7. kafka集群 