package com.pinshang.qingyun.report.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.search.dto.BreakageEnteringIDTO;
import com.pinshang.qingyun.report.search.hystrix.HandBreakageHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ClassName HandBreakageClient
 * <AUTHOR>
 * @Date 2021/10/25 17:30
 * @Description HandBreakageClient
 * @Version 1.0
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SEARCH_SERVICE, fallbackFactory = HandBreakageHystrix.class, configuration = FeignClientConfiguration.class)
public interface HandBreakageClient {

    @PostMapping(value = "/hand/breakage/entering")
    @ApiOperation(value = "报损录入", notes = "报损录入", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Boolean entering(@RequestBody BreakageEnteringIDTO idto) throws Throwable;
}
