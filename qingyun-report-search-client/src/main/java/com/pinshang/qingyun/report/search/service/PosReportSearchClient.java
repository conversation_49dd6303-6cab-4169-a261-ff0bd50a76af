package com.pinshang.qingyun.report.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.search.dto.PosToEsODTO;
import com.pinshang.qingyun.report.search.hystrix.PosReportSearchClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SEARCH_SERVICE, fallbackFactory = PosReportSearchClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PosReportSearchClient {

    @RequestMapping(value = "/posReports/daySalesSummaryToES", method = RequestMethod.GET)
    Boolean daySalesSummaryToES(@RequestParam(value = "saleTime") String saleTime);

    @RequestMapping(value = "/posReports/shortDeliveryToES", method = RequestMethod.GET)
    Boolean shortDeliveryToES(@RequestParam(value = "saleTime") String saleTime);

    @RequestMapping(value = "/posReports/bOrderFixToES", method = RequestMethod.POST)
    Boolean bOrderFixToES(@RequestBody List<String> orderCodeList);

    /**
     * 获取ES商品日汇总、商品类别汇总 sum金额 用于和原始金额对比
     * key: dayAmount   商品日汇总
     * key: thirdAmount  商品类别汇总
     * @param saleTime
     * @return
     */
    @RequestMapping(value = "/posReports/getPosReportESSum", method = RequestMethod.GET)
    Map<String, BigDecimal> getPosReportESSum(@RequestParam(value = "saleTime") String saleTime);

    /**
     * pos非当日数据，直接保存进es
     * @return
     */
    @RequestMapping(value = "/posReports/posNotCurrentDaySaveES", method = RequestMethod.POST)
    Boolean posNotCurrentDaySaveES(@RequestBody PosToEsODTO posToEsODTO);

    /**
     * 获取ES短交报表 实发总金额
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/posReports/getShortReportESSum", method = RequestMethod.GET)
    BigDecimal getShortReportESSum(@RequestParam(value = "orderTime") String orderTime);

}
