package com.pinshang.qingyun.report.search.dto;

import lombok.Data;


@Data
public class EsDayThirdSaleSummaryODTO {

    private Long id;

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    /** 一级大类ID*/
    private Long commodityFirstId;
    private String commodityFirstCode;
    private String commodityFirstName;

    /** 二级大类ID*/
    private Long commoditySecondId;
    private String commoditySecondCode;
    private String commoditySecondName;

    /** 三级大类ID*/
    private Long commodityThirdId;
    private String commodityThirdCode;
    private String commodityThirdName;

    /** 销售日期*/
    private String saleTime;

    /** 销售数量*/
    private Double saleQuantity = Double.valueOf(0);

    /** 退货数量*/
    private Double returnQuantity = Double.valueOf(0);

    /** 赠送数量*/
    private Double giveQuantity = Double.valueOf(0);

    /** 销售金额*/
    private Double saleAmount = Double.valueOf(0);

    /** 销售成本*/
    private Double weightAmount = Double.valueOf(0);

    /** 退货金额*/
    private Double returnAmount = Double.valueOf(0);

    /** 赠送金额*/
    private Double giveAmount = Double.valueOf(0);

    /** 让利金额*/
    private Double discountAmount = Double.valueOf(0);


    /** 数量小计*/
    private Double tatalQuantity = Double.valueOf(0);

    /** 金额小计*/
    private Double tatalAmount = Double.valueOf(0);

    /** 不含税金额*/
    private Double noTaxRateAmount = Double.valueOf(0);

    /** 税额*/
    private Double taxRateAmount = Double.valueOf(0);

    /** 不含税成本 */
    private Double noTaxWeightAmount = Double.valueOf(0);
}
