package com.pinshang.qingyun.report.search.hystrix;

import com.pinshang.qingyun.report.search.dto.BreakageEnteringIDTO;
import com.pinshang.qingyun.report.search.service.HandBreakageClient;
import feign.hystrix.FallbackFactory;

/**
 * @ClassName HandBreakageHystrix
 * <AUTHOR>
 * @Date 2021/10/25 17:58
 * @Description HandBreakageHystrix
 * @Version 1.0
 */
public class HandBreakageHystrix implements FallbackFactory<HandBreakageClient> {
    @Override
    public HandBreakageClient create(Throwable cause) {
        return new HandBreakageClient() {
            @Override
            public Boolean entering(BreakageEnteringIDTO idto){
                return null;
            }
        };
    }
}
