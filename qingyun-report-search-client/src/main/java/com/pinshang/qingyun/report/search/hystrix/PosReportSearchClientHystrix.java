package com.pinshang.qingyun.report.search.hystrix;

import com.pinshang.qingyun.report.search.dto.PosToEsODTO;
import com.pinshang.qingyun.report.search.service.PosReportSearchClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component
public class PosReportSearchClientHystrix implements FallbackFactory<PosReportSearchClient> {
    @Override
    public PosReportSearchClient create(Throwable cause) {
        return new PosReportSearchClient() {


            @Override
            public Boolean daySalesSummaryToES(String timeStamp) {
                return null;
            }

            @Override
            public Boolean shortDeliveryToES(String saleTime) {
                return null;
            }

            @Override
            public Boolean bOrderFixToES(List<String> orderCodeList) {
                return null;
            }

            @Override
            public Map<String, BigDecimal> getPosReportESSum(String saleTime) {
                return null;
            }

            @Override
            public Boolean posNotCurrentDaySaveES(PosToEsODTO posToEsODTO) {
                return null;
            }

            @Override
            public BigDecimal getShortReportESSum(String orderTime) {
                return null;
            }
        };
    }
}
