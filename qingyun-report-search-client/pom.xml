<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pinshang.qingyun</groupId>
		<artifactId>qingyun-report-parent</artifactId>
		<version>4.0.7-UP-SNAPSHOT</version>
	</parent>
	<artifactId>qingyun-report-search-client</artifactId>
	<properties>
		<java.version>1.8</java.version>
	</properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-mq</artifactId>
            <optional>true</optional>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>org.springframework.cloud</groupId>-->
            <!--<artifactId>spring-cloud-netflix-core</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>io.github.openfeign</groupId>-->
            <!--<artifactId>feign-core</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>io.github.openfeign</groupId>-->
            <!--<artifactId>feign-hystrix</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-base-mvc</artifactId>
            <optional>true</optional>
		</dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-box</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
    </dependencies>
</project>
