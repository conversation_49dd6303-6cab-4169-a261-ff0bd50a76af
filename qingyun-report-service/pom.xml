<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pinshang.qingyun</groupId>
		<artifactId>qingyun-report-parent</artifactId>
		<version>4.0.7-UP-SNAPSHOT</version>
	</parent>
	<artifactId>qingyun-report-service</artifactId>
	<properties>
		<java.version>1.8</java.version>
        <kettle.version>7.1.0.0-12</kettle.version>
		<jsch.version>0.1.55</jsch.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-basic</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-marketing-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-bigdata-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-upload-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-import-plugin</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-json</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>-->
		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-common-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-smm-client</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-health-check</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-data-query</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.baomidou</groupId>
					<artifactId>mybatis-plus-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-mq</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-components-inventory</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-switch</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-loadBalancer</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-common</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-springcloud-common</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-metrics-client</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-file-export-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>spring-cloud-starter-hystrix</artifactId>-->
		<!--</dependency>-->

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>

		<!--<dependency>-->
			<!--<groupId>com.fasterxml.jackson.core</groupId>-->
			<!--<artifactId>jackson-core</artifactId>-->
		<!--</dependency>-->
		<!--<dependency>-->
			<!--<groupId>com.fasterxml.jackson.core</groupId>-->
			<!--<artifactId>jackson-databind</artifactId>-->
		<!--</dependency>-->
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-joda</artifactId>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>com.fasterxml.jackson.module</groupId>-->
			<!--<artifactId>jackson-module-parameter-names</artifactId>-->
		<!--</dependency>-->
		<!--<dependency>-->
		<!--<groupId>org.springframework.boot</groupId>-->
		<!--<artifactId>spring-boot-starter-security</artifactId>-->
		<!--</dependency>-->

		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>-->

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>spring-cloud-starter-feign</artifactId>-->
		<!--</dependency>-->

		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-base-db</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-base-mvc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-box</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-xs-user-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-sync</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-mq</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-renderer</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>

		<dependency>
			<groupId>tk.mybatis</groupId>
			<artifactId>mapper-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>

		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.10</version>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.jolokia</groupId>
			<artifactId>jolokia-core</artifactId>
		</dependency>

		<!--<dependency>-->
		<!--<groupId>io.springfox</groupId>-->
		<!--<artifactId>springfox-swagger2</artifactId>-->
		<!--</dependency>-->
		<!--<dependency>-->
		<!--<groupId>io.springfox</groupId>-->
		<!--<artifactId>springfox-swagger-ui</artifactId>-->
		<!--</dependency>-->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-boot-starter</artifactId>
			<version>${springfox-swagger.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.pentaho</groupId>
			<artifactId>kettle-core</artifactId>
			<version>${kettle.version}</version>
		</dependency>
		<dependency>
			<groupId>org.pentaho</groupId>
			<artifactId>kettle-engine</artifactId>
			<version>${kettle.version}</version>
		</dependency>
		<dependency>
			<groupId>org.pentaho</groupId>
			<artifactId>metastore</artifactId>
			<version>${kettle.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-vfs2</artifactId>
			<version>2.1</version>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-shop-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-shop-admin-client</artifactId>
			<version>${qingyun.shop.version}</version>
		</dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-product-client</artifactId>
        </dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-price-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-storage-client</artifactId>
		</dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-order-client</artifactId>
        </dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-xd-report-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-xd-order-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-xd-wms-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.baomidou</groupId>
					<artifactId>mybatis-plus-boot-starter</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.pinshang.qingyun</groupId>
					<artifactId>qingyun-base</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-xd-promotion-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-weixin-client</artifactId>
		</dependency>

		<!--Hutool Java工具包-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>4.5.7</version>
		</dependency>
		<!--集成logstash-->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>5.3</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>2.1.7</version>
		</dependency>
		<dependency>
			<groupId>com.pinshang.qingyun</groupId>
			<artifactId>qingyun-report-search-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<scope>compile</scope>
		</dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-store-client</artifactId>
			<version>1.2.8-UP-SNAPSHOT</version>
        </dependency>
		<dependency>
			<groupId>qingyun-infrastructure</groupId>
			<artifactId>qingyun-infrastructure-test</artifactId>
			<scope>test</scope>
		</dependency>
    </dependencies>
	<build>
        <plugins>
            <plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.6.6</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
            </plugin>
        </plugins>
    </build>
</project>
