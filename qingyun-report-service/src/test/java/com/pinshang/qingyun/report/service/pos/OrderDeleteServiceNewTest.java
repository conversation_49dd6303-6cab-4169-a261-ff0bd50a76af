package com.pinshang.qingyun.report.service.pos;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pinshang.qingyun.infrastructure.springcloud.common.event.ComponentListener;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.report.client.report.service.PosAdminClient;
import com.pinshang.qingyun.report.dto.pos.MacODTO;
import com.pinshang.qingyun.report.dto.pos.OrderDeleteMessage;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.mapper.EmployeeUserMapper;
import com.pinshang.qingyun.report.mapper.pos.CommodityDeleteMapper;
import com.pinshang.qingyun.report.mapper.pos.OrderDeleteItemMapper;
import com.pinshang.qingyun.report.mapper.pos.OrderDeleteMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.context.annotation.Import;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.List;
import java.util.Arrays;

import static org.mockito.Mockito.when;

@Import(value = {OrderDeleteService.class, OrderDeleteServiceNewTest.MockConfig.class})
@Slf4j
public class OrderDeleteServiceNewTest extends BaseDbUnitTest{


    @Autowired
    private OrderDeleteService orderDeleteService;

    @MockBean
    private PosAdminClient posAdminClient;

    private final PodamFactory podam = new PodamFactoryImpl();

    @MockBeans({
            @MockBean(OrderDeleteMapper.class),
//            @MockBean(PosAdminClient.class),
            @MockBean(ShopMapper.class),
            @MockBean(CommodityMapper.class),
            @MockBean(EmployeeUserMapper.class),
            @MockBean(CommodityDeleteMapper.class),
            @MockBean(OrderDeleteItemMapper.class),
            @MockBean(SMMUserClient.class)
    })
    @TestConfiguration
    static class MockConfig {

    }

    @Override
    protected List<String> getInitSqlScripts() {

        return Arrays.asList("db/data.sql", "db/orderDelete.sql");
    }

    @Test
    void contextLoads() {
        // 测试上下文加载是否成功
        log.info("单测启动成功");
    }

    @Test
    public void saveOrderDelete() {

        MacODTO macODTO = podam.manufacturePojo(MacODTO.class);
        when(posAdminClient.getMacById(101937830053532L)).thenReturn(macODTO);

        String message = "{\"shopId\":4258,\"shopName\":\"POS1鲜家秋月店（112）\",\"posMacCode\":\"053324\",\"orderCode\":805332425072200003,\"totalQuantity\":2.000,\"totalAmount\":6.60,\"deleteDate\":1753159791479,\"employeeId\":101937830053532,\"deleteType\":20,\"orderItems\":[{\"id\":101977539395695,\"createTime\":1753159776000,\"createId\":101937830053532,\"updateTime\":1753159776000,\"updateId\":101937830053532,\"orderId\":101977539395484,\"commodityId\":164069034440166720,\"quantity\":2.000,\"price\":5.00,\"weightPrice\":0.00,\"promotionPrice\":3.80,\"bySalesPrice\":3.80,\"salePrice\":3.30,\"promotionStatus\":0,\"totalAmount\":6.60,\"couponAmount\":0.00,\"returnStatus\":0,\"isWeight\":0,\"isDiscounted\":2,\"posItemTag\":1,\"goBackDel\":0,\"weightCode\":\"300016601000000025\",\"barCode\":\"000166\",\"consignmentId\":-1,\"stallId\":70}],\"suspendId\":101937830053532,\"suspendDate\":1753160001000,\"posMacId\":101937830053532,\"posType\":2}";
        JSONObject jsonObject = JSON.parseObject(message);
        OrderDeleteMessage orderDeleteMessage = JSON.toJavaObject(jsonObject, OrderDeleteMessage.class);
        orderDeleteService.saveOrderDelete(orderDeleteMessage);
    }
}
