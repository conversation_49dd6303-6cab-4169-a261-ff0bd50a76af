package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.service.ReportService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;

/**
 * @ClassName ReportServiceTest
 * <AUTHOR>
 * @Date 2022/10/27 14:02
 * @Description ReportServiceTest
 * @Version 1.0
 */
public class ReportServiceTest extends AbstractJunitBase {
    @Autowired
    private ReportService reportService;

    @Test
    public void categoryAllSalesWeekPercentTest() throws ParseException {
        /*TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserId(1L);
        FastThreadLocalUtil.setQY(tokenInfo);
        CategoryAllSalesWeekPercentIDTO idto = new CategoryAllSalesWeekPercentIDTO();
        idto.setPageNo(1);
        idto.setPageSize(10);
        idto.setSalesDate("2020-11-25");
        reportService.categoryAllSalesWeekPercent(idto);*/
    }
    @Test
    public void categoryAllSalesMonthPercentTest() throws ParseException {
        /*TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserId(1091L);
        FastThreadLocalUtil.setQY(tokenInfo);
        CategoryAllSalesMonthPercentIDTO idto = new CategoryAllSalesMonthPercentIDTO();
        idto.setPageNo(1);
        idto.setPageSize(20);
        idto.setSalesDate("2022-11-14");
        idto.setOrgCode("100098");
        reportService.categoryAllSalesMonthPercent(idto);*/
    }
}
