package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.listener.ReportListener;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @ClassName ReportListenerTest
 * <AUTHOR>
 * @Date 2021/11/5 15:00
 * @Description ReportListenerTest
 * @Version 1.0
 */
@Rollback(value = false)
public class ReportListenerTest extends AbstractJunitBase {
    @Autowired
    private ReportListener reportListener;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Test
    public void saveBreakageEntingTest(){
        /*String message = "{\n" +
                "\t\"wrapper\": \"kafka\",\n" +
                "\t\"type\": \"POS_BREAKAGE_ENTERING_VOLCANO\",\n" +
                "\t\"data\": {\n" +
                "\t\t\"commodityId\": 999965182752699066,\n" +
                "\t\t\"reasonId\": 9131078242860602641,\n" +
                "\t\t\"breakageNum\": 1.00,\n" +
                "\t\t\"breakage\": 1,\n" +
                "\t\t\"stockAreaId\": 1,\n" +
                "\t\t\"breakageChannel\": 2,\n" +
                "\t\t\"referCode\": \"8100121110500502\",\n" +
                "\t\t\"shopId\": 16,\n" +
                "\t\t\"reasonName\": \"损坏\"\n" +
                "\t},\n" +
                "\t\"optionType\": \"INSERT\",\n" +
                "\t\"uuid\": \"84f5ca2d-45b2-4469-ba1a-f1f9953c54a9\",\n" +
                "\t\"keyId\": \"0\"\n" +
                "}";
        reportListener.saveBreakageEnting(message);*/

        //weChatSendMessageService.sendWeChatMessage("测试测试");
    }
}
