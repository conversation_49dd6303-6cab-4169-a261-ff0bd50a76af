package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.dto.BreakageEnteringODTO;
import com.pinshang.qingyun.report.dto.BreakageEnteringSearchIDTO;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.service.BreakageEnteringService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @ClassName BreakageEnteringServiceTest
 * <AUTHOR>
 * @Date 2021/10/26 19:28
 * @Description BreakageEnteringServiceTest
 * @Version 1.0
 */
@Rollback(value = false)
public class BreakageEnteringServiceTest extends AbstractJunitBase {
    @Autowired
    private BreakageEnteringService breakageEnteringService;

    @Test
    public void breakageEnteringListTest(){
        BreakageEnteringSearchIDTO idto = new BreakageEnteringSearchIDTO();
        idto.setShopId(16L);
        idto.setBeginTime("2021-10-26");
        idto.setEndTime("2021-10-26");
        idto.setBreakageChannel(1);
        idto.setReferCode("1");
        idto.setPageNo(1);
        idto.setPageSize(20);
        TablePageInfo<BreakageEnteringODTO> result = breakageEnteringService.breakageEnteringList(idto);
        System.out.println(result.toString());
    }
}
