package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.shop.ShopTaxForJmByCateIDTO;
import com.pinshang.qingyun.report.dto.shop.ShopTaxForJmByCateODTO;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName ShopCommodityTaxServiceTest
 * <AUTHOR>
 * @Date 2022/11/1 15:02
 * @Description ShopCommodityTaxServiceTest
 * @Version 1.0
 */
public class ShopCommodityTaxServiceTest extends AbstractJunitBase {
    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;

    @Test
    public void testInsertOrUpdateCategorySalesOnLine(){
        shopCommodityTaxService.insertOrUpdateCategorySalesOnLine("2022-10-14");
    }

    @Test
    public void shopTaxForJmByCate(){
        ShopTaxForJmByCateIDTO idto = new ShopTaxForJmByCateIDTO();
        idto.setType(1);
//        idto.setDate("2024-07-04");
        TablePageInfo<ShopTaxForJmByCateODTO> result = shopCommodityTaxService.shopTaxForJmByCate(idto);
        System.out.println(result);
    }
}
