package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.xdrealtime.XdRealTimeReport;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @ClassName XdRealTimeReportTest
 * <AUTHOR>
 * @Date 2022/3/3 17:12
 * @Description XdRealTimeReportTest
 * @Version 1.0
 */
public class XdRealTimeReportTest extends AbstractJunitBase {
    @Autowired
    private XdRealTimeReport xdRealTimeReport;

    @Test
    @Rollback(value = false)
    public void reportByDbTest(){
        xdRealTimeReport.reportByDb("2022-02-10");
        System.out.println("==============");
    }
}
