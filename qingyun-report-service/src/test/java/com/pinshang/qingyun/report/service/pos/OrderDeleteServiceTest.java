package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.dto.pos.OrderDeleteIDTO;
import com.pinshang.qingyun.report.dto.pos.OrderDeleteODTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName OrderDeleteServiceTest
 * <AUTHOR>
 * @Date 2021/7/12 17:21
 * @Description OrderDeleteServiceTest
 * @Version 1.0
 */
public class OrderDeleteServiceTest extends AbstractJunitBase {
    @Autowired
    private OrderDeleteService orderDeleteService;

    @Test
    public void listOrderDeletetest(){
        OrderDeleteIDTO idto = new OrderDeleteIDTO();
        idto.setPageSize(10);
        idto.setPageNo(1);
        idto.setBeginDate("2021-07-07");
        idto.setEndDate("2021-07-10");
        //idto.setShopId(16L);
        TablePageInfo<OrderDeleteODTO> result = orderDeleteService.listOrderDelete(idto);
        System.out.println(result.getList().toString());
    }
}
