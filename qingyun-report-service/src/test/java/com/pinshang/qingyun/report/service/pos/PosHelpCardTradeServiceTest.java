package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.box.utils.DateUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.io.IOException;
import java.util.Date;

/**
 * @ClassName PosHelpCardTradeServiceTest
 * <AUTHOR>
 * @Date 2023/2/28 11:19
 * @Description PosHelpCardTradeServiceTest
 * @Version 1.0
 */
public class PosHelpCardTradeServiceTest extends AbstractJunitBase {

    @Autowired
    private PosHelpCardTradeService posHelpCardTradeService;

    @Test
    @Rollback(value = false)
    public void readFileTest() throws IOException {
        Date nowDate = new Date();
        Date yesterday = DateUtil.addDay(nowDate, -1);
        String date = DateUtil.get4yMd(yesterday);
        posHelpCardTradeService.readFile(date);
    }

    @Test
    @Rollback(value = false)
    public void autoCheck(){

        String str = "{\"test\": 12}";
        posHelpCardTradeService.autoCheck("2023-02-20");
    }

    @Test
    @Rollback(value = false)
    public void helpCardAutoCheckJob() throws IOException {
        posHelpCardTradeService.helpCardAutoCheckJob("2023-03-09");
    }
}
