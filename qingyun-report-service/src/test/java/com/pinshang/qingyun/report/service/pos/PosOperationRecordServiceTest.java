package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO;
import com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName PosOperationRecordService
 * <AUTHOR>
 * @Date 2021/7/12 15:05
 * @Description PosOperationRecordService
 * @Version 1.0
 */
public class PosOperationRecordServiceTest extends AbstractJunitBase {
    @Autowired
    private PosOperationRecordService posOperationRecordService;

    @Test
    public void signalCommodityDeleteTest(){
        CommodityDeleteIDTO idto = new CommodityDeleteIDTO();
        idto.setShopId(3L);
        idto.setBeginTime("2021-07-07");
        idto.setEndTime("2021-07-12");
        TablePageInfo<CommodityDeleteODTO> result = posOperationRecordService.signalCommodityDelete(idto);
        System.out.println(result.getList().toString());
    }

}
