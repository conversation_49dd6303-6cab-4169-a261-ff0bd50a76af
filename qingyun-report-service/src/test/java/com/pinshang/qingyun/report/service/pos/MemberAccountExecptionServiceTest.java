package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.AbstractJunitBase;
import com.pinshang.qingyun.report.dto.pos.MemberExceptionUseIDTO;
import com.pinshang.qingyun.report.dto.pos.MemberExceptionUseODTO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName MemberAccountExecptionServiceTest
 * <AUTHOR>
 * @Date 2021/7/13 9:51
 * @Description MemberAccountExecptionServiceTest
 * @Version 1.0
 */
public class MemberAccountExecptionServiceTest extends AbstractJunitBase {
    @Autowired
    private MemberAccountExecptionService memberAccountExecptionService;

    @Test
    public void listMemeberAccountExecptionUserTest(){
       /* MemberExceptionUseIDTO idto = new MemberExceptionUseIDTO();
        idto.setPageSize(10);
        idto.setPageNo(1);
        idto.setShopId(3L);
        PageInfo<MemberExceptionUseODTO> result = memberAccountExecptionService.listMemeberAccountExecptionUser(idto);
        System.out.println(result.getList().toString());*/
    }

}
