package com.pinshang.qingyun;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationReport.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        properties = {
        "application.name.switch=dev-three-",
        "spring.profiles.active=dev"
})
public  abstract class AbstractJunitBase {
    @BeforeClass
    public static void init (){
        //System.setProperty("es.set.netty.runtime.available.processors","false");
        setFastUtil();
    }

    public static void setFastUtil(){
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserId(1L);
        tokenInfo.setShopId(16L);
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(16L);
        shopIdList.add(97L);
        tokenInfo.setShopIdList(shopIdList);
        FastThreadLocalUtil.setQY(tokenInfo);
    }
}