DROP TABLE IF EXISTS `t_md_shop` CASCADE;
CREATE TABLE `t_md_shop` (
                             `id` bigint(32) unsigned NOT NULL,
                             `enterprise_id` bigint(20) unsigned NOT NULL COMMENT '企业id',
                             `store_id` bigint(20) NOT NULL COMMENT '客户id',
                             `shop_code` varchar(32) NOT NULL COMMENT '门店code',
                             `shop_name` varchar(128) NOT NULL COMMENT '门店名称',
                             `shop_short_name` varchar(20) DEFAULT NULL COMMENT '门店简称',
                             `shop_aid` varchar(100) DEFAULT NULL COMMENT '助记码',
                             `shop_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '门店类型：1-门店，2-鲜食店',
                             `management_mode` tinyint(2) DEFAULT NULL COMMENT '经营模式：1-直营、2-外包',
                             `contacts` varchar(20) DEFAULT NULL COMMENT '联系人',
                             `mobile` varchar(20) NOT NULL COMMENT '手机',
                             `phone` varchar(32) DEFAULT NULL COMMENT '电话',
                             `remark` varchar(100) DEFAULT NULL COMMENT '备注',
                             `status` tinyint(2) unsigned DEFAULT NULL COMMENT '状态:0-线上不可见,1-线上可见',
                             `shop_status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '状态:0-停用,1-启用',
                             `country_id` bigint(20) DEFAULT NULL COMMENT '国家id',
                             `province_id` bigint(20) DEFAULT NULL COMMENT '省份id',
                             `city_id` bigint(20) DEFAULT NULL COMMENT '市id',
                             `area_id` bigint(20) DEFAULT NULL COMMENT '区域id',
                             `town_id` bigint(20) DEFAULT NULL COMMENT '乡镇街道ID',
                             `real_detail_address` varchar(100) DEFAULT NULL COMMENT '实际详细地址',
                             `detail_address` varchar(100) DEFAULT NULL COMMENT '详细地址:乡村街道等',
                             `shop_area` decimal(12,2) DEFAULT NULL COMMENT '门店面积',
                             `longitude` decimal(12,6) DEFAULT NULL COMMENT '经度',
                             `latitude` decimal(12,6) DEFAULT NULL COMMENT '纬度',
                             `baidu_longitude` decimal(12,6) DEFAULT NULL COMMENT '百度经度',
                             `baidu_latitude` decimal(12,6) DEFAULT NULL COMMENT '百度纬度',
                             `polygon` text COMMENT '配送范围:多边形:二维经纬度json数据',
                             `shop_img` varchar(200) DEFAULT NULL COMMENT '门店图片',
                             `create_id` bigint(32) unsigned NOT NULL COMMENT '创建人',
                             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_id` bigint(32) unsigned NOT NULL COMMENT '更新人',
                             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `business_license_name` varchar(50) DEFAULT '' COMMENT '营业执照名称',
                             `open_date` date DEFAULT NULL COMMENT '开业日期',
                             `first_on_visible_date` date DEFAULT NULL COMMENT '首次线上可见时间',
                             PRIMARY KEY (`id`),
                             KEY `idx_store_id` (`store_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门店表';

DROP TABLE IF EXISTS `T_EMPLOYEE_USER` CASCADE;
CREATE TABLE T_EMPLOYEE_USER (
                                 EMPLOYEE_ID BIGINT NOT NULL COMMENT '职员ID(smm.t_smm_employee 表主键)',
                                 EMPLOYEE_CODE VARCHAR(50) NOT NULL COMMENT '工号(smm.t_smm_employee 字段)',
                                 EMPLOYEE_NAME VARCHAR(100) NOT NULL COMMENT '真实姓名(smm.t_smm_employee 字段)',
                                 EMPLOYEE_PHONE VARCHAR(20) DEFAULT NULL COMMENT '手机号',
                                 EMPLOYEE_STATE TINYINT NOT NULL COMMENT '职员状态：1-在职、4-离职(smm.t_smm_employee 字段)',
                                 EMPLOYEE_ACCOUNT_STATE TINYINT NOT NULL COMMENT '账号状态：0-未开通、1-已开通、2-已关闭(smm.t_smm_employee 字段)',
                                 USER_ID BIGINT DEFAULT 0 COMMENT '用户ID(smm.t_smm_user 主键)',
                                 IS_DELETED BOOLEAN DEFAULT FALSE COMMENT '用户是否删除(smm.t_smm_user 主键)',
                                 PRIMARY KEY (EMPLOYEE_ID),
                                 CONSTRAINT UNIQUE_EMPLOYEECODE UNIQUE (EMPLOYEE_CODE)
);

DROP TABLE IF EXISTS `t_pos_report_order_delete` CASCADE;
CREATE TABLE `t_pos_report_order_delete` (
                                             `id` bigint(32) NOT NULL AUTO_INCREMENT,
                                             `ref_id` bigint(32) DEFAULT NULL COMMENT '原始记录id',
                                             `shop_id` bigint(32) NOT NULL COMMENT '门店id',
                                             `shop_code` varchar(32) NOT NULL COMMENT '门店code',
                                             `shop_name` varchar(128) NOT NULL COMMENT '门店名称',
                                             `mac_code` varchar(20) NOT NULL COMMENT 'pos机号',
                                             `pos_mac_id` bigint(20) DEFAULT NULL COMMENT 'pos机id',
                                             `pos_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'pos机类型 1-收银pos, 2-自助pos',
                                             `order_code` bigint(20) NOT NULL COMMENT '订单号',
                                             `quantity` decimal(10,3) NOT NULL COMMENT '商品数量',
                                             `total_amount` decimal(14,2) NOT NULL COMMENT '订单金额',
                                             `operate_type` tinyint(2) NOT NULL COMMENT '操作类型：1整单取消 2挂单未结算 3挂单删除',
                                             `order_rest_time` datetime DEFAULT NULL COMMENT '挂单时间',
                                             `order_rest_casher_id` bigint(32) DEFAULT NULL,
                                             `order_rest_casher_code` varchar(20) DEFAULT NULL COMMENT '挂单人账号',
                                             `order_rest_casher_name` varchar(50) DEFAULT NULL COMMENT '挂单人姓名',
                                             `order_delete_time` datetime DEFAULT NULL COMMENT '作废时间',
                                             `order_delete_casher_id` bigint(32) DEFAULT NULL,
                                             `order_delete_casher_code` varchar(20) DEFAULT NULL COMMENT '作废人账号',
                                             `order_delete_casher_name` varchar(50) DEFAULT NULL COMMENT '作废人姓名',
                                             `operate_id` bigint(20) NOT NULL COMMENT '操作人：挂单删除，挂单未结算以挂单人为主;整单取消以作废人为主',
                                             `operate_code` varchar(20) NOT NULL COMMENT '操作人编码：挂单删除，挂单未结算以挂单人为主;整单取消以作废人为主',
                                             `operate_name` varchar(50) NOT NULL COMMENT '操作人姓名：挂单删除，挂单未结算以挂单人为主;整单取消以作废人为主',
                                             `operate_date` date NOT NULL COMMENT '操作时间：挂单删除，挂单未结算以挂单时间为主;整单取消以作废时间为主',
                                             `operate_time` datetime NOT NULL COMMENT '操作时间：挂单删除，挂单未结算以挂单时间为主;整单取消以作废时间为主',
                                             `authorizer_code` varchar(32) DEFAULT NULL COMMENT '授权人账号',
                                             `authorizer_name` varchar(64) DEFAULT NULL COMMENT '授权人姓名',
                                             `create_id` bigint(32) NOT NULL,
                                             `create_time` datetime NOT NULL,
                                             `update_id` bigint(20) NOT NULL,
                                             `update_time` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `idx_shop_id` (`shop_id`) USING BTREE,
                                             KEY `idx_operate_time` (`operate_time`) USING BTREE,
                                             KEY `idx_operate_id` (`operate_id`) USING BTREE,
                                             KEY `idx_order_code` (`order_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='订单作废记录表';

DROP TABLE IF EXISTS `t_commodity` CASCADE;
CREATE TABLE `t_commodity` (
                               `id` bigint(20) unsigned NOT NULL COMMENT '商品ID',
                               `commodity_code` varchar(16) NOT NULL COMMENT '商品编码',
                               `commodity_code_siss` varchar(30) NOT NULL COMMENT '同步到思讯pos自编码',
                               `commodity_name` varchar(100) NOT NULL COMMENT '商品名称',
                               `commodity_spec` varchar(128) NOT NULL COMMENT '商品规格',
                               `commodity_first_kind_id` bigint(20) unsigned NOT NULL COMMENT '商品一级分类ID',
                               `commodity_first_kind_name` varchar(16) NOT NULL COMMENT '商品一级分类名称',
                               `commodity_second_kind_id` bigint(20) unsigned NOT NULL COMMENT '商品二级分类ID',
                               `commodity_second_kind_name` varchar(16) NOT NULL COMMENT '商品二级分类名称',
                               `commodity_third_kind_id` bigint(20) unsigned NOT NULL COMMENT '商品三级分类ID',
                               `commodity_third_kind_name` varchar(160) NOT NULL COMMENT '商品三级分类名称',
                               `commodity_unit_name` varchar(8) NOT NULL COMMENT '商品计量单位',
                               `commodity_factory_id` bigint(20) unsigned DEFAULT NULL COMMENT '商品工厂ID(t_factory表主键)(left join pinshang.t_factory fa on fa.id = c.commodity_factory_id)',
                               `commodity_factory_name` varchar(16) DEFAULT NULL COMMENT '商品工厂名称',
                               `commodity_factory_code` varchar(16) DEFAULT NULL COMMENT '商品工厂编码(t_factory表factory_code)',
                               `commodity_state` tinyint(1) unsigned NOT NULL COMMENT '状态：0-停用,1-启用',
                               `commodity_package_id` bigint(20) unsigned DEFAULT NULL COMMENT '包装类型ID（select * from t_dictionary d where d.dictionary_id = 566968834114367170;）',
                               `commodity_package_name` varchar(50) DEFAULT NULL COMMENT '(整包或散装)',
                               `commodity_package_spec` decimal(10,3) DEFAULT NULL COMMENT '包装规格',
                               `commodity_workshop_id` bigint(20) unsigned DEFAULT NULL COMMENT '商品生产组ID(t_factory_workshop表主键)(left join pinshang.t_factory_workshop fw on fw.id = c.new_workshop_id )',
                               `status` tinyint(1) unsigned NOT NULL COMMENT '淘汰状态:0-淘汰,1-正常',
                               `purchase_status` tinyint(1) unsigned NOT NULL COMMENT '可采状态:1-可采，0-不可采',
                               `commodity_is_quick_freeze` smallint(6) DEFAULT NULL COMMENT '是否速冻产品',
                               `commodity_is_instant` smallint(6) DEFAULT '0' COMMENT '是否即食',
                               `commodity_workshop_name` varchar(16) DEFAULT NULL COMMENT '商品生产组名称',
                               `commodity_workshop_code` varchar(16) DEFAULT NULL COMMENT '商品生产组编码',
                               `commodity_workshop_director` varchar(16) DEFAULT NULL COMMENT '商品生产组的车间主任(t_factory_workshop表director)(left join pinshang.t_factory_workshop fw on fw.id = c.new_workshop_id )',
                               `commodity_flowshop_id` int(11) unsigned DEFAULT NULL COMMENT '商品车间ID',
                               `commodity_flowshop_name` varchar(16) DEFAULT NULL COMMENT '商品车间名称(t_factory_flowshop 表主键)',
                               `tax_rate_id` bigint(20) unsigned DEFAULT NULL COMMENT '税率id',
                               `tax_rate` decimal(10,4) unsigned DEFAULT NULL COMMENT '税率',
                               `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
                               `first_cost` decimal(10,2) DEFAULT NULL COMMENT '生产成本价',
                               `retail_price` decimal(10,2) DEFAULT NULL COMMENT '零售价',
                               `is_weight` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否称重0-不称量,1-称重',
                               `is_summary` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否框汇总(指定商品送货到指定客户时才切隔分拣，主要是用于排序，此商品靠后排序，无其它用途)',
                               `is_frame` tinyint(1) DEFAULT NULL,
                               `bar_code` varchar(32) DEFAULT NULL COMMENT '主条形码',
                               `logistics_model` tinyint(1) unsigned DEFAULT NULL COMMENT '物流配送模式0=直送，1＝配送，2＝直通',
                               `batch_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '批次状态,0:非批次 1:批次',
                               `tax_category_code` varchar(30) DEFAULT NULL COMMENT '税收分类编码',
                               `tax_free_type_id` bigint(20) DEFAULT NULL COMMENT '免税分类（字典表taxFreeType）',
                               `sell_weight` int(11) unsigned DEFAULT NULL COMMENT '起卖重量(克)',
                               `sell_weight_range` int(11) unsigned DEFAULT NULL COMMENT '加重幅度(克)',
                               `box_capacity` decimal(10,2) DEFAULT NULL COMMENT '采购箱规（正数）：用于大仓向供应商采购',
                               `sales_box_capacity` decimal(10,3) DEFAULT NULL COMMENT '销售箱装量（正数）：用于清美向门店销售',
                               `quality_days` int(11) DEFAULT NULL COMMENT '保质期天数',
                               `storage_condition` varchar(1000) DEFAULT NULL COMMENT '保质期贮存条件',
                               `origin` varchar(100) DEFAULT NULL COMMENT '产地',
                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                               `company_id` bigint(20) DEFAULT NULL COMMENT '公司ID：商品 --> 工厂.公司',
                               `stock_type` tinyint(4) DEFAULT NULL COMMENT '1=依据大仓, 2=不限量订货,3=限量供应',
                               `product_type` tinyint(4) DEFAULT NULL COMMENT '商品类型：1-普通商品、2-组合商品、3-资产商品',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `commodity_code` (`commodity_code`),
                               UNIQUE KEY `commodity_code_siss` (`commodity_code_siss`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='新清美订单(一期普通客户订单)子表-宽表(用于统计查询报表)';

DROP TABLE IF EXISTS `t_pos_report_order_delete_item` CASCADE;
CREATE TABLE `t_pos_report_order_delete_item` (
                                                  `id` bigint(32) NOT NULL AUTO_INCREMENT,
                                                  `order_delete_id` bigint(32) NOT NULL,
                                                  `commodity_id` bigint(20) NOT NULL COMMENT '商品id',
                                                  `commodity_code` varchar(16) NOT NULL COMMENT '商品编码',
                                                  `commodity_name` varchar(100) NOT NULL COMMENT '商品名称',
                                                  `commodity_unit` varchar(30) NOT NULL COMMENT '单位',
                                                  `commodity_spec` varchar(128) NOT NULL COMMENT '商品规格',
                                                  `bar_code` varchar(32) NOT NULL COMMENT '主条形码',
                                                  `quanty` decimal(14,3) NOT NULL COMMENT '数量',
                                                  `price` decimal(14,2) NOT NULL COMMENT '单价',
                                                  `sale_price` decimal(14,2) DEFAULT NULL COMMENT '售价',
                                                  `create_id` bigint(20) NOT NULL,
                                                  `create_time` datetime NOT NULL,
                                                  `update_id` bigint(20) NOT NULL,
                                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_order_delete_id` (`order_delete_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=831341 DEFAULT CHARSET=utf8 COMMENT='订单作废记录明细表';






