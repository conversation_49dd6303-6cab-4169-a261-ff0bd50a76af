package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderDeleteDetailODTO {

    @ApiModelProperty("门店code")
    private String shopCode;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("订单号")
    private Long orderCode;

    @ApiModelProperty("收银员编码")
    private String employeeName;
    @ApiModelProperty("收银员姓名")
    private String createName;

    @ApiModelProperty("作废时间")
    private Date orderDeleteTime;

    private List<OrderItem> items;

    @ApiModelProperty("商品数量")
    private BigDecimal orderTotalQuantity;

    @ApiModelProperty("商品数量")
    private BigDecimal orderTotalAmount;


    @Data
    public static class OrderItem {
        private Long id;
        @ApiModelProperty("条码")
        private String barCode;
        @ApiModelProperty("商品名称")
        private String commodityName;
        @ApiModelProperty("数量")
        private BigDecimal quanty;
        @ApiModelProperty("单位")
        private String commodityUnit;
        @ApiModelProperty("单价")
        private BigDecimal price;
        @ApiModelProperty("小计")
        private BigDecimal itemTotalAmount;
    }

}
