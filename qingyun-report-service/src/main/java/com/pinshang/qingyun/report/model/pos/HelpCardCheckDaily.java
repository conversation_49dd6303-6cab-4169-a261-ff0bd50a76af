package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName HelpCardCheckDaily
 * <AUTHOR>
 * @Date 2023/2/28 15:17
 * @Description HelpCardCheckDaily
 * @Version 1.0
 */
@Data
@Table(name = "t_pos_help_card_check_daily")
public class HelpCardCheckDaily {
    @Id
    private Long id;

    private Long areaId;

    private String areaName;

    private Long shopId;

    @ApiModelProperty("设备号")
    private String payCode;

    @ApiModelProperty("政府确认金额")
    private BigDecimal confirmAmount;

    @ApiModelProperty("品鲜交易金额")
    private BigDecimal pxTraderAmount;

    @ApiModelProperty("差异金额")
    private BigDecimal diffAmount;

    @ApiModelProperty("交易日期 yyyy-MM-dd")
    private Date transactionDate;

    private Date createTime;

    private Long createId;

}
