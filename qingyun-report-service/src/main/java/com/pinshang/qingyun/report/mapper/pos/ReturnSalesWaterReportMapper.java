package com.pinshang.qingyun.report.mapper.pos;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.report.dto.pos.CommodityBarCodeDTO;
import com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterIDTO;
import com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ReturnSalesWaterReportMapper {
    /**
     * 从 销售流水报表（t_pos_report_sales_water）copy数据
     *
     * @param start,end
     * @return
     */
    Integer replaceReturnFromSalesBySaleTime(@Param("start") String start, @Param("end")String end);

    /**
     *
     * @param idto
     * @return
     */
    List<ReturnSalesWaterODTO> queryList(ReturnSalesWaterIDTO idto);

    /**
     *
     * @param idto
     * @return
     */
    ReturnSalesWaterODTO sumList(ReturnSalesWaterIDTO idto);

    /**
     * 根据 commodityCode 获取条码列表
     * @param stringList
     * @return
     */
    List<CommodityBarCodeDTO> queryByCommodityCodeList(List<String> stringList);

}
