package com.pinshang.qingyun.report.enums;

/**
 * 支付类型
 * Created by megnday zhang on 2018/5/24.
 */
public enum PaytypeEnum {
    MONEY("ZF025", "现金"),
    MO_LING("ZF027", "抹零"),
    ALIPAY("ZF024", "支付宝"),
    WECHAT("ZF023", "微信"),
    ALIPAY_ACCOUNT("ZF028", "记账(聚合)"),
    WECHAT_ACCOUNT("ZF029", "记账(索迪)"),
    BANK("ZF026", "银行卡"),
    UNIONPAY_ALI("ZF030","支付宝聚合"),
    UNIONPAY_WECHAT("ZF031","微信聚合"),
    BANK_DIRECT("ZF032","银行卡直连"),
    OK_CARD("ZF033","OK卡"),
    UNIONPAY_QUICK_PASS("ZF034","云闪付"),
    INTEGRAL("ZF035","积分支付"),
    SODEXO("ZF022","索迪斯"),
    QM_CARD("ZF040","清美卡"),
    QM_PAY("ZF041","清美支付"),
    QM_ACCOUNT("ZF042","记账(清美)"),

    DISCOUNT_COUPON_CODE("ZF048", "付费券码（抖音团购）"),

    ;

    /** 编码 */
    private String code;

    /** 名称 */
    private String name;

    PaytypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
