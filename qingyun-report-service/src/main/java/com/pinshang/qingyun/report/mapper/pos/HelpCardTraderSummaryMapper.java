package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.HelpCardAccountCheckItem;
import com.pinshang.qingyun.report.model.pos.HelpCardTraderSummary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public interface HelpCardTraderSummaryMapper extends MyMapper<HelpCardTraderSummary> {

    List<HelpCardCashierWaterPageODTO> helpCardCashierWaterPage(HelpCardCashierWaterPageIDTO idto);

    BigDecimal helpCardCashierWaterSum(HelpCardCashierWaterPageIDTO idto);

    List<HelpCardCommoditySummaryODTO> helpCardCommoditySummary(HelpCardCommoditySummaryIDTO idto);

    BigDecimal helpCardCommoditySummarySum(HelpCardCommoditySummaryIDTO idto);

    List<HelpCardShopSummaryODTO> helpCardShopSummary(HelpCardShopSummaryIDTO idto);

    BigDecimal helpCardShopSummarySum(HelpCardShopSummaryIDTO idto);

    List<HelpCardAmountByDate> selectAmountByDate(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<HelpCardCheckSummaryODTO> helpCardCheckSummary(HelpCardCheckSummaryIDTO idto);

    BigDecimal helpCardCheckSummarySum(HelpCardCheckSummaryIDTO idto);

    List<HelpCardAccountCheckItem> selectAmount4CheckItem(@Param("beginTime") String beginTime, @Param("endTime") String endTime , @Param("areaId") Long areaId );

    List<HelpCardPxAmountByStoreIdListODTO> selectPxAmountByStoreIdList(HelpCardPxAmountByStoreIdListIDTO idto);

    int deleteHelpCardTraderSummaryByOrderCode(@Param("orderCode") String orderCode);
}
