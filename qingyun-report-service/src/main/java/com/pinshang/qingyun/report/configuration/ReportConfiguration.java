package com.pinshang.qingyun.report.configuration;

import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheConfiguration;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;

/**
 * Created by weican on 2018-11-14.
 */
@Configuration
@Import(FileCacheConfiguration.class)
public class ReportConfiguration {
    @Resource
    private RedissonClient redissonClient;
    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor(redissonClient);
    }
}
