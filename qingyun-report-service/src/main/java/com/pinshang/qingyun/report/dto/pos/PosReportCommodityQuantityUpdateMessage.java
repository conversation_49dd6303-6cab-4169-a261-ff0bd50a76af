package com.pinshang.qingyun.report.dto.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PosReportCommodityQuantityUpdateMessage {

    private Long itemId;

    private Long shopId;

    private String shopCode;

    private String shopName;

    private String macCode;

    private Long orderCode;

    private Long commodityId;

    /**
     * 1-增加, 2-减少
     */
    private Integer operateType;

    private BigDecimal quantity;

    private BigDecimal commodityPrice;

    private Long casherId;

    private String casherCode;

    private String casherName;

    private String authorizerCode;

    private String authorizerName;

    private Date operateTime;

    private Long posMacId;

    /**
     * 1-收银pos, 2-自助pos
     */
    private Integer posType;
}
