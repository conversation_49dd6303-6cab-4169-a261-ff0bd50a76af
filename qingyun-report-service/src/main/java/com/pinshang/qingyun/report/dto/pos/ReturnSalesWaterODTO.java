package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2021/7/7 18:02
 */
@Data
public class ReturnSalesWaterODTO {
    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;
    @ApiModelProperty("POS机号")
    private String macCode;
    @ApiModelProperty("收银员编号")
    private String employeeNumber;
    @ApiModelProperty("收银员姓名")
    private String createName;
    @ApiModelProperty("交易时间ymd")
    private String saleTime;
    @ApiModelProperty("退货原因")
    private String returnReason;
    @ApiModelProperty("有无原单")
    private String originExist;
    @ApiModelProperty("成交金额")
    private BigDecimal transactionAmount;
    @ApiModelProperty("原单成交金额")
    private BigDecimal originTransactionAmount;
    @ApiModelProperty("后台特价")
    private BigDecimal backgroudBargainPrice;
    @ApiModelProperty("手动议价后")
    private BigDecimal handEnd;
    @ApiModelProperty("订单号")
    private String orderCode;
    @ApiModelProperty("交易时间hms")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String saleTimeHms;
    @ApiModelProperty("货号")
    private String commodityCode;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("品名")
    private String commodityName;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("成交价格")
    private BigDecimal transactionPrice;
    @ApiModelProperty("数量")
    private BigDecimal quantity;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("份数")
    private BigDecimal number;
    @ApiModelProperty("授权人编号")
    private String opUserCode;
    @ApiModelProperty("授权人名称")
    private String opUserName;
    @ApiModelProperty("收银渠道")
    private String transactionChannel;
    @ApiModelProperty("原单交易时间")
    private String originSaleTime;
    @ApiModelProperty("原单交易时间")
    private String originSaleTimeHms;
    @ApiModelProperty("原单号")
    private String originOrderCode;
    @ApiModelProperty("会员卡号")
    private String memberCardNo;
    @ApiModelProperty("会员姓名")
    private String memberName;
    @ApiModelProperty("大类")
    private String commodityFirstKind;
    @ApiModelProperty("中类")
    private String commoditySecondKind;
    @ApiModelProperty("小类")
    private String commodityThirdKind;
    @ApiModelProperty("品牌名称")
    private String brandName;
    @ApiModelProperty("供应商名称")
    private String supplierName;
    @ApiModelProperty("条形码列表")
    private List<String> barCodeList;
}
