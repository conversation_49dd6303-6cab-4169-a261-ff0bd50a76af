package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.report.enums.GiftCardSalesTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GiftCardSalesWaterPage {

    @ApiModelProperty(value = "门店类型")
    @ExcelIgnore
    private Integer shopType;

    @ExcelProperty(value = "门店类型")
    private String shopTypeName;

    @ApiModelProperty(value = "门店编码")
    @ExcelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty(value = "门店名称")
    @ExcelProperty("门店")
    private String shopName;

    @ApiModelProperty(value = "订单号")
    @ExcelProperty("销售单号")
    private String orderCode;

    @ApiModelProperty(value = "流水时间")
    @ExcelProperty("销售时间")
    private Date saleTime;

    @ApiModelProperty(value = "商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty(value = "序列表")
    @ExcelProperty("序列号")
    private String cardSn;

    @ApiModelProperty(value = "卡号")
    @ExcelProperty("卡号")
    private String cardNo;

    @ApiModelProperty(value = "数量")
    @ExcelProperty("数量")
    private Integer num;

    @ApiModelProperty(value = "面值")
    @ExcelProperty("面额")
    private Integer parValue;

    @ApiModelProperty(value = "商品金额")
    @ExcelProperty("收款金额")
    private BigDecimal price;

    @ApiModelProperty(value = "销售渠道 1POS 2团购 3APP 30抖音团购")
    @ExcelIgnore
    private Integer cardSourceType;
    @ExcelProperty("销售渠道")
    private String cardSourceTypeName;

    @ApiModelProperty(value = "员工姓名")
    @ExcelProperty("收银员/核销人")
    private String employeeName;

    @ApiModelProperty(value = "POS机号")
    @ExcelProperty("POS机")
    private String macCode;

    @ApiModelProperty(value = "部门")
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    @ApiModelProperty(value = "计量单位")
    @ExcelIgnore
    private String commodityUnit;

    @ApiModelProperty(value = "条形码")
    @ExcelIgnore
    private String barCode;
    @ExcelIgnore
    private Long shopId;

    public String getShopTypeName() {
        if (null != this.shopType) {
            return  null != ShopTypeEnums.get(this.shopType) ? ShopTypeEnums.get(this.shopType).getName() : null;
        } else {
            return null;
        }
    }

    public String getCardSourceTypeName() {
        if (null != this.cardSourceType) {
            return GiftCardSalesTypeEnum.getEnumByCOde(this.cardSourceType).getDesc();
        } else {
            return null;
        }

    }

}
