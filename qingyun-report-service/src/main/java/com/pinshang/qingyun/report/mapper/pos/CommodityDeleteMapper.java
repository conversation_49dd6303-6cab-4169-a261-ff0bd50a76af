package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.AIMonitorCommodityDTO;
import com.pinshang.qingyun.report.model.pos.CommodityDelete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommodityDeleteMapper extends MyMapper<CommodityDelete> {

    List<AIMonitorCommodityDTO> listCommodityByOrderCode(@Param("orderCode") String orderCode);
}
