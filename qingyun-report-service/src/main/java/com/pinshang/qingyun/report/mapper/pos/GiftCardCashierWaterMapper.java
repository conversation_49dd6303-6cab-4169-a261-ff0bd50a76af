package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterPage;
import com.pinshang.qingyun.report.dto.pos.QueryGiftCardWaterDTO;
import com.pinshang.qingyun.report.model.pos.GiftCardCashierWater;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface GiftCardCashierWaterMapper extends MyMapper<GiftCardCashierWater> {

    List<GiftCardCashierWaterPage> cashierWaterPage(@Param("dto") QueryGiftCardWaterDTO dto);

    GiftCardCashierWaterPage cashierWaterSum(@Param("dto") QueryGiftCardWaterDTO dto);
}
