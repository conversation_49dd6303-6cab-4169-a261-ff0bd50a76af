package com.pinshang.qingyun.report.util;

import com.pinshang.qingyun.base.util.Sequence;
import jodd.util.PropertiesUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;

@Slf4j
public class PosIdWorker {

    private static Sequence worker = new Sequence();

    public static String WIN_PATH = "C:/qingyun-pos/wars/id";
    public static String LINUX_PATH = "/var/local/id";
    private static boolean isLocal;
    private static long workerId;
    private static AtomicLong al = new AtomicLong();
    private static final long tbase = 1555405836955L;

    public static String getPath(){
        String os = System.getProperty("os.name");
        if(os.toLowerCase().startsWith("win")){
            return WIN_PATH;
        }else{
            return LINUX_PATH;
        }
    }


    static {
        try {
            Properties properties = PropertiesUtil.createFromFile(getPath());
            String workerIdStr = properties.getProperty("workerId");
            workerId = Long.parseLong(workerIdStr);
            if(workerId > 9999) {
                throw new RuntimeException("workerId 不能大于 9999");
            }
            isLocal = true;
        } catch (IOException e) {
            log.error(e.getMessage());
            log.error("启动未配置Sequence");
            isLocal = false;
        }
    }

    public PosIdWorker() {
    }

    public static void main(String[] args) {
        IntStream.range(0,1000).forEach((i) -> {
            long id = PosIdWorker.getId();
            System.out.println(id);
        });
    }

    public static long getId() {
        if(isLocal){
            long l = System.currentTimeMillis();
            long aot = al.getAndIncrement() % 10;
            if (aot == 9L){
                l = tilNextMillis(l);
            }
            return workerId * 100_000_000_000_000L + (l - tbase) * 10 + aot;
        }else{
            return worker.nextId();
        }
    }

    private static long tilNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }

        return timestamp;
    }

    public static String getIdStr() {
        return String.valueOf(worker.nextId());
    }

    public static synchronized String get32UUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
