package com.pinshang.qingyun.report.controller.pos;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterPage;
import com.pinshang.qingyun.report.dto.pos.SummaryAmountDTO;
import com.pinshang.qingyun.report.dto.pos.SummaryAmountIDTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.joinShop.*;
import com.pinshang.qingyun.report.service.pos.JoinShopService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/joinShop")
@Slf4j
public class JoinShopController {

    @Autowired
    private JoinShopService joinShopService;

    /**
     * 加盟店 能提现的支付方式 汇总金额
     * @param dto
     * @return
     */
    @PostMapping("/getSummaryAmount")
    public List<SummaryAmountDTO> getSummaryAmount(@RequestBody SummaryAmountIDTO dto) {
        return joinShopService.withDrawAmountList(dto);
    }

    @PostMapping("/singleProductSalesPage")
    @ApiOperation("钱大妈加盟 单品实时销售")
    public TablePageInfo<SingleProductSalesDTO> singleProductSalesPage(@RequestBody SingleProductSalesIDTO dto) {
        return joinShopService.singleProductSalesPage(dto);
    }

    @GetMapping("/export/singleProductSalesPage")
    @ApiOperation("钱大妈加盟 单品实时销售导出")
    public void exportSingleProductSalesPage(SingleProductSalesIDTO dto, HttpServletResponse response) throws IOException {
        dto.initExportPage();
        TablePageInfo<SingleProductSalesDTO> pageInfo = joinShopService.singleProductSalesPage(dto);
        List<SingleProductSalesExportDTO> dataList = new ArrayList<>();
        if (pageInfo.getSize() > 0) {
            SingleProductSalesExportDTO singleProductSalesExportDTO = BeanCloneUtils.copyTo((SingleProductSalesDTO) pageInfo.getHeader(), SingleProductSalesExportDTO.class) ;
            singleProductSalesExportDTO.setCommodityFirstName("合计");

            dataList.add(singleProductSalesExportDTO);
            List<SingleProductSalesExportDTO> list = BeanCloneUtils.copyTo(pageInfo.getList(), SingleProductSalesExportDTO.class);
            dataList.addAll(list);
        }

        try {
            ExcelUtil.setFileNameAndHead( response,"单品实时销售");
            EasyExcel.write(response.getOutputStream(), SingleProductSalesExportDTO.class).autoCloseStream(Boolean.FALSE).sheet("单品实时销售")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("单品实时销售--导出异常",e);
        }
    }

    @PostMapping("/analysisBySynthesis")
    public List<AnalysisBySynthesisDTO> analysisBySynthesis(@RequestBody AnalysisBySynthesisIDTO dto) {
        return joinShopService.analysisBySynthesis(dto);
    }
}
