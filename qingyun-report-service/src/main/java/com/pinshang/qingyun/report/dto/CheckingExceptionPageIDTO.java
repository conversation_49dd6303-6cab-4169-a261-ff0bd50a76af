package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2021/5/21 11:30
 */
@Data
public class CheckingExceptionPageIDTO extends Pagination {

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("月份")
    private String month;
    @ApiModelProperty("支付类型")
    private String receivableName;

    @ApiModelProperty("开始时间")
    private String beginDate;
    @ApiModelProperty("结束时间")
    private String endDate;


}
