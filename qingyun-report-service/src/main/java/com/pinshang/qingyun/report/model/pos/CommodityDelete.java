package com.pinshang.qingyun.report.model.pos;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@Entity
@Table(name = "t_pos_report_commodity_delete")
@NoArgsConstructor
public class CommodityDelete {

   @Id
   private Long id;

   private Long refId;

   private  Long shopId;

   private String shopName;

    /** POS机号 */
   private String macCode;

    /** 商品编码 */
   private String commodityCode;

    /** 商品名称 */
   private String commodityName;

    /** 商品规格 */
   private String commoditySpec;

    /** 商品单位 */
   private String commodityUnit;

    /** 数量 */
   private BigDecimal quantity;

    /** 零售价 */
   private BigDecimal commodityPrice;

    /** 收银员id */
   private Long casherId;

    /** 收银员编码 */
   private String casherCode;

    /**  收银员名称 */
   private String casherName;

    /** 操作时间 */
   private Date operateTime;

    /** 创建时间 */
   private Date createTime;

   /* 订单id */
   private Long orderCode;

    /**
     * 授权人账号
     */
   private String authorizerCode;

    /**
     * 授权人姓名
     */
   private String authorizerName;


    /** pos机id **/
    private Long posMacId;

    /** pos机类型 1-收银pos, 2-自助pos **/
    private Integer posType;
}
