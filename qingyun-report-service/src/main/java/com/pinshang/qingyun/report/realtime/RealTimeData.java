package com.pinshang.qingyun.report.realtime;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class RealTimeData {

    private RealData data1;
    private RealData data2;
    private RealData data3;
    private RealData data4;
    private RealData data5;
    private RealData data6;
    private Data7 data7;
    private Data8 data8;
    private MapCharWrapper data9;
    private MapCharWrapper data10;
}

@Data
class MapCharWrapper{
    private List<MapChart> dataList = new ArrayList<>();
    private String sum;
}

@Data
class MapChart{
    private String id;
    private String name;
    private List<BigDecimal> value;
    private String orders;
}

@Data
class Data7{
    private String yearTotalTurnover;
}

@Data
class Data8{
    private String monthlyOrderQuantity;
    private String monthlyTotalTurnover;
    private String AverageCustomerPrice;
}

@Data
class RealData{
    String title;
    String type;
    List<?> dataList;
    List<String> xAxis;
    List<BigDecimal> series;
}
@Data
class SeriesAndxaxisList {
    List<String> xAxis = new ArrayList<>();
    List<BigDecimal> series = new ArrayList<>();
}
@Data
class TurnoverData{
    String shopId;
    String shopName;
    String shopSale;
    String shopPercentag;
}

@Data
class GroundEffect{
    String shopId;
    String shopName;
    BigDecimal effect;
    BigDecimal shopArea;
}

@Data
class GrossProfit{
    String shopId;
    String shopName;
    String shopSale;
    String shopPercentag;
}

@Data
class CategorySales{
    String categoryName;
    String percentag;
}