package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;


@Table(name = "t_pos_weight_over_price")
@Data
public class WeightOverPrice extends BaseIDPO {


    /** 门店id */
    private Long  shopId;

    /** '交易时间' */
    private Date saleTime;

    /** '成交金额' */
    private BigDecimal transactionAmount;

    /**  '后台零售金额' */
    private BigDecimal  retailAmount;

    /** '超后台零售金额' */
    private BigDecimal  overAmount;

    private Date createTime;
}
