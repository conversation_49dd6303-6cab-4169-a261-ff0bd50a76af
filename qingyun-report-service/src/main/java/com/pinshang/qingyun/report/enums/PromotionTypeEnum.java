package com.pinshang.qingyun.report.enums;

public enum PromotionTypeEnum {
    /*SELL_ONE(1, "促销"),
    SELL_TWO(2, "促销"),
    SELL_THREE(3, "促销"),
    SELL_FOUR(4, "促销"),
    SELL_FIVE(5, "促销"),
    SELL_SIX(6, "促销"),*/
    SELL_SEVEN(7, "特价"),
    SELL_EIGHT(8, "优惠券"),
    HANDLE_BARGAIN(21, "手工议价"),
    HANDLE_DISCOUNT(22, "手工折扣"),
    GIVEN(23, "赠送"),
    ORDER_BARGAIN(24, "整单议价"),
    ORDER_DISCOUNT(25, "整单折扣"),
    ;

    /** 状态 */
    private Integer code;
    /** 描述 */
    private String description;

    PromotionTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
