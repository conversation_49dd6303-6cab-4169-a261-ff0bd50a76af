package com.pinshang.qingyun.report.service.shop;

import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShopCommodityTaxTransService {

    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Async
    public void commodityTax(String day) {
        try {

            shopCommodityTaxService.commodityTax(day);

        }catch (Exception e){
            log.error("商品总表汇总异常: ", e);
            StringBuffer sb = new StringBuffer();
            sb.append("商品总表汇总异常" + day);

            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }
}
