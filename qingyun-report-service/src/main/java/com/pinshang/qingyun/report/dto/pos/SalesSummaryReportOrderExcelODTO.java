package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @see ExcelSheetTitleEnum#BEST_SELLS_REPORT
 *
 * {"一级分类","商品编码","条形码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","平均售价"}
 */
@Data
public class SalesSummaryReportOrderExcelODTO {

    @ExcelProperty("一级分类")
    private String commodityFirstName;
    @ExcelProperty("商品编码")
    private String commodityCode;
    @ExcelProperty("条形码")
    private String barCode;
    @ExcelProperty("商品名称")
    private String commodityName;
    @ExcelProperty("规格")
    private String commoditySpec;
    @ExcelProperty("单位")
    private String commodityUnit;
    @ExcelProperty("销售数量")
    private BigDecimal tatalQuantity;
    @ExcelProperty("销售金额")
    private BigDecimal tatalAmount;
    @ExcelProperty("销售成本")
    private BigDecimal weightAmount;
    @ExcelProperty("毛利额")
    private BigDecimal grossprofitmarginAmount;
    @ExcelProperty("毛利率")
    private String grossprofitrate;
    @ExcelProperty("平均售价")
    private BigDecimal avgSalePrice;

}
