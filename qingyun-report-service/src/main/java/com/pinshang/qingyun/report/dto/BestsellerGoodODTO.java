package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/10/27
 */
@Data
public class BestsellerGoodODTO{

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("大类名称")
    private String commodityFirstName;

    private Long commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("主副条码集合")
    private List<String> barCodeList;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("单位")
    private String commodityUnitName;
    @ApiModelProperty("是否称重 0 否  1 是")
    private Integer isWeight;

    @ApiModelProperty("pos可售状态 0 否  1 是")
    private Integer commoditySaleStatus;


    @ApiModelProperty("库存数量")
    private BigDecimal stockQuantity;
    @ApiModelProperty("库存份数")
    private BigDecimal stockNum;

    @ApiModelProperty("线下销售数量")
    private BigDecimal totalQuantity;


    @ApiModelProperty("缺货商品数")
    private BigDecimal shortCount;
    @ApiModelProperty("总畅销品数")
    private BigDecimal totalCount;
    @ApiModelProperty("畅销品缺货率")
    private String shortPercent;
}
