package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import lombok.Data;

import java.util.List;

@Data
public class CommodityTaxReportIDto extends Pagination {
    private static final long serialVersionUID = 1L;

    private Long enterpriseId;

    private Long shopId;

    /** 一级分类id */
    private Long    cateId1;
    /** 二级分类id */
    private Long    cateId2;
    /** 三级分类id */
    private Long    cateId3;

    private String beginDate;

    @Change(value = DataQueryConstant.NOW)
    private String endDate;

    private String commodityKey;

    private String barCode;

    /** 1=门店汇总  2= 商品汇总  3= 大类汇总  4=中类汇总  5=小类汇总*/
    private int collectType;

    private List<Long> shopIdList;

    private Long commodityId;
}
