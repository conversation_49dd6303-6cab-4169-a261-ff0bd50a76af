package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/12/16
 */
@Data
public class BlendCollectODTO {

    private Long shopId;
    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("门店")
    private String shopName;

    @ApiModelProperty("日期")
    private String collectDate;

    @ApiModelProperty("收款项 1: 聚合  2:现金")
    private Integer collectType;

    @ApiModelProperty("实收金额")
    private BigDecimal collectAmount;

    @ApiModelProperty("手续费")
    private BigDecimal feeAmount;

    @ApiModelProperty("操作人")
    private String createName;

    @ApiModelProperty("导入时间")
    private Date createTime;


    @ApiModelProperty("聚合实收金额")
    private BigDecimal convergeCollectAmount;
    @ApiModelProperty("聚合手续费")
    private BigDecimal convergeFeeAmount;
    @ApiModelProperty("聚合手续费比例")
    private String convergeFeeTaxRate;


    @ApiModelProperty("聚合售卡金额")
    private BigDecimal convergeGiftCardAmount;
    @ApiModelProperty("聚合售卡手续费")
    private BigDecimal convergeGiftCardFeeAmount;
    @ApiModelProperty("聚合售卡实收金额")
    private BigDecimal convergeGiftCardRealAmount;


    @ApiModelProperty("聚合pos实收")
    private BigDecimal convergePosRealAmount;
    @ApiModelProperty("聚合pos手续费")
    private BigDecimal convergePosFeeAmount;



    @ApiModelProperty("现金实收金额")
    private BigDecimal cashCollectAmount;
    @ApiModelProperty("现金售卡金额")
    private BigDecimal cashGiftCardCollectAmount;
    @ApiModelProperty("现金pos实收")
    private BigDecimal cashPosCollectAmount;
}
