package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 商品库存报表(入参)
 * Created by mengday.zhang on 2018/3/29.
 */

@Data
@AllArgsConstructor
public class CommodityStockReportIDto extends Pagination {
    public CommodityStockReportIDto(){}

    @ApiModelProperty(hidden = true)
    private Long enterpriseId;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("一级分类id")
    private Long cateId1;
    @ApiModelProperty("二级分类id")
    private Long cateId2;
    @ApiModelProperty("三级分类id")
    private Long cateId3;

    @ApiModelProperty("商品状态：0-停用，1-启用")
    private Integer commodityState;

    @ApiModelProperty("App状态：0-上架，1-下架")
    private Integer appStatus;

    @ApiModelProperty("编码/名称/助记码")
    private String codeNameAid;

    @ApiModelProperty("条形码")
    private String barCode;

    /** 前7天的起点 */
    private String previous7DayStart;
    /** 前30天的起点 */
    private String previous30DayStart;
    /** 新年的起点 */
    private String newYearStart;
    /** 当前时间的零零点 */
    private String currentDateTimeStart;
}
