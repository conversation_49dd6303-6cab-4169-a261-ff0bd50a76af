package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ConsignmentSalesWaterDTO {

    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门")
    private String orgName;

    @ApiModelProperty(value = "门店编码")
    @ExcelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty(value = "门店名称")
    @ExcelProperty("门店")
    private String shopName;

    @ApiModelProperty(value = "订单号")
    @ExcelProperty("销售单号")
    private String orderCode;

    @ApiModelProperty(value = "流水时间")
    @ExcelProperty("销售时间")
    private Date saleTime;


    @ApiModelProperty(value = "商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty(value = "计量单位")
    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ApiModelProperty(value = "条形码")
    @ExcelProperty("条形码")
    private String barCode;

    @ApiModelProperty(value = "数量")
    @ExcelProperty("数量")
    private Integer num;

    @ApiModelProperty(value = "商品金额")
    @ExcelProperty("销售金额")
    private BigDecimal price;

    @ApiModelProperty(value = "员工姓名")
    @ExcelProperty("收银员")
    private String employeeName;

    @ApiModelProperty(value = "POS机号")
    @ExcelProperty("POS机")
    private String macCode;
}
