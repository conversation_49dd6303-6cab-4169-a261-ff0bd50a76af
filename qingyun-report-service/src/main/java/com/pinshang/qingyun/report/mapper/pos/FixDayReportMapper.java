package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.model.pos.CashierWaterDay;
import com.pinshang.qingyun.report.model.pos.GuestListSummaryDay;
import com.pinshang.qingyun.report.model.pos.DaySaleSummary;
import com.pinshang.qingyun.report.model.pos.ThirdSummary;
import com.pinshang.qingyun.report.model.shop.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface FixDayReportMapper {

    ThirdSummary getThirdSummaryList(@Param("saleTime") String saleTime, @Param("shopId") Long shopId, @Param("commodityThirdId") BigDecimal commodityThirdId);
    int batchUpdateThirdSummary(List<ThirdSummary> updateThirdSummaryList);

    DaySaleSummary getDaySaleSummary(@Param("saleTime") String saleTime,@Param("shopId") Long shopId,@Param("commodityId") Long commodityId);
    int batchUpdateDaySummary(List<DaySaleSummary> updateDaySummaryList);

    GuestListSummaryDay getDayGuestListSummary(@Param("saleTime") String saleTime, @Param("shopId") Long shopId, @Param("macId") Long macId);
    int batchUpdateDayGuestListSummary(List<GuestListSummaryDay> updateGuestListSummaryDay);

    CashierWaterDay getDayCashierWater(@Param("saleTime") String saleTime, @Param("shopId") Long shopId, @Param("employeeNumber") String employeeNumber, @Param("macCode") String macCode, @Param("saleType") String saleType, @Param("payType") String payType);
    int batchUpdateDayCashierWater(List<CashierWaterDay> updateCashierWaterDay);


    CommodityTax getCommodityTax(@Param("saleTime")String saleTime,@Param("shopId") Long shopId,@Param("commodityId") Long commodityId);
    CommodityThirdCateTax getCommodityThirdCateTax(@Param("saleTime")String saleTime,@Param("shopId") Long shopId,@Param("commodityThirdId") Long commodityThirdId);
    CommoditySecondCateTax getCommoditySecondCateTax(@Param("saleTime")String saleTime,@Param("shopId") Long shopId,@Param("commoditySecondId") Long commoditySecondId);
    CommodityFirstCateTax getCommodityFirstCateTax(@Param("saleTime")String saleTime,@Param("shopId") Long shopId,@Param("commodityFirstId") Long commodityFirstId);
    CommodityShopTax getCommodityShopTax(@Param("saleTime")String saleTime,@Param("shopId") Long shopId);

    int batchUpdateCommodityTax(List<CommodityTax> updateCommodityTaxList);
    int batchUpdateCommodityThirdTax(List<CommodityThirdCateTax> updateCommodityTaxList);
    int batchUpdateCommoditySecondTax(List<CommoditySecondCateTax> updateCommodityTaxList);
    int batchUpdateCommodityFirstTax(List<CommodityFirstCateTax> updateCommodityTaxList);
    int batchUpdateCommodityShopTax(List<CommodityShopTax> updateCommodityTaxList);
}
