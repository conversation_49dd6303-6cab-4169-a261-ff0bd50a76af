package com.pinshang.qingyun.report.dto.mdCheck;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class MdCheckReportIDTO extends Pagination {


    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("门店idList")
    private List<Long> shopIdList;

    private Long shopId;

    @ApiModelProperty("方案idList")
    private List<Long> planIdList;




}
