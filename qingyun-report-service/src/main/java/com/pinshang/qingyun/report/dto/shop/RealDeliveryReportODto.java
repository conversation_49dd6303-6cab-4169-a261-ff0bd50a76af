package com.pinshang.qingyun.report.dto.shop;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class RealDeliveryReportODto {

	private String commodityId;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	private String barCode;
	private String barCodes;	// 子码列表

	private String commodityFirstName;

	private String commodityUnitName;

	private BigDecimal price;

	private BigDecimal orderNum;
	private BigDecimal deliveryNum;
	private BigDecimal realDeliveryAmount;
	private BigDecimal differNum;

	private String factoryName;
	private String workshopName;
	private String flowshopName;
	private String warehouseName;
	private String storeTypeName;
}
