package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PosHelpCardAccountCheckingODTO
 * <AUTHOR>
 * @Date 2023/2/23 17:55
 * @Description PosHelpCardAccountCheckingODTO
 * @Version 1.0
 */
@Data
public class HelpCardAccountCheckAddIDTO {
    @ApiModelProperty("区域id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("对账开始时间 yyyy-MM-dd")
    private String checkDateBegin;

    @ApiModelProperty("对账结束时间 yyyy-MM-dd")
    private String checkDateEnd;

    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("对账金额")
    private BigDecimal checkAmount;

}
