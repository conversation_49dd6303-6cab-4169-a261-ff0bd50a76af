package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-01-23
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class ShopActualSummaryIDTO extends Pagination {

    @ApiModelProperty(position = 1, value ="门店id")
    private Long shopId;

    @ApiModelProperty(position = 2, required = true, value ="月份(yyyy-MM)")
    private String month;

    @ApiModelProperty(hidden = true)
    private List<Long> shopIdList;
}