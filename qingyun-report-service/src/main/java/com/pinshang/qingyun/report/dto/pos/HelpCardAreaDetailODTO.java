package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName HelpCardAreaSettingInsertIDTO
 * <AUTHOR>
 * @Date 2023/2/20 15:50
 * @Description HelpCardAreaSettingInsertIDTO
 * @Version 1.0
 */
@Data
public class HelpCardAreaDetailODTO {
    @ApiModelProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("开票主体id-公司id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long invoiceSubjectId;

    @ApiModelProperty("开票主题名称")
    private String companyName;

    @ApiModelProperty("开票抬头")
    private String invoiceTitle;

    @ApiModelProperty("发票类型 1-普票、2-专票")
    private Integer invoiceType;

    @ApiModelProperty("税号")
    private String taxIdentificationNumber;

    @ApiModelProperty("地址电话")
    private String addressPhone;

    @ApiModelProperty("开户账户")
    private String account;

    @ApiModelProperty("手续费比例")
    private String serviceChargeRatio;
}
