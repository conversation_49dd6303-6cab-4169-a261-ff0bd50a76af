package com.pinshang.qingyun.report.dto.shop;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommodityTaxODTO {
    private Long shopId;
    private Long commodityId;
    private Long commodityFirstId;
    private Long commoditySecondId;
    private Long commodityThirdId;
    private Long cateId;

    private Integer visitNum; //来客数
    //订单
    private BigDecimal totalSales =BigDecimal.ZERO;//总销售额
    private BigDecimal totalSalesQuantity =BigDecimal.ZERO; //总销售数量
    private BigDecimal onlineSales =BigDecimal.ZERO;//线上销售额
    private BigDecimal offlineSales =BigDecimal.ZERO;//线下销售额
    private BigDecimal offlineRatio;//线下销售额占比
    private BigDecimal costTotal =BigDecimal.ZERO;//成本金额合计
    private BigDecimal grossProfitRate;//毛利率

    //收货
    private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额
    //退货
    private BigDecimal saleReturnOrderTotal =BigDecimal.ZERO;//退货金额
    //库存调整
    private BigDecimal stockAdjustAmountTotal =BigDecimal.ZERO;//库存更正金额

    private BigDecimal weightPrice =BigDecimal.ZERO; //移动成本价

    private String dateTime;

    //库存
    private BigDecimal stockQuantityPrice;//库存金额
    private BigDecimal stockQuantityDay;//库存天数
    private BigDecimal stockQuantity;//库存
    private BigDecimal negativeStockQuantity;//负库存
}
