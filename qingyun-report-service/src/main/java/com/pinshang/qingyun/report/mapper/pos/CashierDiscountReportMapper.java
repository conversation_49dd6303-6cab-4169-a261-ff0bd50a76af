package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.CashierDiscountReportOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.CashierDiscountReportOrderODTO;
import com.pinshang.qingyun.report.dto.pos.HandDiscountDetailIDTO;
import com.pinshang.qingyun.report.dto.pos.HandDiscountDetailODTO;
import com.pinshang.qingyun.report.model.pos.CashierDiscountReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CashierDiscountReportMapper extends MyMapper<CashierDiscountReport> {

    List<CashierDiscountReportOrderODTO> cashierDiscountReport(@Param("idto") CashierDiscountReportOrderIDTO idto);

    int deleteCashierDiscountReportByOrderCodes(@Param("orderCode") Long orderCodes);

    int deleteCashierDiscountReportByTimeRange(@Param("saleTime") String saleTime,@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<HandDiscountDetailODTO> listHandDiscountDetailReport(@Param("vo") HandDiscountDetailIDTO idto);
    HandDiscountDetailODTO listHandDiscountDetailReportSum(@Param("vo") HandDiscountDetailIDTO idto);
}
