package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.client.report.service.PosReportClient;
import com.pinshang.qingyun.report.dto.ExceptionMemberODTO;
import com.pinshang.qingyun.report.dto.pos.MemberExceptionUseIDTO;
import com.pinshang.qingyun.report.dto.pos.MemberExceptionUseODTO;
import com.pinshang.qingyun.report.mapper.pos.MemberAccountExecptionMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.report.dto.ShopODTO;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class MemberAccountExecptionService {

    @Autowired
    private MemberAccountExecptionMapper memberAccountExecptionMapper;

    @Autowired
    private PosReportClient posReportClient;
    
    @Autowired
    protected SMMUserClient smmUserClient;

    @Autowired
    private ShopMapper shopMapper;

    @Value("#{'${whiteList.nonIntegralUserIdList:}'.split(',')}")
    private List<Long> nonIntegralUserIdList;


    @Transactional(rollbackFor = Exception.class)
    public void insertExecptionMemberReport(Long shopId, Long userId, String cashDate, Long saleCount) {
        // 1. 检查是否存在，不存在插入，存在更新
        Integer count = memberAccountExecptionMapper.findCount(shopId, userId, cashDate);

        ExceptionMemberODTO execptionMember = posReportClient.findMemberAccountExecptionInfo(shopId, userId, cashDate);
        if (count != null && count > 0) {
            memberAccountExecptionMapper.updateSaleCount(shopId, userId, cashDate, saleCount, execptionMember.getSaleTotalAmount());
        } else {
            execptionMember.setSaleCount(saleCount);
            memberAccountExecptionMapper.insertExecptionMember(execptionMember);
        }
    }

    public PageInfo<MemberExceptionUseODTO> listMemeberAccountExecptionUser(MemberExceptionUseIDTO idto) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return new PageInfo<>();
        }
        if(null == idto.getShopId()){
            idto.setShopIdList(shopIdList);
        }else{
            if(!shopIdList.contains(idto.getShopId())){
                return new PageInfo<>();
            }
        }

        if (YesOrNoEnums.YES.getCode().equals(idto.getRemoveAbnormalUser())) {
            idto.setNonIntegralUserIdList(nonIntegralUserIdList);
        }

        PageInfo<MemberExceptionUseODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            memberAccountExecptionMapper.listMemeberAccountExecptionUser(idto);
        });
        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            List<Long> shopIds = pageInfo.getList().stream().map(MemberExceptionUseODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));
            pageInfo.getList().forEach(e -> {
                e.setStoreCode(shopMap.get(e.getShopId()));
            });
        }
        return pageInfo;
    }



    /**
     * 会员号异常使用、手动优惠报表(job)
     * @param saleTime
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean exceptionHandleReport(String saleTime) {

        // 会员号异常使用
        memberAccountExecptionMapper.deleteMemberAccountExecptionBySaleTime(saleTime);
        memberAccountExecptionMapper.insertMemberAccountExecptionReport(saleTime);

        // 手动优惠报表
        memberAccountExecptionMapper.deleteHandDiscountBySaleTime(saleTime);
        memberAccountExecptionMapper.insertHandDiscountReport(saleTime);

        // pos每半个小时销售金额汇总表(目前供大屏使用)
        memberAccountExecptionMapper.deletePosReportHourSummaryBySaleTime(saleTime);
        memberAccountExecptionMapper.insertPosReportHourSummary(saleTime);
        return Boolean.TRUE;
    }
}
