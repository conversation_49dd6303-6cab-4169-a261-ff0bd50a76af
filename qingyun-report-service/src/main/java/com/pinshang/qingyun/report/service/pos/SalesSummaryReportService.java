package com.pinshang.qingyun.report.service.pos;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.PosReportSummaryTypeEnum;
import com.pinshang.qingyun.report.enums.PromotionTypeEnum;
import com.pinshang.qingyun.report.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.mapper.CategoryMapper;
import com.pinshang.qingyun.report.mapper.pos.SalesSummaryReportMapper;
import com.pinshang.qingyun.report.mapper.pos.ThirdSummaryReportMapper;
import com.pinshang.qingyun.report.model.Category;
import com.pinshang.qingyun.report.model.pos.SalesSummaryReport;
import com.pinshang.qingyun.report.model.pos.ThirdSummary;
import com.pinshang.qingyun.report.service.BaseService;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.org.SelectShopParentOrgInfoIDTO;
import com.pinshang.qingyun.smm.dto.org.ShopAndShopParentOrgInfoODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SalesSummaryReportService extends BaseService<SalesSummaryReportMapper,SalesSummaryReport> {

    @Autowired
    private SalesSummaryReportMapper salesSummaryReportMapper;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private ThirdSummaryReportMapper thirdSummaryReportMapper;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Autowired
    private PosReportFixService posReportFixService;
    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 商品销售汇总,类别销售汇总
     * 门店，类别，商品销售汇总分析
     * @param idto
     * @return
     */
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(SalesSummaryReportOrderIDTO idto, Integer type){
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        if (!StringUtil.isNullOrEmpty(idto.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(idto.getOrgCode());
            if (!CollectionUtils.isEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (CollectionUtils.isEmpty(shopIdList)) {
                    TablePageInfo info=new TablePageInfo();
                    info.setList(null);
                    return info;
                }
            }else {
                TablePageInfo info = new TablePageInfo();
                info.setList(null);
                return info;
            }
        }

        idto.setShopIdList(shopIdList);

        // 商品销售汇总 、商品销售汇总分析
        // （汇总类型选择商品汇总（区分门店）时，选择全部门店时，必须输入商品；选择门店时，可以查询全部商品的销售数据）
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode()){
            if(idto.getShopId() == null && idto.getCommodityId() == null && StringUtils.isBlank(idto.getBarCode())){
                QYAssert.isTrue(false,"门店和商品不能全部为空");
            }
        }

        PageInfo<SalesSummaryReportOrderODTO> pageDate = null;
        //类别或者门店汇总方法
        boolean isCateOrShop = null != idto.getSummaryType() && ((idto.getSummaryType() == PosReportSummaryTypeEnum.THREE.getCode() || idto.getSummaryType()== PosReportSummaryTypeEnum.FOUR.getCode() || idto.getSummaryType()== PosReportSummaryTypeEnum.FIVE.getCode() || idto.getSummaryType()== PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType()== PosReportSummaryTypeEnum.SEVEN.getCode() ) );
        if(isCateOrShop){
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                salesSummaryReportMapper.cateShopSalesSummaryReport(idto);
            });
//            if (!CollectionUtils.isEmpty(pageDate.getList()) &&
//                    (idto.getIsWithShop().equals(1) || idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType() == PosReportSummaryTypeEnum.SEVEN.getCode())) {
//                List<Long> shopIds = new ArrayList<>();
//                if (null != idto.getShopId()) {
//                    shopIds.add(idto.getShopId());
//                } else {
//                    shopIds = shopIdList;
//                }
//                Map<Long, ShopAndShopParentOrgInfoODTO> map = getOrgInformation(shopIds);
//                if (null != map) {
//                    pageDate.getList().stream().forEach(e -> {
//                        if (null != map.get(e.getShopId())) {
//                            e.setOrgName(map.get(e.getShopId()).getParentOrgName());
//                        }
//                    });
//                }
//            }
        }else{
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                salesSummaryReportMapper.commoditySalesSummaryReport(idto);
            });
//            if (!CollectionUtils.isEmpty(pageDate.getList()) && idto.getSummaryType().equals(1)) {
//                List<Long> shopIds = new ArrayList<>();
//                if (null != idto.getShopId()) {
//                    shopIds.add(idto.getShopId());
//                } else {
//                    shopIds = shopIdList;
//                }
//                Map<Long, ShopAndShopParentOrgInfoODTO> map = getOrgInformation(shopIds);
//                if (null != map) {
//                    pageDate.getList().stream().forEach(e -> {
//                        if (null != map.get(e.getShopId())) {
//                            e.setOrgName(map.get(e.getShopId()).getParentOrgName());
//                        }
//                    });
//                }
//            }

        }

//        if ((idto.getSummaryType().equals(PosReportSummaryTypeEnum.ONE.getCode()) || idto.getIsWithShop().equals(PosReportWithShopEnum.ONE.getCode()) || idto.getSummaryType() == PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType() == PosReportSummaryTypeEnum.SEVEN.getCode())
//                   && !CollectionUtils.isEmpty(pageDate.getList())) {
//            List<Long> shopIds = new ArrayList<>();
//            if (null != idto.getShopId()) {
//                shopIds.add(idto.getShopId());
//            } else {
//                shopIds = shopIdList;
//            }
//            Map<Long, ShopAndShopParentOrgInfoODTO> map = getOrgInformation(shopIds);
//            if (null != map) {
//                pageDate.getList().stream().forEach(e -> {
//                    if (null != map.get(e.getShopId())) {
//                        e.setOrgName(map.get(e.getShopId()).getParentOrgName());
//                    }
//                });
//            }
//        }


        List<SalesSummaryReportOrderODTO> list=pageDate.getList();
        TotalSalesSummaryODTO total=new TotalSalesSummaryODTO();
        if(!CollectionUtils.isEmpty(list)){
            //获取类别
            List <Category> cateList = categoryMapper.selectAll();
            Map<BigDecimal,Category> cateMap = new HashMap<>();
            for(Category dto:cateList){
                cateMap.put(new BigDecimal(dto.getId()),dto);
            }

            Map<Long,String> barCodeMap=new HashMap<>();
            //isCommodity 查询是否包括商品信息展示
            boolean isCommodity = null != idto.getSummaryType() && (idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode() || idto.getSummaryType()== PosReportSummaryTypeEnum.TWO.getCode());
            if(isCommodity){
                barCodeMap = commodityService.getCommodityBarCodeMap(null);
            }
            //查询销售金额合计
            SalesSummaryReportOrderODTO totalODTO=getTotalSaleAmount(idto,isCateOrShop);
            BigDecimal totalSalesAmount=new BigDecimal(BigInteger.ZERO);
            if(type ==0){
                totalSalesAmount=totalODTO.getSaleAmount();
            }else if(type ==1){
                totalSalesAmount=totalODTO.getTatalAmount();
            }
            for(SalesSummaryReportOrderODTO odto:list){
                Category categoryODto = cateMap.get(odto.getCommodityFirstId());
                if(null != categoryODto){
                    odto.setCommodityFirstName(categoryODto.getCateName());
                    odto.setCateCode(categoryODto.getCateCode());
                }
                if(isCommodity){
                    String barCodes = barCodeMap.get(Long.valueOf(odto.getCommodityId()));
                    odto.setBarCodeList(barCodes);
                    odto.setBarCode(barCodes != null ? barCodes.split(",")[0]:"");
                }

                odto.setGrossprofitmarginAmount(odto.getTatalAmount().subtract(odto.getWeightAmount()));
                if(null !=odto.getTatalAmount() && odto.getTatalAmount().compareTo(BigDecimal.ZERO) != 0){
                    odto.setGrossprofitrate(odto.getGrossprofitmarginAmount().divide(odto.getTatalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }
                if(!isCateOrShop){
                    odto.setNoTaxSaleAmount(odto.getTatalAmount().divide(new BigDecimal(1).add(odto.getTaxRate()),2,BigDecimal.ROUND_HALF_UP));
                    odto.setNoTaxWeightAmount(odto.getWeightAmount().divide(new BigDecimal(1).add(odto.getTaxRate()),2,BigDecimal.ROUND_HALF_UP));
                }else{
                    odto.setNoTaxSaleAmount(odto.getNoTaxRateAmount());
                }
                if(null !=totalSalesAmount && totalSalesAmount.compareTo(BigDecimal.ZERO) != 0){
                    if(type ==0){
                        odto.setSalesAmountPercent(odto.getSaleAmount().divide(totalSalesAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                    }else if(type ==1){
                        odto.setSalesAmountPercent(odto.getTatalAmount().divide(totalSalesAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                    }
                }
            }
            //设置总值
            if(type ==0){
                total.setTotalSaleQuantity(totalODTO.getSaleQuantity());
                total.setTotalReturnQuantity(totalODTO.getReturnQuantity());
                total.setTotalGiveQuantity(totalODTO.getGiveQuantity());
                total.setTotalSaleAmount(totalODTO.getSaleAmount());
                total.setTotalReturnAmount(totalODTO.getReturnAmount());
                total.setTotalGiveAmount(totalODTO.getGiveAmount());
                total.setTotalDiscountAmount(totalODTO.getDiscountAmount());
                total.setTotalQuantity(totalODTO.getTatalQuantity());
                total.setTotalAmount(totalODTO.getTatalAmount());
            }else if(type ==1){
                total.setTotalSaleQuantity(totalODTO.getTatalQuantity());
                total.setTotalSaleAmount(totalODTO.getTatalAmount());
                total.setTotalSaleWeight(totalODTO.getWeightAmount());
                total.setTotalGrossprofitmarginAmount(totalODTO.getTatalAmount().subtract(totalODTO.getWeightAmount()));
            }

        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }

    private Map<Long, ShopAndShopParentOrgInfoODTO> getOrgInformation(List<Long> shopIdList) {
        Map<Long, ShopAndShopParentOrgInfoODTO> map = null;
        SelectShopParentOrgInfoIDTO shopParentOrgInfoIDTO = new SelectShopParentOrgInfoIDTO();
        shopParentOrgInfoIDTO.setShopIdList(shopIdList);
        List<ShopAndShopParentOrgInfoODTO> orgInfoList = orgClient.selectShopAndShopParentOrgInfoList(shopParentOrgInfoIDTO);
        if (null != orgInfoList && orgInfoList.size() > 0) {
            map = orgInfoList.stream().collect(Collectors.toMap(ShopAndShopParentOrgInfoODTO::getShopId, e -> e));
        }
        return map;
    }

    /**
     * 销售汇总  总数
     * @param idto
     * @param isCateOrShop
     * @return
     */
    private SalesSummaryReportOrderODTO getTotalSaleAmount(SalesSummaryReportOrderIDTO idto,boolean isCateOrShop) {
        SalesSummaryReportOrderIDTO saleIDTO=new SalesSummaryReportOrderIDTO();
        BeanUtils.copyProperties(idto,saleIDTO);
        saleIDTO.setSummaryType(null);
        saleIDTO.setIsWithShop(null);
        List<SalesSummaryReportOrderODTO> saleList=new ArrayList<>();
        if(isCateOrShop){
            saleList=salesSummaryReportMapper.cateShopSalesSummaryReport(saleIDTO);
        }else{
            saleList=salesSummaryReportMapper.commoditySalesSummaryReport(saleIDTO);
        }
        return saleList.get(0);
    }

    /**
     * 畅销品排行  (不需要分页)
     * @param idto
     * @return
     */
    public TablePageInfo<SalesSummaryReportOrderODTO> bestSellerReport(SalesSummaryReportOrderIDTO idto){
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), idto.getOrgCode(), shopTypeList, idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }

        idto.setShopIdList(shopIdList);

        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }
        idto.setIsTotal(0);
        List<SalesSummaryReportOrderODTO> list=salesSummaryReportMapper.bestSellerReport(idto);

        TotalSalesSummaryODTO total=new TotalSalesSummaryODTO();
        if(!CollectionUtils.isEmpty(list)){
            Map<Long,String> barCodeMap=commodityService.getCommodityBarCodeMap(null);
            BigDecimal tatalQuantity=BigDecimal.ZERO;
            BigDecimal tatalAmount=BigDecimal.ZERO;
            BigDecimal weightAmount=BigDecimal.ZERO;
            for(SalesSummaryReportOrderODTO odto:list){
                String barCodes=barCodeMap.get(Long.valueOf(odto.getCommodityId()));
                odto.setBarCodeList(barCodes);
                odto.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
                odto.setGrossprofitmarginAmount(odto.getTatalAmount().subtract(odto.getWeightAmount()));
                if(null !=odto.getTatalAmount() && odto.getTatalAmount().compareTo(BigDecimal.ZERO) != 0){
                    odto.setGrossprofitrate(odto.getGrossprofitmarginAmount().divide(odto.getTatalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }
                if(null !=odto.getTatalQuantity() && odto.getTatalQuantity().compareTo(BigDecimal.ZERO) != 0){
                    odto.setAvgSalePrice(odto.getTatalAmount().divide(odto.getTatalQuantity(),2,BigDecimal.ROUND_HALF_UP));
                }
                tatalQuantity=tatalQuantity.add(odto.getTatalQuantity());
                tatalAmount=tatalAmount.add(odto.getTatalAmount());
                weightAmount=weightAmount.add(odto.getWeightAmount());
            }
            //设置总值
            total.setTotalSaleQuantity(tatalQuantity);
            total.setTotalSaleAmount(tatalAmount);
            total.setTotalSaleWeight(weightAmount);
            total.setTotalGrossprofitmarginAmount(tatalAmount.subtract(weightAmount));

        }
        TablePageInfo tablePageInfo = new TablePageInfo();
        tablePageInfo.setHeader(total);
        tablePageInfo.setList(list);
        return tablePageInfo;
    }

    /**
     * 促销销售表
     * @param idto
     * @return
     */
    public TablePageInfo<SalesSummaryReportOrderODTO> salesPromotionReport(SalesSummaryReportOrderIDTO idto){
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList) || (null != idto.getShopId() && !shopIdList.contains(idto.getShopId()))){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }
        PageInfo<SalesSummaryReportOrderODTO> pageDate = null;
        idto.setIsTotal(0);
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            salesSummaryReportMapper.salesPromotionReport(idto);
        });
        List<SalesSummaryReportOrderODTO> list=pageDate.getList();
        TotalSalesSummaryODTO total=new TotalSalesSummaryODTO();
        if(!CollectionUtils.isEmpty(list)){
            Map<Long,String> barCodeMap=commodityService.getCommodityBarCodeMap(null);
            for(SalesSummaryReportOrderODTO dto:list){
                String barCodes=barCodeMap.get(Long.valueOf(dto.getCommodityId()));
                dto.setBarCodeList(barCodes);
                dto.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
                dto.setPromotionType(setPromotionType(dto));
            }

            //设置总值
            SalesSummaryReportOrderODTO totalODTO=getTotalsalesPromotionReport(idto);
            total.setTotalSaleQuantity(totalODTO.getTatalQuantity());
            total.setTotalSaleAmount(totalODTO.getTatalAmount());
            total.setTotalDiscountAmount(totalODTO.getDiscountAmount());
            total.setTotalGiveAmount(totalODTO.getGiveAmount());
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }
    //促销销售表  总数
    public SalesSummaryReportOrderODTO getTotalsalesPromotionReport(SalesSummaryReportOrderIDTO idto){
        SalesSummaryReportOrderIDTO saleIDTO=new SalesSummaryReportOrderIDTO();
        BeanUtils.copyProperties(idto,saleIDTO);
        saleIDTO.setIsTotal(1);
        List<SalesSummaryReportOrderODTO> list=salesSummaryReportMapper.salesPromotionReport(saleIDTO);
        return  list.get(0);
    }
    public String setPromotionType(SalesSummaryReportOrderODTO dto){
        Map<String,String> typeMap=new HashMap<String,String>();
        Map<String,String> promotionMap=new HashMap<String,String>();
        for(PromotionTypeEnum enu: PromotionTypeEnum.values()){
            promotionMap.put(enu.getCode()+"",enu.getDescription());
        }
        String str[] =dto.getPromotionType().split(",");
        StringBuffer sb=new StringBuffer("");
        for(int i=0;i<str.length;i++){
            if(promotionMap.containsKey(str[i])){
                typeMap.put(promotionMap.get(str[i]),promotionMap.get(str[i]));
            }
        }
        if(StringUtils.isNotBlank(dto.getPromotionKey())){
            String promotionKey []=dto.getPromotionKey().split(";");
            for(int i=0;i<promotionKey.length;i++){
                String [] t2=promotionKey[i].split(",");
                typeMap.put(t2[1],t2[1]);
            }
        }
        for (String key : typeMap.keySet()) {
            sb.append(key+",");
        }

        return sb.toString().equals("")?"":sb.toString().substring(0,sb.toString().length()-1);
    }

    @Transactional(rollbackFor = Exception.class)
    public void commodityDaySalesSummaryReport(String saleTime) {
        log.info("-------------------execting commodityDaySalesSummaryReport-------------------");

        // pos商品销售汇总——商品日汇总sum
        // 1. 删除saleTime这天的数据
        salesSummaryReportMapper.deleteDaySalesSummaryReportBySaleTime(saleTime);

        // 2. 重新插入
        salesSummaryReportMapper.insertPosReportDaySalesSummary(saleTime);

        // POS后台/销售汇总/客单分析报表
        salesSummaryReportMapper.deleteGuestListSummaryDayReportBySaleTime(saleTime);
        salesSummaryReportMapper.insertGuestListSummaryDayReportBySaleTime(saleTime);

        salesSummaryReportMapper.deleteCashierWaterDayReportBySaleTime(saleTime);
        salesSummaryReportMapper.insertCashierWaterDayReportBySaleTime(saleTime);
    }

    /**
     * pos 类别日汇总
     * @param day
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean thirdCateDaySummary(String day) {
        //获取时间
        // String day = getDay();

        //删除当前日数据
        Example example = new Example(ThirdSummary.class);
        example.createCriteria().andEqualTo("saleTime", day);
        thirdSummaryReportMapper.deleteByExample(example);

        // 查询日汇总数据
        List<ThirdSummary> list = salesSummaryReportMapper.cateShopSalesDayReport(day);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
            //获取类别
            setThirdSummaryCateCode(list);
            //新增数据
//            thirdSummaryReportMapper.insertList(list);
            batchInsertThirdSummaryReport(list);
        }
        return Boolean.TRUE;
    }

    /**
     * 设置大类、中类、小类的 code
     * @param insertThirdSummaryList
     */
    public void setThirdSummaryCateCode(List<ThirdSummary> insertThirdSummaryList) {
        //获取类别
        Map<BigDecimal, Category> cateMap = getCategoryMap();
        for (ThirdSummary dto : insertThirdSummaryList) {
            dto.setCommodityFirstCode(null != cateMap.get(dto.getCommodityFirstId()) ? cateMap.get(dto.getCommodityFirstId()).getCateCode() : "");
            dto.setCommoditySecondCode(null != cateMap.get(dto.getCommoditySecondId()) ? cateMap.get(dto.getCommoditySecondId()).getCateCode() : "");
            dto.setCommodityThirdCode(null != cateMap.get(dto.getCommodityThirdId()) ? cateMap.get(dto.getCommodityThirdId()).getCateCode() : "");
        }
    }

    /**
     * 获取类别Map
     * @return
     */
    public Map<BigDecimal, Category> getCategoryMap() {
        List<Category> cateList = categoryMapper.selectAll();
        Map<BigDecimal, Category> cateMap = new HashMap<>();
        for (Category dto : cateList) {
            cateMap.put(new BigDecimal(dto.getId()), dto);
        }
        return cateMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertThirdSummaryReport(List<ThirdSummary> cs){
        int index = 0;
        int count = 2000;
        while (true) {
            List<ThirdSummary> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                thirdSummaryReportMapper.insertList(items);
                index += items.size();
            } else {
                break;
            }
        }
    }


    /**
     * 商品销售汇总和收银流水金额对比(job)
     * @param saleTime
     */
    public void comparePosReportAmount(String saleTime) {
        Set<OrderSaleTypeODTO> errorList = new HashSet<>();
        List<OrderSaleTypeODTO> amountErrorList = salesSummaryReportMapper.comparePosReportAmount(saleTime + " 00:00:00", saleTime + " 23:59:59");
        if(!CollectionUtils.isEmpty(amountErrorList)){
            errorList.addAll(amountErrorList);
            weChatSendMessageService.sendWeChatMessage(saleTime + "商品销售汇总和收银流水金额对比异常");
        }

        List<OrderSaleTypeODTO> repeatOrderCodeList = salesSummaryReportMapper.queryPosRepeatOrderCode(saleTime);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(repeatOrderCodeList)){
            errorList.addAll(repeatOrderCodeList);
            String orderCodeList = repeatOrderCodeList.stream().map(item -> item.getOrderCode()).collect(Collectors.joining(","));
            log.warn("pos重复消费,订单号重复  orderCodeList : {}", orderCodeList);
            weChatSendMessageService.sendWeChatMessage(saleTime + "pos重复消费,订单号重复");
        }

        // 删除 t_pos_report_unique
        salesSummaryReportMapper.deleteAllPosReportUnique();

        // 如果异常单子不为空，则先删除。再重新消费
        if(!CollectionUtils.isEmpty(errorList)) {
            errorList.forEach(item -> {
                // 删除
                posReportFixService.deletePosReportByOrderCode(item.getOrderCode());

                // 发送消费消息
                JSONObject data = new JSONObject();
                data.put("orderCode", item.getOrderCode());
                data.put("saleType", item.getSaleType());
                mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.POS_ORDER_PAYMENT_TOPIC,
                        data,
                        MqMessage.MQ_KAFKA,
                        KafkaMessageTypeEnum.POS_ORDER_PAY_OR_RETURN.name(),
                        KafkaMessageOperationTypeEnum.INSERT.name());
            });

        }
        // 删除 t_pos_report_guest_list_summary_10Day 大屏临时查询表
        //salesSummaryReportMapper.deleteGuestListSummary10Day(DateUtil.getDateFormate(DateUtil.addDay(new Date(), -10), "yyyy-MM-dd"));
    }
}
