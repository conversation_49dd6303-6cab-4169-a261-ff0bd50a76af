package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.ShopODTO;
import com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO;
import com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.mapper.pos.CashierWaterReportMapper;
import com.pinshang.qingyun.report.mapper.pos.PosOperationRecordMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class PosOperationRecordService {

    @Autowired
    private PosOperationRecordMapper operationRecordMapper;

    @Autowired
    private GuestListSummaryReportService guestListSummaryReportService;

    @Autowired
    private CashierWaterReportMapper cashierWaterReportMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopMapper shopMapper;

    public TablePageInfo<CommodityDeleteODTO> signalCommodityDelete(CommodityDeleteIDTO idto) {
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
        } else {
            QYAssert.isTrue(false, "操作日期不能为空！");
        }
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo<>();
        }

        int dayDif = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        if(idto.getShopId() == null){
            QYAssert.isTrue(dayDif <= 3, "最大时间跨度为3天");

            if (SpringUtil.isNotEmpty(shopIdList)) {
                idto.setShopIdList(shopIdList);
            }
        } else {
            QYAssert.isTrue(dayDif <= 31, "最大时间跨度为31天");
            if(!shopIdList.contains(idto.getShopId())){
                return new TablePageInfo<>();
            }
        }

        PageInfo<CommodityDeleteODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            operationRecordMapper.signalCommodityDelete(idto);
        });

        if (pageInfo.getSize() > 0) {
            List<Long> shopIds = pageInfo.getList().stream().map(CommodityDeleteODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));
            pageInfo.getList().forEach(e -> {
                e.setStoreCode(shopMap.get(e.getShopId()));
            });
        }

        CommodityDeleteODTO header = operationRecordMapper.signalCommodityDeleteSum(idto);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }

    public TablePageInfo<CommodityDeleteODTO> signalCommodityDeleteTotal(CommodityDeleteIDTO idto) {
        idto.setIsCurrentDay(DateUtils.isCurrentDay(idto.getBeginTime(),idto.getEndTime()));
        QYAssert.isTrue(idto.getType() != null, "查询类型不能为空！");
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
        }

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo<>();
        }
        if(idto.getShopId() == null){
            if (SpringUtil.isNotEmpty(shopIdList)) {
                idto.setShopIdList(shopIdList);
            }
        }else if(!shopIdList.contains(idto.getShopId())){
            return new TablePageInfo<>();
        }

        //以收银表为主表，查询收银的流水
        PageInfo<CommodityDeleteODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            cashierWaterReportMapper.getNetSales(idto);
        });

        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {

            List<Long> shopIds = pageInfo.getList().stream().map(CommodityDeleteODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));

            //查询单品删除的统计
            List<CommodityDeleteODTO> deleteODTOS = operationRecordMapper.signalCommodityDeleteTotal(idto);
            //按照门店汇总 以门店 + pos机类型为维度
            CommodityDeleteODTO one = null;
            if (idto.getType().equals(1)) {
                Map<String, CommodityDeleteODTO> map = deleteODTOS.stream().collect(Collectors.toMap(k ->k.getShopId()+"-"+k.getPosType(), it->it));
                for (CommodityDeleteODTO e : pageInfo.getList()) {
                    e.setStoreCode(shopMap.get(e.getShopId()));
                    one = map.get(e.getShopId()+"-"+e.getPosType());
                    if (one != null) {
                        e.setQuantity(one.getQuantity());
                        e.setCommodityAmount(one.getCommodityAmount());
                        if (null != e.getNetSales() && e.getNetSales().compareTo(BigDecimal.valueOf(0)) != 0) {
                          e.setDeleteSalesRatio(one.getCommodityAmount().multiply(new BigDecimal(100)).divide(e.getNetSales(), 2, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }
            } else if (idto.getType().equals(2)) {
                //2按日期汇总 门店 + 日期+pos机  为维度
                Map<String, CommodityDeleteODTO> map = deleteODTOS.stream().collect(Collectors.toMap(k->k.getShopId().toString()+k.getOperateTime()+"-"+k.getPosType(), it->it));
                for (CommodityDeleteODTO e : pageInfo.getList()) {
                    e.setStoreCode(shopMap.get(e.getShopId()));
                    one = map.get(e.getShopId().toString()+e.getOperateTime()+"-"+e.getPosType());
                    if (one != null) {
                        e.setQuantity(one.getQuantity());
                        e.setCommodityAmount(one.getCommodityAmount());
                        if (null != e.getNetSales() && e.getNetSales().compareTo(BigDecimal.valueOf(0)) != 0) {
                            e.setDeleteSalesRatio(e.getCommodityAmount().multiply(new BigDecimal(100)).divide(e.getNetSales(), 2, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }
            } else if (idto.getType().equals(3)) {
                //3按收银员汇总 门店，日期，收银员
                Map<String, CommodityDeleteODTO> map = deleteODTOS.stream().collect(Collectors.toMap(k->k.getShopId().toString()+k.getOperateTime()+k.getCasherId().toString()+"-"+k.getPosType(), it->it));
                for (CommodityDeleteODTO e : pageInfo.getList()) {
                    e.setStoreCode(shopMap.get(e.getShopId()));
                    one = map.get(e.getShopId().toString()+e.getOperateTime()+e.getCasherId()+"-"+e.getPosType());
                    if (one != null) {
                        e.setQuantity(one.getQuantity());
                        e.setCommodityAmount(one.getCommodityAmount());
                        if (null != e.getNetSales() && e.getNetSales().compareTo(BigDecimal.valueOf(0)) != 0) {
                            e.setDeleteSalesRatio(e.getCommodityAmount().multiply(new BigDecimal(100)).divide(e.getNetSales(), 2, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }
            }

            //获取删除单品的总金额
            CommodityDeleteODTO header = operationRecordMapper.signalCommodityDeleteTotalSum(idto);
            //获取收银的总金额
            if (header == null) {
                header = new CommodityDeleteODTO();
            }
            Double sales = cashierWaterReportMapper.getNetSalesCount(idto);
            if (sales != 0) {
                header.setNetSales(new BigDecimal(sales).setScale(2, BigDecimal.ROUND_HALF_UP));
                if (header.getCommodityAmount() != null) {
                    header.setDeleteSalesRatio(header.getCommodityAmount().multiply(new BigDecimal(100)).divide(header.getNetSales(), 2, BigDecimal.ROUND_HALF_UP));
                }
            }
            TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
            tablePageInfo.setHeader(header);
            return tablePageInfo;
        }
        return null;
    }
}
