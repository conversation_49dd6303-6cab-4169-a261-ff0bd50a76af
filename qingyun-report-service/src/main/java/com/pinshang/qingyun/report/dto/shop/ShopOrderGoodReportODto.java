package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ShopOrderGoodReportODto {

	private Integer shopType;

	@ApiModelProperty("所属部门")
	private String orgName;
	@ApiModelProperty("门店编码")
	private String shopCode;
	@ApiModelProperty("门店名称")
	private String shopName;

	@ApiModelProperty("大类名称")
	private String commodityFirstName;
	@ApiModelProperty("中类名称")
	private String commoditySecondName;
	@ApiModelProperty("小类名称")
	private String commodityThirdName;

	@ApiModelProperty("条形码")
	private String barCode;

	@ApiModelProperty("主副条码集合")
	private List<String> barCodeList;

	private String commodityId;
	@ApiModelProperty("商品编码")
	private String commodityCode;
	@ApiModelProperty("商品名称")
	private String commodityName;
	@ApiModelProperty("规格")
	private String commoditySpec;
	@ApiModelProperty("单位")
	private String commodityUnitName;


	@ApiModelProperty("订货数量")
	private BigDecimal orderNum;
	@ApiModelProperty("发货数量")
	private BigDecimal deliveryNum;
	@ApiModelProperty("实发金额")
	private BigDecimal realDeliveryAmount;
	@ApiModelProperty("差异数量")
	private BigDecimal differNum;


	@ApiModelProperty("工厂")
	private String factoryName;
	@ApiModelProperty("生产组")
	private String workshopName;

	public String getShopTypeName() {
		return ShopTypeEnums.getName(this.shopType);
	}
}
