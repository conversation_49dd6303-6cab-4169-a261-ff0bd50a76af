package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@Data
public class CheckCommodityGroupCompleteIDTO extends Pagination {

    @ApiModelProperty("所属部门编码")
    private String orgCode;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("考核方案开始日期(yyyy-MM-dd)")
    private String planBeginDate;
    @ApiModelProperty("考核方案结束日期(yyyy-MM-dd)")
    private String planEndDate;

    @ApiModelProperty("考核商品组id")
    private Long checkGroupId;

    @ApiModelProperty("考核类型（金额0 / 数量1）")
    private Integer checkType;

    @ApiModelProperty("考核方案编码或者名称")
    private String checkPlanCodeOrName;
}
