package com.pinshang.qingyun.report.controller;

import com.pinshang.qingyun.report.realtime.RealTimeData;
import com.pinshang.qingyun.report.realtime.RealTimeReportService;
import com.pinshang.qingyun.report.realtime.RedisDataInit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/mobile/realTimeSalesMonitoring")
@RestController
@CrossOrigin
public class RealTimeSalesMonitoringController {

    @Autowired
    RealTimeReportService realTimeReportService;

    @Autowired
    RedisDataInit redisDataInit;

    @GetMapping("data")
    public RealTimeData getTopNdata(){
        RealTimeData data = realTimeReportService.getData();
        return data;
    }

    @GetMapping("chartData")
    public RealTimeData getChartData(){
        RealTimeData chartData = realTimeReportService.getChartData();
        return chartData;
    }

    @GetMapping("data/{id}")
    public RealTimeData getShopData(@PathVariable("id") Long id){
        RealTimeData shopData8And7 = realTimeReportService.getShopData8And7(id);
        return shopData8And7;
    }

    @GetMapping("init")
    public Boolean init(){
        return realTimeReportService.init();
    }

    @GetMapping("initToday")
    public Boolean initToday(){
        return redisDataInit.initToDay();
    }

    @GetMapping("initToday2")
    public Boolean initToday2(){
        return redisDataInit.initToDay2();
    }

    @GetMapping("initTodayReturn")
    public Boolean initTodayReturn(){
        return redisDataInit.initTodayReturn();
    }

    @GetMapping("initMonth")
    public Boolean initMonth(){
        return redisDataInit.initMonth();
    }
}
