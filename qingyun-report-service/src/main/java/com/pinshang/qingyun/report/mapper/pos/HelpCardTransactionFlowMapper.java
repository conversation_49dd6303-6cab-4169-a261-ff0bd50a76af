package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.HelpCardTransactionFlow;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName HelpCardTransactionFlowMapper
 * <AUTHOR>
 * @Date 2023/2/27 18:56
 * @Description HelpCardTransactionFlowMapper
 * @Version 1.0
 */
public interface HelpCardTransactionFlowMapper extends MyMapper<HelpCardTransactionFlow> {
    List<HelpCardAmountByDate> selectAmountByDate(@Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("payCode") String payCode);

    BigDecimal selectAmountByDateSum(@Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("payCode") String payCode);

    List<HelpCardTradeWaterPageODTO> helpCardTradeWaterPage(HelpCardTradeWaterPageIDTO idto);
}
