package com.pinshang.qingyun.report.dto.shop;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DaySaleAimODTO {

    private  Long id;
    /**
     * 门店
     */
    private  Long shopId;

    private String shopCode;

    private String shopName;

    /**
     * 日期:(yyyy-MM-dd)
     */
    private String saleTime;

    /**
     * 目标销售额
     */
    private BigDecimal aimSaleAmount;

    /**
     * 备注
     */
    private  String remark;

    /**
     * 昨天及以前的历史记录不允许删除 1，不能删除，修改   0，可以
     */
    private  Integer isHistory;
}
