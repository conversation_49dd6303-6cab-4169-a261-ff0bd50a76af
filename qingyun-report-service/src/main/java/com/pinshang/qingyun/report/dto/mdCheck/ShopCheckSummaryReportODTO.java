package com.pinshang.qingyun.report.dto.mdCheck;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/11/16
 */
@Data
public class ShopCheckSummaryReportODTO {
    @ApiModelProperty("考核商品组")
    private String checkGroupName;

    @ApiModelProperty("各个部门完成率")
    private List<OrgItem> orgCompleteRateList;

    @ApiModelProperty("合计完成率")
    private Integer totalCompleteRate;


    @Data
    public static class OrgItem {
        @ApiModelProperty("组织名")
        private String orgName;
        @ApiModelProperty("完成率")
        private String completeRate;
        @ApiModelProperty("排序")
        private Integer sort;
    }
}
