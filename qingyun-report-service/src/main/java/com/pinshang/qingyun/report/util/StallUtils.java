package com.pinshang.qingyun.report.util;

import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;

import java.util.Objects;

/**
 * @Author: sk
 * @Date: 2024/9/27
 */
public class StallUtils {


    /**
     * 判断是否是大店/普通门店
     *
     * @param managementMode token中的managementMode
     */
    public static Boolean isBigShop(Integer managementMode) {
        return ManagementModeEnums.档口分包.getCode().equals(managementMode);
    }

    /**
     * 判断是否是单门店/总部 登录
     *
     * @param tokenShopId token中的shopId
     */
    public static Boolean isSingleShop(Long tokenShopId) {
        return Objects.nonNull(tokenShopId);
    }
}
