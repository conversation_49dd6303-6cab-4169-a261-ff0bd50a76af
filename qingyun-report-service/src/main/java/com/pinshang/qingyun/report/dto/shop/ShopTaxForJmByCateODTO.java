package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/07/03
 * @Version 1.0
 */
@Data
public class ShopTaxForJmByCateODTO{
    @ExcelIgnore
    private Long shopId;

    @ExcelProperty("门店")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopCode,keyName = "shopId")
    private String shopCode;

    @ExcelIgnore
    @ApiModelProperty("大类id")
    private Long commodityFirstKindId;

    @ApiModelProperty("大类")
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateName,keyName = "commodityFirstKindId")
    private String commodityFirstKindName;

    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateCode,keyName = "commodityFirstKindId")
    private String commodityFirstKindCode;

    @ExcelIgnore
    @ApiModelProperty("中类id")
    private Long commoditySecondKindId;

    @ExcelIgnore
    @ApiModelProperty("中类")
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateName,keyName = "commoditySecondKindId")
    private String commoditySecondKindName;

    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateCode,keyName = "commoditySecondKindId")
    private String commoditySecondKindCode;

    @ExcelIgnore
    @ApiModelProperty("小类id")
    private Long commodityThirdKindId;

    @ExcelIgnore
    @ApiModelProperty("小类")
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateName,keyName = "commodityThirdKindId")
    private String commodityThirdKindName;

    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateCode,keyName = "commodityThirdKindId")
    private String commodityThirdKindCode;

    @ExcelIgnore
    private Long commodityId;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityCode,keyName = "commodityId")
    private String commodityCode;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commoditySpec,keyName = "commodityId")
    private String commoditySpec;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityUnit,keyName = "commodityId")
    private String commodityUnitName;


    @ExcelProperty("销售金额")
    @ApiModelProperty("线下销售金额")
    private BigDecimal totalSales=BigDecimal.ZERO;

    @ExcelProperty("进货金额")
    @ApiModelProperty("进货金额")
    private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额

    @ExcelProperty("毛利额")
    @ApiModelProperty("毛利额")
    private BigDecimal grossProfitMargin = BigDecimal.ZERO;

    @ExcelProperty("毛利率")
    @ApiModelProperty("毛利率")
    private BigDecimal grossMargin = BigDecimal.ZERO;

    @ExcelProperty("进货数量")
    @ApiModelProperty("进货数量")
    private BigDecimal orderQuanty =BigDecimal.ZERO;//进货数量

    @ExcelProperty("销售数量")
    @ApiModelProperty("线下销售数量")
    private BigDecimal totalQuantity=BigDecimal.ZERO;

    @ExcelProperty("损耗数量")
    @ApiModelProperty("损耗数量")
    private BigDecimal lossQuantity = BigDecimal.ZERO;

    @ExcelIgnore
    @ApiModelProperty("退货金额")
    private BigDecimal saleReturnOrderTotal=BigDecimal.ZERO;//退货金额

    @ExcelIgnore
    @ApiModelProperty("退货数量")
    private BigDecimal saleReturnOrderQuantity =BigDecimal.ZERO;//退货数量

    @ExcelIgnore
    @ApiModelProperty("赠品金额")
    private BigDecimal giveQuantity = BigDecimal.ZERO;

}

