package com.pinshang.qingyun.report.model.pos;

import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Table(name = "t_pos_report_receipt_difference")
public class ReceiptDifference {

    private Long id;

    /**
     * 对账日期
     */
    private Date date;

    private Long shopId;

    /**
     * 收款项编码
     */
    private String receivableCode;

    /**
     * 收款项名称
     */
    private String receivableName;

    /**
     * 销售金额
     */
    private BigDecimal totalSale;

    /**
     * 手续费
     */
    private BigDecimal poundage;

    /**
     * 实收金额
     */
    private BigDecimal amount;

    /**
     * 差异
     */
    private BigDecimal difference;

    private Long createId;

    private Date createTime;
}
