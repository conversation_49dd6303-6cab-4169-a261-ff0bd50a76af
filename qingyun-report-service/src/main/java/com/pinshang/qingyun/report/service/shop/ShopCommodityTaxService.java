package com.pinshang.qingyun.report.service.shop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.service.SystemPropClient;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.CategoryAllSalesPercentODTO;
import com.pinshang.qingyun.report.dto.OnToOffAnalyzeDTO;
import com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.shop.*;
import com.pinshang.qingyun.report.enums.XsjmReportTypeEnum;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.mapper.shop.*;
import com.pinshang.qingyun.report.model.shop.*;
import com.pinshang.qingyun.report.service.DataQueryChangeService;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.report.CommodityTaxReportODto;
import com.pinshang.qingyun.shop.dto.shop.CommodityTaxInfoByShopIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopReportClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.report.dto.GuestAnalysisDTO;
import com.pinshang.qingyun.xd.report.dto.GuestAnalysisIDTO;
import com.pinshang.qingyun.xd.report.dto.QueryCategorySalesStatisticsSumDailyODTO;
import com.pinshang.qingyun.xd.report.service.GuestAnalysisClient;
import com.pinshang.qingyun.xd.report.service.XdCategorySalesStatisticsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShopCommodityTaxService {

    @Autowired
    private CommodityTaxMapper commodityTaxMapper;

    @Autowired
    private CommodityTaxThirdCateMapper commodityTaxThirdCateMapper;

    @Autowired
    private CommodityTaxSecondCateMapper commodityTaxSecondCateMapper;

    @Autowired
    private CommodityTaxFirstCateMapper commodityTaxFirstCateMapper;

    @Autowired
    private CommodityShopTaxMapper commodityShopTaxMapper;

    @Autowired
    private ShopReportClient shopReportClient;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private SystemPropClient systemPropClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private SMMUserClient sMMUserClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private GuestAnalysisClient guestAnalysisClient;

    @Autowired
    private XdCategorySalesStatisticsClient xdCategorySalesStatisticsClient;

    @Autowired
    private IRenderService renderService;
    @Autowired
    private DataQueryChangeService dataQueryChangeService;

    @Transactional(rollbackFor = Exception.class)
    public void clearShopTaxData(String day) {
        Example example = new Example(CommodityTax.class);
        example.createCriteria().andEqualTo("dateTime", day);
        commodityTaxMapper.deleteByExample(example);

        Example shopExample = new Example(CommodityShopTax.class);
        shopExample.createCriteria().andEqualTo("dateTime", day);
        commodityShopTaxMapper.deleteByExample(shopExample);

        Example firstCateExample = new Example(CommodityFirstCateTax.class);
        firstCateExample.createCriteria().andEqualTo("dateTime", day);
        commodityTaxFirstCateMapper.deleteByExample(firstCateExample);

        Example secondExample = new Example(CommoditySecondCateTax.class);
        secondExample.createCriteria().andEqualTo("dateTime", day);
        commodityTaxSecondCateMapper.deleteByExample(secondExample);


        Example thirdExample = new Example(CommodityThirdCateTax.class);
        thirdExample.createCriteria().andEqualTo("dateTime", day);
        commodityTaxThirdCateMapper.deleteByExample(thirdExample);
    }

    public List<CommodityTax> getPosCommodityTaxFromOffline(String day) {
        return commodityShopTaxMapper.getPosCommodityTaxFromOffline(day);
    }

    public List<CommodityTax> queryOffLineVisitNum(String day) {
        return commodityShopTaxMapper.queryOffLineVisitNum(day);
    }

    public void insertCommodityTax(List<CommodityTax> list) {
        commodityTaxMapper.insertList(list);
    }

    public void insertCommodityThirdTax(List<CommodityThirdCateTax> list) {
        commodityTaxThirdCateMapper.insertList(list);
    }

    public void insertCommoditySecondTax(List<CommoditySecondCateTax> list) {
        commodityTaxSecondCateMapper.insertList(list);
    }

    public void insertCommodityFirstTax(List<CommodityFirstCateTax> list) {
        commodityTaxFirstCateMapper.insertList(list);
    }

    public void insertCommodityShopTax(List<CommodityShopTax> list) {
        commodityShopTaxMapper.insertList(list);
    }

    public List<ShopCommoditySaleStatisticsODTO> queryShopCommoditySaleList(Long shopId) {
        return commodityTaxMapper.queryShopCommoditySaleList(shopId);
    }

    public List<CommodityOrderSalesODTO> findMonthSalesQuanty(Long commodityId, Long storeId, String inBegin, String yesterday, int flag) {
        return commodityTaxMapper.findMonthSalesQuanty(commodityId, storeId, inBegin, yesterday, flag);
    }

    /**
     *查询线上销售额 和订货返回值通用设置
     * @param list
     * @param onlineList
     */
    private void setList(List<CommodityTax> list, List<CommodityTaxReportODto> onlineList) {
        if(CollectionUtils.isNotEmpty(onlineList)){
            for(CommodityTaxReportODto odto:onlineList){
                CommodityTax commodityTax = new CommodityTax();
                BeanUtils.copyProperties(odto,commodityTax);
                list.add(commodityTax);
            }
        }
    }

    /**
     * 查询线上销售额
     * @param day
     * @return
     */
    public List<CommodityTax> getCommodityTaxFromOnline(String day){
        List<CommodityTax> list = new ArrayList<>();
        List<CommodityTaxReportODto> onlineList = shopReportClient.getCommodityTaxFromOnline(day, ShopTypeEnums.XS.getCode());
        setList(list, onlineList);
        return list;
    }

    /**
     * 查询订货
     * @param day
     * @return
     */
    public List<CommodityTax> getCommodityTaxFromSaleOrder(String day){
        List<CommodityTax> list = new ArrayList<>();
        List<Integer> shopTypeList = new ArrayList<>();
        shopTypeList.add(ShopTypeEnums.XS.getCode());
        shopTypeList.add(ShopTypeEnums.XJ.getCode());
        shopTypeList.add(ShopTypeEnums.HYD.getCode());
        List<CommodityTaxReportODto> saleOrderList = shopReportClient.getCommodityTaxFromSaleOrder(day,shopTypeList);
        setList(list, saleOrderList);
        return list;
    }

    /**
     * 查询退货
     * @param day
     * @return
     */
    public List<CommodityTax> getCommodityTaxFromReturnOrder(String day){
        List<CommodityTax> list = new ArrayList<>();
        List<Integer> shopTypeList = new ArrayList<>();
        shopTypeList.add(ShopTypeEnums.XS.getCode());
        shopTypeList.add(ShopTypeEnums.XJ.getCode());
        List<CommodityTaxReportODto> saleOrderList = shopReportClient.getCommodityTaxFromReturnOrder(day,shopTypeList);
        setList(list, saleOrderList);
        return list;
    }

    /**
     * 分任务查询各数据信息
     */
    private Map<String, List<CommodityTax>> getTaskdata(String day) {
        Map<String,List<CommodityTax>> dataMap = new HashMap<>(256*1024);

        //查询分3个任务，分批查询   # 200
        //查询线上销售额
       /* CompletableFuture<List<CommodityTax>> f1 = CompletableFuture.supplyAsync(() -> {
            List<CommodityTax>	onlineList = getCommodityTaxFromOnline(day);
            dataMap.put("onlineList",onlineList);
            return onlineList;
        });*/

        //查询线下销售额(pos)  # 120000
        CompletableFuture<List<CommodityTax>> f2 = CompletableFuture.supplyAsync(() -> {
            List<CommodityTax> posOfflineList = getPosCommodityTaxFromOffline(day);
            dataMap.put("posOfflineList",posOfflineList);
            return posOfflineList;
        });

        //查询进货  # 60000
        CompletableFuture<List<CommodityTax>> f3 = CompletableFuture.supplyAsync(() -> {
            List<CommodityTax> saleOrderList = getCommodityTaxFromSaleOrder(day);
            dataMap.put("saleOrderList",saleOrderList);
            return saleOrderList;
        });

        //查询退货
        CompletableFuture<List<CommodityTax>> f4 = CompletableFuture.supplyAsync(() -> {
            List<CommodityTax> returnOrderList = getCommodityTaxFromReturnOrder(day);
            dataMap.put("returnOrderList",returnOrderList);
            return returnOrderList;
        });

        //所有查询结束，任务才结束
        CompletableFuture<Void> all = CompletableFuture.allOf(f2,f3,f4);
        all.join();
        return dataMap;
    }

    /**
     * 各数据放入map
     * @param returnList
     * @param posOfflineList
     * @param posOfflineListMap
     */
    private void setListMap(List<CommodityTax> returnList, List<CommodityTax> posOfflineList, List<CommodityTax> saleOrderList, List<CommodityTax> returnOrderList, Map<String, CommodityTax> posOfflineListMap, Map<String, CommodityTax> saleOrderListMap, Map<String, CommodityTax> returnOrderListMap) {
       /* if(CollectionUtils.isNotEmpty(onlineList)){
            returnList.addAll(onlineList);
            for(CommodityTax entry:onlineList){
                onlineListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }*/
        if(CollectionUtils.isNotEmpty(posOfflineList)){
            returnList.addAll(posOfflineList);
            for(CommodityTax entry:posOfflineList){
                posOfflineListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }
        if(CollectionUtils.isNotEmpty(saleOrderList)){
            returnList.addAll(saleOrderList);
            for(CommodityTax entry:saleOrderList){
                saleOrderListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }

        if(CollectionUtils.isNotEmpty(returnOrderList)){
            returnList.addAll(returnOrderList);
            for(CommodityTax entry:returnOrderList){
                returnOrderListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }
    }

    /**
     * 查询当天的产生交易的商品信息
     * @param commodityIdSet
     * @return
     */
    public List<CommodityTax> getCommodityTaxes(Set<Long> commodityIdSet, Integer shopType) {
        List<CommodityTax> commodityTaxList = new ArrayList<>();
        if(commodityIdSet.size() > 0){
            List<CommodityTax> commodityList = commodityMapper.getCommodityListByIds(commodityIdSet);
            List<Shop> shopList = shopMapper.getAllShopListByShopType(shopType);
            if(CollectionUtils.isEmpty(shopList)){return commodityTaxList;}

            for(Shop shop:shopList){
                for(CommodityTax comm:commodityList){
                    CommodityTax commodityTax = new CommodityTax();
                    BeanUtils.copyProperties(comm,commodityTax);
                    commodityTax.setShopId(shop.getId());
                    commodityTaxList.add(commodityTax);
                }
            }
        }else{
            commodityTaxList = new ArrayList<>();
        }
        return commodityTaxList;
    }

    /**
     * 获取pos、线上销售额、订货数据信息
     * @param day
     * @return
     */
    public List<CommodityTax> getCommodityTax(String day) {
        List<CommodityTax> returnList = new ArrayList<>();

        //分任务获取pos、线上销售额、订货数据信息
        Map<String, List<CommodityTax>> dataMap = getTaskdata(day);

        //获取数据
        List<CommodityTax> posOfflineList = dataMap.get("posOfflineList");
        List<CommodityTax> saleOrderList = dataMap.get("saleOrderList");
        List<CommodityTax> returnOrderList = dataMap.get("returnOrderList");

        //定义放各种数据map
        Map<String,CommodityTax> posOfflineListMap = new HashMap<>(128*1024);
        Map<String,CommodityTax> saleOrderListMap = new HashMap<>(64*1024);
        Map<String,CommodityTax> returnOrderListMap = new HashMap<>(256);

        //减少循环
        setListMap(returnList, posOfflineList, saleOrderList, returnOrderList, posOfflineListMap, saleOrderListMap, returnOrderListMap);

        Map<Long, List<CommodityTax>> commodityIdMap = returnList.stream().collect(Collectors.groupingBy(CommodityTax::getCommodityId));
        Set<Long> commodityIdSet = commodityIdMap.keySet();
        returnList = new ArrayList<>();

        // 查询当天的产生交易的商品信息
        List<CommodityTax> commodityTaxList = getCommodityTaxes(commodityIdSet, null);

        // 组装数据
        for (CommodityTax commodityTax : commodityTaxList) {
            // 线上
            /*CommodityTax onlineTax = onlineListMap.get(commodityTax.getShopId()+""+commodityTax.getCommodityId());
            if(null != onlineTax) {
                commodityTax.setOnlineSales(onlineTax.getTotalSales());
                commodityTax.setTotalSales((commodityTax.getTotalSales() == null ? BigDecimal.ZERO:commodityTax.getTotalSales()).add(onlineTax.getTotalSales()));
                commodityTax.setTotalQuanty((commodityTax.getTotalQuanty() == null ? BigDecimal.ZERO:commodityTax.getTotalQuanty()).add(onlineTax.getTotalQuanty()));
                commodityTax.setCostTotal((commodityTax.getCostTotal() == null ? BigDecimal.ZERO:commodityTax.getCostTotal()).add(onlineTax.getCostTotal()));
                commodityTax.setOnlineCost((commodityTax.getOnlineCost() == null ? BigDecimal.ZERO:commodityTax.getOnlineCost()).add(onlineTax.getCostTotal()));
            }
            */
            // 线下pos
            CommodityTax posofflineTax = posOfflineListMap.get(commodityTax.getShopId()+""+commodityTax.getCommodityId());
            if(null != posofflineTax ) {
                commodityTax.setOfflineSales(commodityTax.getOfflineSales().add(posofflineTax.getTotalSales()));
                commodityTax.setTotalSales((commodityTax.getTotalSales() == null ? BigDecimal.ZERO:commodityTax.getTotalSales()).add(posofflineTax.getTotalSales()));
                commodityTax.setTotalQuanty((commodityTax.getTotalQuanty() == null ? BigDecimal.ZERO:commodityTax.getTotalQuanty()).add(posofflineTax.getTotalQuanty()));
                commodityTax.setCostTotal((commodityTax.getCostTotal() == null ? BigDecimal.ZERO:commodityTax.getCostTotal()).add(posofflineTax.getCostTotal()));
                commodityTax.setOfflineCost((commodityTax.getOfflineCost() == null ? BigDecimal.ZERO:commodityTax.getOfflineCost()).add(posofflineTax.getCostTotal()));
            }

            //进货
            CommodityTax saleOrderEntry=saleOrderListMap.get(commodityTax.getShopId()+""+commodityTax.getCommodityId());
            if(null != saleOrderEntry ) {
                commodityTax.setOrderTotal(saleOrderEntry.getOrderTotal());
                commodityTax.setOrderQuanty(saleOrderEntry.getOrderQuanty());
            }

            //退货
            CommodityTax returnOrderEntry=returnOrderListMap.get(commodityTax.getShopId()+""+commodityTax.getCommodityId());
            if(null != returnOrderEntry ) {
                commodityTax.setSaleReturnOrderTotal(returnOrderEntry.getSaleReturnOrderTotal());
            }
        }

        // 处理数据
        for (CommodityTax taxEntry : commodityTaxList) {
            if((taxEntry.getOfflineSales() != null && taxEntry.getOfflineSales().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getOrderTotal() != null && taxEntry.getOrderTotal().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getSaleReturnOrderTotal() != null && taxEntry.getSaleReturnOrderTotal().compareTo(BigDecimal.ZERO) != 0)){
                taxEntry.setDateTime(day);
                returnList.add(taxEntry);
            }
        }

        return  returnList;
    }


    /**
     * 获取门店，大类，中类来客数
     * @param day
     * @param type 0:门店  1:大类   2:中类
     * @return
     */
    public Map<String, Integer> getVisitMap(String day, Integer type){
        Map<String, Integer> visitMap = new HashMap<>(256*1024);
        List<CommodityTaxODTO> odtoList = commodityShopTaxMapper.queryOffLineVisitNumByType(day,type);
        if(CollectionUtils.isNotEmpty(odtoList)){
            for(CommodityTaxODTO c: odtoList){
                if(type == 0){
                    visitMap.put(c.getShopId() + "",c.getVisitNum());
                }
                if(type == 1){
                    visitMap.put(c.getShopId() + "" + c.getCommodityFirstId(),c.getVisitNum());
                }
                if(type == 2){
                    visitMap.put(c.getShopId() + "" + c.getCommoditySecondId(),c.getVisitNum());
                }
            }
        }
        return visitMap;
    }
    /**
     * 商品总表汇总
     * @param
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public Boolean commodityTax(String day) {

        long listBegin = System.currentTimeMillis();

        // 获取pos、线上销售额、订货数据信息
        List<CommodityTax> commodityTaxList = getCommodityTax(day);
        if(CollectionUtils.isEmpty(commodityTaxList)){
            return false;
        }else {
            for(CommodityTax tax : commodityTaxList){
                tax.setOnlineSales(null != tax.getOnlineSales() ? tax.getOnlineSales() : BigDecimal.ZERO);
                tax.setOnlineCost(null != tax.getOnlineCost() ? tax.getOnlineCost() : BigDecimal.ZERO);
            }
        }
        long listEnd = System.currentTimeMillis();
        log.info("查询商品级别数据耗时:" + (listEnd - listBegin) + "ms");

        //清除数据
        clearShopTaxData(day);

        if(CollectionUtils.isNotEmpty(commodityTaxList)){
            if(commodityTaxList.size() > 2000 ){
                batchInsertCommodityTax(commodityTaxList);
            }else{
                insertCommodityTax(commodityTaxList);
            }
        }

        long insertEnd = System.currentTimeMillis();
        log.info("插入商品级别数据耗时:" + +(insertEnd - listEnd) + "ms");

        //查询线下来客数
        Map<String, Integer> shopVisitMap = getVisitMap(day,0);
        Map<String, Integer> firstVisitMap = getVisitMap(day,1);
        Map<String, Integer> secondVisitMap = getVisitMap(day,2);

        long visitListEnd = System.currentTimeMillis();
        log.info("查询线下来客数耗时:" + +(visitListEnd - insertEnd) + "ms");


        //小类级别汇总
        List<CommodityThirdCateTax> thirdCateList = commodityThirdCateTax(commodityTaxList);
        long thirdEnd = System.currentTimeMillis();
        log.info("小类级别汇总耗时:" + +(thirdEnd - visitListEnd) + "ms");

        //中类级别汇总
        List<CommoditySecondCateTax> secondCateList = commoditySecondCateTax(thirdCateList, secondVisitMap);
        long secondEnd = System.currentTimeMillis();
        log.info("中类级别汇总耗时:" + +(secondEnd - thirdEnd) + "ms");

        //大类级别汇总
        List<CommodityFirstCateTax> firstCateList = commodityFirstCateTax(secondCateList, firstVisitMap);
        long firstEnd = System.currentTimeMillis();
        log.info("大类级别汇总耗时:" + +(firstEnd - secondEnd) + "ms");

        //门店级别汇总
        commodityShopTax(firstCateList, shopVisitMap);
        long shopEnd = System.currentTimeMillis();
        log.info("门店级别汇总耗时:" + +(shopEnd - firstEnd) + "ms");

        return Boolean.TRUE;
    }

    /**
     * 批量插入商品级别数据
     * @param cs
     */
    public void batchInsertCommodityTax(List<CommodityTax> cs) {
        int index = 0;
        int count = 2000;
        while (true) {
            List<CommodityTax> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                insertCommodityTax(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 小类级别汇总
     *
     * @param
     */
    public List<CommodityThirdCateTax> commodityThirdCateTax(List<CommodityTax> list) {
        List<CommodityThirdCateTax> thirdCateList = new ArrayList<>();

        Map<String, CommodityThirdCateTax> thirdCateMap = new HashMap<>(32*1024);
        for (CommodityTax c : list) {
            String thirdKey = c.getShopId() + "" + c.getCommodityThirdId();
            if (thirdCateMap.get(thirdKey) != null) {
                CommodityThirdCateTax thirdCateTax = thirdCateMap.get(thirdKey);
                thirdCateTax.setTotalSales(thirdCateTax.getTotalSales().add(c.getTotalSales()));
                thirdCateTax.setOnlineSales(thirdCateTax.getOnlineSales().add(c.getOnlineSales()));
                thirdCateTax.setOfflineSales(thirdCateTax.getOfflineSales().add(c.getOfflineSales()));
                thirdCateTax.setCostTotal(thirdCateTax.getCostTotal().add(c.getCostTotal()));
                thirdCateTax.setOnlineCost(thirdCateTax.getOnlineCost().add(c.getOnlineCost()));
                thirdCateTax.setOfflineCost(thirdCateTax.getOfflineCost().add(c.getOfflineCost()));
                thirdCateTax.setOrderTotal(thirdCateTax.getOrderTotal().add(c.getOrderTotal()));
                thirdCateTax.setSaleReturnOrderTotal(thirdCateTax.getSaleReturnOrderTotal().add(c.getSaleReturnOrderTotal()));
                thirdCateTax.setStockAdjustAmountTotal(thirdCateTax.getStockAdjustAmountTotal().add(c.getStockAdjustAmountTotal()));
                thirdCateTax.setTotalQuanty(thirdCateTax.getTotalQuanty().add(c.getTotalQuanty()));
                thirdCateTax.setOrderQuanty(thirdCateTax.getOrderQuanty().add(c.getOrderQuanty()));
            } else {
                CommodityThirdCateTax thirdCateTax = new CommodityThirdCateTax();
                SpringUtil.copyProperties(c, thirdCateTax);
                thirdCateMap.put(thirdKey, thirdCateTax);
            }
        }
        for (CommodityThirdCateTax value : thirdCateMap.values()) {
            thirdCateList.add(value);
        }
        insertCommodityThirdTax(thirdCateList);
        return thirdCateList;
    }

    /**
     * 中类级别汇总
     *
     * @param
     */
    public List<CommoditySecondCateTax> commoditySecondCateTax(List<CommodityThirdCateTax> thirdCateList, Map<String, Integer> secondVisitMap) {
        List<CommoditySecondCateTax> secondCateList = new ArrayList<>();

        Map<String, CommoditySecondCateTax> secondCateMap = new HashMap<>(16*1024);
        for (CommodityThirdCateTax c : thirdCateList) {
            String secondKey = c.getShopId() + "" + c.getCommoditySecondId();
            if (secondCateMap.get(secondKey) != null) {
                CommoditySecondCateTax secondCateTax = secondCateMap.get(secondKey);
                secondCateTax.setTotalSales(secondCateTax.getTotalSales().add(c.getTotalSales()));
                secondCateTax.setOnlineSales(secondCateTax.getOnlineSales().add(c.getOnlineSales()));
                secondCateTax.setOfflineSales(secondCateTax.getOfflineSales().add(c.getOfflineSales()));
                secondCateTax.setCostTotal(secondCateTax.getCostTotal().add(c.getCostTotal()));
                secondCateTax.setOnlineCost(secondCateTax.getOnlineCost().add(c.getOnlineCost()));
                secondCateTax.setOfflineCost(secondCateTax.getOfflineCost().add(c.getOfflineCost()));
                secondCateTax.setOrderTotal(secondCateTax.getOrderTotal().add(c.getOrderTotal()));
                secondCateTax.setSaleReturnOrderTotal(secondCateTax.getSaleReturnOrderTotal().add(c.getSaleReturnOrderTotal()));
                secondCateTax.setStockAdjustAmountTotal(secondCateTax.getStockAdjustAmountTotal().add(c.getStockAdjustAmountTotal()));
                secondCateTax.setTotalQuanty(secondCateTax.getTotalQuanty().add(c.getTotalQuanty()));
                secondCateTax.setOrderQuanty(secondCateTax.getOrderQuanty().add(c.getOrderQuanty()));
            } else {
                CommoditySecondCateTax secondCateTax = new CommoditySecondCateTax();
                SpringUtil.copyProperties(c, secondCateTax);
                secondCateMap.put(secondKey, secondCateTax);
            }
        }
        for (CommoditySecondCateTax value : secondCateMap.values()) {
            secondCateList.add(value);
        }
        for (CommoditySecondCateTax tax : secondCateList) {
            String key = tax.getShopId() + "" + tax.getCommoditySecondId();
            if (secondVisitMap.get(key) != null) {
                tax.setOfflineVisitorNumber(secondVisitMap.get(key));
                tax.setOfflineAverageAmount(tax.getOfflineSales().divide(new BigDecimal(tax.getOfflineVisitorNumber()), 2, BigDecimal.ROUND_HALF_UP));
            }
        }
        insertCommoditySecondTax(secondCateList);
        return secondCateList;
    }

    /**
     * 大类级别汇总
     *
     * @param
     */
    public List<CommodityFirstCateTax> commodityFirstCateTax(List<CommoditySecondCateTax> secondCateList, Map<String, Integer> firstVisitMap) {
        List<CommodityFirstCateTax> firstCateList = new ArrayList<>();

        Map<String, CommodityFirstCateTax> firstCateMap = new HashMap<>(8*1024);
        for (CommoditySecondCateTax c : secondCateList) {
            String firstKey = c.getShopId() + "" + c.getCommodityFirstId();
            if (firstCateMap.get(firstKey) != null) {
                CommodityFirstCateTax firstCateTax = firstCateMap.get(firstKey);
                firstCateTax.setTotalSales(firstCateTax.getTotalSales().add(c.getTotalSales()));
                firstCateTax.setOnlineSales(firstCateTax.getOnlineSales().add(c.getOnlineSales()));
                firstCateTax.setOfflineSales(firstCateTax.getOfflineSales().add(c.getOfflineSales()));
                firstCateTax.setCostTotal(firstCateTax.getCostTotal().add(c.getCostTotal()));
                firstCateTax.setOnlineCost(firstCateTax.getOnlineCost().add(c.getOnlineCost()));
                firstCateTax.setOfflineCost(firstCateTax.getOfflineCost().add(c.getOfflineCost()));
                firstCateTax.setOrderTotal(firstCateTax.getOrderTotal().add(c.getOrderTotal()));
                firstCateTax.setSaleReturnOrderTotal(firstCateTax.getSaleReturnOrderTotal().add(c.getSaleReturnOrderTotal()));
                firstCateTax.setStockAdjustAmountTotal(firstCateTax.getStockAdjustAmountTotal().add(c.getStockAdjustAmountTotal()));
                firstCateTax.setTotalQuanty(firstCateTax.getTotalQuanty().add(c.getTotalQuanty()));
                firstCateTax.setOrderQuanty(firstCateTax.getOrderQuanty().add(c.getOrderQuanty()));
            } else {
                CommodityFirstCateTax firstCateTax = new CommodityFirstCateTax();
                SpringUtil.copyProperties(c, firstCateTax);
                firstCateMap.put(firstKey, firstCateTax);
            }
        }
        for (CommodityFirstCateTax value : firstCateMap.values()) {
            firstCateList.add(value);
        }
        for (CommodityFirstCateTax tax : firstCateList) {
            String key = tax.getShopId() + "" + tax.getCommodityFirstId();
            if (firstVisitMap.get(key) != null) {
                tax.setOfflineVisitorNumber(firstVisitMap.get(key));
                tax.setOfflineAverageAmount(tax.getOfflineSales().divide(new BigDecimal(tax.getOfflineVisitorNumber()), 2, BigDecimal.ROUND_HALF_UP));
            }
        }
        insertCommodityFirstTax(firstCateList);
        return firstCateList;
    }

    /**
     * 门店级别汇总
     *
     * @param
     */
    public void commodityShopTax(List<CommodityFirstCateTax> firstCateList, Map<String, Integer> shopVisitMap) {
        List<CommodityShopTax> cst = new ArrayList<>();
        Map<Long, CommodityShopTax> shopMap = new HashMap<>(512);
        for (CommodityFirstCateTax c : firstCateList) {
            if (shopMap.get(c.getShopId()) != null) {
                CommodityShopTax shopTax = shopMap.get(c.getShopId());
                shopTax.setTotalSales(shopTax.getTotalSales().add(c.getTotalSales()));
                shopTax.setOnlineSales(shopTax.getOnlineSales().add(c.getOnlineSales()));
                shopTax.setOfflineSales(shopTax.getOfflineSales().add(c.getOfflineSales()));
                shopTax.setCostTotal(shopTax.getCostTotal().add(c.getCostTotal()));
                shopTax.setOnlineCost(shopTax.getOnlineCost().add(c.getOnlineCost()));
                shopTax.setOfflineCost(shopTax.getOfflineCost().add(c.getOfflineCost()));
                shopTax.setOrderTotal(shopTax.getOrderTotal().add(c.getOrderTotal()));
                shopTax.setSaleReturnOrderTotal(shopTax.getSaleReturnOrderTotal().add(c.getSaleReturnOrderTotal()));
                shopTax.setStockAdjustAmountTotal(shopTax.getStockAdjustAmountTotal().add(c.getStockAdjustAmountTotal()));
                shopTax.setTotalQuanty(shopTax.getTotalQuanty().add(c.getTotalQuanty()));
                shopTax.setOrderQuanty(shopTax.getOrderQuanty().add(c.getOrderQuanty()));
            } else {
                CommodityShopTax shopTax = new CommodityShopTax();
                SpringUtil.copyProperties(c, shopTax);
                shopMap.put(c.getShopId(), shopTax);
            }
        }
        for (CommodityShopTax value : shopMap.values()) {
            cst.add(value);
        }
        for (CommodityShopTax tax : cst) {
            String key = tax.getShopId() + "";
            if (shopVisitMap.get(key) != null) {
                tax.setOfflineVisitorNumber(shopVisitMap.get(key));
                tax.setOfflineAverageAmount(tax.getOfflineSales().divide(new BigDecimal(tax.getOfflineVisitorNumber()), 2, BigDecimal.ROUND_HALF_UP));
            }
        }
        insertCommodityShopTax(cst);
    }


    /**
     * 上个月的销量top5(自动订货)
     * @param shopAutoCommodityTaxDTO
     * @return
     */
    public List<Long> queryTopShopCommoditySale(ShopAutoCommodityTaxDTO shopAutoCommodityTaxDTO) {
        return commodityTaxMapper.queryTopShopCommoditySale(shopAutoCommodityTaxDTO);
    }


    /**
     * 商品总表汇总job(只保留31天数据，专为PDA订货用)
     * @param timeStamp
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean commodityTaxMonth(String timeStamp) {

        commodityTaxMapper.deleteCommodityTaxMonth(timeStamp);

        commodityTaxMapper.insertCommodityTaxMonth(timeStamp);

        // 先删除31天之前的数据
        Calendar cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE,   -31);
        String day = DateUtil.getDateFormate(cal.getTime(),"yyyy-MM-dd");
        commodityTaxMapper.deleteCommodityTaxMonth30(day);
        return Boolean.TRUE;
    }


    /**
     * 线上线下客单分析报表
     * 一个门店，销售额是-2，这个时候统计门店数量的时候不统计这个门店，但是这个-2的销售额要统计进去
     * @return
     */
    public List<OnToOffAnalyzeDTO> onToOffAnalyze(OnToOffAnalyzeIDTO dto) {
        QYAssert.isTrue(null != dto.getToday(), "日期不能为空");
        List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        List<OnToOffAnalyzeDTO> res = new ArrayList<>();
        if(CollectionUtils.isEmpty(shopIdList)){
            return res;
        }

        if (!StringUtil.isNullOrEmpty(dto.getDeptNo())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(dto.getDeptNo());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (null != dto.getShopId()) {
                    shopIdList.retainAll(Arrays.asList(dto.getShopId()));
                }
                if (CollectionUtils.isEmpty(shopIdList)) {
                    return res;
                }
            }else {
                return res;
            }
        }
        if (StringUtil.isNullOrEmpty(dto.getDeptNo()) && null != dto.getShopId()) {
            shopIdList = new ArrayList<>();
            shopIdList.add(dto.getShopId());
        }
        dto.setShopIdList(shopIdList);

        Date date = DateUtil.parseDate(dto.getToday(), "yyyy-MM-dd");
        //String monthFirst = DateUtil.get4yMd(DateUtil.firstDayOfMonth(date));
        Date monthFirst = DateUtil.firstDayOfMonth(date);
        dto.setMonthFirst(DateUtil.get4yMd(monthFirst));
        Date lastMonthToday = DateUtil.addMonth(date, -1);
        if (DateUtil.isLastDayOfMonth(date)) {
            //当前日期是当前月份最后一天，则获取上个月的最后一天
            lastMonthToday = DateUtil.lastDayOfMonth(lastMonthToday);
        }
        dto.setLastMonthToday(DateUtil.get4yMd(lastMonthToday));

        Date lastMonthFirst = DateUtil.firstDayOfMonth(lastMonthToday);
        dto.setLastMonthFirst(DateUtil.get4yMd(lastMonthFirst));

        Date lastWeek = DateUtil.addDay(date, -7);
        dto.setLastWeek(DateUtil.get4yMd(lastWeek));
        log.info("线上线下客单分析参数={}", dto);
        if(dataQueryChangeService.enableDataQuery()){
            onToOffAnalyzeByDataQuery(dto, res);
        }else{
            res = commodityShopTaxMapper.onToOffAnalyze(dto);
        }


        //本月间隔天数 10.1到10.2间隔属于2天
        BigDecimal monthIntervalDay = new BigDecimal(DateUtil.getDayDif(date, monthFirst) + 1);
        //上个月间隔天数
        BigDecimal lastMonthIntervalDay = new BigDecimal(DateUtil.getDayDif(lastMonthToday, lastMonthFirst) + 1);

        if (CollectionUtils.isNotEmpty(res)) {
            for (OnToOffAnalyzeDTO shopTax : res) {
                shopTax.setAverageAmount(BigDecimal.ZERO);
                if (null != shopTax.getVisitorNumber() && shopTax.getVisitorNumber().compareTo(BigDecimal.ZERO) > 0) {
                    shopTax.setAverageAmount(shopTax.getSales().divide(shopTax.getVisitorNumber(), 2, BigDecimal.ROUND_HALF_UP));
                }
                 if ((shopTax.getTime().equals(1) || shopTax.getTime().equals(2)) && !shopTax.getShopNum().equals(BigDecimal.ZERO)) {
                     shopTax.setSingleAverageVisitor( shopTax.getVisitorNumber().divide(shopTax.getShopNum(), 2, BigDecimal.ROUND_HALF_UP) );
                     shopTax.setSingleAverageSale(shopTax.getSales().divide(shopTax.getShopNum(), 2, BigDecimal.ROUND_HALF_UP));
                 } else if (shopTax.getTime().equals(4) && !shopTax.getShopNum().equals( BigDecimal.ZERO )) {
                     shopTax.setSingleAverageVisitor( shopTax.getVisitorNumber().divide(shopTax.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(lastMonthIntervalDay, 2, BigDecimal.ROUND_HALF_UP) );
                     shopTax.setSingleAverageSale(shopTax.getSales().divide(shopTax.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(lastMonthIntervalDay, 2, BigDecimal.ROUND_HALF_UP));
                 } else if (shopTax.getTime().equals(3) && !shopTax.getShopNum().equals( BigDecimal.ZERO )) {
                     shopTax.setSingleAverageVisitor( shopTax.getVisitorNumber().divide(shopTax.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(monthIntervalDay, 2, BigDecimal.ROUND_HALF_UP) );
                     shopTax.setSingleAverageSale(shopTax.getSales().divide(shopTax.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(monthIntervalDay, 2, BigDecimal.ROUND_HALF_UP));
                 } else {
                     shopTax.setSingleAverageVisitor(BigDecimal.ZERO);
                     shopTax.setSingleAverageSale(BigDecimal.ZERO);
                 }
//                OnToOffAnalyzeDTO off = new OnToOffAnalyzeDTO();
//                off.setTime(shopTax.getType());
//                off.setChannel(2);
//                off.setSales(shopTax.getOfflineSales());
//                off.setVisitorNumber(shopTax.getOfflineVisitorNumber());
//                off.setAverageAmount(BigDecimal.ZERO);
//                if (null != off.getVisitorNumber() && off.getVisitorNumber().compareTo(BigDecimal.ZERO) > 0) {
//                    off.setAverageAmount(off.getSales().divide(off.getVisitorNumber(), 2, BigDecimal.ROUND_HALF_UP));
//                }
//                off.setShopNum(shopTax.getShopNum());
//
//                OnToOffAnalyzeDTO online = new OnToOffAnalyzeDTO();
//                online.setTime(shopTax.getType());
//                online.setChannel(1);
//                online.setSales(shopTax.getOnlineSales());
//                online.setVisitorNumber(shopTax.getOnlineVisitorNumber());
//                online.setAverageAmount(BigDecimal.ZERO);
//                if (null != online.getVisitorNumber() && online.getVisitorNumber().compareTo(BigDecimal.ZERO) > 0) {
//                    online.setAverageAmount(online.getSales().divide(online.getVisitorNumber(), 2, BigDecimal.ROUND_HALF_UP));
//                }
//                online.setShopNum(shopTax.getShopNum());
//
//                if (shopTax.getType().equals(3) && !off.getShopNum().equals( BigDecimal.ZERO )) {
//                    off.setSingleAverageVisitor( off.getVisitorNumber().divide(off.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(monthIntervalDay, 2, BigDecimal.ROUND_HALF_UP) );
//                    off.setSingleAverageSale(off.getSales().divide(off.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(monthIntervalDay, 2, BigDecimal.ROUND_HALF_UP));
//
//                    online.setSingleAverageVisitor( online.getVisitorNumber().divide(online.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(monthIntervalDay, 2, BigDecimal.ROUND_HALF_UP) );
//                    online.setSingleAverageSale(online.getSales().divide(online.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(monthIntervalDay, 2, BigDecimal.ROUND_HALF_UP));
//                } else if (shopTax.getType().equals(4) && !off.getShopNum().equals( BigDecimal.ZERO )) {
//                    off.setSingleAverageVisitor( off.getVisitorNumber().divide(off.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(lastMonthIntervalDay, 2, BigDecimal.ROUND_HALF_UP) );
//                    off.setSingleAverageSale(off.getSales().divide(off.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(lastMonthIntervalDay, 2, BigDecimal.ROUND_HALF_UP));
//
//                    online.setSingleAverageVisitor( online.getVisitorNumber().divide(online.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(lastMonthIntervalDay, 2, BigDecimal.ROUND_HALF_UP) );
//                    online.setSingleAverageSale(online.getSales().divide(online.getShopNum(), 2, BigDecimal.ROUND_HALF_UP).divide(lastMonthIntervalDay, 2, BigDecimal.ROUND_HALF_UP));
//                } else if ( (shopTax.getType().equals(1) || shopTax.getType().equals(2)) && !off.getShopNum().equals(BigDecimal.ZERO)) {
//                    off.setSingleAverageVisitor( off.getVisitorNumber().divide(off.getShopNum(), 2, BigDecimal.ROUND_HALF_UP) );
//                    off.setSingleAverageSale(off.getSales().divide(off.getShopNum(), 2, BigDecimal.ROUND_HALF_UP));
//
//                    online.setSingleAverageVisitor( online.getVisitorNumber().divide(online.getShopNum(), 2, BigDecimal.ROUND_HALF_UP) );
//                    online.setSingleAverageSale(online.getSales().divide(online.getShopNum(), 2, BigDecimal.ROUND_HALF_UP));
//                } else {
//                    off.setSingleAverageVisitor( BigDecimal.ZERO );
//                    off.setSingleAverageSale( BigDecimal.ZERO );
//
//                    online.setSingleAverageVisitor( BigDecimal.ZERO );
//                    online.setSingleAverageSale( BigDecimal.ZERO );
//                }
//                res.add(off);
//                res.add(online);
            }
        }
        res.sort(Comparator.comparing(OnToOffAnalyzeDTO::getChannel).reversed().thenComparing(OnToOffAnalyzeDTO::getTime));
        return res;
    }

    private void onToOffAnalyzeByDataQuery(OnToOffAnalyzeIDTO dto, List<OnToOffAnalyzeDTO> res) {
        // 1.线下今日
        dto.setBeginDate(dto.getToday());
        dto.setEndDate(dto.getToday());
        List<OnToOffAnalyzeDTO> offLinetodayList = commodityShopTaxMapper.onToOffLineDataAnalyze(dto);
        if(CollectionUtils.isNotEmpty(offLinetodayList)) {
            offLinetodayList.forEach(item -> {
                item.setTime(1);
                item.setChannel(2);
            });
            res.addAll(offLinetodayList);
        }
        // 2.线下上周
        dto.setBeginDate(dto.getLastWeek());
        dto.setEndDate(dto.getLastWeek());
        List<OnToOffAnalyzeDTO> offLineLastWeekList = commodityShopTaxMapper.onToOffLineDataAnalyze(dto);
        if(CollectionUtils.isNotEmpty(offLineLastWeekList)) {
            offLineLastWeekList.forEach(item -> {
                item.setTime(2);
                item.setChannel(2);
            });
            res.addAll(offLineLastWeekList);
        }
        // 3.线下本月
        dto.setBeginDate(dto.getMonthFirst());
        dto.setEndDate(dto.getToday());
        List<OnToOffAnalyzeDTO> offLineMonthList = commodityShopTaxMapper.onToOffLineDataAnalyze(dto);
        if(CollectionUtils.isNotEmpty(offLineMonthList)) {
            offLineMonthList.forEach(item -> {
                item.setTime(3);
                item.setChannel(2);
            });
            res.addAll(offLineMonthList);
        }
        // 4.线下上月
        dto.setBeginDate(dto.getLastMonthFirst());
        dto.setEndDate(dto.getLastMonthToday());
        List<OnToOffAnalyzeDTO> offLineLastMonthList = commodityShopTaxMapper.onToOffLineDataAnalyze(dto);
        if(CollectionUtils.isNotEmpty(offLineLastMonthList)) {
            offLineLastMonthList.forEach(item -> {
                item.setTime(4);
                item.setChannel(2);
            });
            res.addAll(offLineLastMonthList);
        }


        // 1.线上今日
        dto.setBeginDate(dto.getToday());
        dto.setEndDate(dto.getToday());
        List<OnToOffAnalyzeDTO> onLinetodayList = commodityShopTaxMapper.onToOnLineAnalyzeCode(dto);
        if(CollectionUtils.isNotEmpty(onLinetodayList)) {
            onLinetodayList.forEach(item -> {
                item.setTime(1);
                item.setChannel(1);
            });
            res.addAll(onLinetodayList);
        }
        // 2.线上上周
        dto.setBeginDate(dto.getLastWeek());
        dto.setEndDate(dto.getLastWeek());
        List<OnToOffAnalyzeDTO> onLineLastWeekList = commodityShopTaxMapper.onToOnLineAnalyzeCode(dto);
        if(CollectionUtils.isNotEmpty(onLineLastWeekList)) {
            onLineLastWeekList.forEach(item -> {
                item.setTime(2);
                item.setChannel(1);
            });
            res.addAll(onLineLastWeekList);
        }
        // 3.线上本月
        dto.setBeginDate(dto.getMonthFirst());
        dto.setEndDate(dto.getToday());
        List<OnToOffAnalyzeDTO> onLineMonthList = commodityShopTaxMapper.onToOnLineAnalyzeCode(dto);
        if(CollectionUtils.isNotEmpty(onLineMonthList)) {
            onLineMonthList.forEach(item -> {
                item.setTime(3);
                item.setChannel(1);
            });
            res.addAll(onLineMonthList);
        }
        // 4.线上上月
        dto.setBeginDate(dto.getLastMonthFirst());
        dto.setEndDate(dto.getLastMonthToday());
        List<OnToOffAnalyzeDTO> onLineLastMonthList = commodityShopTaxMapper.onToOnLineAnalyzeCode(dto);
        if(CollectionUtils.isNotEmpty(onLineLastMonthList)) {
            onLineLastMonthList.forEach(item -> {
                item.setTime(4);
                item.setChannel(1);
            });
            res.addAll(onLineLastMonthList);
        }
    }

    /**
     * 添加或者修改线上来客数和线上客单价
     */
    @Transactional
    public Integer insertOrUpdateOnline(String time) {
        List<Long> shopIdList = shopMapper.selectAll().stream().map(Shop::getId).collect(Collectors.toList());
        if (null != shopIdList && shopIdList.size() > 0) {
            GuestAnalysisIDTO dto = new GuestAnalysisIDTO();
            dto.setShopIdList(shopIdList);
            dto.setTime(time);
            List<GuestAnalysisDTO> list = guestAnalysisClient.getGuestAnalysis(dto);
            if (null != list && list.size() > 0) {
                Example example = new Example(CommodityShopTax.class);
                example.createCriteria().andEqualTo("dateTime", time)
                .andIn("shopId", shopIdList);
                Map<Long, CommodityShopTax> map = commodityShopTaxMapper.selectByExample(example).stream().collect(Collectors.toMap(CommodityShopTax::getShopId, e -> e));
                CommodityShopTax commodityShopTax = null;
                for (GuestAnalysisDTO guestAnalysis : list) {
                    if (map.containsKey(guestAnalysis.getShopId())) {
                        commodityShopTax = map.get(guestAnalysis.getShopId());
                    } else {
                        commodityShopTax = new CommodityShopTax();
                    }
                    commodityShopTax.setOnlineVisitorNumber(guestAnalysis.getOnlineVisitorNumber());
                    commodityShopTax.setOnlineSales(guestAnalysis.getDeliverySuccessOrderAmount());
                    if (commodityShopTax.getOnlineVisitorNumber() != 0) {
                        commodityShopTax.setOnlineAverageAmount(commodityShopTax.getOnlineSales().divide( new BigDecimal(commodityShopTax.getOnlineVisitorNumber()), 2, BigDecimal.ROUND_HALF_UP ));
                    }
                    if (map.containsKey(guestAnalysis.getShopId())) {
                        commodityShopTax.setId(map.get(guestAnalysis.getShopId()).getId());
                        commodityShopTaxMapper.updateByPrimaryKeySelective(commodityShopTax);
                    } else {
                        commodityShopTax.setDateTime(time);
                        commodityShopTax.setShopId(guestAnalysis.getShopId());
                        commodityShopTaxMapper.insert(commodityShopTax);
                    }
                }
            }
        }
        return 1;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateCategorySalesOnLine(String time){
        List<QueryCategorySalesStatisticsSumDailyODTO> categorySalesStatisticsSumList =
                xdCategorySalesStatisticsClient.queryCategorySalesStatisticsSumDaily(time);
        if(SpringUtil.isEmpty(categorySalesStatisticsSumList)){
            return true;
        }
        Example example = new Example(CommodityFirstCateTax.class);
        example.createCriteria().andEqualTo("dateTime", time);
        List<CommodityFirstCateTax> oldRecordList = commodityTaxFirstCateMapper.selectByExample(example);
        Map<String, CommodityFirstCateTax> oldRecordMap = SpringUtil.isEmpty(oldRecordList) ? new HashMap<>() :
                oldRecordList.stream().collect(Collectors.toMap(it -> it.getShopId() + "-" + it.getCommodityFirstId(), Function.identity()));
        List<CommodityFirstCateTax> updateList  = new ArrayList<>(categorySalesStatisticsSumList.size());
        List<CommodityFirstCateTax> insertList  = new ArrayList<>(categorySalesStatisticsSumList.size());

        for(QueryCategorySalesStatisticsSumDailyODTO item : categorySalesStatisticsSumList){
            CommodityFirstCateTax oldRecord = oldRecordMap.get(item.getShopId() + "-" + item.getCommodityFirstKindId());
            if(null != oldRecord){
                oldRecord.setOnlineSales(item.getOrderAccount());
                oldRecord.setTotalSales(item.getOrderAccount().add(oldRecord.getOfflineSales()));
                updateList.add(oldRecord);
            }else{
                CommodityFirstCateTax insertRecord = new CommodityFirstCateTax();
                insertRecord.setShopId(item.getShopId());
                insertRecord.setOnlineSales(item.getOrderAccount());
                insertRecord.setTotalSales(item.getOrderAccount());
                insertRecord.setCommodityFirstId(item.getCommodityFirstKindId());
                insertRecord.setOfflineSales(BigDecimal.ZERO);
                insertRecord.setTotalQuanty(BigDecimal.ZERO);
                insertRecord.setOrderTotal(BigDecimal.ZERO);
                insertRecord.setOrderQuanty(BigDecimal.ZERO);
                insertRecord.setCostTotal(BigDecimal.ZERO);
                insertRecord.setSaleReturnOrderTotal(BigDecimal.ZERO);
                insertRecord.setStockAdjustAmountTotal(BigDecimal.ZERO);
                insertRecord.setOfflineVisitorNumber(0);
                insertRecord.setOfflineAverageAmount(BigDecimal.ZERO);
                insertRecord.setDateTime(time);
                insertList.add(insertRecord);
            }
        }
        this.batchInsertOnlineSales(insertList);
        this.batchUpdateOnlineSales(updateList);
        return true;
    }

    private void batchInsertOnlineSales(List<CommodityFirstCateTax> insertList){
        if(SpringUtil.isEmpty(insertList)){
            return;
        }
        int index = 0;
        int count = 2000;
        while (true) {
            List<CommodityFirstCateTax> items = insertList.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                commodityTaxFirstCateMapper.batchInsertOnlineSales(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    private void batchUpdateOnlineSales(List<CommodityFirstCateTax> updateList){
        if(SpringUtil.isEmpty(updateList)){
            return;
        }
        int index = 0;
        int count = 2000;
        while (true) {
            List<CommodityFirstCateTax> items = updateList.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                commodityTaxFirstCateMapper.batchUpdateOnlineSales(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 鲜家加盟-分类数据报表、商品分析表
     * @param idto
     * @return
     */
    public TablePageInfo<ShopTaxForJmByCateODTO> shopTaxForJmByCate(ShopTaxForJmByCateIDTO idto){
        TablePageInfo<ShopTaxForJmByCateODTO> tablePageInfo = new TablePageInfo<>();
        QYAssert.notNull(idto.getType(), "汇总类型不能为空");
        QYAssert.notNull(idto.getStartDate(), "开始日期不能为空");
        QYAssert.notNull(idto.getEndDate(), "结束日期不能为空");

        // 时间判断，选门店可以查询31天，不选门店只能查询1天
        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), "yyyy-MM-dd"), DateUtil.parseDate(idto.getStartDate(), "yyyy-MM-dd"));
        if(idto.getShopId() != null && idto.getShopId() > 0) {
            QYAssert.isTrue(diff < 31, "最多可以查询31天");
        }else {
            QYAssert.isTrue(diff < 1, "请选择门店");
        }

        QYAssert.isTrue(null != idto.getType(), "分类类型不能为空");
        idto.setShopType(ShopTypeEnums.XSJM.getCode());

        Set<String> keyList = new HashSet<>();
        CommodityTaxInfoByShopIDTO dto = this.handleCommodityTaxFromReturnOrderByShopIDTO(idto);
        // 查询退货
        List<CommodityTaxReportODto> returnOrderList = shopReportClient.getCommodityTaxFromReturnOrderByShop(dto);
        Map<String, CommodityTaxReportODto> returnOrderMap = this.getCommodityTaxReportMap(keyList, returnOrderList, idto.getType());
        // 查询进货
        List<CommodityTaxReportODto> orderList = shopReportClient.getCommodityTaxFromSaleOrderByShop(dto);
        Map<String, CommodityTaxReportODto> orderMap = this.getCommodityTaxReportMap(keyList, orderList, idto.getType());
        // 查询销售金额
        List<ShopTaxForJmByCateODTO> salesList = commodityShopTaxMapper.getPosCommodityTaxFromOfflineByCate(idto);
        Map<String, ShopTaxForJmByCateODTO> salesMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(salesList)){
            salesMap = salesList.stream().collect(Collectors.toMap(it -> {
                if(XsjmReportTypeEnum.FIRST_CATE_SUMMARY.getCode().equals(idto.getType())){
                    String key = it.getShopId() + "-" + it.getCommodityFirstKindId();
                    keyList.add(key);
                    return key;
                }else if(XsjmReportTypeEnum.SECOND_CATE_SUMMARY.getCode().equals(idto.getType())){
                    String key = it.getShopId() + "-" + it.getCommoditySecondKindId();
                    keyList.add(key);
                    return key;
                }else if(XsjmReportTypeEnum.THIRD_CATE_SUMMARY.getCode().equals(idto.getType())){
                    String key = it.getShopId() + "-" + it.getCommodityThirdKindId();
                    keyList.add(key);
                    return key;
                }else if(XsjmReportTypeEnum.COMMODITY_SUMMARY.getCode().equals(idto.getType())){
                    String key = it.getShopId() + "-" + it.getCommodityId();
                    keyList.add(key);
                    return key;
                }else {
                    return "";
                }
            } , Function.identity()));
        }
        // 组合并且过滤毛利率, 排序, 求sum
        if(SpringUtil.isEmpty(keyList)){
            tablePageInfo.setList(new ArrayList<>());
            return tablePageInfo;
        }
        List<ShopTaxForJmByCateODTO> list = new ArrayList<>();
        ShopTaxForJmByCateODTO sum = new ShopTaxForJmByCateODTO();
        BigDecimal totalSalesSum = BigDecimal.ZERO;
        BigDecimal orderTotalSum = BigDecimal.ZERO;
        BigDecimal orderQuantySum = BigDecimal.ZERO;
        BigDecimal totalQuantitySum = BigDecimal.ZERO;
        for (String key : keyList) {
            CommodityTaxReportODto returnEntry = returnOrderMap.get(key);
            //退货金额
            BigDecimal saleReturnOrderTotal = (null == returnEntry || null == returnEntry.getSaleReturnOrderTotal())?
                    BigDecimal.ZERO : returnEntry.getSaleReturnOrderTotal();
            //退货数量
            BigDecimal saleReturnOrderQuantity = (null == returnEntry || null == returnEntry.getSaleReturnOrderQuantity())?
                    BigDecimal.ZERO : returnEntry.getSaleReturnOrderQuantity();


            CommodityTaxReportODto orderEntry = orderMap.get(key);
            // 进货金额
            BigDecimal orderTotal = (null == orderEntry || null == orderEntry.getOrderTotalDecimal())?
                    BigDecimal.ZERO : orderEntry.getOrderTotalDecimal();
            // 进货数量
            BigDecimal orderQuanty = (null == orderEntry || null == orderEntry.getOrderQuanty())?
                    BigDecimal.ZERO : orderEntry.getOrderQuanty();

            ShopTaxForJmByCateODTO saleEntry = salesMap.get(key);
            // 销售数量合计=销售数量+赠品数量
            BigDecimal totalQuantity = (null == saleEntry || null == saleEntry.getTotalQuantity())?
                    BigDecimal.ZERO : saleEntry.getTotalQuantity();
            totalQuantity = (null == saleEntry || null == saleEntry.getGiveQuantity()) ?
                    BigDecimal.ZERO : totalQuantity.add(saleEntry.getGiveQuantity());
            // 销售金额
            BigDecimal totalSales = (null == saleEntry || null == saleEntry.getOrderQuanty())?
                    BigDecimal.ZERO : saleEntry.getTotalSales();
            // 销售金额=销售成交金额合计-退货金额合计
            totalSales = totalSales;
            // 进货金额=实收金额合计-退货金额合计-少货金额合计
            orderTotal = orderTotal.subtract(saleReturnOrderTotal);
            // 进货数量=实收数量合计-退货数量合计-少货数量合计
            orderQuanty = orderQuanty.subtract(saleReturnOrderQuantity);
            // 销售数量=销售数量合计-退货数量合计
            totalQuantity = totalQuantity;
            //毛利额=销售金额-进货金额
            BigDecimal grossProfitMargin = totalSales.subtract(orderTotal);
            // 毛利率=毛利额/销售金额*100
            BigDecimal grossMargin = totalSales.compareTo(BigDecimal.ZERO) == 0 ?
                    BigDecimal.ZERO : grossProfitMargin.divide(totalSales, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            // 损耗数量=进货数量-销售数量-退货数量-少货数量, 销售数量在上面已经减去了退货数量
            BigDecimal lossQuantity = orderQuanty.subtract(totalQuantity);
            // 判断毛利率大于最大或者小于最小, 则跳出去
            if(StringUtils.isNotBlank(idto.getGrossMarginMax()) && StringUtils.isNotBlank(idto.getGrossMarginMin())){
                BigDecimal grossMarginMax = new BigDecimal(idto.getGrossMarginMax());
                BigDecimal grossMarginMin = new BigDecimal(idto.getGrossMarginMin());
                if(grossMargin.compareTo(grossMarginMax) > 0 || grossMargin.compareTo(grossMarginMin) < 0){
                    continue;
                }
            }

            // 计算合计
            totalSalesSum = totalSalesSum.add(totalSales);
            orderTotalSum = orderTotalSum.add(orderTotal);
            orderQuantySum = orderQuantySum.add(orderQuanty);
            totalQuantitySum = totalQuantitySum.add(totalQuantity);

            ShopTaxForJmByCateODTO item = new ShopTaxForJmByCateODTO();
            this.handleCommodityItemTaxBaseInfoForJm(item, key, returnEntry, saleEntry, orderEntry, idto.getType());
            item.setTotalSales(totalSales);
            // 进货金额四舍五入保留2位
            item.setOrderTotal(orderTotal.setScale(2, BigDecimal.ROUND_HALF_UP));
            item.setGrossMargin(grossMargin);
            item.setGrossProfitMargin(grossProfitMargin);
            item.setOrderQuanty(orderQuanty);
            item.setTotalQuantity(totalQuantity);
            item.setLossQuantity(lossQuantity);
            list.add(item);
        }

        // 进货总金额四舍五入保留2位
        orderTotalSum = orderTotalSum.setScale(2, BigDecimal.ROUND_HALF_UP);
        renderService.render(list,"shopTaxForJmByCate");
        if(XsjmReportTypeEnum.FIRST_CATE_SUMMARY.getCode().equals(idto.getType())){
            list = list.stream()
                    .sorted(Comparator.comparing(ShopTaxForJmByCateODTO::getShopCode)
                            .thenComparing(ShopTaxForJmByCateODTO::getCommodityFirstKindCode))
                    .collect(Collectors.toList());
        }else if(XsjmReportTypeEnum.SECOND_CATE_SUMMARY.getCode().equals(idto.getType())){
            list = list.stream()
                    .sorted(Comparator.comparing(ShopTaxForJmByCateODTO::getShopCode)
                            .thenComparing(ShopTaxForJmByCateODTO::getCommoditySecondKindCode))
                    .collect(Collectors.toList());
        }else if(XsjmReportTypeEnum.THIRD_CATE_SUMMARY.getCode().equals(idto.getType())){
            list = list.stream()
                    .sorted(Comparator.comparing(ShopTaxForJmByCateODTO::getShopCode)
                            .thenComparing(ShopTaxForJmByCateODTO::getCommodityThirdKindCode))
                    .collect(Collectors.toList());
        }else if(XsjmReportTypeEnum.COMMODITY_SUMMARY.getCode().equals(idto.getType())){
            list = list.stream()
                    .sorted(Comparator.comparing(ShopTaxForJmByCateODTO::getShopCode)
                            .thenComparing(ShopTaxForJmByCateODTO::getCommodityCode))
                    .collect(Collectors.toList());
        }

        sum.setTotalQuantity(totalQuantitySum);
        sum.setTotalSales(totalSalesSum);
        sum.setOrderTotal(orderTotalSum);
        sum.setOrderQuanty(orderQuantySum);
        BigDecimal lossQuantitySum = orderQuantySum.subtract(totalQuantitySum);
        sum.setLossQuantity(lossQuantitySum);
        BigDecimal grossProfitMarginSum = totalSalesSum.subtract(orderTotalSum);
        sum.setGrossProfitMargin(grossProfitMarginSum);
        sum.setGrossMargin(null);

        PageInfo<ShopTaxForJmByCateODTO> pageInfo = ListToPageInfoUtil.convert(list, idto.getPageSize(), idto.getPageNo());
        tablePageInfo = BeanCloneUtils.copyTo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }

    private void handleCommodityItemTaxBaseInfoForJm( ShopTaxForJmByCateODTO item, String key,
                                                      CommodityTaxReportODto returnEntry,ShopTaxForJmByCateODTO saleEntry,
                                                      CommodityTaxReportODto orderEntry , Integer type){
        String[] keys = key.split("-");
        Long cateId = Long.parseLong(keys[1]);
        Long shopId = Long.parseLong(keys[0]);
        Long commodityFirstKindId = 0L;
        Long commoditySecondKindId = 0L;
        Long commodityThirdKindId = 0L;
        if(null != orderEntry){
            commodityFirstKindId = orderEntry.getCommodityFirstId();
            commoditySecondKindId = orderEntry.getCommoditySecondId();
            commodityThirdKindId = orderEntry.getCommodityThirdId();
        }else if(null != returnEntry){
            commodityFirstKindId = returnEntry.getCommodityFirstId();
            commoditySecondKindId = returnEntry.getCommoditySecondId();
            commodityThirdKindId = returnEntry.getCommodityThirdId();
        }else if(null != saleEntry){
            commodityFirstKindId = saleEntry.getCommodityFirstKindId();
            commoditySecondKindId = saleEntry.getCommoditySecondKindId();
            commodityThirdKindId = saleEntry.getCommodityThirdKindId();
        }
        if(XsjmReportTypeEnum.FIRST_CATE_SUMMARY.getCode().equals(type)){
            item.setCommodityFirstKindId(cateId);
        }else if(XsjmReportTypeEnum.SECOND_CATE_SUMMARY.getCode().equals(type)){
            item.setCommoditySecondKindId(cateId);
            item.setCommodityFirstKindId(commodityFirstKindId);
        }else if(XsjmReportTypeEnum.THIRD_CATE_SUMMARY.getCode().equals(type)){
            item.setCommodityFirstKindId(commodityFirstKindId);
            item.setCommoditySecondKindId(commoditySecondKindId);
            item.setCommodityThirdKindId(cateId);
        }else if(XsjmReportTypeEnum.COMMODITY_SUMMARY.getCode().equals(type)){
            item.setCommodityFirstKindId(commodityFirstKindId);
            item.setCommoditySecondKindId(commoditySecondKindId);
            item.setCommodityThirdKindId(commodityThirdKindId);
            item.setCommodityId(cateId);
        }
        item.setShopId(shopId);
    }

    private CommodityTaxInfoByShopIDTO handleCommodityTaxFromReturnOrderByShopIDTO(ShopTaxForJmByCateIDTO idto){
        String beginTime = idto.getStartDate() + " 00:00:00";
        String endTime = idto.getEndDate() + " 23:59:59";
        CommodityTaxInfoByShopIDTO dto = new CommodityTaxInfoByShopIDTO();
        List<Integer> shopTypeList = new ArrayList<>();
        shopTypeList.add(ShopTypeEnums.XSJM.getCode());
        dto.setShopTypeList(shopTypeList);
        dto.setBeginTime(beginTime);
        dto.setEndTime(endTime);
        if(null != idto.getShopId()) {
            dto.setShopIdList(Collections.singletonList(idto.getShopId()));
        }
        if(null != idto.getCate1()){
            dto.setCate1(idto.getCate1());
        }
        if(null != idto.getCate2()){
            dto.setCate2(idto.getCate2());
        }
        if(null != idto.getCate3()){
            dto.setCate3(idto.getCate3());
        }
        if(null != idto.getCommodityId()){
            dto.setCommodityId(idto.getCommodityId());
        }
        if(StringUtils.isNotBlank(idto.getBarCode())){
            dto.setBarCode(idto.getBarCode());
        }
        dto.setType(idto.getType());
        return dto;
    }

    private  Map<String, CommodityTaxReportODto> getCommodityTaxReportMap( Set<String> keyList, List<CommodityTaxReportODto> orderList,Integer type){
        Map<String, CommodityTaxReportODto> orderMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(orderList)){
            orderMap = orderList.stream().collect(Collectors.toMap(it -> {
                if(XsjmReportTypeEnum.FIRST_CATE_SUMMARY.getCode().equals(type)){
                    String key = it.getShopId() + "-" + it.getCommodityFirstId();
                    keyList.add(key);
                    return key;
                }else if(XsjmReportTypeEnum.SECOND_CATE_SUMMARY.getCode().equals(type)){
                    String key = it.getShopId() + "-" + it.getCommoditySecondId();
                    keyList.add(key);
                    return key;
                }else if(XsjmReportTypeEnum.THIRD_CATE_SUMMARY.getCode().equals(type)){
                    String key = it.getShopId() + "-" + it.getCommodityThirdId();
                    keyList.add(key);
                    return key;
                }else if(XsjmReportTypeEnum.COMMODITY_SUMMARY.getCode().equals(type)){
                    String key = it.getShopId() + "-" + it.getCommodityId();
                    keyList.add(key);
                    return key;
                }else {
                    return "";
                }
            } , Function.identity()));
        }
        return orderMap;
    }


}
