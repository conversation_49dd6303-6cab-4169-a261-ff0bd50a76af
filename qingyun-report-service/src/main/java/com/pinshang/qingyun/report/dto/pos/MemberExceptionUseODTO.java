package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberExceptionUseODTO extends Pagination {

	@ApiModelProperty(position = 1, value ="会员卡号")
	private String memberCardNo;

	@ApiModelProperty(position = 2, value ="会员姓名")
	private String memberName;

	@ApiModelProperty(position = 3, value ="会员类型")
	private String memberType;

	@ApiModelProperty(position = 4, value ="收银日期")
	private Date cashDate;

	@ApiModelProperty(position = 5, value ="销售数量")
	private Long saleCount;

	@ApiModelProperty(position = 6, value ="销售总金额")
	private BigDecimal saleTotalAmount;

	@ApiModelProperty(position = 7, value ="门店简称")
	private String shopName;

	@ApiModelProperty(position = 8, value ="门店编码")
	private String shopCode;

	@ApiModelProperty(position = 9, value ="客户编码")
	private String storeCode;

	@ApiModelProperty(position = 10, value ="门店id")
	private Long shopId;
}
