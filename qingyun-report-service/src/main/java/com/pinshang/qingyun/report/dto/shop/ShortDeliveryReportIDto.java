package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;

public class ShortDeliveryReportIDto extends Pagination{

	private static final long serialVersionUID = 1L;

	private Long enterpriseId;
	
	private Long shopId;

	/** 一级分类id */
	private Long    cateId1;
	/** 二级分类id */
	private Long    cateId2;
	/** 三级分类id */
	private Long    cateId3;
	
	private String beginDate;

	private String endDate;

	private String commodityKey;

	private String barCode;

	private String orderCode;

	private String factoryCode;

	private Boolean differ;
	private Integer shopType;
	private  Long commodityId;

	private Integer isHq;

	private Integer managementMode;

	public Integer getManagementMode() {
		return managementMode;
	}

	public void setManagementMode(Integer managementMode) {
		this.managementMode = managementMode;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public Integer getShopType() {
		return shopType;
	}

	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Long getCateId1() {
		return cateId1;
	}

	public void setCateId1(Long cateId1) {
		this.cateId1 = cateId1;
	}

	public Long getCateId2() {
		return cateId2;
	}

	public void setCateId2(Long cateId2) {
		this.cateId2 = cateId2;
	}

	public Long getCateId3() {
		return cateId3;
	}

	public void setCateId3(Long cateId3) {
		this.cateId3 = cateId3;
	}

	public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getCommodityKey() {
		return commodityKey;
	}

	public void setCommodityKey(String commodityKey) {
		this.commodityKey = commodityKey;
	}

	public String getOrderCode() {
		return orderCode;
	}

	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}

	public String getFactoryCode() {
		return factoryCode;
	}

	public void setFactoryCode(String factoryCode) {
		this.factoryCode = factoryCode;
	}

	public Boolean isDiffer() {
		return differ;
	}

	public void setDiffer(Boolean differ) {
		this.differ = differ;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public Integer getIsHq() {
		return isHq;
	}

	public void setIsHq(Integer isHq) {
		this.isHq = isHq;
	}
}
