package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AIMonitorPaymentDTO {

    @ApiModelProperty(position = 2, value = "支付类型 收入 支出1:收入2:支出")
    private Integer saleType;

    @ApiModelProperty(position = 3, value = "支付方式 现金 支付宝 微信等")
    private String payType;

    @ApiModelProperty(position = 4, value = "支付方式 现金 支付宝 微信等名称")
    private String payTypeName;

    @ApiModelProperty(position = 5, value = "支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(position = 1, value = "支付id")
    private Long paymentId;

    @ApiModelProperty(position = 6, value = "用户支付金额")
    private BigDecimal userPayment;
}
