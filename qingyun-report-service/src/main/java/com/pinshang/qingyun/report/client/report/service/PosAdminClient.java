package com.pinshang.qingyun.report.client.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.client.report.hystrix.PosAdminClientHystrix;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.dto.pos.joinShop.PayTypeListDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_POS_ADMIN_SERVICE, fallbackFactory = PosAdminClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PosAdminClient {

	/**
	 * 获取零售设置
	 * @return
	 */
	@RequestMapping(value = "/retailSetting/getRetailSettingODTO", method = RequestMethod.GET)
	RetailSettingODTO getRetailSettingODTO();

	@GetMapping("/mac/getMacById/{macId}")
	MacODTO getMacById(@PathVariable("macId") Long  macId);

	@GetMapping("/joinShop/withDrawRateList")
	List<PayTypeListDTO> withDrawRateList();

	@GetMapping("/payment/print")
	TradeODTO queryPrint(@RequestParam(value = "orderCode", required = false) Long orderCode);


}
