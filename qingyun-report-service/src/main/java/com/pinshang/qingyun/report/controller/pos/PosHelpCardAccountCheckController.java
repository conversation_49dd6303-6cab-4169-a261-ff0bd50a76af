package com.pinshang.qingyun.report.controller.pos;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.service.pos.PosHelpCardAccountCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName HelpCardAccountCheckingController
 * <AUTHOR>
 * @Date 2023/2/23 17:26
 * @Description HelpCardAccountCheckingController
 * @Version 1.0
 */
@Api(value = "PosHelpCardAccountCheck")
@RestController
@RequestMapping("/posHelpCardAccountCheck")
@Slf4j
public class PosHelpCardAccountCheckController {
    @Autowired
    private PosHelpCardAccountCheckService posHelpCardAccountCheckService;

    @MethodRender
    @ApiOperation(value = "帮困卡交易对账分页", notes = "帮困卡交易对账分页", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/page")
    public PageInfo<HelpCardAccountCheckPageODTO> page(@RequestBody HelpCardAccountCheckPageIDTO idto){
        return posHelpCardAccountCheckService.page(idto);
    }

    @MethodRender
    @ApiOperation(value = "帮困卡交易对账品鲜交易金额显示详情", notes = "帮困卡交易对账品鲜交易金额显示详情", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/traderAmountDetail")
    public TablePageInfo<HelpCardAccountCheckTraderAmountDetailODTO> traderAmountDetail(@RequestBody HelpCardAccountCheckTraderAmountDetailIDTO idto){
        return posHelpCardAccountCheckService.traderAmountDetail(idto);
    }

    @MethodRender
    @ApiOperation(value = "帮困卡交易对账新增", notes = "帮困卡交易对账新增", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/add")
    public Boolean add(@RequestBody HelpCardAccountCheckAddIDTO idto){
        return posHelpCardAccountCheckService.add(idto);
    }

    @ApiOperation(value = "获取品鲜交易金额", notes = "获取品鲜交易金额", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectPxTraderAmount")
    public BigDecimal selectPxTraderAmount(@RequestBody HelpCardSelectPxTraderAmountIDTO idto){
        return posHelpCardAccountCheckService.selectPxTraderAmount(idto);
    }

    @ApiOperation(value = "帮困卡交易对账详情", notes = "帮困卡交易对账详情", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/detail")
    public HelpCardAccountCheckDetailODTO detail(@RequestParam("id") Long id){
        return posHelpCardAccountCheckService.detail(id);
    }

    @ApiOperation(value = "帮困卡交易对账 状态修改", notes = "帮困卡交易对账 状态修改", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/statusUpdate")
    public Boolean statusUpdate(@RequestBody HelpCardAccountCheckStatusUpdateIDTO idto){
        return posHelpCardAccountCheckService.statusUpdate(idto);
    }

    /**
     * 根据 区域 所属公司 商品
     * 只查询已对账状态(过滤未对账)
     * @param idto
     * @return
     */
    @MethodRender
    @ApiOperation(value = "根据 区域 所属公司查询对账汇总", notes = "根据 区域 所属公司 查询对账汇总", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectCheckDetail4InvoiceByCompany")
    public PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByCompany(@RequestBody HelpCardCheckDetail4InvoiceIDTO idto){
        return posHelpCardAccountCheckService.selectCheckDetail4InvoiceByCompany(idto);
    }

    @MethodRender
    @ApiOperation(value = "根据 区域 查询对账汇总", notes = "根据 区域 查询对账汇总", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectCheckDetail4InvoiceByArea")
    public PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByArea(@RequestBody HelpCardCheckDetail4InvoiceIDTO idto){
        return posHelpCardAccountCheckService.selectCheckDetail4InvoiceByArea(idto);
    }

    @MethodRender
    @ApiOperation(value = "查询对账明细", notes = "查询对账明细", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectCheckDetail4Invoice")
    public List<HelpCardCheckDetail4InvoiceItemODTO> selectCheckDetail4Invoice(@RequestBody HelpCardCheckDetail4InvoiceItemIDTO idto){
        return posHelpCardAccountCheckService.selectCheckDetail4Invoice(idto);
    }


    @ApiOperation(value = "帮困卡支付金额结算数据", notes = "帮困卡支付金额结算数据", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectPxAmountByStoreIdList")
    public List<HelpCardPxAmountByStoreIdListODTO> selectPxAmountByStoreIdList(@RequestBody HelpCardPxAmountByStoreIdListIDTO idto){
        return posHelpCardAccountCheckService.selectPxAmountByStoreIdList(idto);
    }
}
