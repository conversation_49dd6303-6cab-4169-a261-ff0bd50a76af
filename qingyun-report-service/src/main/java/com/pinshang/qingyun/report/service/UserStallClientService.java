package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 大店补货  服务service
 * </p>
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserStallClientService {

    private final UserStallClient userStallClient;

    public List<Long> selectUserStallIdList(Long userId,Long shopId) {
        SelectUserStallIdListIDTO idto  = new SelectUserStallIdListIDTO();
        idto.setUserId(userId);
        idto.setShopId(shopId);
        idto.setStallStatus(YesOrNoEnums.YES.getCode());
        return userStallClient.selectUserStallIdList(idto);
    }
}
