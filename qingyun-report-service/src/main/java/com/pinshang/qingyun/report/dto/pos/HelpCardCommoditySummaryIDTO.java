package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HelpCardCommoditySummaryIDTO
 * <AUTHOR>
 * @Date 2023/2/27 10:35
 * @Description HelpCardCommoditySummaryIDTO
 * @Version 1.0
 */
@Data
public class HelpCardCommoditySummaryIDTO extends Pagination {
    @ApiModelProperty("交易日期-开始")
    private String beginTime;

    @ApiModelProperty("交易日期-结束")
    private String endTime;

    @ApiModelProperty("部门code")
    private String orgCode;

    private Long shopId;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("条码")
    private String barCode;

    private List<Long> shopIdList;
}
