package com.pinshang.qingyun.report.dto.pos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDeleteMessage {
    private Long id;
    private Long shopId;
    private String shopName;
    private String posMacCode;
    private Long orderCode;
    private BigDecimal totalQuantity;
    private BigDecimal totalAmount;
    private Date deleteDate;
    private Long employeeId;
    private Integer deleteType;
    private List<OrderItem> orderItems;

    /**
     * 挂单人
     */
    private Long suspendId;

    /**
     * 挂单时间
     */
    private Date suspendDate;

    /**
     * 授权人账号
     */
    private String authorizerCode;

    /**
     * 授权人姓名
     */
    private String authorizerName;


    /** pos机id **/
    private Long posMacId;

    /** pos机类型 1-收银pos, 2-自助pos **/
    private Integer posType;
}
