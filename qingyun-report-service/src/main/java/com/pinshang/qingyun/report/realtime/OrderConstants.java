package com.pinshang.qingyun.report.realtime;

import java.util.concurrent.TimeUnit;

public class OrderConstants {


    public static class Daile{


        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{real_time_sales_monitoring}:order:daily:";
        //过期时间
        public static final int EXPIRE_TIME = 60;
        public static final TimeUnit TIME_UNIT = TimeUnit.DAYS;
        //全部门店的单日总销售   销售趋势图
        public static final String TOTAL_TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "total_turnover:";
        //全部门店的单日总毛利  趋势图
        public static final String TOTAL_GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "total_gross_profit:";
        //全部门店的单日订单量
        public static final String ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "order_quantity:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //门店订单量快照 由定时每N分钟一次
        public static final String SHOP_ORDER_QUANTITY_SNAPSHOT = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity_snapshot:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店月销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";

        public static class Online{
            private static final String DAILY_REAL_TIME_SALES_MONITORING = "{real_time_sales_monitoring}:order:daily:ONLINE:";
            //过期时间
            public static final int EXPIRE_TIME = 60;
            public static final TimeUnit TIME_UNIT = TimeUnit.DAYS;
            //全部门店的单日总销售   销售趋势图
            public static final String TOTAL_TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "total_turnover:";
            //全部门店的单日总毛利  趋势图
            public static final String TOTAL_GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "total_gross_profit:";
            //全部门店的单日订单量
            public static final String ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "order_quantity:";
            //门店订单量
            public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
            //门店订单量快照 由定时每N分钟一次
            public static final String SHOP_ORDER_QUANTITY_SNAPSHOT = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity_snapshot:";
            //单门店毛利 排名用
            public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
            //单门店月销售 排名用
            public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
        }

        public static class Pos{
            private static final String DAILY_REAL_TIME_SALES_MONITORING = "{real_time_sales_monitoring}:order:daily:POS:";
            //过期时间
            public static final int EXPIRE_TIME = 60;
            public static final TimeUnit TIME_UNIT = TimeUnit.DAYS;
            //全部门店的单日总销售   销售趋势图
            public static final String TOTAL_TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "total_turnover:";
            //全部门店的单日总毛利  趋势图
            public static final String TOTAL_GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "total_gross_profit:";
            //全部门店的单日订单量
            public static final String ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "order_quantity:";
            //门店订单量
            public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
            //门店订单量快照 由定时每N分钟一次
            public static final String SHOP_ORDER_QUANTITY_SNAPSHOT = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity_snapshot:";
            //单门店毛利 排名用
            public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
            //单门店销售 排名用
            public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
        }


    }

    public static class Monthly{
        private static final String MONTHLY_REAL_TIME_SALES_MONITORING = "{real_time_sales_monitoring}:order:monthly:";

        //过期时间
        public static final int EXPIRE_TIME = 400;
        public static final TimeUnit TIME_UNIT = TimeUnit.DAYS;
        //单门店月销售 排名用
        public static final String TURNOVER = MONTHLY_REAL_TIME_SALES_MONITORING + "turnover:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = MONTHLY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店订单数量
        public static final String SHOP_ORDER_QUANTITY = MONTHLY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";

        //类总销售 环形占比
        public static final String CATEGORY_SALES = MONTHLY_REAL_TIME_SALES_MONITORING + "category_sales:";
        //全部门店的月总销售
        public static final String TOTAL_TURNOVER = MONTHLY_REAL_TIME_SALES_MONITORING + "total_turnover:";
        //全部门店的月总毛利
        public static final String TOTAL_GROSS_PROFIT = MONTHLY_REAL_TIME_SALES_MONITORING + "total_gross_profit:";
        //全部门店的月订单量
        public static final String ORDER_QUANTITY = MONTHLY_REAL_TIME_SALES_MONITORING + "order_quantity:";
    }

    public static class Year{
        private static final String YEAR_REAL_TIME_SALES_MONITORING = "{real_time_sales_monitoring}:order:year:";
        //过期时间
        public static final int EXPIRE_TIME = 1000;
        public static final TimeUnit TIME_UNIT = TimeUnit.DAYS;
        //全部门店的单日总销售  销售趋势图
        public static final String TOTAL_TURNOVER = YEAR_REAL_TIME_SALES_MONITORING + "total_turnover:";
        //全部门店的单日总毛利  趋势图
        public static final String TOTAL_GROSS_PROFIT = YEAR_REAL_TIME_SALES_MONITORING + "total_gross_profit:";
        //全部门店的单日订单量
        public static final String ORDER_QUANTITY = YEAR_REAL_TIME_SALES_MONITORING + "order_quantity:";

        public static final String SHOP_ORDER_QUANTITY = YEAR_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
    }
}
