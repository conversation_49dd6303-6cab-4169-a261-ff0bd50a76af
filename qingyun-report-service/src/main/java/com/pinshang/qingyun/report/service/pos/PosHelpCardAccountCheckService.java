package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.client.report.service.PosHelpCardClient;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.HelpCardAccountCheckStatusEnum;
import com.pinshang.qingyun.report.mapper.pos.HelpCardAccountCheckItemMapper;
import com.pinshang.qingyun.report.mapper.pos.HelpCardAccountCheckMapper;
import com.pinshang.qingyun.report.mapper.pos.HelpCardTraderSummaryMapper;
import com.pinshang.qingyun.report.model.pos.HelpCardAccountCheck;
import com.pinshang.qingyun.report.model.pos.HelpCardAccountCheckItem;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.store.dto.storeCompany.StoreCompanyNameODTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName PosHelpCardAccountCheckingService
 * <AUTHOR>
 * @Date 2023/2/23 17:28
 * @Description PosHelpCardAccountCheckingService
 * @Version 1.0
 */
@Service
public class PosHelpCardAccountCheckService {

    @Autowired
    private HelpCardAccountCheckMapper helpCardAccountCheckMapper;

    @Autowired
    private HelpCardTraderSummaryMapper helpCardTraderSummaryMapper;

    @Autowired
    private HelpCardAccountCheckItemMapper helpCardAccountCheckItemMapper;

    @Autowired
    private PosHelpCardClient posHelpCardClient;

    @Autowired
    private StoreCompanyClient storeCompanyClient;


    public PageInfo<HelpCardAccountCheckPageODTO> page(@RequestBody HelpCardAccountCheckPageIDTO idto){
        PageInfo<HelpCardAccountCheckPageODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardAccountCheckMapper.page(idto);
        });
        List<HelpCardAccountCheckPageODTO> list = pageInfo.getList();
        if(SpringUtil.isNotEmpty(list)){
            list.forEach(it -> {
                String checkDateBeginStr = DateUtil.get4yMd(it.getCheckDateBegin());
                String checkDateEndStr = DateUtil.get4yMd(it.getCheckDateEnd());
                it.setCheckDateRange(checkDateBeginStr.concat("~").concat(checkDateEndStr));
            });
        }
       return pageInfo;
    }

    public TablePageInfo<HelpCardAccountCheckTraderAmountDetailODTO> traderAmountDetail(HelpCardAccountCheckTraderAmountDetailIDTO idto){
        TablePageInfo<HelpCardAccountCheckTraderAmountDetailODTO> tablePageInfo = new TablePageInfo<>();
        PageInfo<HelpCardAccountCheckTraderAmountDetailODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardAccountCheckMapper.traderAmountDetail(idto.getCheckId(), idto.getShopId());
        });
        List<HelpCardAccountCheckTraderAmountDetailODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }

        BigDecimal sum = helpCardAccountCheckMapper.traderAmountDetailSum(idto.getCheckId(), idto.getShopId());
        tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo,TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean statusUpdate(HelpCardAccountCheckStatusUpdateIDTO idto){
        HelpCardAccountCheck oldRecord = helpCardAccountCheckMapper.selectByPrimaryKey(idto.getCheckId());
        if(idto.getStatus() == HelpCardAccountCheckStatusEnum.NOT_CHECK.getCode()){
            QYAssert.isTrue(HelpCardAccountCheckStatusEnum.CHECKED.getCode() == oldRecord.getStatus(), "当前对账状态不是已对账");
            HelpCardAccountCheck checkInfo = helpCardAccountCheckMapper.selectByPrimaryKey(idto.getCheckId());
            // 查询当前区域是否还有其它未对账
            Example example = new Example(HelpCardAccountCheck.class);
            example.createCriteria().andEqualTo("areaId", checkInfo.getAreaId()).andEqualTo("status", 1).andNotEqualTo("id", idto.getCheckId());
            List<HelpCardAccountCheck> areaCheckInfoList = helpCardAccountCheckMapper.selectByExample(example);
            QYAssert.isTrue(SpringUtil.isEmpty(areaCheckInfoList), "该区域存在未对账的对账单");
        }else if(idto.getStatus() == HelpCardAccountCheckStatusEnum.CHECKED.getCode()){
            QYAssert.isTrue(HelpCardAccountCheckStatusEnum.NOT_CHECK.getCode() == oldRecord.getStatus(), "当前对账状态不是未对账");
        }else if(idto.getStatus() == HelpCardAccountCheckStatusEnum.CANCEL.getCode()){
            QYAssert.isTrue(HelpCardAccountCheckStatusEnum.NOT_CHECK.getCode() == oldRecord.getStatus(), "当前对账状态不是待对账");
        }
        HelpCardAccountCheck updateEntity = new HelpCardAccountCheck();
        updateEntity.setId(idto.getCheckId());
        updateEntity.setStatus(idto.getStatus());
        updateEntity.preUpdate();
        helpCardAccountCheckMapper.updateByPrimaryKeySelective(updateEntity);
        return true;
    }

    public HelpCardAccountCheckDetailODTO detail(@RequestParam("id") Long id){
        HelpCardAccountCheck checkInfo = helpCardAccountCheckMapper.selectByPrimaryKey(id);
        return BeanCloneUtils.copyTo(checkInfo, HelpCardAccountCheckDetailODTO.class);
    }

    public BigDecimal selectPxTraderAmount(HelpCardSelectPxTraderAmountIDTO idto){
        QYAssert.isTrue(null!= idto.getBeginDate() && null!= idto.getEndDate(), "日期不能为空");
        idto.setBeginDate(idto.getBeginDate() + " 00:00:00");
        idto.setEndDate(idto.getEndDate() + " 23:59:59");
        return helpCardAccountCheckMapper.selectPxTraderAmount(idto.getAreaId(), idto.getBeginDate(), idto.getEndDate());
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean add(@RequestBody HelpCardAccountCheckAddIDTO idto){
        QYAssert.isTrue(null!= idto.getCheckDateBegin() && null!= idto.getCheckDateEnd(), "对账开始结束时间不能为空");
        idto.setCheckDateBegin(idto.getCheckDateBegin() + " 00:00:00");
        idto.setCheckDateEnd(idto.getCheckDateEnd() + " 23:59:59");
        // 查询当前区域是否还有其它未对账
        Example example = new Example(HelpCardAccountCheck.class);
        example.createCriteria().andEqualTo("areaId", idto.getAreaId()).andEqualTo("status", 1);
        List<HelpCardAccountCheck> areaCheckInfoList = helpCardAccountCheckMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isEmpty(areaCheckInfoList), "该区域存在未对账的对账单");
        // 与该区域已对账的对账单有日期交叉
        List<Long> unionList = helpCardAccountCheckMapper.selectUnionDateList(idto.getAreaId(), idto.getCheckDateBegin(), idto.getCheckDateEnd(), null);
        QYAssert.isTrue(SpringUtil.isEmpty(unionList), "与该区域已对账的对账单有日期交叉");
        HelpCardAccountCheck checkInfo = new HelpCardAccountCheck();
        checkInfo.setAreaId(idto.getAreaId());
        checkInfo.setCheckAmount(idto.getCheckAmount());
        checkInfo.setCheckDateBegin(DateUtil.parseDate(idto.getCheckDateBegin(), "yyyy-MM-dd HH:mm:ss"));
        checkInfo.setCheckDateEnd(DateUtil.parseDate(idto.getCheckDateEnd(), "yyyy-MM-dd HH:mm:ss"));
        checkInfo.setCheckDateMonth(idto.getCheckDateMonth());
        BigDecimal pxTraderAmount = helpCardAccountCheckMapper.selectPxTraderAmount(idto.getAreaId(), idto.getCheckDateBegin(), idto.getCheckDateEnd());
        checkInfo.setPxTraderAmount(null == pxTraderAmount ? BigDecimal.ZERO : pxTraderAmount);
        checkInfo.setDiffAmount(checkInfo.getCheckAmount().subtract(checkInfo.getPxTraderAmount()));
        checkInfo.setStatus(HelpCardAccountCheckStatusEnum.NOT_CHECK.getCode());
        checkInfo.preInsert();
        HelpCardAreaDetailODTO areaInfo = posHelpCardClient.areaSettingDetail(idto.getAreaId());
        checkInfo.setAreaName(areaInfo.getAreaName());
        helpCardAccountCheckMapper.insertSelective(checkInfo);
        this.insertCheckItem(idto.getCheckDateBegin(), idto.getCheckDateEnd(), checkInfo.getId(), idto.getAreaId());
        return true;
    }


    /**
     * 插入对账详情
     * @param beginTime
     * @param endTime
     * @param checkId
     * @return
     */
    private boolean insertCheckItem(String beginTime , String endTime, Long checkId, Long areaId ){
        List<HelpCardAccountCheckItem> checkItemList = helpCardTraderSummaryMapper.selectAmount4CheckItem(beginTime, endTime, areaId);
        if(SpringUtil.isEmpty(checkItemList)){
            return true;
        }
        // 获取companyId
        List<Long> storeIdList = checkItemList.stream().map(HelpCardAccountCheckItem::getStoreId).collect(Collectors.toList());
        List<StoreCompanyNameODTO> storeInfoList = storeCompanyClient.selectComapnyNameByStoreIds(storeIdList);
        Map<Long, Long> companyMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(storeInfoList)) {
            companyMap = storeInfoList.stream().filter(it -> null != it.getCompanyId()).collect(Collectors.toMap(StoreCompanyNameODTO::getStoreId, StoreCompanyNameODTO::getCompanyId));
        }
        for(HelpCardAccountCheckItem it : checkItemList){
            it.setCompanyId(companyMap.get(it.getStoreId()));
            it.setCheckId(checkId);
            it.preInsert();
        }
        helpCardAccountCheckItemMapper.insertList(checkItemList);
        return true;
    }

    /**
     * 根据 区域 所属公司 商品
     * 只查询已对账状态(过滤未对账)
     * @param idto
     * @return
     */
    public PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByCompany(HelpCardCheckDetail4InvoiceIDTO idto){
        PageInfo<HelpCardCheckDetail4InvoiceODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardAccountCheckItemMapper.selectCheckDetail4InvoiceByCompany(idto);
        });
        List<HelpCardCheckDetail4InvoiceODTO> list = pageInfo.getList();
        if (SpringUtil.isNotEmpty(list)){
            List<Long> areaIdList = list.stream().map(HelpCardCheckDetail4InvoiceODTO::getAreaId).collect(Collectors.toList());
            Map<Long, String> rationMap = this.getServiceChargeRatioMap(areaIdList);
            list.forEach(it -> {
                if(it.getPxTotalAmount().compareTo(BigDecimal.ZERO)!=0) {
                    BigDecimal rate = it.getPxAmount().divide(it.getPxTotalAmount(), 9, RoundingMode.HALF_UP);
                    it.setTotalAmount(it.getTotalAmount().multiply(rate).setScale(2,RoundingMode.HALF_UP));
                }
                if(null != it.getCheckDateBegin() && null != it.getCheckDateEnd()) {
                    // 计算手续费 对账金额*(1-手续费比例)
                    if(null != rationMap.get(it.getAreaId())){
                        BigDecimal ration = new BigDecimal(rationMap.get(it.getAreaId()));
                        BigDecimal invoiceRation = BigDecimal.ONE.subtract(ration);
                        it.setInvoiceAmount(it.getTotalAmount().multiply(invoiceRation).setScale(2, RoundingMode.HALF_UP));
                    }
                    String beginDate = DateUtil.get4yMd(it.getCheckDateBegin());
                    String endDate = DateUtil.get4yMd(it.getCheckDateEnd());
                    it.setCheckDateRange(beginDate.concat("~").concat(endDate));
                }
            });
        }
        return pageInfo;
    }

    private Map<Long, String> getServiceChargeRatioMap(List<Long> areaIdList){
        HelpCardAreaSettingPageIDTO areaIdto = new HelpCardAreaSettingPageIDTO();
        areaIdto.setAreaIdList(areaIdList);
        List<HelpCardAreaSettingPageODTO> areaInfoList = posHelpCardClient.areaDetailListByIds(areaIdto);
        if(SpringUtil.isNotEmpty(areaInfoList)){
           return areaInfoList.stream().collect(Collectors.toMap(HelpCardAreaSettingPageODTO::getId, HelpCardAreaSettingPageODTO::getServiceChargeRatio));
        }
        return new HashMap<>();
    }

    public PageInfo<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByArea(HelpCardCheckDetail4InvoiceIDTO idto){
        PageInfo<HelpCardCheckDetail4InvoiceODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardAccountCheckItemMapper.selectCheckDetail4InvoiceByArea(idto);
        });
        List<HelpCardCheckDetail4InvoiceODTO> list = pageInfo.getList();
        if (SpringUtil.isNotEmpty(list)){
            list.forEach(it -> {
                List<Long> areaIdList = list.stream().map(HelpCardCheckDetail4InvoiceODTO::getAreaId).collect(Collectors.toList());
                Map<Long, String> rationMap = this.getServiceChargeRatioMap(areaIdList);
                if(null != it.getCheckDateBegin() && null != it.getCheckDateEnd()) {
                    // 计算手续费 对账金额*(1-手续费比例)
                    if(null != rationMap.get(it.getAreaId())){
                        BigDecimal ration = new BigDecimal(rationMap.get(it.getAreaId()));
                        BigDecimal invoiceRation = BigDecimal.ONE.subtract(ration);
                        it.setInvoiceAmount(it.getTotalAmount().multiply(invoiceRation).setScale(2, RoundingMode.HALF_UP));
                    }
                    String beginDate = DateUtil.get4yMd(it.getCheckDateBegin());
                    String endDate = DateUtil.get4yMd(it.getCheckDateEnd());
                    it.setCheckDateRange(beginDate.concat("~").concat(endDate));
                }
            });
        }
        return pageInfo;
    }

    public List<HelpCardCheckDetail4InvoiceItemODTO> selectCheckDetail4Invoice(HelpCardCheckDetail4InvoiceItemIDTO idto){
        // step 1 获取开票金额
        BigDecimal sumAmount = BigDecimal.ZERO;
        BigDecimal sumInvoiceAmount = BigDecimal.ZERO;
        HelpCardCheckDetail4InvoiceIDTO idtoInvoice = new HelpCardCheckDetail4InvoiceIDTO();
        idtoInvoice.setIdList(idto.getCheckIds());
        HelpCardCheckDetail4InvoiceODTO helpCardCheckDetail4InvoiceODTO = null;
        if(idto.getCompanyId()!=null){
            idtoInvoice.setCompanyId(idto.getCompanyId());
            PageInfo<HelpCardCheckDetail4InvoiceODTO> page = selectCheckDetail4InvoiceByCompany(idtoInvoice);
            sumInvoiceAmount = getSumTotalInvoice(sumInvoiceAmount, page);
        }else{
            PageInfo<HelpCardCheckDetail4InvoiceODTO> page = selectCheckDetail4InvoiceByArea(idtoInvoice);
            sumInvoiceAmount = getSumTotalInvoice(sumInvoiceAmount, page);
        }
        // step 2 明细总计 算出 比例 金额合计 分母
        List<HelpCardCheckDetail4InvoiceItemODTO> helpCardCheckDetail4InvoiceItemODTOS = helpCardAccountCheckMapper.selectCheckDetail4Invoice(idto);
        for (HelpCardCheckDetail4InvoiceItemODTO helpCardCheckDetail4InvoiceItemODTO : helpCardCheckDetail4InvoiceItemODTOS) {
            sumAmount = sumAmount.add(helpCardCheckDetail4InvoiceItemODTO.getAmount());
        }

        // step 3 算出明细 金额
        BigDecimal avgSumAmount = BigDecimal.ZERO;
        for (HelpCardCheckDetail4InvoiceItemODTO helpCardCheckDetail4InvoiceItemODTO : helpCardCheckDetail4InvoiceItemODTOS) {
            BigDecimal rate = helpCardCheckDetail4InvoiceItemODTO.getAmount().divide(sumAmount, 9, RoundingMode.HALF_UP);
            BigDecimal avgInvoice = sumInvoiceAmount.multiply(rate).setScale(2,RoundingMode.HALF_UP);
            helpCardCheckDetail4InvoiceItemODTO.setAmount(avgInvoice);
            avgSumAmount = avgSumAmount.add(avgInvoice);
        }
        // step 4 存在差异 扣除
        if(sumInvoiceAmount.compareTo(avgSumAmount)!=0){
            BigDecimal subtractInvoice = sumInvoiceAmount.subtract(avgSumAmount);
            for (HelpCardCheckDetail4InvoiceItemODTO helpCardCheckDetail4InvoiceItemODTO : helpCardCheckDetail4InvoiceItemODTOS) {
                if(helpCardCheckDetail4InvoiceItemODTO.getAmount().doubleValue() > subtractInvoice.doubleValue()){
                    helpCardCheckDetail4InvoiceItemODTO.setAmount(helpCardCheckDetail4InvoiceItemODTO.getAmount().add(subtractInvoice));
                    break;
                }
            }
        }
        return helpCardCheckDetail4InvoiceItemODTOS;
    }

    private BigDecimal getSumTotalInvoice(BigDecimal sumInvoiceAmount, PageInfo<HelpCardCheckDetail4InvoiceODTO> page) {
        if(page.getList() !=null && !page.getList().isEmpty()) {
            for (HelpCardCheckDetail4InvoiceODTO cardCheckDetail4InvoiceODTO : page.getList()) {
                sumInvoiceAmount = sumInvoiceAmount.add(cardCheckDetail4InvoiceODTO.getInvoiceAmount());
            }
        }
        return sumInvoiceAmount;
    }

    public List<HelpCardPxAmountByStoreIdListODTO> selectPxAmountByStoreIdList(HelpCardPxAmountByStoreIdListIDTO idto){
        if(StringUtils.isNotBlank(idto.getBeginDate())){
            idto.setBeginDate(idto.getBeginDate() + " 00:00:00");
        }
        if(StringUtils.isNotBlank(idto.getEndDate())){
            idto.setEndDate(idto.getEndDate() + " 23:59:59");
        }
        return helpCardTraderSummaryMapper.selectPxAmountByStoreIdList(idto);
    }

}
