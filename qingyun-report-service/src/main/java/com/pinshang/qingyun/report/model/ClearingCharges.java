package com.pinshang.qingyun.report.model;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "t_clearing_charges")
@Data
//收银月报
public class ClearingCharges {

    private Long id;
    //导入日期
    private Date importDate;
    //合计实收
    private BigDecimal actualTotal;
    //手续费合计
    private BigDecimal poundage;
    //导入月份
    private String importDateMonth;

    private String createdName;

    private Integer isDelete;
    private Long createdId;
    private Date createTime;
}
