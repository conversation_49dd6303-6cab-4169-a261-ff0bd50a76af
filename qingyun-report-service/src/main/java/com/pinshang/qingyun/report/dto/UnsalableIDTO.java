package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class UnsalableIDTO extends Pagination {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("大类id")
    private Long cate1;

    @ApiModelProperty("中类id")
    private Long cate2;

    @ApiModelProperty("小类id")
    private Long cate3;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("滞销天数")
    private Integer unsalableDay;

    private List<Long> commodityIdList;
}
