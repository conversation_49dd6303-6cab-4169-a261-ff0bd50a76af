package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderSalesAnalysisExportRespVo {

    @ExcelProperty("门店")
    private String shopName;

    @ExcelProperty("分类")
    private String cateName;

    @ExcelProperty("订货金额")
    private BigDecimal orderTotal;

    @ExcelProperty("订货占比")
    private String orderTotalRatio;

    @ExcelProperty("销售金额")
    private BigDecimal offlineSales;

    @ExcelProperty("销售占比")
    private String offlineSalesRatio;

    @ExcelProperty("退货金额")
    private BigDecimal saleReturnOrderTotal;

    @ExcelProperty("销售与订货差额")
    private BigDecimal difference;
}
