package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AIMonitorCommodityDTO {

    @ApiModelProperty("是否称重")
    private Integer isWeight;

    @ApiModelProperty(position = 1, value = "itemId")
    private Long itemId;

    @ApiModelProperty(position = 2, value = "商品id")
    private Long commodityId;

    @ApiModelProperty(position = 3, value = "品名")
    private String commodityName;

    @ApiModelProperty(position = 4, value = "单位")
    private String commodityUnit;

    @ApiModelProperty(position = 5, value = "条形码")
    private String barCode;

    @ApiModelProperty(position = 6, value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(position = 7, value = "份数")
    private Integer num;

    @ApiModelProperty(position = 8, value = "原价")
    private BigDecimal price;

    @ApiModelProperty(position = 9, value = "售价")
    private BigDecimal salePrice;

    @ApiModelProperty(position = 10, value = "小计")
    private BigDecimal totalAmount;

    @ApiModelProperty("1删除 2减少 3单品改价和单品折扣")
    private Integer operateType;

    @ApiModelProperty("减少之前的数量")
    private BigDecimal oldQuantity;

    @ApiModelProperty("折扣信息")
    private Integer promotionType;



}
