package com.pinshang.qingyun.report.dto.pos;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReceiptDifferenceDTO {
    /**
     * 对账日期
     */
    private Date date;

    private Long shopId;

    /**
     * 收款项编码
     */
    private String receivableCode;

    /**
     * 收款项名称
     */
    private String receivableName;

    /**
     * 销售金额
     */
    private BigDecimal totalSale;

    /**
     * 手续费
     */
    private BigDecimal poundage;

    /**
     * 实收金额
     */
    private BigDecimal amount;

    /**
     * 差异
     */
    private BigDecimal difference;

    private Long createId;
}
