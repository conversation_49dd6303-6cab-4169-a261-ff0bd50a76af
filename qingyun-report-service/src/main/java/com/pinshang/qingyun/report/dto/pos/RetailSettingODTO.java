package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RetailSettingODTO {

	@ApiModelProperty("id")
	private Long id;
	@ApiModelProperty("后台特价单品允许手动折扣和议价")
	private Integer backAllowDiscount;
	@ApiModelProperty("单品的手动折扣基于后台特价（如果有特价）")
	private Integer singleAllowDiscount;
	@ApiModelProperty("后台促销商品允许整单折扣和整单议价")
	private Integer backPromotionalAllowDiscount;
	@ApiModelProperty("手动折扣或议价的商品允许整单折扣和整单议价")
	private Integer handleAllowDiscount;
	@ApiModelProperty("整单折扣基于优惠后的金额")
	private Integer discountAmount;
	@ApiModelProperty("收银时累加非称重的相同商品")
	private Integer bankNotWeighed;
	@ApiModelProperty("前台允许销售价格为空的商品")
	private Integer frontAllowEmpty;
	@ApiModelProperty("按单退货时，弹出选择商品窗口")
	private Integer returnCommodityModal;
	@ApiModelProperty("按单退货时，一单允许多次退货")
	private Integer returnAllowMore;
	@ApiModelProperty("现金结算时，金额尾数处理方式")
	private Integer amountDealType;
	@ApiModelProperty("每台收银机挂单不超过")
	private Integer restingOrderCountLimit;
	private Integer windowCloseTime;
	@ApiModelProperty("进入屏保的等待时长")
	private Integer wattingTime;
	@ApiModelProperty("同一个会员号一天内订单数大于等于execeptionOrderCount将被视作异常使用")
	private Integer execeptionOrderCount;

	@ApiModelProperty("同一个会员号一天内订单数大于等于x，将被视作异常使用")
	private Long orderMorethanException;

	@ApiModelProperty("同一个收银员一天内优惠总金额大于等于x元，将被视作异常操作")
	private BigDecimal discountMorethanException;

	@ApiModelProperty("同一个收银员一天内作废订单总金额大于等于x元，将被视作异常操作")
	private BigDecimal cancelMorethanException;
}
