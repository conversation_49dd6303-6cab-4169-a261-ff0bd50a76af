package com.pinshang.qingyun.report.dto.mdCheck;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class MdCheckReportODTO {

    /** 上级组织 */
    private Long orgId;
    private String orgCode;
    @ApiModelProperty("所属部门")
    private String orgName;

    private Long shopId;
    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;
    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("考核方案编码")
    private String planCode;
    @ApiModelProperty("考核方案名称")
    private String planName;

    @ApiModelProperty("考核方案开始日期")
    private String planBeginDate;
    @ApiModelProperty("考核方案结束日期")
    private String planEndDate;
    @ApiModelProperty("实际开始日期")
    private String realBeginDate;
    @ApiModelProperty("实际结束日期")
    private String realEndDate;


    @ApiModelProperty("考核类型（金额0 / 数量1）")
    private Integer type;

    @ApiModelProperty("考核商品组id")
    private String checkGroupId;
    @ApiModelProperty("考核商品组名称")
    private String checkGroupName;
    @ApiModelProperty("考核商品组商品list")
    private List<String> checkCommodityList;


    @ApiModelProperty("考核总目标")
    private BigDecimal totalPlanAim;
    @ApiModelProperty("日均考核目标")
    private BigDecimal dayPlanAim;

    @ApiModelProperty("期间考核目标")
    private BigDecimal durationPlanAim;
    @ApiModelProperty("期间实际完成")
    private BigDecimal completeAim;
    @ApiModelProperty("期间实际完成率")
    private String completeAimPercent;


}
