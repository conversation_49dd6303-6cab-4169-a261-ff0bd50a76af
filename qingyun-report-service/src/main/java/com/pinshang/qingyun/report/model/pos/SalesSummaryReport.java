package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_pos_report_sales_summary")
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesSummaryReport  extends BaseModel<SalesSummaryReport> {
    /* @Id
    private Long id;*/
    private Long orderCode;//订单编码

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    /** 一级大类ID*/
    private BigDecimal commodityFirstId;
    private String commodityFirstName;

    /** 二级大类ID*/
    private BigDecimal commoditySecondId;
    private String commoditySecondName;

    /** 三级大类ID*/
    private BigDecimal commodityThirdId;
    private String commodityThirdName;

    /** 商品ID*/
    private Long commodityId;

    /** 商品编码*/
    private String commodityCode;

    /** 商品名称*/
    private String commodityName;

    /** 助记码*/
    private String commodityAid;

    /** 规格*/
    private String commoditySpec;

    /** 单位*/
    private String commodityUnit;

    /** 售价(最终分摊价格)*/
    private BigDecimal salePrice;

    /** 零售价*/
    private BigDecimal commodityPrice;

    /** 销售数量*/
    private BigDecimal saleQuantity;

    /** 退货数量*/
    private BigDecimal returnQuantity;

    /** 赠送数量*/
    private BigDecimal giveQuantity;

    /** 销售金额*/
    private BigDecimal saleAmount;

    /** 销售成本*/
    private BigDecimal weightAmount;

    /** 退货金额*/
    private BigDecimal returnAmount;

    /** 赠送金额*/
    private BigDecimal giveAmount;

    /** 让利金额*/
    private BigDecimal discountAmount;

    /** 数量小计*/
    private BigDecimal tatalQuantity;

    /** 金额小计*/
    private BigDecimal tatalAmount;

    /** 税率*/
    private BigDecimal taxRate;

    /** 不含税金额*/
    private BigDecimal noTaxRateAmount;

    /** 税额*/
    private BigDecimal taxRateAmount;

    /** 销售日期*/
    private Date saleTime;

    /** 特价类型*/
    private String promotionType;

    /**
     * 促销方案：编码，名称
     */
    private String promotionKey;

    /**
     * 代销商  默认-1
     */
    private Long consignmentId;

    private Long stallId;
    //private Long createId;
   // private Date createTime;
}
