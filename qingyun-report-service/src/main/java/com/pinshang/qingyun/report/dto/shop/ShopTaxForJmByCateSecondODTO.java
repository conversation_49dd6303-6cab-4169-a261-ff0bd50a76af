package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/07/03
 * @Version 1.0
 */
@Data
public class ShopTaxForJmByCateSecondODTO {
    @ExcelIgnore
    private Long shopId;

    @ExcelProperty("门店")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ExcelIgnore
    @ApiModelProperty("大类id")
    private Long commodityFirstKindId;

    @ApiModelProperty("大类")
    @ExcelProperty("大类")
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateName,keyName = "commodityFirstKindId")
    private String commodityFirstKindName;

    @ExcelIgnore
    @ApiModelProperty("中类id")
    private Long commoditySecondKindId;

    @ExcelProperty("中类")
    @ApiModelProperty("中类")
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateName,keyName = "commodityFirstKindId")
    private String commoditySecondKindName;

    @ExcelIgnore
    @ApiModelProperty("小类id")
    private Long commodityThirdKindId;

    @ExcelIgnore
    @ApiModelProperty("小类")
    @FieldRender(fieldType = FieldTypeEnum.CATEGORY,fieldName = RenderFieldHelper.Category.cateName,keyName = "commodityFirstKindId")
    private String commodityThirdKindName;

    @ExcelProperty("销售金额")
    @ApiModelProperty("线下销售金额")
    private BigDecimal totalSales=BigDecimal.ZERO;

    @ExcelProperty("进货金额")
    @ApiModelProperty("进货金额")
    private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额

    @ExcelProperty("毛利额")
    @ApiModelProperty("毛利额")
    private BigDecimal grossProfitMargin = BigDecimal.ZERO;

    @ExcelIgnore
    @ApiModelProperty("毛利率")
    private BigDecimal grossMargin = BigDecimal.ZERO;

    @ExcelProperty("毛利率")
    @ApiModelProperty("毛利率")
    private String grossMarginStr ="0.00";

    @ExcelProperty("进货数量")
    @ApiModelProperty("进货数量")
    private BigDecimal orderQuanty =BigDecimal.ZERO;//进货数量

    @ExcelProperty("销售数量")
    @ApiModelProperty("线下销售数量")
    private BigDecimal totalQuantity=BigDecimal.ZERO;

    @ExcelProperty("损耗数量")
    @ApiModelProperty("损耗数量")
    private BigDecimal lossQuantity = BigDecimal.ZERO;

    @ExcelIgnore
    @ApiModelProperty("退货金额")
    private BigDecimal saleReturnOrderTotal=BigDecimal.ZERO;//退货金额

    @ExcelIgnore
    @ApiModelProperty("退货数量")
    private BigDecimal saleReturnOrderQuantity =BigDecimal.ZERO;//退货数量

    @ExcelIgnore
    @ApiModelProperty("赠品金额")
    private BigDecimal giveQuantity = BigDecimal.ZERO;

}

