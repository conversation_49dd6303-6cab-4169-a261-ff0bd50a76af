package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderCancelODTO {

    @ApiModelProperty("门店ID")
    private  Long shopId;

    @ApiModelProperty("门店code")
    private String shopCode;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("门店名称")
    private String shopName;


    @ApiModelProperty("收银员账号")
    private String casherCode;

    @ApiModelProperty("收银员姓名")
    private String casherName;

    @ApiModelProperty("收银日期")
    private Date casherDate;

    @ApiModelProperty("订单作废总金额")
    private BigDecimal cancelTotalAmount;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

    @ApiModelProperty("pos机类型名称")
    private String posTypeName;

}
