package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2021/7/8 10:03
 */
@Data
public class ReturnSalesWaterIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;
    @ApiModelProperty("所属部门编码")
    private String orgCode;

    @ApiModelProperty("门店id列表")
    private List<Long> shopIdList;
    @ApiModelProperty("日期开始")
    private String saleTimeBegin;
    @ApiModelProperty("日期结束")
    private String saleTimeEnd;
    @ApiModelProperty("收银员编号")
    private String employeeNumber;
    @ApiModelProperty("有无原单 1:有原单 0 无原单")
    private Integer originExist;
    @ApiModelProperty("退货原因")
    private String returnReason;
    @ApiModelProperty("品名Code")
    private String commodityCode;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("大类ID")
    private Long commodityFirstId;
    @ApiModelProperty("订单号")
    private String orderCode;
    @ApiModelProperty("原单号")
    private String originOrderCode;
    @ApiModelProperty("小票金额")
    private BigDecimal priceMin;
    @ApiModelProperty("小票金额")
    private BigDecimal priceMax;
    @ApiModelProperty("开始小时")
    private String hourBegin;
    @ApiModelProperty("结束小时")
    private String hourEnd;

    public void checkData() {
        QYAssert.isTrue(!(StringUtils.isBlank(commodityCode) && StringUtils.isBlank(barCode) && shopIdList.isEmpty()), "门店、商品、条形码，不能都为空");
        QYAssert.isTrue(StringUtils.isNotBlank(saleTimeBegin) && StringUtils.isNotBlank(saleTimeEnd), "请选择日期");
        //日期，必填项，日期范围选择；单选门店（全部商品）时能选择的最大时间跨度为31天（含）；
        QYAssert.isTrue(dateBetween(saleTimeBegin, saleTimeEnd, ChronoUnit.DAYS) < 32L,"最多选31天");
        if (priceMin != null && priceMax != null) {
            checkPrice();
        }
    }

    public void checkPrice() {
        QYAssert.isTrue(new BigDecimal("0").compareTo(priceMin) < 0, "小票金额区间无效");
        QYAssert.isTrue(priceMin.compareTo(priceMax) <= 0, "小票金额区间无效");
        QYAssert.isTrue(priceMax.compareTo(new BigDecimal("9999999999.99")) <= 0, "小票金额区间无效");
    }
    public static long dateBetween(String startStr, String endStr, ChronoUnit chronoUnit) {
        if (StringUtils.isNotBlank(startStr) && StringUtils.isNotBlank(endStr)) {
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDate startDate = LocalDate.parse(startStr, fmt);
            LocalDate endDate = LocalDate.parse(endStr, fmt);
            return chronoUnit.between(startDate, endDate);
        }
        return 0L;
    }

}
