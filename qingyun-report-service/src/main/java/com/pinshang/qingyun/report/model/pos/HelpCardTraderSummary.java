package com.pinshang.qingyun.report.model.pos;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 帮困卡消费
 * <AUTHOR>
 */
@Table(name = "t_pos_help_card_trader_summary")
@NoArgsConstructor
@Data
public class HelpCardTraderSummary {

    private Long id;

    private Long areaId;

    private String areaName;

    private Long shopId;

    private String payCode;

    private String cardNo;

    private String orderCode;

    private Long commodityId;

    private Date transactionDate;

    private BigDecimal amount;

    private BigDecimal quantity;

    private Long createId;

    private Date createTime;
}
