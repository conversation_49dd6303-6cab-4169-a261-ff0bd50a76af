package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "t_pos_report_day_sales_summary")
@Data
public class DaySaleSummary extends BaseModel<DaySaleSummary> {

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    /** 一级大类ID*/
    private BigDecimal commodityFirstId;
    private String commodityFirstCode;
    private String commodityFirstName;

    /** 二级大类ID*/
    private BigDecimal commoditySecondId;
    private String commoditySecondCode;
    private String commoditySecondName;

    /** 三级大类ID*/
    private BigDecimal commodityThirdId;
    private String commodityThirdCode;
    private String commodityThirdName;

    /** 商品ID*/
    private Long commodityId;

    /** 商品编码*/
    private String commodityCode;

    /** 商品名称*/
    private String commodityName;

    /** 助记码*/
    private String commodityAid;

    /** 规格*/
    private String commoditySpec;

    /** 单位*/
    private String commodityUnit;

    /** "零售价 */
    private BigDecimal commodityPrice;

    /** 税率*/
    private BigDecimal taxRate;

    /** 销售日期*/
    private Date saleTime;

    /** 销售数量*/
    private BigDecimal saleQuantity = BigDecimal.ZERO;

    /** 退货数量*/
    private BigDecimal returnQuantity = BigDecimal.ZERO;

    /** 赠送数量*/
    private BigDecimal giveQuantity = BigDecimal.ZERO;

    /** 销售金额*/
    private BigDecimal saleAmount = BigDecimal.ZERO;

    /** 销售成本*/
    private BigDecimal weightAmount = BigDecimal.ZERO;

    /** 退货金额*/
    private BigDecimal returnAmount = BigDecimal.ZERO;

    /** 赠送金额*/
    private BigDecimal giveAmount = BigDecimal.ZERO;

    /** 让利金额*/
    private BigDecimal discountAmount = BigDecimal.ZERO;


    /** 数量小计*/
    private BigDecimal tatalQuantity = BigDecimal.ZERO;

    /** 金额小计*/
    private BigDecimal tatalAmount = BigDecimal.ZERO;

    /** 不含税金额*/
    private BigDecimal noTaxRateAmount = BigDecimal.ZERO;

    /** 税额*/
    private BigDecimal taxRateAmount = BigDecimal.ZERO;

    private Long consignmentId;

}
