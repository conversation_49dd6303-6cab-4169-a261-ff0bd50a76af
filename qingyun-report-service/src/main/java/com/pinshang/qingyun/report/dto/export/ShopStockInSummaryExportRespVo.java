package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/1
 * 门店实收结算汇总表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopStockInSummaryExportRespVo {

    @ExcelProperty("部门")
    private String orgName;
    @ExcelProperty("客户编码")
    private String storeCode;
    @ExcelProperty("门店名称")
    private String shopName;

    @ExcelProperty("订货金额")
    private BigDecimal orderAmount;
    @ExcelProperty("实发金额")
    private BigDecimal deliveryAmount;
    @ExcelProperty("调拨入库金额")
    private BigDecimal allotInAmount;
    @ExcelProperty("云超调入金额")
    private BigDecimal cloudInAmount;
    @ExcelProperty("实退金额")
    private BigDecimal returnAmount;
    @ExcelProperty("调拨出库金额")
    private BigDecimal allotOutAmount;
    @ExcelProperty("实收金额合计")
    private BigDecimal totalAmount;

    @ExcelProperty("后台大类")
    private String commodityFirstCateName;
    @ExcelProperty("后台中类")
    private String commoditySecondCateName;
    @ExcelProperty("后台小类")
    private String commodityThirdCateName;
}
