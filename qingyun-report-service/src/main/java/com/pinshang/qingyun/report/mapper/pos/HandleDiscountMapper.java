package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.model.pos.HandDiscountDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: sk
 * @Date: 2021/7/6
 */
@Repository
public interface HandleDiscountMapper extends MyMapper<HandDiscountDetail> {

    int deleteHandleDiscountReportByOrderCodes(@Param("orderCode") String orderCode);

    int deleteHandleDiscountReportByTimeRange(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);
}
