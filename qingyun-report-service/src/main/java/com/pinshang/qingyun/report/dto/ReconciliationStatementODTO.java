package com.pinshang.qingyun.report.dto;

import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReconciliationStatementODTO {
    private String shopName;
    //pos收现金
    private BigDecimal posCash;
    //pos抹零
    private BigDecimal moLing;
    //现金
    private BigDecimal cash;
    @Transient
    private BigDecimal cashDiff;

    //pos银行卡
    private BigDecimal posBankCard;
    //银行卡手续费
    private BigDecimal bankCardPoundage;
    //银行
    private BigDecimal bankCard;
    @Transient
    private BigDecimal bankDiff;


    //pos银联聚合
    private BigDecimal posUnionPay;
    //银联聚合手续费
    private BigDecimal unionPoundage;
    //银联聚合
    private BigDecimal unionPay;
    @Transient
    private BigDecimal unionPayDiff;


    //pos ok
    private BigDecimal posOkCard;
    //ok卡手续费
    private BigDecimal okCardPoundage;
    //ok卡收
    private BigDecimal okCard;
    @Transient
    private BigDecimal okCardDiff;



    //pos支付宝记账
    private BigDecimal posTallyAli;
    //支付宝记账手续费
    private BigDecimal tallyAliPoundage;
    //支付宝记账
    private BigDecimal tallyAli;
    @Transient
    private BigDecimal tallAliDiff;


    //pos微信记账
    private BigDecimal posTallyWechat;
    //微信记账手续费
    private BigDecimal tallyWechatPoundage;
    //微信记账
    private BigDecimal tallyWechat;
    @Transient
    private BigDecimal tallyWechatDiff;


    //pos//总收
    private BigDecimal posTotalAmount;
    @Transient
    private BigDecimal totalMoLing;
    //总手续费
    private BigDecimal totalPoundage;
    //总收
    private BigDecimal totalAmount;
    @Transient
    private BigDecimal totalDiff;
    private Integer isDelete;


    public BigDecimal getTotalMoLing() {
        return getMoLing();
    }

    public BigDecimal getCashDiff() {
        return cash.subtract(posCash);
    }

    public BigDecimal getBankDiff() {
        return bankCard.add(bankCardPoundage).subtract(posBankCard);
    }

    public BigDecimal getUnionPayDiff() {
        return unionPay.add(unionPoundage).subtract(posUnionPay);
    }

    public BigDecimal getTallyWechatDiff() {
        return tallyWechat.add(tallyWechatPoundage).subtract(posTallyWechat);
    }

    public BigDecimal getTallAliDiff() {
        return tallyAli.add(tallyAliPoundage).subtract(posTallyAli);
    }

    public BigDecimal getOkCardDiff() {
        return okCard.add(okCardPoundage).subtract(posOkCard);
    }

    public BigDecimal getTotalDiff() {
        return totalAmount.add(totalPoundage).subtract(posTotalAmount);
    }
}
