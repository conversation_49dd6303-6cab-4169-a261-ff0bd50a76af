package com.pinshang.qingyun.report.model;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.*;

/**
* 手动优惠报表
*
* <AUTHOR> <PERSON>
* @since 2019-07-02
*/
@Data
@ToString
@Entity
@Table(name = "t_pos_report_hand_discount")
@NoArgsConstructor
public class HandDiscount extends BaseModel<HandDiscount> {

	/** 门店id */
	private Long shopId;

	/** 门店名称 */
	private String shopName;

	/** 收银员id */
	private Long casherId;

	/** 收银账号 */
	private String casherNumber;

	/** 收银日期 */
	private Date casherTime;

	/** 手动优惠总金额 */
	private BigDecimal discountTotalAmount;

}
