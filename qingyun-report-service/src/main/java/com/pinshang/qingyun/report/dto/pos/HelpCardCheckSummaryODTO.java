package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PosHelpCardCommoditySummaryODTO
 * <AUTHOR>
 * @Date 2023/2/27 10:34
 * @Description PosHelpCardCommoditySummaryODTO
 * @Version 1.0
 */
@Data
public class HelpCardCheckSummaryODTO {
    @ExcelIgnore
    private Long areaId;

    @ExcelIgnore
    private Long shopId;

    @ExcelIgnore
    private Long storeId;

    @ApiModelProperty("部门")
    @ExcelProperty(value = "部门")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    @ApiModelProperty("区域名称")
    @ExcelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode , keyName = "storeId")
    private String storeCode;

    @ApiModelProperty("门店名称")
    @ExcelProperty(value = "门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ApiModelProperty("帮困卡设备号")
    @ExcelProperty(value = "帮困卡设备号")
    private String payCode;

    @ApiModelProperty("政府确认金额")
    @ExcelProperty(value = "政府确认金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal zfConfirmAmount;

    @ApiModelProperty("品鲜交易金额")
    @ExcelProperty(value = "品鲜交易金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal pxTraderAmount;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty("差异金额")
    @ExcelProperty(value = "差异金额")
    private BigDecimal diffAmount;

    @ApiModelProperty("门店编码")
    @ExcelProperty(value = "门店编码")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopCode,keyName = "shopId")
    private String shopCode;
}
