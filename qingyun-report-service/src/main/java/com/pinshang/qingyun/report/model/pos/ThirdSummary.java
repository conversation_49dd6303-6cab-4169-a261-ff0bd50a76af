package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_pos_report_third_summary")
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThirdSummary extends BaseModel<ThirdSummary> {

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    /** 一级大类ID*/
    private BigDecimal commodityFirstId;
    private String commodityFirstCode;
    private String commodityFirstName;

    /** 二级大类ID*/
    private BigDecimal commoditySecondId;
    private String commoditySecondCode;
    private String commoditySecondName;

    /** 三级大类ID*/
    private BigDecimal commodityThirdId;
    private String commodityThirdCode;
    private String commodityThirdName;

    /** 销售数量*/
    private BigDecimal saleQuantity = BigDecimal.ZERO;

    /** 退货数量*/
    private BigDecimal returnQuantity = BigDecimal.ZERO;

    /** 赠送数量*/
    private BigDecimal giveQuantity = BigDecimal.ZERO;

    /** 销售金额*/
    private BigDecimal saleAmount = BigDecimal.ZERO;

    /** 销售成本*/
    private BigDecimal weightAmount = BigDecimal.ZERO;

    /** 退货金额*/
    private BigDecimal returnAmount = BigDecimal.ZERO;

    /** 赠送金额*/
    private BigDecimal giveAmount = BigDecimal.ZERO;

    /** 让利金额*/
    private BigDecimal discountAmount = BigDecimal.ZERO;


    /** 数量小计*/
    private BigDecimal tatalQuantity = BigDecimal.ZERO;

    /** 金额小计*/
    private BigDecimal tatalAmount = BigDecimal.ZERO;

    /** 不含税金额*/
    private BigDecimal noTaxRateAmount = BigDecimal.ZERO;

    /** 税额*/
    private BigDecimal taxRateAmount = BigDecimal.ZERO;

    /** 不含税成本*/
    private BigDecimal noTaxWeightAmount = BigDecimal.ZERO;

    /** 销售日期*/
    private Date saleTime;
}
