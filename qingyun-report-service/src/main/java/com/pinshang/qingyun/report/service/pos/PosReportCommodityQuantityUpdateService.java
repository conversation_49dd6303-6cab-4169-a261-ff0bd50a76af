package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.pos.PosTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.report.enums.AbnormalQueryTypeEnum;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.mapper.pos.CashierWaterReportMapper;
import com.pinshang.qingyun.report.mapper.pos.PosReportCommodityQuantityUpdateMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.Commodity;
import com.pinshang.qingyun.report.model.pos.PosReportCommodityQuantityUpdate;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PosReportCommodityQuantityUpdateService {

    @Autowired
    private PosReportCommodityQuantityUpdateMapper posReportCommodityQuantityUpdateMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private CashierWaterReportMapper cashierWaterReportMapper;


    public void insertQuantityUpdate(Long keyId ,PosReportCommodityQuantityUpdateMessage message) {

        PosReportCommodityQuantityUpdate data = BeanCloneUtils.copyTo(message, PosReportCommodityQuantityUpdate.class);
        data.setId(keyId);
        data.setCreateTime(new Date());
        //查询商品
        Commodity commodity = commodityMapper.getCommodityByComodityId(message.getCommodityId());
        if(null != commodity){
            data.setCommodityCode(commodity.getCommodityCode());
            data.setCommodityName(commodity.getCommodityName());
            data.setCommoditySpec(commodity.getCommoditySpec());
            data.setCommodityUnit(commodity.getCommodityUnitName());
        }
        posReportCommodityQuantityUpdateMapper.insert(data);
    }

    public TablePageInfo<CommodityQuantityUpdateDTO> page(CommodityQuantityUpdateIDTO idto) {
        if(!StringUtil.isBlank(idto.getStartOperateDate()) && !StringUtil.isBlank(idto.getEndOperateDate())){
            idto.setStartOperateDate(idto.getStartOperateDate()+ " 00:00:00");
            idto.setEndOperateDate(idto.getEndOperateDate()+ " 23:59:59");
        } else {
            QYAssert.isTrue(false, "操作日期不能为空！");
        }

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo<>();
        }

        int dayDif = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndOperateDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getStartOperateDate(), DateUtil.DEFAULT_DATE_FORMAT));
        if(idto.getShopId() == null){
            QYAssert.isTrue(dayDif <= 3, "最大时间跨度为3天");

            if (SpringUtil.isNotEmpty(shopIdList)) {
                idto.setShopIdList(shopIdList);
            }
        } else {
            QYAssert.isTrue(dayDif <= 31, "最大时间跨度为31天");
            if(!shopIdList.contains(idto.getShopId())){
                return new TablePageInfo<>();
            }
            idto.setShopIdList(Arrays.asList(idto.getShopId()));
        }

        PageInfo<CommodityQuantityUpdateDTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            posReportCommodityQuantityUpdateMapper.list(idto);
        });
        CommodityQuantityUpdateDTO total = null;
        if (pageInfo.getSize() > 0) {
            List<Long> shopIds = pageInfo.getList().stream().map(CommodityQuantityUpdateDTO::getShopId).collect(Collectors.toList());
            List<com.pinshang.qingyun.report.dto.ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(com.pinshang.qingyun.report.dto.ShopODTO::getShopId, com.pinshang.qingyun.report.dto.ShopODTO::getStoreCode));
            pageInfo.getList().forEach(e -> {
                e.setStoreCode(shopMap.get(e.getShopId()));
            });

            total = posReportCommodityQuantityUpdateMapper.sum(idto);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }



    public TablePageInfo<CommodityDeleteODTO> commodityQuantityUpdateReport(CommodityDeleteIDTO idto) {
        idto.setIsCurrentDay(DateUtils.isCurrentDay(idto.getBeginTime(),idto.getEndTime()));
        QYAssert.isTrue(idto.getType() != null, "查询类型不能为空！");
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
        }

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo<>();
        }
        if(idto.getShopId() == null){
            if (SpringUtil.isNotEmpty(shopIdList)) {
                idto.setShopIdList(shopIdList);
            }
        }else if(!shopIdList.contains(idto.getShopId())){
            return new TablePageInfo<>();
        }

        //目前这个报表只查询自助收银的
        idto.setPosType(PosTypeEnum.SELF_POS.getCode());

        //以收银表为主表，查询收银的流水
        PageInfo<CommodityDeleteODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            cashierWaterReportMapper.getNetSales(idto);
        });

        BigDecimal ratio = new BigDecimal("100");

        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            List<Long> shopIds = pageInfo.getList().stream().map(CommodityDeleteODTO::getShopId).collect(Collectors.toList());
            List<com.pinshang.qingyun.report.dto.ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(com.pinshang.qingyun.report.dto.ShopODTO::getShopId, com.pinshang.qingyun.report.dto.ShopODTO::getStoreCode));

            List<CommodityQuantityUpdateReportDTO> report = posReportCommodityQuantityUpdateMapper.report(idto);

            //1 -增加, 2 -减少
            //以门店为维度
            CommodityQuantityUpdateReportDTO reportDTO = null;
            if (idto.getType().equals(AbnormalQueryTypeEnum.SHOP.getCode())) {
                Map<String, CommodityQuantityUpdateReportDTO> map = report.stream().collect(Collectors.toMap(e -> e.getShopId()+"-"+e.getOperateType(), it -> it));
                for (CommodityDeleteODTO e : pageInfo.getList()) {
                    e.setStoreCode(shopMap.get(e.getShopId()));
                    if (map.containsKey(e.getShopId()+"-1")) {
                        reportDTO = map.get(e.getShopId()+"-1");
                        e.setAddAmount(reportDTO.getAmount());
                        e.setAddNum(reportDTO.getNum());
                        e.setAddRatio(e.getAddAmount().multiply(ratio).divide(e.getNetSales(),2, RoundingMode.HALF_UP));
                    }
                    if (map.containsKey(e.getShopId()+"-2")) {
                        reportDTO = map.get(e.getShopId()+"-2");
                        e.setReduceAmount(reportDTO.getAmount());
                        e.setReduceNum(reportDTO.getNum());
                        e.setReduceRatio(e.getReduceAmount().multiply(ratio).divide(e.getNetSales(),2, RoundingMode.HALF_UP));
                    }
                }
            } else if (idto.getType().equals(AbnormalQueryTypeEnum.SHOP_DATE.getCode())) {
               // 2按日期+ 门店汇总
                Map<String, CommodityQuantityUpdateReportDTO> map = report.stream().collect(Collectors.toMap(e -> e.getShopId().toString()+e.getOperateTime()+"-"+e.getOperateType(), it -> it));
                for (CommodityDeleteODTO e : pageInfo.getList()) {
                    e.setStoreCode(shopMap.get(e.getShopId()));
                    if (map.containsKey(e.getShopId().toString()+e.getOperateTime()+"-1")) {
                        reportDTO = map.get(e.getShopId().toString()+e.getOperateTime()+"-1");
                        e.setAddAmount(reportDTO.getAmount());
                        e.setAddNum(reportDTO.getNum());
                        e.setAddRatio(e.getAddAmount().multiply(ratio).divide(e.getNetSales(),2, RoundingMode.HALF_UP));
                    }
                    if (map.containsKey(e.getShopId().toString()+e.getOperateTime()+"-2")) {
                        reportDTO = map.get(e.getShopId().toString()+e.getOperateTime()+"-2");
                        e.setReduceAmount(reportDTO.getAmount());
                        e.setReduceNum(reportDTO.getNum());
                        e.setReduceRatio(e.getReduceAmount().multiply(ratio).divide(e.getNetSales(),2, RoundingMode.HALF_UP));
                    }
                }
            } else if (idto.getType().equals(AbnormalQueryTypeEnum.SHOP_DATE_CASHIER.getCode())) {
                // 3 门店，日期，收银员
                Map<String, CommodityQuantityUpdateReportDTO> map = report.stream().collect(Collectors.toMap(e -> e.getShopId().toString()+e.getOperateTime()+e.getCasherId()+"-"+e.getOperateType(), it -> it));
                for (CommodityDeleteODTO e : pageInfo.getList()) {
                    e.setStoreCode(shopMap.get(e.getShopId()));
                    if (map.containsKey(e.getShopId().toString()+e.getOperateTime()+e.getPosMacId()+"-1")) {
                        reportDTO = map.get(e.getShopId().toString()+e.getOperateTime()+e.getPosMacId()+"-1");
                        e.setAddAmount(reportDTO.getAmount());
                        e.setAddNum(reportDTO.getNum());
                        e.setAddRatio(e.getAddAmount().multiply(ratio).divide(e.getNetSales(),2, RoundingMode.HALF_UP));
                    }
                    if (map.containsKey(e.getShopId().toString()+e.getOperateTime()+e.getPosMacId()+"-2")) {
                        reportDTO = map.get(e.getShopId().toString()+e.getOperateTime()+e.getPosMacId()+"-2");
                        e.setReduceAmount(reportDTO.getAmount());
                        e.setReduceNum(reportDTO.getNum());
                        e.setReduceRatio(e.getReduceAmount().multiply(ratio).divide(e.getNetSales(),2, RoundingMode.HALF_UP));
                    }
                }
            }

            List<CommodityQuantityUpdateReportDTO> reportSum = posReportCommodityQuantityUpdateMapper.reportSum(idto);
            CommodityDeleteODTO header = new CommodityDeleteODTO();
            Map<Integer, CommodityQuantityUpdateReportDTO> map = reportSum.stream().collect(Collectors.toMap(CommodityQuantityUpdateReportDTO::getOperateType, e -> e));
            header.setAddNum(map.containsKey(1) ? map.get(1).getNum() : 0L);
            header.setAddAmount(map.containsKey(1) ? map.get(1).getAmount() : BigDecimal.ZERO);
            header.setReduceNum(map.containsKey(2) ? map.get(2).getNum() : 0L);
            header.setReduceAmount(map.containsKey(2) ? map.get(2).getAmount() : BigDecimal.ZERO);
            //            for (CommodityQuantityUpdateReportDTO sum : reportSum) {
//                if (sum.getOperateType() == 1) {
//                    header.setAddNum(sum.getNum());
//                    header.setAddAmount(sum.getAmount());
//                } else if (sum.getOperateType() == 2) {
//                    header.setReduceNum(sum.getNum());
//                    header.setReduceAmount(sum.getAmount());
//                }
//            }
            Double sales = cashierWaterReportMapper.getNetSalesCount(idto);
            if (sales != 0) {
                header.setNetSales(new BigDecimal(sales).setScale(2, BigDecimal.ROUND_HALF_UP));
                if (header.getReduceAmount() != null) {
                    header.setReduceRatio(header.getReduceAmount().multiply(ratio).divide(header.getNetSales(), 2, BigDecimal.ROUND_HALF_UP));
                }
                if (header.getAddAmount() != null) {
                    header.setAddRatio(header.getAddAmount().multiply(ratio).divide(header.getNetSales(), 2, BigDecimal.ROUND_HALF_UP));
                }
            }
            TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
            tablePageInfo.setHeader(header);
            return tablePageInfo;

        }

        return null;
    }



}
