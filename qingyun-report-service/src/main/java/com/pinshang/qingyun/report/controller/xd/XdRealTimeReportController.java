package com.pinshang.qingyun.report.controller.xd;

import com.pinshang.qingyun.report.xdrealtime.XdRealTimeReport;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/xd/xdRealTimeReport")
public class XdRealTimeReportController {

    @Autowired
    private XdRealTimeReport xdRealTimeReport;

    @GetMapping("report")
    public boolean report(@RequestParam("dateTime") String dateTime){
        xdRealTimeReport.report(dateTime);
        return true;
    }

    @GetMapping("xdRedisInit")
    public boolean xdRedisInit(){
        return xdRealTimeReport.xdRedisInit();
    }

    @GetMapping("reportByDb")
    @ApiModelProperty(value = "日销售报告", notes = "mysql查取日销售报告")
    public boolean reportByDb(@RequestParam("dateTime") String dateTime){
        xdRealTimeReport.reportByDb(dateTime);
        return true;
    }
}
