package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.dto.index.*;
import com.pinshang.qingyun.report.mapper.IndexReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IndexReportService {

    @Autowired
    private IndexReportMapper indexReportMapper;

    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    /**
     * 查询年月周的四大框数据
     * @param vo
     * @return
     */
    public SalesDataEntry selectSalesDataHistory(HomePageVo vo){
        SalesDataEntry salesDataEntry = new SalesDataEntry();
        salesDataEntry.setVisitorNumber(BigDecimal.ZERO);
        //1、总销售
        if(vo.getSourceType().equals(1)){
            salesDataEntry = indexReportMapper.selectSalesDataHistory(vo);
            this.selectOffLineSalesDataHistory(salesDataEntry,vo);
            return salesDataEntry;
        }else if(vo.getSourceType().equals(3)){
            this.selectOffLineSalesDataHistory(salesDataEntry,vo);
            return salesDataEntry;
        }else {
            return indexReportMapper.selectSalesDataHistory(vo);
        }
    }

    /**
     * 曲线图周月年
     * @param vo
     * @return
     */
    public List<YearOnYearDataEntry> selectSelectDataGraphicsHistory(HomePageVo vo){
        List<YearOnYearDataEntry> yearOnYearDataEntries = new ArrayList<>();
        if(vo.getSourceType().equals(1)){
            this.offLineDataGraphics(yearOnYearDataEntries,vo);
            this.selectHistoryDataGraphics(yearOnYearDataEntries,vo);
        }else if(vo.getSourceType().equals(3)){
            this.offLineDataGraphics(yearOnYearDataEntries,vo);
        }else {
            this.selectHistoryDataGraphics(yearOnYearDataEntries,vo);
        }
        List<YearOnYearDataEntry> yearDataEntries = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(yearOnYearDataEntries)){
            Map<String, BigDecimal> collect3 = yearOnYearDataEntries.stream().collect(Collectors.groupingBy(YearOnYearDataEntry::getDate, CollectorsUtil.summingBigDecimalMax(YearOnYearDataEntry::getSalesVolume)));
            collect3.forEach((k,v)->{
                YearOnYearDataEntry yearOnYearDataEntry = new YearOnYearDataEntry();
                yearOnYearDataEntry.setDate(k);
                yearOnYearDataEntry.setSalesVolume(v);
                yearDataEntries.add(yearOnYearDataEntry);
            });
        }
        return yearDataEntries;
    }

    /**
     * 门店排序周月年
     * @param vo
     * @return
     */
    public List<StoreSaleEntry> selectHistoryStoreSale(HomePageVo vo){
        List<StoreSaleEntry> storeSaleEntrieList = new ArrayList<>();
        if(vo.getSourceType().equals(1)){
            this.offLineSelectHistoryStoreSale(storeSaleEntrieList,vo);
            this.selectHistoryStoreSale(storeSaleEntrieList,vo);
        }else if(vo.getSourceType().equals(3)){
            this.offLineSelectHistoryStoreSale(storeSaleEntrieList,vo);
        }else {
            this.selectHistoryStoreSale(storeSaleEntrieList,vo);
        }
        //根据属性去重
        List<StoreSaleEntry> storeSaleEntries = storeSaleEntrieList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StoreSaleEntry::getShopId))), ArrayList::new));
        //根据属性统计 指定字段
        Map<Long, BigDecimal> shopSalesMap = storeSaleEntrieList.stream().collect(Collectors.groupingBy(StoreSaleEntry::getShopId, CollectorsUtil.summingBigDecimalMax(StoreSaleEntry::getShopSale)));
        storeSaleEntries.forEach(p->{
            if(shopSalesMap.containsKey(p.getShopId())){
                p.setShopSale(shopSalesMap.get(p.getShopId()));
            }
        });
        return storeSaleEntries;
    }

    /**
     * 数据补偿
     * @param vo
     * @return
     */
    public List<IndexEntry> compensateIndex(CompensateVo vo){
        List<IndexEntry> indexEntries = indexReportMapper.compensateIndex(vo);
        if(SpringUtil.isNotEmpty(indexEntries)){
            DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
            indexEntries.forEach(p->{
                p.setDateTime(dateFormat.format(vo.getEndTime()));
            });
        }
        return indexEntries;
    }

    /**
     * 根据时间 段 查询销售数据
     * @param vo
     * @return
     */
    public List<IndexEntry> selectOffHistorySaleByTimeSlot(CompensateVo vo){
        DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
        List<IndexEntry> indexEntries = indexReportMapper.selectOffHistorySaleByTimeSlot(vo);
        //将时间 归位
        indexEntries.forEach(p->{
            p.setDateTime(omitTheDecimal(p.getDateTime()));
        });
        //根据门店id 时间点 去重
        List<IndexEntry> collect1 = indexEntries.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(p -> p.getDateTime() + p.getShopId()))), ArrayList::new));
        //根据时间点 门店id 统计数据
        Map<String, BigDecimal> collect = indexEntries.stream().collect(Collectors.groupingBy(p-> p.getDateTime()+"_"+p.getShopId(), CollectorsUtil.summingBigDecimalMax(IndexEntry::getTotalSales)));
        collect1.forEach(p->{
            String key = p.getDateTime() + "_" + p.getShopId();
            if(collect.containsKey(key)){
                p.setTotalSales(collect.get(key));
            }
        });
        return collect1;
    }
    /**
     * 监控商品总表(门店)和前置仓日汇总表
     * @param dateTime
     */
    public void monitorXsXdReportDay(String dateTime) {
        Long commodityShopTaxCount = indexReportMapper.countCommodityShopTaxByTime(dateTime);
        Long realTimeReportByDateCount = indexReportMapper.countRealTimeReportByTime(dateTime);
        if(commodityShopTaxCount.equals(0L) || realTimeReportByDateCount.equals(0L)){
            StringBuffer sb = new StringBuffer();
            sb.append("【首页鲜食前置仓数据异常】");
            sb.append("鲜食记录数:"+commodityShopTaxCount);
            sb.append("前置仓记录数:"+realTimeReportByDateCount);
            //发送短信消息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }

    /**
     * 线下销售
     * @param salesDataEntry
     * @param vo
     * @return
     */
    private void selectOffLineSalesDataHistory(SalesDataEntry salesDataEntry,HomePageVo vo){
        SalesDataEntry selectOffLineSalesDataHistory = indexReportMapper.selectOffLineSalesDataHistory(vo);
        salesDataEntry.setPosSales(selectOffLineSalesDataHistory.getPosSales());
        salesDataEntry.setPosCost(selectOffLineSalesDataHistory.getPosCost());
        salesDataEntry.setVisitorNumber(salesDataEntry.getVisitorNumber().add(selectOffLineSalesDataHistory.getVisitorNumber()));
    }

    /**
     * 根据渠道获取指定日期范围内的 销售数据
     * @param yearOnYearDataEntries
     * @param vo
     */
    private void selectHistoryDataGraphics(List<YearOnYearDataEntry> yearOnYearDataEntries,HomePageVo vo){
        List<YearOnYearDataEntry> onLineDataGraphicsHistory = indexReportMapper.selectDataGraphicsHistory(vo);
        if(CollectionUtils.isNotEmpty(onLineDataGraphicsHistory)){
            yearOnYearDataEntries.addAll(onLineDataGraphicsHistory);
        }
    }

    /**
     * 曲线图获取线下数据
     * @param yearOnYearDataEntries
     * @param vo
     */
    private void offLineDataGraphics(List<YearOnYearDataEntry> yearOnYearDataEntries,HomePageVo vo){
        List<YearOnYearDataEntry> offLineDataGraphicsHistory = indexReportMapper.selectSelectOffLineDataGraphicsHistory(vo);
        if(CollectionUtils.isNotEmpty(offLineDataGraphicsHistory)){
            yearOnYearDataEntries.addAll(offLineDataGraphicsHistory);
        }
    }

    /**
     * 门店排序数据
     * @param storeSaleEntries
     * @param vo
     */
    private void selectHistoryStoreSale(List<StoreSaleEntry> storeSaleEntries,HomePageVo vo){
        List<StoreSaleEntry> historyStoreSale = indexReportMapper.selectHistoryStoreSale(vo);
        if(CollectionUtils.isNotEmpty(historyStoreSale)){
            storeSaleEntries.addAll(historyStoreSale);
        }
    }
    /**
     * 门店排序 线下数据
     * @param storeSaleEntries
     * @param vo
     */
    private void offLineSelectHistoryStoreSale(List<StoreSaleEntry> storeSaleEntries,HomePageVo vo){
        List<StoreSaleEntry> offLineHistoryStoreSale = indexReportMapper.selectOffLineHistoryStoreSale(vo);
        if(CollectionUtils.isNotEmpty(offLineHistoryStoreSale)){
            storeSaleEntries.addAll(offLineHistoryStoreSale);
        }
    }
    /**
     * 将时间直接 舍去
     * 15:20:26 -> 15:00:00
     * 15:59:26 -> 15:30:00
     * @param strDate
     * @return
     */
    private String omitTheDecimal(String strDate){
        DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
        Date date = null;
        try {
            date = dateFormat.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        BigDecimal bigDecimal = new BigDecimal( date.getTime()- calendar.getTime().getTime()).divide(new BigDecimal(1000 * 60 * 30), RoundingMode.CEILING).setScale(BigDecimal.ROUND_HALF_UP);
        BigDecimal multiply = bigDecimal.divide(new BigDecimal(2)).multiply(new BigDecimal(60));
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date);
        calendar1.set(Calendar.HOUR_OF_DAY, 0);
        calendar1.set(Calendar.MINUTE, 0);
        calendar1.set(Calendar.SECOND, 0);
        calendar1.add(Calendar.MINUTE,multiply.intValue());
        return dateFormat.format(calendar1.getTime());
    }
}
