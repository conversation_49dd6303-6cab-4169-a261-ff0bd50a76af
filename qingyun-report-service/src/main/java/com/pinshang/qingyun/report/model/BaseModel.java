package com.pinshang.qingyun.report.model;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.util.PosIdWorker;
import lombok.Data;

import javax.persistence.Id;
import java.util.Date;

/**
 * Created by crell
 * 2018/5/7.
 */
@Data
public class BaseModel<T> {

    @Id
    private Long id;

    private Date createTime;

    private Long createId;

    private Date updateTime;

    private Long updateId;

    public void preInsert(){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = null;
        if(tokenInfo != null) {
            userId = tokenInfo.getUserId();
        }
        if (userId != null){
            this.createId = userId;
            this.updateId = userId;
        }
        this.id = PosIdWorker.getId();
        this.createTime = new Date();
        this.updateTime = this.createTime;
    }

    public void preUpdate(){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = null;
        if(tokenInfo != null) {
            userId = tokenInfo.getUserId();
        }
        if (userId != null){
            this.updateId = userId;
        }
        this.updateTime = new Date();
    }
}
