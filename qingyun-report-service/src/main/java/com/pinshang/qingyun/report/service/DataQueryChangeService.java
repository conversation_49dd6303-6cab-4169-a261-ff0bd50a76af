package com.pinshang.qingyun.report.service;/**
 * @Author: sk
 * @Date: 2025/6/6
 */

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.report.constant.RedissKeyConst;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025年06月06日 上午10:51
 */

@Service
public class DataQueryChangeService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private DictionaryClient dictionaryClient;

    /**
     * 是否启用数据路由,根据数据字典配置(optionValue 1=启用  0=停用)
     *
     * 如下报表走判断:
     * 类别销售周同比、门店销售周同比、品类销售周同比、品类销售月同比、类别客单周同比
     * @return
     */
    public Boolean enableDataQuery(){
        RBucket<Integer> bucket = redissonClient.getBucket(RedissKeyConst.ENABLE_DATA_QUERY);
        Integer enableDataQuery = bucket.get();
        if(enableDataQuery != null) {
            return YesOrNoEnums.YES.getCode().equals(enableDataQuery);
        }

        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("enableDataQuery");
        if(dictionaryODTO == null || StringUtils.isBlank(dictionaryODTO.getOptionValue())) {
            return true;
        }

        Integer optionValue = Integer.valueOf(dictionaryODTO.getOptionValue());
        bucket.set(optionValue, 5, TimeUnit.MINUTES);

        return YesOrNoEnums.YES.getCode().equals(optionValue);
    }
}
