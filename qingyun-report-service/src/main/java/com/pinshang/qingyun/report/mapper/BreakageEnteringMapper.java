package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordIDTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordODTO;
import com.pinshang.qingyun.report.model.Commodity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BreakageEnteringMapper {

    void saveBreakageEnting(@Param("idto") BreakageEnteringIDTO idto, @Param("commodity")Commodity commodity);

    Long queryTotalTimesForPass24Hours(@Param("shopId")Long shopId, @Param("consignmentId")Long consignmentId);

    List<BreakagedCommodityODTO> breakagedCommodityForPass24Hours(@Param("shopId") Long shopId, @Param("consignmentId")Long consignmentId);

    List<BreakageEnteringODTO> breakageEnteringList(@Param("idto") BreakageEnteringSearchIDTO idto);

    BreakageEnteringODTO breakageEnteringListSum(@Param("idto") BreakageEnteringSearchIDTO idto);

    //报损
    List<BreakageEnteringDayODTO> breakageEnteringDayList(@Param("idto") BreakageEnteringSearchDayIDTO idto);
    BreakageEnteringDayODTO breakageEnteringListDaySum(@Param("idto") BreakageEnteringSearchDayIDTO idto);

    void deleteBreakageEnteringDay(@Param("dateTime") String dateTime);
    void insertBreakageEnteringDay(@Param("beginDateTime") String beginDateTime, @Param("endDateTime")String endDateTime);

    List<ShopBreakageRecordODTO> selectBreakageRecordList(@Param("vo") ShopBreakageRecordIDTO idto);
}
