package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OnToOffAnalyzeIDTO {

    @Change(value = DataQueryConstant.NOW)
    @ApiModelProperty("查询得日期")
    private String today;

    @ApiModelProperty("部门编号")
    private String deptNo;

    @ApiModelProperty("门店id")
    private Long shopId;

    /**
     * 上周今天
     */
    private String lastWeek;

    /**
     * 本月开始
     */
    private String monthFirst;

    /**
     * 上个月开始
     */
    private String lastMonthFirst;

    /**
     * 上个月的今天
     */
    private String lastMonthToday;

    private List<Long> shopIdList;

    private String beginDate;

    private String endDate;
}
