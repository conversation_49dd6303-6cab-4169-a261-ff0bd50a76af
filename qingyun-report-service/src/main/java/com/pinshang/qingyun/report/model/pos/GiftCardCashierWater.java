package com.pinshang.qingyun.report.model.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_gift_card_cashier_water")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GiftCardCashierWater {

    private Long id;

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("销售渠道 1POS 2团购 3APP")
    private Integer cardSourceType;

    @ApiModelProperty("订单金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("支付方式")
    private String payType;

    @ApiModelProperty("支付方式名称")
    private String payName;

    @ApiModelProperty("POS机号")
    private String macCode;

    @ApiModelProperty("流水时间")
    private Date saleTime;

    @ApiModelProperty("员工号")
    private String employeeNumber;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("1清美卡售卖 2香烟售卖")
    private Integer type;
}
