package com.pinshang.qingyun.report.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.annotation.NotAdvice;
import com.pinshang.qingyun.report.dto.CategorySalesWeekPercentODTO;
import com.pinshang.qingyun.report.dto.ShopSalesWeekODTO;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.export.OrderSalesAnalysisExportRespVo;
import com.pinshang.qingyun.report.dto.pos.HelpCardCommoditySummaryODTO;
import com.pinshang.qingyun.report.dto.shop.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.ShopReportService;
import com.pinshang.qingyun.report.util.ReportUtil;
import com.pinshang.qingyun.report.util.ViewExcel;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/shopReport")
@Api(value = "门店报表接口", tags = "shopReport", description = "门店报表相关接口")
@Slf4j
public class ShopReportController {

    @Autowired
    private ShopReportService shopReportService;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private IRenderService renderService;

    /**
     * 获取短交报表实发总金额
     * @param timeStamp
     * @return
     */
    @GetMapping("/getShortDeliveryRealDeliveryAmount")
    @NotAdvice
    public BigDecimal getShortDeliveryRealDeliveryAmount(@RequestParam(value = "timeStamp") String timeStamp){
        return shopReportService.getShortDeliveryRealDeliveryAmount(timeStamp);
    }

    /**
     * 获取商品实发汇总 总金额
     * @param timeStamp
     * @return
     */
    @GetMapping("/getCommodityRealDeliveryAmount")
    @NotAdvice
    public BigDecimal getCommodityRealDeliveryAmount(@RequestParam(value = "timeStamp") String timeStamp){
        return shopReportService.getCommodityRealDeliveryAmount(timeStamp);
    }

    /**
     * 已经废弃，转移到qingyun-order
     * @param shortDeliveryReportIDto
     * @return
     */
    @ApiOperation(value = "门店短交报表", notes = "门店短交报表",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shortDeliveryReport", method = RequestMethod.GET)
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        return shopReportService.shortDeliveryReport(shortDeliveryReportIDto);
    }
    @ApiOperation(value = "门店短交报表-导出", notes = "门店短交报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/shortDeliveryReport", method = RequestMethod.GET)
    public ModelAndView exportInfoShortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        Assert.isTrue(shortDeliveryReportIDto.getShopId() != null, "权限有问题或未选择门店");
        Assert.isTrue(shortDeliveryReportIDto.getBeginDate() != null, "请选择送货日期");
        Assert.isTrue(shortDeliveryReportIDto.getEndDate() != null, "请选择送货日期");
        Assert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(shortDeliveryReportIDto.getBeginDate(), "yyyy-MM-dd"), 1), DateTimeUtil.parse(shortDeliveryReportIDto.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        shortDeliveryReportIDto.setPageNo(1);
        shortDeliveryReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<ShortDeliveryReportODto> result = shopReportService.shortDeliveryReport(shortDeliveryReportIDto);
        List<ShortDeliveryReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (null != list && !list.isEmpty()) {
            for (ShortDeliveryReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getShopTypeName());
                dataLst.add(dto.getShopName());
                dataLst.add(dateFormat.format(dto.getDeliveryDate()));
                dataLst.add(dto.getOrderCode());
                dataLst.add(dto.getCategoryName());
                dataLst.add(dto.getSecondCategoryName());
                dataLst.add(dto.getThirdCategoryName());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(dto.getPrice().toString());
                dataLst.add(dto.getOrderNum().toString());
                dataLst.add(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                dataLst.add(dto.getDifferNum() == null ? "" : dto.getDifferNum().toString());
                dataLst.add(dto.getRate() == null ? "" : dto.getRate().toString());
                dataLst.add(dto.getFactoryName());
                dataLst.add(dto.getWorkshopName());
                dataLst.add(dto.getStoreCode());
                dataLst.add(dto.getCreateName());
                if(shortDeliveryReportIDto.getIsHq() != null && 1 == shortDeliveryReportIDto.getIsHq()) {
                    dataLst.add(dto.getStoreLineGroupName());
                    dataLst.add(dto.getWarehouseName());
                    dataLst.add(dto.getManagementModeName());
                }
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "短交报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        if(shortDeliveryReportIDto.getIsHq() != null && 1 == shortDeliveryReportIDto.getIsHq()) {
            map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_SHORT_DELIVERY_HQ);
        }else{
            map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_SHORT_DELIVERY);
        }
        map.put("data", data);
//        map.put("title", "短交报表");
//        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    /*
       @ApiOperation(value = "门店调整单报表", notes = "门店调整单报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
        @RequestMapping(value = "/stockAdjustReport", method = RequestMethod.GET)
        public PageInfo<StockAdjustReportODto> stockAdjustReport(StockAdjustReportIDto stockAdjustReportIDto) {
            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
            stockAdjustReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
            if (stockAdjustReportIDto.getShopId() == null) {
                stockAdjustReportIDto.setShopId(tokenInfo.getShopId());
            }
            return shopReportService.stockAdjustReport(stockAdjustReportIDto);
        }*/
   /* @ApiOperation(value = "门店调整单报表-导出", notes = "门店调整单报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/shopStockAdjust", method = RequestMethod.GET)
    public ModelAndView exportInfoShopStockAdjust(StockAdjustReportIDto stockAdjustReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        stockAdjustReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (stockAdjustReportIDto.getShopId() == null) {
            stockAdjustReportIDto.setShopId(tokenInfo.getShopId());
        }
        stockAdjustReportIDto.setPageNo(1);
        stockAdjustReportIDto.setPageSize(Integer.MAX_VALUE);
        PageInfo<StockAdjustReportODto> result = shopReportService.stockAdjustReport(stockAdjustReportIDto);
        List<StockAdjustReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        String shopName = "";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (null != list && !list.isEmpty()) {
            for (StockAdjustReportODto dto : list) {
                if (i == 0) shopName = dto.getShopName();
                dataLst = new ArrayList<>();
                dataLst.add(dateFormat.format(dto.getAdjustDate()));
                dataLst.add(dto.getAdjustCode());
                dataLst.add(dto.getCategoryName());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(dto.getAdjustType());
                dataLst.add(dto.getAdjustNum().toString());
                dataLst.add(dto.getRemark());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "库存调整报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_STOCK_ADJUST);
        map.put("data", data);
        map.put("title", "库存调整报表[" + shopName + "]");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }*/

    /*@ApiOperation(value = "查询退单明细表（门店）|（总部）", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value="/findReturnOrderDetailsReport",method = RequestMethod.GET)
    public PageInfo<ReturnOrderDetailsReportODto> findReturnOrderDetailsReport(ReturnOrderDetailsReportIDto returnOrderDetailsReportIDto) throws  Exception{
        Assert.isTrue(org.apache.commons.lang.StringUtils.isNotBlank(returnOrderDetailsReportIDto.getOrderTimeEndStr()), "请选择截至日期");
        Assert.isTrue(org.apache.commons.lang.StringUtils.isNotBlank(returnOrderDetailsReportIDto.getOrderTimeStartStr()), "请选择起始日期");
        TokenInfo ti = FastThreadLocalUtil.getQY();
        if (returnOrderDetailsReportIDto.getShopId() == null) {
            returnOrderDetailsReportIDto.setShopId(ti.getShopId());
        }
        return shopReportService.findReturnOrderDetailsReport(returnOrderDetailsReportIDto);
    }*/

    /*@ApiOperation(value = "查询退单明细表导出（门店）|（总部）", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value="/exportInfo/findReturnOrderDetailsReport",method = RequestMethod.GET)
    public ModelAndView exportInfoFindAllReturnOrderDetailsReport(ReturnOrderDetailsReportIDto returnOrderDetailsReportIDto)throws  Exception{

        TokenInfo ti = FastThreadLocalUtil.getQY();
        Assert.isTrue(org.apache.commons.lang.StringUtils.isNotBlank(returnOrderDetailsReportIDto.getOrderTimeEndStr()), "请选择截至日期");
        Assert.isTrue(org.apache.commons.lang.StringUtils.isNotBlank(returnOrderDetailsReportIDto.getOrderTimeStartStr()), "请选择起始日期");
        if (returnOrderDetailsReportIDto.getShopId() == null) {
            returnOrderDetailsReportIDto.setShopId(ti.getShopId());
        }
        returnOrderDetailsReportIDto.setPageNo(1);
        returnOrderDetailsReportIDto.setPageSize(Integer.MAX_VALUE);
        PageInfo<ReturnOrderDetailsReportODto> pageDate = shopReportService.findReturnOrderDetailsReport(returnOrderDetailsReportIDto);
        List<ReturnOrderDetailsReportODto> list = pageDate.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataList = new ArrayList<>();
        int i = 0;
        if(null != list && !list.isEmpty()){
            for (ReturnOrderDetailsReportODto dto : list) {

                //"退货日期","退单编号","商品分类","条形码","商品编码","商品名称","规格","单价","退货金额","退货数量"
                dataList = new ArrayList<>();
                dataList.add(dto.getShopName());
                dataList.add(DateUtil.get4yMd(dto.getCreateTime()));
                dataList.add(dto.getOrderCode());
                dataList.add(dto.getCateName());
                dataList.add(dto.getBarCode());
                dataList.add(dto.getCommodityCode());
                dataList.add(dto.getCommodityName());
                dataList.add(dto.getCommoditySpec());
                dataList.add(dto.getCommodityUnit());//单位
                dataList.add(toStr(dto.getPrice()));
                dataList.add(toStr(dto.getReturnQuantity()));
                dataList.add(dto.getTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                dataList.add(dto.getReturnReasonName());//退货原因
                dataList.add(dto.getRemark());//备注
                data.put("key_"+ i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "退单明细查询" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.RETURN_ORDER_DETAILS_REPORT_LIST);
        map.put("data", data);
        map.put("title", "退单明细查询");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }*/
    private String toStr(Object o) {
        if(o == null) {
            return "";
        }else if(o instanceof Date) {
            return DateFormatUtils.format((Date)o, "yyyy-MM-dd HH:mm:ss");
        }else {
            return o.toString();
        }
    }


    @ApiOperation(value = " 商品总表（含税）", notes = " 商品总表（含税）", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/commodityTaxReport", method = RequestMethod.GET)
    public PageInfo<CommodityTaxReportODto> commodityTaxReport(CommodityTaxReportIDto commodityTaxReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        commodityTaxReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        commodityTaxReportIDto.setShopIdList(shopIdList);
//        Assert.isTrue(commodityTaxReportIDto.getShopId() != null, "请选择门店");
        Assert.isTrue(StringUtils.isNotBlank(commodityTaxReportIDto.getBeginDate()), "请选择日期");
        Assert.isTrue(StringUtils.isNotBlank(commodityTaxReportIDto.getEndDate()), "请选择日期");
        return shopReportService.commodityTaxReport(commodityTaxReportIDto);
    }

    @FileCacheQuery(bizCode = "COMMODITY_TAX_REPORT")
    @ApiOperation(value = "商品总表（含税）-导出", notes = "商品总表（含税）-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/commodityTaxReport", method = RequestMethod.GET)
    public void exportInfoCommodityTaxReport(CommodityTaxReportIDto commodityTaxReportIDto,  HttpServletResponse response) throws IOException {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        commodityTaxReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
//        Assert.isTrue(shopId != null, "请选择门店");
        Assert.isTrue(StringUtils.isNotBlank(commodityTaxReportIDto.getBeginDate()), "请选择日期");
        Assert.isTrue(StringUtils.isNotBlank(commodityTaxReportIDto.getEndDate()), "请选择日期");
//        Assert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(beginDate, "yyyy-MM-dd"),1), DateTimeUtil.parse(endDate, "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        commodityTaxReportIDto.setPageNo(1);
        commodityTaxReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<CommodityTaxReportODto> result = shopReportService.commodityTaxReport(commodityTaxReportIDto);
        List<CommodityTaxReportODto> list = result.getList();
        if(SpringUtil.isEmpty(list)){
            list = new ArrayList<>();
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品总表（含税）" + "_" + sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead( response,filename);
            if (commodityTaxReportIDto.getCollectType() == 1) {
                EasyExcel.write(response.getOutputStream(), CommodityTaxShopReportODto.class).autoCloseStream(Boolean.FALSE).sheet("商品总表（含税）")
                        .doWrite( list );
            } else if (commodityTaxReportIDto.getCollectType() == 2) {
                EasyExcel.write(response.getOutputStream(), CommodityTaxCommodityReportODto.class).autoCloseStream(Boolean.FALSE).sheet("商品总表（含税）")
                        .doWrite( list );
            } else if (commodityTaxReportIDto.getCollectType() == 3 || commodityTaxReportIDto.getCollectType() == 4 || commodityTaxReportIDto.getCollectType() == 5) {
                EasyExcel.write(response.getOutputStream(), CommodityTaxCategoryReportODto.class).autoCloseStream(Boolean.FALSE).sheet("商品总表（含税）")
                        .doWrite( list );
            }

        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("商品总表（含税）",e);
        }
    }



    @ApiOperation(value = "门店实收商品分析表", notes = "门店实收商品分析表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto idto) throws Exception{
        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            return shopReportService.actualReceiptAnalysisReport(idto);
        }
        if(!StringUtil.isBlank(idto.getMonthBegin()) && !StringUtil.isBlank(idto.getMonthEnd())){
            return shopReportService.actualReceiptAnalysisMonthReport(idto);
        }
        PageInfo<ActualReceiptAnalysisODto> p = new PageInfo();
        p.setList(new ArrayList<>());
        return p;
    }

    @ApiOperation(value = "门店实收商品分析表-导出", notes = "门店实收商品分析表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public ModelAndView exportInfoActualReceiptAnalysisReport(ActualReceiptAnalysisIDto actualReceiptAnalysisIDto)throws Exception {
        actualReceiptAnalysisIDto.setPageNo(1);
        actualReceiptAnalysisIDto.setPageSize(Integer.MAX_VALUE);
        Boolean isMonth = !StringUtil.isBlank(actualReceiptAnalysisIDto.getMonthBegin()) && !StringUtil.isBlank(actualReceiptAnalysisIDto.getMonthEnd());

        PageInfo<ActualReceiptAnalysisODto> result = actualReceiptAnalysisReport(actualReceiptAnalysisIDto);
        List<ActualReceiptAnalysisODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        int i = 0;
        String shopName = "";
        if (null != list && !list.isEmpty()) {
            for (ActualReceiptAnalysisODto dto : list) {
                List<String> dataList = new ArrayList<>();
                if (i == 0) {
                    if (actualReceiptAnalysisIDto.getShopId() != null) {
                        shopName = dto.getShopName();
                    } else {
                        shopName = "全部";
                    }
                }
                dataList.add(dto.getShopName());
                dataList.add(dto.getCateName());
                dataList.add(dto.getBarCode());
                dataList.add(dto.getCommodityCode());
                dataList.add(dto.getCommodityName());
                dataList.add(dto.getCommoditySpec());
                dataList.add(toStr(dto.getTotalQuantity()));
                dataList.add(toStr(dto.getTotalRealDeliveryQuantity()));
                dataList.add(toStr(dto.getTotalRealReceiveQuantity()));
                dataList.add(toStr(dto.getQuantityDifference()));
                //dataList.add(toStr(dto.getStockQuantity()));
                dataList.add(toStr(dto.getSupplyPrice()));
                dataList.add(toStr(dto.getTotalSupplyPrice()));
                dataList.add(dto.getSupplierName());
                dataList.add(dto.getRealName());
                data.put("key_" + i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "门店实收商品分析表" + "_" + sdf.format(new Date());
        if(isMonth){
            filename = "门店实收商品分析表(月度)" + "_" + sdf.format(new Date());
        }
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle",!isMonth ? ExcelSheetTitleEnum.ACTUAL_RECEIPT_ANALYSIS_REPORT : ExcelSheetTitleEnum.ACTUAL_RECEIPT_ANALYSIS_REPORT_MONTH);
        map.put("data", data);
        map.put("title", !isMonth ? "门店实收商品分析表[" + shopName + "]" : "门店实收商品分析表(月度)[" + shopName + "]");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    /*
    @ApiOperation(value = "app运营报表", notes = " app运营报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/appOperateReport", method = RequestMethod.GET)
    public PageInfo<AppOperateReportODto> appOperateReport(AppOperateReportIDto appOperateReportIDto) throws Exception{
        return shopReportService.appOperateReport(appOperateReportIDto);
    }


    @ApiOperation(value = "app运营报表-导出", notes = "app运营报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/appOperateReport", method = RequestMethod.GET)
    public ModelAndView exportInfoAppOperateReport(AppOperateReportIDto appOperateReportIDto) throws Exception{
        appOperateReportIDto.setPageNo(1);
        appOperateReportIDto.setPageSize(Integer.MAX_VALUE);
        PageInfo<AppOperateReportODto> result = shopReportService.appOperateReport(appOperateReportIDto);
        List<AppOperateReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        int i = 0;
        String shopName = "";
        if (null != list && !list.isEmpty()) {
            for (AppOperateReportODto dto : list) {
                List<String> dataList = new ArrayList<>();
                if (i == 0) {
                    if (appOperateReportIDto.getShopId() != null) {
                        shopName = dto.getShopName();
                    } else {
                        shopName = "全部";
                    }
                }
                dataList.add(dto.getShopName());
                dataList.add(toStr(dto.getTotalUserCount()));
                dataList.add(toStr(dto.getNewUserCount()));
                dataList.add(toStr(dto.getOldUserCount()));
                dataList.add(toStr(dto.getTotalOrderCount()));
                dataList.add(toStr(dto.getRealTotalAmount()));
                dataList.add(toStr(dto.getOutOrderCount()));
                dataList.add(toStr(dto.getOutRealAmount()));
                dataList.add(toStr(dto.getAverageAmount()));
                dataList.add(toStr(dto.getReturnNum()));
                dataList.add(toStr(dto.getReturnAmount()));
                data.put("key_" + i++, dataList);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "app运营报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.APP_OPERATE_REPORT);
        map.put("data", data);
        map.put("title", "app运营报表[" + shopName + "]");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }
    */


    /*
    @ApiOperation(value = " 商品销售明细表", notes = " 商品销售明细表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/commodityOnlineReport", method = RequestMethod.GET)
    public PageInfo<CommodityOnlineReportODto> commodityOnlineReport(CommodityReportIDto reportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        reportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        Assert.isTrue(StringUtils.isNotBlank(reportIDto.getBeginDate()), "请选择日期");
        Assert.isTrue(StringUtils.isNotBlank(reportIDto.getEndDate()), "请选择日期");
        return shopReportService.commodityOnlineReport(reportIDto);
    }

    @ApiOperation(value = "商品销售明细表-导出", notes = "商品销售明细表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/commodityOnlineReport", method = RequestMethod.GET)
    public ModelAndView exportInfoCommodityOnlineReport(CommodityReportIDto reportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        reportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        Assert.isTrue(StringUtils.isNotBlank(reportIDto.getBeginDate()), "请选择日期");
        Assert.isTrue(StringUtils.isNotBlank(reportIDto.getEndDate()), "请选择日期");
        reportIDto.setPageNo(1);
        reportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<CommodityOnlineReportODto> result = shopReportService.commodityOnlineReport(reportIDto);
        List<CommodityOnlineReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        if (null != list && !list.isEmpty()) {
            for (CommodityOnlineReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getCateName1());
                dataLst.add(dto.getCateName2());
                dataLst.add(dto.getCateName3());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getBrandName());
                dataLst.add(dto.getRealQuantity() == null ? "" : dto.getRealQuantity().toString());
                dataLst.add(dto.getRealAmount() == null ? "" : dto.getRealAmount().toString());
                dataLst.add(dto.getReturnQuantity() == null ? "" : dto.getReturnQuantity().toString());
                dataLst.add(dto.getReturnAmount() == null ? "" : dto.getReturnAmount().toString());
                dataLst.add(dto.getSalesAmount() == null ? "" : dto.getSalesAmount().toString());
                dataLst.add(dto.getGrossProfitRate() == null ? "" : dto.getGrossProfitRate().toString());
                dataLst.add(dto.getBasePrice() == null ? "" : dto.getBasePrice().toString());
                dataLst.add(dto.getAveragePrice() == null ? "" : dto.getAveragePrice().toString());
                dataLst.add(dto.getCustomerCount() == null ? "" : dto.getCustomerCount().toString());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品销售明细表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_COMMODITY_ONLINE_REPORT);
        map.put("data", data);
        map.put("title", "商品销售明细表");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }
    */

    /*
    @ApiOperation(value = "商品库存情况表", notes = " 商品库存情况表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/commodityStockReport", method = RequestMethod.GET)
    public PageInfo<CommodityStockReportODto> commodityStockReport(CommodityStockReportIDto commodityStockReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        commodityStockReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());

        return shopReportService.commodityStockReport(commodityStockReportIDto);
    }


    @ApiOperation(value = "商品库存情况表-导出", notes = "商品库存情况表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/commodityStockReport", method = RequestMethod.GET)
    public ModelAndView exportInfoComodityStockReport(CommodityStockReportIDto commodityStockReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long enterpriseId = tokenInfo.getEnterpriseId();

        commodityStockReportIDto.setPageNo(1);
        commodityStockReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<CommodityStockReportODto> pageInfo = shopReportService.commodityStockReport(commodityStockReportIDto);
        List<CommodityStockReportODto> list = pageInfo.getList();
        Map<String, List<String>> data = new HashMap<>();
        if (null != list && !list.isEmpty()) {
            List<String> dataList = null;
            int i = 0;
            for (CommodityStockReportODto item : list) {
                dataList = new ArrayList<>();
                dataList.add(item.getShopName());
                dataList.add(item.getCommodityFirstKind());
                dataList.add(item.getCommoditySecondKind());
                dataList.add(item.getCommodityThirdKind());
                dataList.add(item.getBarCode());
                dataList.add(item.getCommodityCode());
                dataList.add(item.getCommodityName());
                dataList.add(item.getCommoditySpec());
                dataList.add(item.getBrandName());
                dataList.add(item.getCommodityState());
                dataList.add(item.getAppStatus());
                dataList.add(StringUtil.changeToString(item.getWeightPrice()));
                dataList.add(StringUtil.changeToString(item.getStockQuantity()));
                dataList.add(StringUtil.changeToString(item.getStockPrice()));
                dataList.add(StringUtil.changeToString(item.getMonthInitStock()));
                dataList.add(StringUtil.changeToString(item.getCurrentMonthInStorageQuantity()));
                dataList.add(StringUtil.changeToString(item.getCommodityPrice()));

                data.put("key_" + i++, dataList);
            }
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品库存情况表" + "_" + sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.COMMODITY_STOCK_REPORT);
        map.put("data", data);
        map.put("title", "商品库存情况表");
        map.put("titleCells", (short) 3);

        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }
    */

    /*
    @ApiOperation(value = "分类顾客数报表", notes = "分类顾客数报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/cateUserReport", method = RequestMethod.GET)
    public PageInfo<CateUserReportODto> cateUserReport(CateUserReportIDto cateUserReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        cateUserReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        return shopReportService.cateUserReport(cateUserReportIDto);
    }

    //导出分类顾客数报表
    @RequestMapping(value = "/exportInfo/cateUserReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView exportCateUserReport(CateUserReportIDto cateUserReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        cateUserReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        cateUserReportIDto.setPageNo(1);
        cateUserReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<CateUserReportODto> result = shopReportService.cateUserReport(cateUserReportIDto);
        List<CateUserReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (null != list && !list.isEmpty()) {
            for (CateUserReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getCateName());
                dataLst.add(dto.getUserCount() == null ? "" : dto.getUserCount().toString());
                dataLst.add(dto.getSaleCount() == null ? "" : dto.getSaleCount().toString());
                dataLst.add(dto.getSaleAmount() == null ? "" : dto.getSaleAmount().toString());
                dataLst.add(dto.getSingleAmount() == null ? "" : dto.getSingleAmount().toString());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "分类顾客数报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_CATE_USER);
        map.put("data", data);
        map.put("title", "分类顾客数报表");
        map.put("titleCells", (short) 3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);

    }
    */

    /*
    @ApiOperation(value = "商品信息明细报表", notes = "商品信息明细报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/commodityDetailReport", method = RequestMethod.GET)
    private PageInfo<CommodityDetailReportODto> commodityDetailReport(CommodityDetailReportIDto commodityDetailReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        commodityDetailReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());

        return shopReportService.commodityDetailReport(commodityDetailReportIDto);
    }

    @ApiOperation(value = "商品信息明细报表-导出", notes = "商品信息明细报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/commodityDetailReport", method = RequestMethod.GET)
    public ModelAndView exportInfoCommodityDetailReport(CommodityDetailReportIDto commodityDetailReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        commodityDetailReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        commodityDetailReportIDto.setPageNo(1);
        commodityDetailReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<CommodityDetailReportODto> pageInfo = shopReportService.commodityDetailReport(commodityDetailReportIDto);
        List<CommodityDetailReportODto> list = pageInfo.getList();
        Map<String, List<String>> data = new HashMap<>();
        if (null != list && !list.isEmpty()) {
            List<String> dataList = null;
            int i = 0;
            for (CommodityDetailReportODto item : list) {
                dataList = new ArrayList<>();
                dataList.add(item.getShopName());
                dataList.add(item.getCommodityFirstKind());
                dataList.add(item.getCommoditySecondKind());
                dataList.add(item.getCommodityThirdKind());
                dataList.add(item.getBarCode());
                dataList.add(item.getCommodityCode());
                dataList.add(item.getCommodityName());
                dataList.add(item.getCommoditySpec());
                dataList.add(item.getBrandName());
                dataList.add(item.getCommoditySubName());
                dataList.add(item.getCommodityState());
                dataList.add(item.getAppStatus());
                dataList.add(item.getCommodityPackageKind());
                dataList.add(item.getIsWeight());
                dataList.add(StringUtil.changeToString(item.getSellWeight()));
                dataList.add(item.getCommodityUnit());
                dataList.add(StringUtil.changeToString(item.getTaxRate()));
                dataList.add(item.getCommodityIsInstant());
                dataList.add(item.getStorageCondition());
                dataList.add(StringUtil.changeToString(item.getQualityDays()));
                dataList.add(item.getOrigin());
                dataList.add(item.getLogisticsModel());
                dataList.add(item.getSupplierName());
                dataList.add(StringUtil.changeToString(item.getSalesBoxCapacity()));
                dataList.add(StringUtil.changeToString(item.getBoxCapacity()));
                dataList.add(item.getDefaultPicUrl());
                dataList.add(StringUtil.changeToString(item.getPicQuantity()));
                dataList.add(item.getImgTextPicUrl());
                dataList.add(StringUtil.changeToString(item.getWeightPrice()));
                dataList.add(StringUtil.changeToString(item.getPurchasePrice()));
                dataList.add(StringUtil.changeToString(item.getBasePrice()));
                dataList.add(StringUtil.changeToString(item.getCommodityPrice()));
                dataList.add(StringUtil.changeToString(item.getPrevious7DayQuantity()));
                dataList.add(StringUtil.changeToString(item.getPrevious30DayQuantity()));
                dataList.add(StringUtil.changeToString(item.getYearToTodayQuantity()));
                dataList.add(StringUtil.changeToString(item.getStockQuantity()));


                data.put("key_" + i++, dataList);
            }
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品信息明细报表" + "_" + sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.COMMODITY_DETAIL_REPORT);
        map.put("data", data);
        map.put("title", "商品信息明细报表");
        map.put("titleCells", (short) 3);

        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }
    */

    @ApiOperation(value = "订货销售分析", notes = "订货销售分析", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/orderSalesAnalysis", method = RequestMethod.GET)
    public TablePageInfo<OrderSalesAnalysisODTO> orderSalesAnalysis(OrderSalesAnalysisIDTO orderSalesAnalysisIDTO){
        TablePageInfo<OrderSalesAnalysisODTO> orderSalesAnalysisODTOPageInfo = shopReportService.orderSalesAnalysis(orderSalesAnalysisIDTO);
        return orderSalesAnalysisODTOPageInfo;
    }

    @ApiOperation(value = "导出订货销售分析", notes = "订货销售分析导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "exportInfo/orderSalesAnalysis", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "ORDER_SALES_ANALYSIS_REPORT")
    public void exportInfoOrderSalesAnalysis(OrderSalesAnalysisIDTO orderSalesAnalysisIDTO, HttpServletResponse response) throws IOException {
        orderSalesAnalysisIDTO.setPageNo(1);
        orderSalesAnalysisIDTO.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<OrderSalesAnalysisODTO> orderSalesAnalysisODTOPageInfo = shopReportService.orderSalesAnalysis(orderSalesAnalysisIDTO);
        List<OrderSalesAnalysisODTO> list = orderSalesAnalysisODTOPageInfo.getList();

        String shopName = "全部";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            List<OrderSalesAnalysisExportRespVo> exportList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                if(orderSalesAnalysisIDTO.getShopId() != null){
                    shopName = list.get(0).getShopName();
                }
                String row1 = "订货销售分析报表(" + shopName + ")";
                String row2 = "日期:" + orderSalesAnalysisIDTO.getBeginDate() + "~" + orderSalesAnalysisIDTO.getEndDate();
                List<List<String>> headList = ReportUtil.getOrderSalesAnalysisHeaderList(row1, row2);

                OrderSalesAnalysisODTO total = (OrderSalesAnalysisODTO) orderSalesAnalysisODTOPageInfo.getHeader();
                exportList = BeanCloneUtils.copyTo(list, OrderSalesAnalysisExportRespVo.class);

                OrderSalesAnalysisExportRespVo totalRespVo = new OrderSalesAnalysisExportRespVo();
                totalRespVo.setShopName("合计");
                totalRespVo.setOrderTotal(total.getOrderTotal());
                totalRespVo.setOfflineSales(total.getOfflineSales());
                totalRespVo.setSaleReturnOrderTotal(total.getSaleReturnOrderTotal());
                totalRespVo.setDifference(total.getDifference());
                exportList.add(0,totalRespVo);

                ExcelUtil.setFileNameAndHead(response,  "订货销售分析报表" + "_" + sdf.format(new Date()));
                EasyExcel.write(response.getOutputStream()).registerWriteHandler(ReportUtil.getStyleStrategy()).head(headList)
                        .autoCloseStream(Boolean.FALSE).sheet("订货销售分析报表").doWrite(exportList);
            }
            /*EasyExcel.write(response.getOutputStream(), OrderSalesAnalysisExportRespVo.class)
                    .autoCloseStream(Boolean.TRUE).sheet("订货销售分析报表").doWrite(exportList);*/
        }catch (Exception e){
            log.error("订货销售分析报表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

        /*Map<String, List<String>> data = new HashMap<>();
        String shopName = "全部";
        if(CollectionUtils.isNotEmpty(list)){
            List<String> dataList = null;
            Long shopId = orderSalesAnalysisIDTO.getShopId();
            int i = 0;

            OrderSalesAnalysisODTO header = (OrderSalesAnalysisODTO)orderSalesAnalysisODTOPageInfo.getHeader();
            dataList = new ArrayList<>();
            dataList.add("合计");
            dataList.add(header.getCateName());
            dataList.add(StringUtil.changeToString(header.getOrderTotal()));
            dataList.add(header.getOrderTotalRatio());
            dataList.add(StringUtil.changeToString(header.getOfflineSales()));
            dataList.add(header.getOfflineSalesRatio());
            dataList.add(StringUtil.changeToString(header.getSaleReturnOrderTotal()));
            dataList.add(StringUtil.changeToString(header.getDifference()));
            data.put("key_" + i++, dataList);

            for(OrderSalesAnalysisODTO item : list){
                dataList = new ArrayList<>();
                if(shopId != null && i == 1){
                    shopName = item.getShopName();
                }
                dataList.add(item.getShopName());
                dataList.add(item.getCateName());
                dataList.add(StringUtil.changeToString(item.getOrderTotal()));
                dataList.add(item.getOrderTotalRatio());
                dataList.add(StringUtil.changeToString(item.getOfflineSales()));
                dataList.add(item.getOfflineSalesRatio());
                dataList.add(StringUtil.changeToString(item.getSaleReturnOrderTotal()));
                dataList.add(StringUtil.changeToString(item.getDifference()));
                data.put("key_" + i++, dataList);
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "订货销售分析报表" + "_" + sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.ORDER_SALES_ANALYSIS_REPORT);
        map.put("data", data);
        map.put("title", "订货销售分析报表(" + shopName + ")");
        map.put("titleCells", (short) 3);
        map.put("tableHeader", "日期:" + orderSalesAnalysisIDTO.getBeginDate() + "~" + orderSalesAnalysisIDTO.getEndDate());
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);*/
    }



    @ApiOperation(value = "类别客单周同比", notes = "类别客单周同比", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/cateAverageAmount", method = RequestMethod.GET)
    public TablePageInfo<CateAverageAmountODTO> cateAverageAmount(CateAverageAmountIDTO cateAverageAmountIDTO){
        TablePageInfo<CateAverageAmountODTO> cateAverageAmountODTOTablePageInfo = shopReportService.cateAverageAmountReport(cateAverageAmountIDTO);
        return cateAverageAmountODTOTablePageInfo;
    }


    @ApiOperation(value = "导出类别客单周同比", notes = "类别客单周同比导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "exportInfo/cateAverageAmount", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "POS_SR_CATEGORY_GUST")
    public void exportCateAverageAmount(CateAverageAmountIDTO cateAverageAmountIDTO, HttpServletResponse response) throws IOException {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        cateAverageAmountIDTO.setPageNo(1);
        cateAverageAmountIDTO.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<CateAverageAmountODTO> cateAverageAmountODTOTablePageInfo = shopReportService.cateAverageAmountReport(cateAverageAmountIDTO);
        List<CateAverageAmountODTO> list = cateAverageAmountODTOTablePageInfo.getList();
        { // 合计, 数据行
            if (cateAverageAmountODTOTablePageInfo.getHeader() instanceof CateAverageAmountODTO){
                CateAverageAmountODTO header = (CateAverageAmountODTO)cateAverageAmountODTOTablePageInfo.getHeader();
                //特别注意: (坑) 方法内存在shopName 为null时, 存在计算逻辑....; 例如: getOfflineAverageAmount
                CateAverageAmountODTO newHeader = new CateAverageAmountODTO();
                //反射调用 get 计算后, 再拿属性值设置
                BeanUtils.copyProperties(header, newHeader);
                newHeader.setShopName("合计");
                list.add(0, newHeader);
            }
        }
        { //变更旧逻辑, 放置表头下一行
            CateAverageAmountODTO rowExcelDto = new CateAverageAmountODTO();
            rowExcelDto.initNullValues();
            Date parse = DateTimeUtil.parse(cateAverageAmountIDTO.getDate(), "yyyy-MM-dd");
            String s = DateTimeUtil.addDay(parse, -7);
            rowExcelDto.setShopName(String.format("今日: %s    上周日期: %s", cateAverageAmountIDTO.getDate(), s));
            list.add(0, rowExcelDto);
        }

        String fileName = String.format("类别客单周同比_%s", new DateTime().toString(DatePattern.PURE_DATETIME_PATTERN));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        EasyExcel.write(response.getOutputStream(), CateAverageAmountODTO.class)
                .autoCloseStream(Boolean.TRUE).sheet("类别客单周同比").doWrite(list);

        /* 已重构, 后续稳定后可删除
        Map<String, List<String>> data = new HashMap<>();
        String shopName = "全部";
        if(CollectionUtils.isNotEmpty(list)){
            List<String> dataList = null;
            Long shopId = cateAverageAmountIDTO.getShopId();
            int i = 0;

            CateAverageAmountODTO header = (CateAverageAmountODTO)cateAverageAmountODTOTablePageInfo.getHeader();
            dataList = new ArrayList<>();
            dataList.add("合计");
            dataList.add(header.getCateName());
            dataList.add(StringUtil.changeToString(header.getOfflineVisitorNumber()));
            dataList.add(StringUtil.changeToString(header.getLastweekOfflineVisitorNumber()));
            dataList.add(header.getVisitorNumberRatio());
            dataList.add(StringUtil.changeToString(header.getOfflineAverageAmount()));
            dataList.add(StringUtil.changeToString(header.getLastweekOfflineAverageAmount()));
            dataList.add(header.getAverageAmountRatio());
            data.put("key_" + i++, dataList);

            for(CateAverageAmountODTO item : list){
                dataList = new ArrayList<>();
                if(shopId != null && i == 1){
                    shopName = item.getShopName();
                }
                dataList.add(item.getShopName());
                dataList.add(item.getCateName());
                dataList.add(StringUtil.changeToString(item.getOfflineVisitorNumber()));
                dataList.add(StringUtil.changeToString(item.getLastweekOfflineVisitorNumber()));
                dataList.add(item.getVisitorNumberRatio());
                dataList.add(StringUtil.changeToString(item.getOfflineAverageAmount()));
                dataList.add(StringUtil.changeToString(item.getLastweekOfflineAverageAmount()));
                dataList.add(item.getAverageAmountRatio());
                data.put("key_" + i++, dataList);
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "类别客单周同比" + "_" + sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.CATE_AVERAGE_AMOUNT);
        map.put("data", data);
        map.put("title", "类别客单周同比(" + shopName + ")");
        map.put("titleCells", (short) 3);
        Date parse = DateTimeUtil.parse(cateAverageAmountIDTO.getDate(), "yyyy-MM-dd");
        String s = DateTimeUtil.addDay(parse, -7);
        map.put("tableHeader", "今日:" + cateAverageAmountIDTO.getDate() + "          上周日期:" + s);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
        */
    }


    @ApiOperation(value = "商品实发汇总表 ", notes = "商品实发汇总表 ",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryReport", method = RequestMethod.GET)
    @MethodRender
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReport(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.realDeliveryReport(realDeliveryReportIDto);
    }

    @ApiOperation(value = "商品实发汇总表-导出", notes = "商品实发汇总表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryReport", method = RequestMethod.GET)
    public ModelAndView exportInfoRealDeliveryReport(RealDeliveryReportIDto realDeliveryReportIDto) {
        //Assert.isTrue(DateUtil.isAfter(DateUtil.addDay(DateTimeUtil.parse(realDeliveryReportIDto.getBeginDate(), "yyyy-MM-dd"),93), DateTimeUtil.parse(realDeliveryReportIDto.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过三个月!");
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<RealDeliveryReportODto> result = shopReportService.realDeliveryReport(realDeliveryReportIDto);
        List<RealDeliveryReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if (null != list && !list.isEmpty()) {
            renderService.render(list,"/exportInfo/realDeliveryReport");
            realTotalAmount = (BigDecimal) result.getHeader();
            for (RealDeliveryReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(dto.getOrderNum() == null ? "" : dto.getOrderNum().toString());
                dataLst.add(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                dataLst.add(dto.getRealDeliveryAmount() == null ? "" : dto.getRealDeliveryAmount().toString());
                dataLst.add(dto.getDifferNum() == null ? "" : dto.getDifferNum().toString());
                dataLst.add(dto.getFactoryName());
                dataLst.add(dto.getWorkshopName());
                dataLst.add(dto.getFlowshopName());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品实发汇总报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_REAL_DELIVERY);
        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        map.put("tableHeader", "实发金额总计:" + realTotalAmount+"元");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "品类实发汇总表 ", notes = "品类实发汇总表 ",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/thirdRealDeliveryReport", method = RequestMethod.GET)
    @MethodRender
    public TablePageInfo<ThirdRealDeliveryReportODto> thirdRealDeliveryReport(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.thirdRealDeliveryReport(realDeliveryReportIDto);
    }


    @ApiOperation("品类实发汇总表导出")
    @PostMapping("/exportThirdRealDeliveryReport")
    @FileCacheQuery(bizCode = "THIRD_REAL_DELIVERY_REPORT")
    public void exportThirdRealDeliveryReport(@RequestBody RealDeliveryReportIDto realDeliveryReportIDto, HttpServletResponse response) throws IOException {
        realDeliveryReportIDto.initExportPage();
        TablePageInfo<ThirdRealDeliveryReportODto> page = shopReportService.thirdRealDeliveryReport(realDeliveryReportIDto);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            List<ThirdRealDeliveryReportODto> list = new ArrayList<>();
            if(SpringUtil.isNotEmpty(page.getList())) {
                renderService.render(page.getList(), "/exportThirdRealDeliveryReport");
                ThirdRealDeliveryReportODto sum = (ThirdRealDeliveryReportODto) page.getHeader();
                sum.setStoreCode("合计");
                list.add(sum);
                list.addAll(page.getList());
            }
            ExcelUtil.setFileNameAndHead(response,  "品类实发汇总表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream(), ThirdRealDeliveryReportODto.class)
                    .autoCloseStream(Boolean.TRUE).sheet("品类实发汇总表").doWrite(list);
        } catch (Exception e) {
            log.error("品类实发汇总表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }


    }


    /**
     * 订单表(月度汇总)
     * @param beginTime
     * @param endTime
     */
    @GetMapping("orderReportMonth")
    public void orderReportMonth(@RequestParam(value = "beginTime") String beginTime,
                                 @RequestParam(value = "endTime") String endTime
    ){
        shopReportService.orderReportMonth(beginTime,endTime);
    }



    @ApiOperation(value = "商品实发汇总表(按客户类型) ", notes = "商品实发汇总表(按客户类型) ",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryStoreTypeReport", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReport(RealDeliveryReportIDto realDeliveryReportIDto) {
        return shopReportService.realDeliveryStoreTypeReport(realDeliveryReportIDto);
    }

    @ApiOperation(value = "商品实发汇总表-导出(按客户类型)", notes = "商品实发汇总表-导出(按客户类型)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryStoreTypeReport", method = RequestMethod.GET)
    public ModelAndView exportInfoRealDeliveryStoreTypeReport(RealDeliveryReportIDto realDeliveryReportIDto) {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<RealDeliveryReportODto> result = shopReportService.realDeliveryStoreTypeReport(realDeliveryReportIDto);
        List<RealDeliveryReportODto> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if (null != list && !list.isEmpty()) {
            realTotalAmount = (BigDecimal) result.getHeader();
            for (RealDeliveryReportODto dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getStoreTypeName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(dto.getOrderNum() == null ? "" : dto.getOrderNum().toString());
                dataLst.add(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                dataLst.add(dto.getRealDeliveryAmount() == null ? "" : dto.getRealDeliveryAmount().toString());
                dataLst.add(dto.getWarehouseName());
                dataLst.add(dto.getFactoryName());
                dataLst.add(dto.getWorkshopName());
                dataLst.add(dto.getFlowshopName());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品实发汇总报表(客户类型)" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SHOP_REAL_DELIVERY_STORE_TYPE);
        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        map.put("tableHeader", "实发金额总计:" + realTotalAmount+"元");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    @ApiOperation(value = "门店订货汇总表》商品导入", notes = "门店订货汇总表》商品导入", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/findCommodityByCommodityCodes")
    public List<String> findCommodityByCommodityCodes(@RequestBody ShopOrderGoodCommodityIDto shopOrderGoodCommodityIDto) {
        return shopReportService.findCommodityByCommodityCodes(shopOrderGoodCommodityIDto);
    }

    @ApiOperation(value = "门店订货汇总表 ", notes = "门店订货汇总表 ",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shopOrderGoodReport", method = RequestMethod.POST)
    public TablePageInfo<ShopOrderGoodReportODto> shopOrderGoodReport(@RequestBody ShopOrderGoodReportIDto idto) {
        return shopReportService.shopOrderGoodReport(idto);
    }

    /**
     * 汇总月短交数据，汇总月考核信息
     * @param beginTime
     * @param endTime
     */
    @ApiOperation(value = "汇总月短交数据，汇总月考核信息 ", notes = "汇总月短交数据，汇总月考核信息 ",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("mdCheckReportMonth")
    public Boolean mdCheckReportMonth(@RequestParam(value = "beginTime") String beginTime,@RequestParam(value = "endTime") String endTime){
        return shopReportService.mdCheckReportMonth(beginTime,endTime);
    }
}
