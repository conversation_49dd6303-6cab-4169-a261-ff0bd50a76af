package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.bigdata.dto.MdCommoditySaleODTO;
import com.pinshang.qingyun.infrastructure.data.query.annotate.DataQuery;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.mdCheck.DayOrderReportODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckGroupODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportODTO;
import com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterODTO;
import com.pinshang.qingyun.report.dto.shop.ShopNegativeStockODTO;
import com.pinshang.qingyun.report.dto.shop.ShopNegativeStockQueryIDto;
import com.pinshang.qingyun.report.model.BlendCollect;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/9/26
 */
@Repository
public interface ReportMapper {

    @DataQuery(value = "categorySalesWeekPercentCode")
    List<CategorySalesWeekPercentODTO> categorySalesWeekPercent(CategorySalesWeekPercentIDTO idto);

    CategorySalesWeekPercentODTO categorySalesWeekPercentHeader(CategorySalesWeekPercentIDTO idto);

    @DataQuery(value = "shopSalesWeekPercentCode")
    List<ShopSalesWeekODTO> shopSalesWeekPercent(ShopSalesWeekIDTO idto);

    List<FirstCateSumODTO> selectFirstCateSum(@Param("start") String start,@Param("end") String end);

    void deleteMdCheckCommodity(@Param("day") String day);

    void insertMdCheckCommodityList(@Param("day") String day, @Param("commodityIdList") List<Long> commodityIdList);

    void batchInsertMdCheckCommodityList(@Param("list") List<MdCommoditySaleODTO> mdCommoditySaleODTOList);

    List<DayOrderReportODTO> getMdCheckCommodityList(@Param("start") String beginDate, @Param("end") String endDate);


    void deleteMdCheckInfo(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    void batchInsertMdCheckInfo(@Param("list") List<MdCheckReportODTO> list);

    List<MdCheckGroupODTO> getMdCheckGroupList(@Param("today") String today, @Param("shopId")Long shopId);

    List<MdCheckReportODTO> getShopCheckPan(@Param("today") String today, @Param("shopId") Long shopId, @Param("checkGroupId") Long checkGroupId);
    List<Long> querySaleCommodityIds(@Param("shopId") Long shopId, @Param("yesterDay") String yesterDay, @Param("unsalableDay") String unsalableDay);

    int deleteBestsellerGoodMonth(@Param("month") String month);

    void insertBestsellerGoodMonth(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<BestsellerGoodODTO> bestsellerGoodMonthReport(@Param("idto") BestsellerGoodIDTO idto);

    void batchSaveOrUpdateBlendCollect(@Param("list") List<BlendCollect> list);

    List<BlendCollectODTO> blendCollectPage(@Param("ito") BlendCollectPageIDTO idto);

    BlendCollectODTO getBlendCollectSum(@Param("ito") BlendCollectPageIDTO idto);

    List<GiftCardCashierWaterODTO> getGiftCardCashierWaterList(@Param("ito") BlendCollectPageIDTO ito);

    List<Long> getBlendCollectShopIdList(@Param("ito") BlendCollectPageIDTO ito);
    List<String> getBlendCollectDateList(@Param("ito") BlendCollectPageIDTO ito);
    List<BlendCollectODTO> getShopDateKey(@Param("ito") BlendCollectPageIDTO ito);
    List<CheckingExceptionODTO> checkingExceptionPage(@Param("ito") CheckingExceptionPageIDTO idto);

    List<ShopNegativeStockODTO> shopNegativeStockReport(@Param("idto") ShopNegativeStockQueryIDto idto);

    @DataQuery(value = "categoryAllSalesWeekPercentCode")
    List<CategoryAllSalesPercentODTO> categoryAllSalesWeekPercent(CategoryAllSalesPercentIDTO idto);

    @DataQuery(value = "categoryAllSalesMonthPercentCode")
    List<CategoryAllSalesPercentODTO> categoryAllSalesMonthPercent(CategoryAllSalesPercentIDTO idto);
}
