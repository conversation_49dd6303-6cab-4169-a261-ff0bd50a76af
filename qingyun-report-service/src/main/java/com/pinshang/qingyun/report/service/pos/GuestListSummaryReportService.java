package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.PosReportGuestListEnum;
import com.pinshang.qingyun.report.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.mapper.pos.GuestListSummaryReportMapper;
import com.pinshang.qingyun.report.model.pos.GuestListSummaryReport;
import com.pinshang.qingyun.report.service.BaseService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.report.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class GuestListSummaryReportService extends BaseService<GuestListSummaryReportMapper, GuestListSummaryReport> {

    @Autowired
    private GuestListSummaryReportMapper guestListSummaryReportMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopClient shopClient;

    /**
     * 客单分析报表
     * @param idto
     * @return
     */
    public TablePageInfo<GuestListSummaryReportOrderODTO> guestSummaryReport(GuestListSummaryReportOrderIDTO idto){
        idto.setIsCurrentDay(DateUtils.isCurrentDay(idto.getBeginDate(),idto.getEndDate()));

        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList) || (null != idto.getShopId() && !shopIdList.contains(idto.getShopId()))){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }
        PageInfo<GuestListSummaryReportOrderODTO> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            guestListSummaryReportMapper.guestSummaryReport(idto);
        });
        List<GuestListSummaryReportOrderODTO> list=pageDate.getList();
        TotalGuestListODTO total=new TotalGuestListODTO();
        if(!CollectionUtils.isEmpty(list)){
            if(null != idto.getSummaryType() && idto.getSummaryType()== PosReportGuestListEnum.ONE.getCode()){//按pos机
                Map<String, GuestListSummaryReportOrderODTO> dtoMap = getStringGuestListSummaryReportOrderODTOMap(idto,2);
                setDataList(list, dtoMap);
            }else if(null != idto.getSummaryType() && idto.getSummaryType()==PosReportGuestListEnum.TWO.getCode()){//按门店
                //计算连带率
                Map<String,BigDecimal> jointRateMap=getJointRateMap(idto);
                for(GuestListSummaryReportOrderODTO dt:list){
                    if(null !=dt.getGuestQuantity() && dt.getGuestQuantity().compareTo(BigDecimal.ZERO) != 0){
                        dt.setAvgGuestPrice(dt.getGuestAmount().divide(dt.getGuestQuantity(),2,BigDecimal.ROUND_HALF_UP));
                        if(jointRateMap.get(dt.getShopId()+dt.getSaleTime())!=null && dt.getGuestQuantity().compareTo(BigDecimal.ZERO)>0){
                            dt.setJointRate(jointRateMap.get(dt.getShopId()+dt.getSaleTime()).divide(dt.getGuestQuantity(),2,BigDecimal.ROUND_HALF_UP)+"");
                        }
                    }
                }
            }
            //获取总值
            //汇总summaryType 设定0 查日汇总表 ——由于时段分析 没有汇总导致
            idto.setSummaryType(0);
            total=getTotalGuestListODTO(idto);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }
    private Map<String,BigDecimal> getJointRateMap(GuestListSummaryReportOrderIDTO idto){
        Map<String,BigDecimal> jointRateMap =new HashMap<>();
        List<SalesSummaryReportOrderODTO> jointList=guestListSummaryReportMapper.getJointRate(idto);
        if(!CollectionUtils.isEmpty(jointList)){
            for(SalesSummaryReportOrderODTO o:jointList){
                jointRateMap.put(o.getShopId()+o.getSaleTime(),o.getTatalQuantity());
            }
        }
        return jointRateMap;
    }
    private Map<String, GuestListSummaryReportOrderODTO> getStringGuestListSummaryReportOrderODTOMap(GuestListSummaryReportOrderIDTO idto,Integer summaryType) {
        GuestListSummaryReportOrderIDTO dto=new GuestListSummaryReportOrderIDTO();
        BeanUtils.copyProperties(idto,dto);
        dto.setSummaryType(summaryType);
        List<GuestListSummaryReportOrderODTO> dtoList=guestListSummaryReportMapper.guestSummaryReport(dto);
        Map<String,GuestListSummaryReportOrderODTO> dtoMap=new HashMap<>();
        for(GuestListSummaryReportOrderODTO dt:dtoList){
            dtoMap.put(dt.getShopId()+dt.getSaleTime(),dt);
        }
        return dtoMap;
    }
    private void setDataList(List<GuestListSummaryReportOrderODTO> list, Map<String, GuestListSummaryReportOrderODTO> dtoMap) {
        for(GuestListSummaryReportOrderODTO dt:list){
            GuestListSummaryReportOrderODTO fromMap=dtoMap.get(dt.getShopId()+dt.getSaleTime());
            dt.setTotalGuestQuantity(fromMap.getGuestQuantity());
            dt.setTotalGuestAmount(fromMap.getGuestAmount());
            if(null !=dt.getGuestQuantity() && dt.getGuestQuantity().compareTo(BigDecimal.ZERO) != 0){
                dt.setAvgGuestPrice(dt.getGuestAmount().divide(dt.getGuestQuantity(),2, BigDecimal.ROUND_HALF_UP));
            }
            if(null !=dt.getTotalGuestAmount() && dt.getTotalGuestAmount().compareTo(BigDecimal.ZERO) != 0){
                dt.setAvgAmount(dt.getGuestAmount().divide(dt.getTotalGuestAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }
            if(null !=dt.getTotalGuestQuantity() && dt.getTotalGuestQuantity().compareTo(BigDecimal.ZERO) != 0){
                dt.setAvgQuantity(dt.getGuestQuantity().divide(dt.getTotalGuestQuantity(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }
        }
    }
    //获取客单总量
    public TotalGuestListODTO getTotalGuestListODTO(GuestListSummaryReportOrderIDTO idto){
        GuestListSummaryReportOrderIDTO dto=new GuestListSummaryReportOrderIDTO();
        BeanUtils.copyProperties(idto,dto);
        //汇总summaryType 设定0 查日汇总表 ——由于时段分析 没有汇总导致
//        dto.setSummaryType(0);
        List<GuestListSummaryReportOrderODTO> list=guestListSummaryReportMapper.guestSummaryReport(dto);
        GuestListSummaryReportOrderODTO totalgustDTO=list.get(0);
        TotalGuestListODTO totalOdto=new TotalGuestListODTO();
        totalOdto.setTotalGuestQuantity(totalgustDTO.getGuestQuantity());
        totalOdto.setTotalGuestAmount(totalgustDTO.getGuestAmount());
        if(null !=totalgustDTO.getGuestQuantity() && totalgustDTO.getGuestQuantity().compareTo(BigDecimal.ZERO) != 0){
            totalOdto.setAvgGuestPrice(totalgustDTO.getGuestAmount().divide(totalgustDTO.getGuestQuantity(),2,BigDecimal.ROUND_HALF_UP));
        }
        return totalOdto;
    }
    /**
     * 客单时段分析报表 (不需要分页)
     * @param idto
     * @return
     */
    public TablePageInfo<GuestListSummaryReportOrderODTO> guestSummaryHourReport(GuestListSummaryReportOrderIDTO idto){
        idto.setIsCurrentDay(DateUtils.isCurrentDay(idto.getBeginDate(),idto.getEndDate()));
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(),ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList) || (null != idto.getShopId() && !shopIdList.contains(idto.getShopId()))){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }
        idto.setSummaryType(PosReportGuestListEnum.THREE.getCode());
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<GuestListSummaryReportOrderODTO> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            guestListSummaryReportMapper.guestSummaryReport(idto);
        });
        List<GuestListSummaryReportOrderODTO> list=pageDate.getList();
        TotalGuestListODTO total=new TotalGuestListODTO();
        if(!CollectionUtils.isEmpty(list)){
            GuestListSummaryReportOrderIDTO dto=new GuestListSummaryReportOrderIDTO();
            BeanUtils.copyProperties(idto,dto);
            dto.setSummaryType(null);
            List<GuestListSummaryReportOrderODTO> dtoList=guestListSummaryReportMapper.guestSummaryReport(dto);
            GuestListSummaryReportOrderODTO totalDto=dtoList.get(0);
            for(GuestListSummaryReportOrderODTO dt:list){
                dt.setHourTimeStr(dt.getHourTime()+"：00"+"-"+(dt.getHourTime()+1)+"：00");
                if(null !=dt.getGuestQuantity() && dt.getGuestQuantity().compareTo(BigDecimal.ZERO) != 0){
                    dt.setAvgGuestPrice(dt.getGuestAmount().divide(dt.getGuestQuantity(),2, BigDecimal.ROUND_HALF_UP));
                }
                if(null !=totalDto.getGuestAmount() && totalDto.getGuestAmount().compareTo(BigDecimal.ZERO) != 0){
                    dt.setAvgAmount(dt.getGuestAmount().divide(totalDto.getGuestAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }
                if(null !=totalDto.getGuestQuantity() && totalDto.getGuestQuantity().compareTo(BigDecimal.ZERO) != 0){
                    dt.setAvgQuantity(dt.getGuestQuantity().divide(totalDto.getGuestQuantity(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }
            }
            //获取总值
            idto.setSummaryType(null);
            total=getTotalGuestListODTO(idto);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }

    public List<NetSalesListDTO> netSalesList(CommodityDeleteIDTO dto) {
        return guestListSummaryReportMapper.netSalesList(dto);
    }

    public Double netSalesCount(String beginTime, String endTime, List<Long> shopIds,
                                    List<String> saleTimes, List<Long> createIds) {
        return guestListSummaryReportMapper.netSalesCount(beginTime, endTime, shopIds, saleTimes, createIds);
    }

  //  netSalesCount

}
