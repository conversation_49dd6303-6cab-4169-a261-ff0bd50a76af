package com.pinshang.qingyun.report.service;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordIDTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordODTO;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.enums.StockAreaEnum;
import com.pinshang.qingyun.report.mapper.BreakageEnteringMapper;
import com.pinshang.qingyun.report.model.Commodity;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.ReportUtil;
import com.pinshang.qingyun.report.util.StallUtils;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoIDTO;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.service.ConsignmentSupplierClient;
import com.pinshang.qingyun.shop.dto.posSync.PosShopCommodityODTO;
import com.pinshang.qingyun.shop.service.PosShopCommodityClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockItemIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationCommodityDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationCommodityIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.StallCommodityODTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdGoodsAllocationCommodityClient;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class BreakageEnteringService {

    @Autowired
    private BreakageEnteringMapper breakageEnteringMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private PosShopCommodityClient posShopCommodityClient;

    @Autowired
    private XdStockClient xdStockClient;

    @Autowired
    private ConsignmentSupplierClient consignmentSupplierClient;

    @Autowired
    private IRenderService renderService;

    @Autowired
    private UserStallClientService userStallClientService;

    @Autowired
    private StallCommodityClient stallCommodityClient;

    @Autowired
    private DdGoodsAllocationCommodityClient ddGoodsAllocationCommodityClient;

    /**
     * 报损录入
     *
     * @param idto 报损信息的数据传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBreakageEnting(BreakageEnteringIDTO idto){
        //报损原因
        DictionaryODTO reasonDict = dictionaryClient.getDictionaryById(idto.getReasonId());

        //设置监控位置
        if (idto.getBreakageChannel() == null || idto.getBreakageChannel() != 2) {
            DictionaryODTO monitorLocationDict = dictionaryClient.getDictionaryById(idto.getMonitorLocationId());
            idto.setMonitorLocationName(monitorLocationDict.getOptionName());
        }

        DictionaryODTO stockAreaDict = dictionaryClient.getDictionaryById(idto.getStockAreaId());
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (isBigShop()) {
            // 处理大店非餐饮类商品的库存校验
            handleBigShopStockValidation(idto, tokenInfo);
        } else {
            // 处理普通门店非餐饮类商品的库存校验
            handleShopStockValidation(idto, tokenInfo, stockAreaDict);
        }

        // 设置报损的基础信息，包括原因名称、店铺ID、创建人等
        setBasicInfo(idto, reasonDict.getOptionName(), tokenInfo);

        // 设置代销商ID
        handleConsignmentId(idto, tokenInfo);

        // 查询商品价格并保存
        Commodity commodity = queryCommodityPrice(idto, tokenInfo);

        // 将报损数据入库，生产日期和到期日期都为空时，设置为null
        idto.setExpireDate(StringUtils.isEmpty(idto.getExpireDate()) ? null : idto.getExpireDate());
        idto.setMakeDate(StringUtils.isEmpty(idto.getMakeDate()) ? null : idto.getMakeDate());
        breakageEnteringMapper.saveBreakageEnting(idto, commodity);

        // 处理WMS库存的扣减
        processWmsStock(idto, tokenInfo, stockAreaDict);
    }

    private static Boolean isBigShop() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return StallUtils.isBigShop(tokenInfo.getManagementMode());
    }

    private void handleBigShopStockValidation(BreakageEnteringIDTO idto, TokenInfo tokenInfo) {
        //非餐饮pos报损执行校验
        if (null == idto.getIsCatering() || (1 != idto.getIsCatering())) {
            StockQueryIDTO req = new StockQueryIDTO();
            req.setStallId(idto.getStallId());
            req.setWarehouseId(tokenInfo.getShopId());
            req.setCommodityList(Collections.singletonList(idto.getCommodityId()));
            //查询大店商品库存
            List<ShopCommodityStockDTO> shopCommodityStockDTOS = xdStockClient.queryBigShopCommodityStock(req);
            QYAssert.isTrue(!CollectionUtils.isEmpty(shopCommodityStockDTOS), "没有查到门店商品库存!");
            ShopCommodityStockDTO commodityStock = shopCommodityStockDTOS.get(0);
            Integer stockAreaId = Math.toIntExact(idto.getStockAreaId());
            if (StorageAreaEnum.SHELF_AREA.getCode().equals(stockAreaId)) {
                QYAssert.isTrue(commodityStock.getStockQuantity().compareTo(idto.getBreakageNum()) >= 0, "报损数量超过排面库存数!");
            } else if (StorageAreaEnum.PICKING_AREA.getCode().equals(stockAreaId)) {
                QYAssert.isTrue(commodityStock.getPickingAreaStock().compareTo(idto.getBreakageNum()) >= 0, "报损数量超过拣货区库存数!");
            } else if (StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(stockAreaId)) {
                // 查询货位上的商品库存
                GoodsAllocationCommodityIDTO dto = new GoodsAllocationCommodityIDTO();
                dto.setShopId(idto.getShopId());
                dto.setStallId(idto.getStallId());
                dto.setCommodityId(idto.getCommodityId());
                dto.setGoodsAllocationId(idto.getGoodsAllocationId());
                GoodsAllocationCommodityDTO goodsAllocationCommodity = ddGoodsAllocationCommodityClient.queryGoodsAllocateStock(dto);
                QYAssert.isTrue(Objects.nonNull(goodsAllocationCommodity) && goodsAllocationCommodity.getStock().compareTo(idto.getBreakageNum()) >= 0, "报损数量超过存储区库存数!");
            } else if (StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(stockAreaId)) {
                QYAssert.isTrue(commodityStock.getStockProvisional().compareTo(idto.getBreakageNum()) >= 0, "报损数量超过临时库存数!");
            }
        }
    }

    private void handleShopStockValidation(BreakageEnteringIDTO idto, TokenInfo tokenInfo, DictionaryODTO stockAreaDict) {
        //非餐饮pos报损执行校验
        if (null == idto.getIsCatering() || (1 != idto.getIsCatering())) {
            List<ShopCommodityStockDTO> shopCommodityStockDTOS = xdStockClient.queryShopCommodityStock(tokenInfo.getShopId(), Collections.singletonList(idto.getCommodityId()));
            QYAssert.isTrue(!CollectionUtils.isEmpty(shopCommodityStockDTOS), "没有查到门店商品库存!");
            ShopCommodityStockDTO commodityStock = shopCommodityStockDTOS.get(0);
            if (stockAreaDict.getOptionCode().equals(StockAreaEnum.NORMAL.getCode().toString())) {
                // 正常库
                QYAssert.isTrue(commodityStock.getStockQuantity().compareTo(idto.getBreakageNum()) >= 0, "报损数量超过正常库存数!");
            } else if (stockAreaDict.getOptionCode().equals(StockAreaEnum.TEMP.getCode().toString())) {
                // 临时库
                QYAssert.isTrue(commodityStock.getQualityQuantity().compareTo(idto.getBreakageNum()) >= 0, "报损数量超过临时库存数!");
            }
        }
    }

    private void setBasicInfo(BreakageEnteringIDTO idto, String reasonName, TokenInfo tokenInfo) {
        idto.setReasonName(reasonName);
        idto.setShopId(tokenInfo.getShopId());
        idto.setCreateId(tokenInfo.getUserId());
        idto.setCreateName(tokenInfo.getRealName());
        idto.setConsignmentId(-1L);
    }

    private void handleConsignmentId(BreakageEnteringIDTO idto, TokenInfo tokenInfo) {
        // 香烟代销
        Map<Long, Long> consignmentSupplierIdsMap = this.queryConsignmentSupplierInfoList(tokenInfo.getStoreId());
        idto.setConsignmentId(consignmentSupplierIdsMap.getOrDefault(idto.getCommodityId(), -1L));
    }

    private Commodity queryCommodityPrice(BreakageEnteringIDTO idto, TokenInfo tokenInfo) {
        Commodity commodity = new Commodity();
        List<PosShopCommodityODTO> shopCommodityODTOS = posShopCommodityClient.listPosShopCommodity(tokenInfo.getShopId(), Collections.singletonList(idto.getCommodityId()));
        if (SpringUtil.isNotEmpty(shopCommodityODTOS)) {
            commodity.setRetailPrice(shopCommodityODTOS.get(0).getCommodityPrice());
            commodity.setCostPrice(shopCommodityODTOS.get(0).getWeightPrice());
        }
        if (isBigShop()) {
            List<StallCommodityODTO> stallCommodityODTOS = stallCommodityClient.searchByCommodityIdAndStall(idto.getCommodityId(), idto.getStallId());
            if (SpringUtil.isNotEmpty(stallCommodityODTOS)) {
                // 大店的成本价 查 t_stall_commodity
                commodity.setCostPrice(stallCommodityODTOS.get(0).getWeightPrice());
            }
        }
        return commodity;
    }

    private void processWmsStock(BreakageEnteringIDTO idto, TokenInfo tokenInfo, DictionaryODTO stockAreaDict) {
        StockIDTO stockIDTO = new StockIDTO();
        stockIDTO.setWarehouseId(tokenInfo.getShopId());
        stockIDTO.setReferId(idto.getCommodityId());
        // 报损relationCode无报损单号, 也不记录商品id
        stockIDTO.setReferCode("");
        stockIDTO.setUserId(tokenInfo.getUserId());

        setStockEnum(idto, stockIDTO, stockAreaDict);


        List<StockItemIDTO> commodityList = new ArrayList<>();
        StockItemIDTO stockItemIDTO = new StockItemIDTO();
        stockItemIDTO.setCommodityId(idto.getCommodityId());
        stockItemIDTO.setQuantity(idto.getBreakageNum().negate());

        Integer breakage = idto.getBreakage();
        if (Objects.nonNull(breakage)) {
            stockItemIDTO.setStockNumber(-breakage);
        }

        if (isBigShop()) {
            //大店参数
            DdStockInOutExtraIDTO ddStockInOutExtraVO = buildDdStockInOutExtra(idto);
            stockItemIDTO.setDdStockInOutExtraVO(ddStockInOutExtraVO);
        }
        commodityList.add(stockItemIDTO);

        stockIDTO.setCommodityList(commodityList);

        xdStockClient.modifyStock(stockIDTO);
    }

    private void setStockEnum(BreakageEnteringIDTO idto, StockIDTO stockIDTO, DictionaryODTO stockAreaDict) {
        if (null != idto.getIsCatering() && 1 == idto.getIsCatering()) {
            // 餐饮质检库报损出库, 不校验数量
            stockIDTO.setStockEnums(StockInOutTypeEnums.OUT_BREAKAGE_QUALITY_CATERING);
        } else {
            if (!isBigShop()) {
                if (stockAreaDict.getOptionCode().equals(StockAreaEnum.NORMAL.getCode().toString())) {
                    stockIDTO.setStockEnums(StockInOutTypeEnums.OUT_BREAKAGE_NORMAL);
                } else if (stockAreaDict.getOptionCode().equals(StockAreaEnum.TEMP.getCode().toString())) {
                    stockIDTO.setStockEnums(StockInOutTypeEnums.OUT_BREAKAGE_QUALITY);
                }
            } else {
                Integer stockAreaId = Math.toIntExact(idto.getStockAreaId());
                if (StorageAreaEnum.SHELF_AREA.getCode().equals(stockAreaId)
                        || StorageAreaEnum.PICKING_AREA.getCode().equals(stockAreaId)
                        || StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(stockAreaId)) {
                    stockIDTO.setStockEnums(StockInOutTypeEnums.OUT_BREAKAGE_NORMAL);
                } else if (StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(stockAreaId)) {
                    stockIDTO.setStockEnums(StockInOutTypeEnums.OUT_BREAKAGE_QUALITY);
                }
            }
        }
    }

    private DdStockInOutExtraIDTO buildDdStockInOutExtra(BreakageEnteringIDTO idto) {
        DdStockInOutExtraIDTO ddStockInOutExtraVO = new DdStockInOutExtraIDTO();
        ddStockInOutExtraVO.setStallId(idto.getStallId());
        ddStockInOutExtraVO.setCommodityId(idto.getCommodityId());
        ddStockInOutExtraVO.setStorageArea(Math.toIntExact(idto.getStockAreaId()));
        ddStockInOutExtraVO.setGoodsAllocationId(idto.getGoodsAllocationId());
        ddStockInOutExtraVO.setGoodsAllocationCode(idto.getGoodsAllocationCode());
        return ddStockInOutExtraVO;
    }

    /**
     * 查询代销商品
     * @param shopId
     * @return
     */
    private Map<Long, Long> queryConsignmentSupplierInfoList(Long storeId){
        ConsignmentSupplierInfoIDTO vo =new ConsignmentSupplierInfoIDTO();
        vo.setStoreIdList(Collections.singletonList(storeId));
        List<ConsignmentSupplierInfoODTO> consignmentSupplierIds = consignmentSupplierClient.batchQueryByStoreIds(vo);
        if(SpringUtil.isNotEmpty(consignmentSupplierIds)){
            return  consignmentSupplierIds.stream().collect(Collectors.toMap(ConsignmentSupplierInfoODTO::getCommodityId, ConsignmentSupplierInfoODTO::getSupplierId));
        }else{
            return new HashMap<>();
        }
    }

    public BreakagedCommodityListODTO breakagedCommodityForPass24Hours() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long shopId = tokenInfo.getShopId();
        // 旧代销商逻辑已弃用, 目前字段作为香烟代销商id
        /*
        Long consignmentId = -1L;
        if (tokenInfo.getConsignmentFlag() != null && 1 == tokenInfo.getConsignmentFlag()) {
            consignmentId = tokenInfo.getConsignmentId();
        }*/

        Long totalTimes = breakageEnteringMapper.queryTotalTimesForPass24Hours(shopId, null);
        List<BreakagedCommodityODTO > list = breakageEnteringMapper.breakagedCommodityForPass24Hours(shopId, null);
        if (SpringUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setCreateTime(formateCreateTime(item.getCreateDate()));
            });
        }

        BreakagedCommodityListODTO odto = new BreakagedCommodityListODTO();
        odto.setTotalTimes(totalTimes);
        odto.setList(list);

        return odto;
    }

    /**
     *
     * 查看报损记录(日汇总)
     * @param idto
     * @return
     */
    public TablePageInfo<BreakageEnteringDayODTO> breakageEnteringDayList(BreakageEnteringSearchDayIDTO idto) {

        QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getBeginTime()) && !StringUtil.isNullOrEmpty(idto.getEndTime()), "请选择报损日期");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), "yyyy-MM-dd"), DateUtil.parseDate(idto.getBeginTime(), "yyyy-MM-dd"));
        QYAssert.isTrue(diff <= 31, "最大时间跨度为31天");


        // 1. 查询列表
        PageInfo<BreakageEnteringDayODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            breakageEnteringMapper.breakageEnteringDayList(idto);
        });

        // 2. 查询sum
        BreakageEnteringDayODTO header = breakageEnteringMapper.breakageEnteringListDaySum(idto);

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }

    /**
     * 报损记录日汇总
     * @param idto
     * @return
     */
    public TablePageInfo<BreakageEnteringODTO> breakageEnteringList(BreakageEnteringSearchIDTO idto) {
        if (!StringUtil.isNullOrEmpty(idto.getBeginTime())) {
            idto.setBeginTime(idto.getBeginTime() + " 00:00:00");
        }
        if (!StringUtil.isNullOrEmpty(idto.getEndTime())) {
            idto.setEndTime(idto.getEndTime() + " 23:59:59");
        }

        QYAssert.isTrue((!StringUtil.isNullOrEmpty(idto.getBeginTime()) && !StringUtil.isNullOrEmpty(idto.getEndTime())
                || idto.getShopId() != null), "门店和报损日期不能都为空");

        if (idto.getShopId() == null) {
            QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getBeginTime()) && !StringUtil.isNullOrEmpty(idto.getEndTime()), "请选择报损日期");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 31, "最大时间跨度为31天");
        }

        /**
         * 旧代销商逻辑已弃用, 目前字段作为香烟代销商id
         */
       /* TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
       Long shopId = tokenInfo.getShopId();
        Long consignmentId = -1L;
        if (tokenInfo.getConsignmentFlag() != null && 1 == tokenInfo.getConsignmentFlag()) {
            consignmentId = tokenInfo.getConsignmentId();
        }
        idto.setConsignmentId(consignmentId);*/


        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (StallUtils.isSingleShop(tokenInfo.getShopId()) && StallUtils.isBigShop(tokenInfo.getManagementMode()) && Objects.isNull(idto.getStallId())) {
            //单门店 大店，没传档口，则查询用户权限下的档口
            List<Long> stallIdList = userStallClientService.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());
            if (CollectionUtils.isEmpty(stallIdList)) {
                //没有档口，返回空的列表
                PageInfo<BreakageEnteringODTO> result = PageInfo.of(Collections.emptyList());
                return BeanUtil.pageInfo2TablePageInfo(result, TablePageInfo.class);
            }
            idto.setStallIdList(stallIdList);
        }
        // 1. 查询列表
        PageInfo<BreakageEnteringODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            breakageEnteringMapper.breakageEnteringList(idto);
        });

        // 2. 查询sum
        BreakageEnteringODTO header = breakageEnteringMapper.breakageEnteringListSum(idto);

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }

    private static String formateCreateTime(Date createDate) {
        Date now = new Date();
        Date todayStart = DateUtil.getStartDateByDate(now);
        Date todayEnd = DateUtil.getEndDateByDate(now);

        Date yesterdayStart = DateUtil.addDay(todayStart, -1);
        Date yesterdayEnd = DateUtil.addDay(todayEnd, -1);

        // 前天 昨天开始 昨天结束 今天开始 今天结束
        String createTime = null;
        if (createDate.after(todayStart) && createDate.before(todayEnd)) {
            createTime = "今天" + DateUtil.getDateFormate(createDate, "HH:mm:ss");
        } else if (createDate.after(yesterdayStart) && createDate.before(yesterdayEnd)) {
            createTime = "昨天" + DateUtil.getDateFormate(createDate, "HH:mm:ss");
        } else {
            createTime = DateUtil.getDateFormate(createDate, DateUtil.DEFAULT_DATE_FORMAT);
        }

        return createTime;
    }

    /**
     * 汇总报损记录
     * @param dateTime
     * @return
     */
    @Async
    @Transactional
    public Boolean breakageEnteringDay(String dateTime) {
        // 删除
        breakageEnteringMapper.deleteBreakageEnteringDay(dateTime);
        // 新增
        breakageEnteringMapper.insertBreakageEnteringDay(dateTime + " 00:00:00", dateTime + " 23:59:59");
        return Boolean.TRUE;
    }

    /**
     * 报损记录-导出
     */
    public ModelAndView exportBreakageEnteringList(BreakageEnteringSearchIDTO idto) {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<BreakageEnteringODTO> pageInfo = this.breakageEnteringList(idto);
        List<BreakageEnteringODTO> list = pageInfo.getList();
        renderService.render(list, "/hand/breakage/export/breakageEnteringList");
        BreakageEnteringODTO header = (BreakageEnteringODTO) pageInfo.getHeader();
        if (header != null) {
            header.setShopName("合计");
        }
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (Objects.nonNull(tokenInfo.getShopId()) && !StallUtils.isBigShop(tokenInfo.getManagementMode())) {
            //是单门店且是非大店
            return exportBreakageEnteringData(
                    list,
                    header,
                    BreakageEnteringShopExcelODTO.class,
                    ExcelSheetTitleEnum.BREAKAGE_ENTERING
            );
        } else {
            return exportBreakageEnteringData(
                    list,
                    header,
                    BreakageEnteringBigShopExcelODTO.class,
                    ExcelSheetTitleEnum.DD_BREAKAGE_ENTERING
            );
        }
    }

    private <T> ModelAndView exportBreakageEnteringData(List<BreakageEnteringODTO> list, BreakageEnteringODTO header, Class<T> dtoClass, ExcelSheetTitleEnum titleEnum) {
        Map<String, List<String>> data = new HashMap<>();

        List<T> tables = new ArrayList<>();

        // 如果有表头，将表头也加入导出的列表
        if (header != null) {
            T headerDto = BeanCloneUtils.copyTo(header, dtoClass);
            tables.add(headerDto);
        }

        // 将列表转换为目标类型的DTO
        if (!CollectionUtils.isEmpty(list)) {
            List<T> dtoList = BeanCloneUtils.copyTo(list, dtoClass);
            // 添加所有的数据行
            tables.addAll(dtoList);
        }

        // 构建导出的数据
        ReportUtil.buildData(tables, data);

        // 返回ModelAndView
        return ReportUtil.buildModelAndView(titleEnum, data, "");
    }

    /***
     * 门店报损记录原始数据 供大店财务 内部往来使用
     * @param idto
     * @return
     */
    public List<ShopBreakageRecordODTO> selectBreakageRecordList(ShopBreakageRecordIDTO idto){
        QYAssert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(idto.getBusinessDate()), "请输入查询日期");
        QYAssert.notNull(idto.getLimitQuantity(), "请输入查询的条数");
        return breakageEnteringMapper.selectBreakageRecordList(idto);
    }
}
