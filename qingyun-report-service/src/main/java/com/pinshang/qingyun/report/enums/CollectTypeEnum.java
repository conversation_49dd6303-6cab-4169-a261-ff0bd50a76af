package com.pinshang.qingyun.report.enums;

/**
 * 混合收款项
 */
public enum CollectTypeEnum {
    CONVERGE(1, "聚合"),
    MONEY(2, "现金")
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;

    CollectTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
