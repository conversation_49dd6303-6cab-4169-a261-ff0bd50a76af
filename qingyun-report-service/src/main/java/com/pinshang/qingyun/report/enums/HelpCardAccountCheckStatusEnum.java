package com.pinshang.qingyun.report.enums;

/**
 * @ClassName HelpCardAccountCheckStatusEnum
 * <AUTHOR>
 * @Date 2023/2/24 14:52
 * @Description HelpCardAccountCheckStatusEnum
 * @Version 1.0
 */
public enum HelpCardAccountCheckStatusEnum {
    NOT_CHECK(1, "未对账"),
    CHECKED(2, "已对账"),
    CANCEL(3, "已取消");

    private int code;

    private String desc;

    HelpCardAccountCheckStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
