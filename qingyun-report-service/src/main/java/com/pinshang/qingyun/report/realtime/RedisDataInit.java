package com.pinshang.qingyun.report.realtime;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.CommodityShopTaxODTO;
import com.pinshang.qingyun.report.mapper.shop.CommodityShopTaxMapper;
import com.pinshang.qingyun.report.model.shop.CommodityShopTax;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxService;
import com.pinshang.qingyun.report.util.DateUtils;
import org.redisson.api.RAtomicDouble;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RedisDataInit {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private CommodityShopTaxMapper commodityShopTaxMapper;
    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;

    public boolean initToDay(){
        Example example = new Example(CommodityShopTax.class);
        Date now = new Date();
        example.createCriteria()
                .andGreaterThanOrEqualTo("dateTime", DateUtil.get4yMd(new Date()))
                .andLessThanOrEqualTo("dateTime", DateUtils.addDays(now,1));
        List<CommodityShopTax> commodityShopTaxes = commodityShopTaxMapper.selectByExample(example);
        Map<String, List<CommodityShopTax>> commodityShopTaxGroupByDateTime = commodityShopTaxes.stream().collect(Collectors.groupingBy(CommodityShopTax::getDateTime));
        for(String dateTime : commodityShopTaxGroupByDateTime.keySet()){
            String data$yyMMdd = dateTime.substring(2);
            String current$yyMM = data$yyMMdd.substring(0,5);
            String grossProfitKey = OrderConstants.Monthly.GROSS_PROFIT + current$yyMM;
            RScoredSortedSet<Long> grossProfitSortedSet = redisson.getScoredSortedSet(grossProfitKey);
            grossProfitSortedSet.clear();

            String orderQuantityKey = OrderConstants.Monthly.ORDER_QUANTITY + current$yyMM;
            RAtomicLong orderQuantityRa = redisson.getAtomicLong(orderQuantityKey);
            orderQuantityRa.delete();

            String turnoverKey = OrderConstants.Monthly.TURNOVER + current$yyMM;
            RScoredSortedSet<Long> turnoverSortedSet = redisson.getScoredSortedSet(turnoverKey);
            turnoverSortedSet.clear();

            String totalGrossProfit = OrderConstants.Daile.TOTAL_GROSS_PROFIT + data$yyMMdd;
            RAtomicDouble totalGrossProfitRa = redisson.getAtomicDouble(totalGrossProfit);
            totalGrossProfitRa.delete();

            String totalTurnoverKeyDay = OrderConstants.Daile.TOTAL_TURNOVER + data$yyMMdd;
            RAtomicDouble totalTurnoverRaDay = redisson.getAtomicDouble(totalTurnoverKeyDay);
            totalTurnoverRaDay.delete();

            String orderQuantityKeyDay = OrderConstants.Daile.ORDER_QUANTITY + data$yyMMdd;
            RAtomicDouble orderQuantityRaDay = redisson.getAtomicDouble(orderQuantityKeyDay);
            orderQuantityRaDay.delete();

            String turnoverKeyDay = OrderConstants.Daile.TURNOVER + data$yyMMdd;
            RScoredSortedSet<Long> turnoverDaySortedSet = redisson.getScoredSortedSet(turnoverKeyDay);
            turnoverDaySortedSet.clear();

            String turnoverPosKeyDay = OrderConstants.Daile.Pos.TURNOVER + data$yyMMdd;
            RScoredSortedSet<Long> turnoverDayPosSortedSet = redisson.getScoredSortedSet(turnoverPosKeyDay);
            turnoverDayPosSortedSet.clear();

            String turnoverOnlineKeyDay = OrderConstants.Daile.Online.TURNOVER + data$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineDaySortedSet = redisson.getScoredSortedSet(turnoverOnlineKeyDay);
            turnoverOnlineDaySortedSet.clear();

            String grossProfitOnlineKeyDay = OrderConstants.Daile.Online.GROSS_PROFIT + data$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitOnlineKeyDay);
            grossProfitOnlineKeyDaySortedSet.clear();

            String grossProfitKeyDay = OrderConstants.Daile.GROSS_PROFIT + data$yyMMdd;
            RScoredSortedSet<Long> grossProfitKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitKeyDay);
            grossProfitKeyDaySortedSet.clear();

            String shopOrderQuantityOnlineKeyDay = OrderConstants.Daile.Online.SHOP_ORDER_QUANTITY + data$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineKeyDaySortedSet = redisson.getScoredSortedSet(shopOrderQuantityOnlineKeyDay);
            shopOrderQuantityOnlineKeyDaySortedSet.clear();

            String grossProfitPosKeyDay = OrderConstants.Daile.Pos.GROSS_PROFIT + data$yyMMdd;
            RScoredSortedSet<Long> grossProfitPosKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitPosKeyDay);
            grossProfitPosKeyDaySortedSet.clear();

            String shopOrderQuantityPosKeyDay = OrderConstants.Daile.Pos.SHOP_ORDER_QUANTITY + data$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityPosKeyDaySortedSet = redisson.getScoredSortedSet(shopOrderQuantityPosKeyDay);
            shopOrderQuantityPosKeyDaySortedSet.clear();

            List<CommodityShopTax> commodityShopTaxesList = commodityShopTaxGroupByDateTime.get(dateTime);

            commodityShopTaxesList.forEach(c -> {
                BigDecimal costTotal = c.getCostTotal(); //成本
                BigDecimal totalSales = c.getTotalSales();//销售
                Integer offlineVisitorNumber = c.getOfflineVisitorNumber();
                Long shopId = c.getShopId();
                if(costTotal != null && totalSales != null && offlineVisitorNumber != null){
                    BigDecimal grossProfit = totalSales.subtract(costTotal); //毛利
                    totalGrossProfitRa.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    totalGrossProfitRa.addAndGet(grossProfit.doubleValue());

                    totalTurnoverRaDay.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    totalTurnoverRaDay.addAndGet(totalSales.doubleValue());

                    orderQuantityRaDay.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    orderQuantityRaDay.addAndGet(offlineVisitorNumber);

                    orderQuantityRa.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    orderQuantityRa.addAndGet(offlineVisitorNumber);

                    grossProfitSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    grossProfitSortedSet.addScore(shopId,grossProfit);

                    turnoverSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    turnoverSortedSet.addScore(shopId,totalSales);


                    grossProfitKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    grossProfitKeyDaySortedSet.addScore(shopId,grossProfit);

                    turnoverDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    turnoverDaySortedSet.addScore(shopId,totalSales);

                    turnoverDayPosSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    turnoverDayPosSortedSet.addScore(shopId,c.getOfflineSales());

                    turnoverOnlineDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    turnoverOnlineDaySortedSet.addScore(shopId,c.getOnlineSales());

                    grossProfitOnlineKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    grossProfitOnlineKeyDaySortedSet.addScore(shopId,c.getOnlineSales().subtract(c.getOnlineCost()));

                    grossProfitPosKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    grossProfitPosKeyDaySortedSet.addScore(shopId,c.getOfflineSales().subtract(c.getOfflineCost()));

                    shopOrderQuantityPosKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    shopOrderQuantityPosKeyDaySortedSet.addScore(shopId,c.getOfflineVisitorNumber().longValue());

                }
            });
        }
        return true;
    }

    public boolean initMonth(){
        Example example = new Example(CommodityShopTax.class);
        Date now = new Date();
        example.createCriteria()
                .andGreaterThanOrEqualTo("dateTime", DateUtils.getMonthFirstDay())
                .andLessThanOrEqualTo("dateTime", DateUtils.addDays(now,1));
        List<CommodityShopTax> commodityShopTaxes = commodityShopTaxMapper.selectByExample(example);
        Map<String, List<CommodityShopTax>> commodityShopTaxGroupByDateTime = commodityShopTaxes.stream().collect(Collectors.groupingBy(CommodityShopTax::getDateTime));
        for(String dateTime : commodityShopTaxGroupByDateTime.keySet()){
            String data$yyMMdd = dateTime.substring(2);
            String current$yyMM = data$yyMMdd.substring(0,5);
            String grossProfitKey = OrderConstants.Monthly.GROSS_PROFIT + current$yyMM;
            RScoredSortedSet<Long> grossProfitSortedSet = redisson.getScoredSortedSet(grossProfitKey);
            grossProfitSortedSet.clear();

            String orderQuantityKey = OrderConstants.Monthly.ORDER_QUANTITY + current$yyMM;
            RAtomicLong orderQuantityRa = redisson.getAtomicLong(orderQuantityKey);
            orderQuantityRa.delete();

            String turnoverKey = OrderConstants.Monthly.TURNOVER + current$yyMM;
            RScoredSortedSet<Long> turnoverSortedSet = redisson.getScoredSortedSet(turnoverKey);
            turnoverSortedSet.clear();

            List<CommodityShopTax> commodityShopTaxesList = commodityShopTaxGroupByDateTime.get(dateTime);

            commodityShopTaxesList.forEach(c -> {
                BigDecimal costTotal = c.getCostTotal(); //成本
                BigDecimal totalSales = c.getTotalSales();//销售
                Integer offlineVisitorNumber = c.getOfflineVisitorNumber();
                Long shopId = c.getShopId();
                if(costTotal != null && totalSales != null && offlineVisitorNumber != null){
                    BigDecimal grossProfit = totalSales.subtract(costTotal); //毛利

                    orderQuantityRa.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    orderQuantityRa.addAndGet(offlineVisitorNumber);

                    grossProfitSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    grossProfitSortedSet.addScore(shopId,grossProfit);

                    turnoverSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                    turnoverSortedSet.addScore(shopId,totalSales);

                }
            });
        }
        return true;
    }


    /**
     * 首页初始化
     */
    public boolean initToDay2(){
        String dateTime = DateUtil.get4yMd(new Date());
        // 查询线下数量、金额、成本、来客数
        List<CommodityShopTaxODTO> shopTaxFromOfflineList = commodityShopTaxMapper.getPosShopTaxFromOffline(dateTime);

        String data$yyMMdd = dateTime.substring(2);
        String current$yyMM = data$yyMMdd.substring(0,5);
        String grossProfitKey = OrderConstants.Monthly.GROSS_PROFIT + current$yyMM;
        RScoredSortedSet<Long> grossProfitSortedSet = redisson.getScoredSortedSet(grossProfitKey);
        grossProfitSortedSet.clear();

        String orderQuantityKey = OrderConstants.Monthly.ORDER_QUANTITY + current$yyMM;
        RAtomicLong orderQuantityRa = redisson.getAtomicLong(orderQuantityKey);
        orderQuantityRa.delete();

        String turnoverKey = OrderConstants.Monthly.TURNOVER + current$yyMM;
        RScoredSortedSet<Long> turnoverSortedSet = redisson.getScoredSortedSet(turnoverKey);
        turnoverSortedSet.clear();

        String totalGrossProfit = OrderConstants.Daile.TOTAL_GROSS_PROFIT + data$yyMMdd;
        RAtomicDouble totalGrossProfitRa = redisson.getAtomicDouble(totalGrossProfit);
        totalGrossProfitRa.delete();

        String totalTurnoverKeyDay = OrderConstants.Daile.TOTAL_TURNOVER + data$yyMMdd;
        RAtomicDouble totalTurnoverRaDay = redisson.getAtomicDouble(totalTurnoverKeyDay);
        totalTurnoverRaDay.delete();

        String orderQuantityKeyDay = OrderConstants.Daile.ORDER_QUANTITY + data$yyMMdd;
        RAtomicDouble orderQuantityRaDay = redisson.getAtomicDouble(orderQuantityKeyDay);
        orderQuantityRaDay.delete();

        String turnoverKeyDay = OrderConstants.Daile.TURNOVER + data$yyMMdd;
        RScoredSortedSet<Long> turnoverDaySortedSet = redisson.getScoredSortedSet(turnoverKeyDay);
        turnoverDaySortedSet.clear();

        String turnoverPosKeyDay = OrderConstants.Daile.Pos.TURNOVER + data$yyMMdd;
        RScoredSortedSet<Long> turnoverDayPosSortedSet = redisson.getScoredSortedSet(turnoverPosKeyDay);
        turnoverDayPosSortedSet.clear();

        String turnoverOnlineKeyDay = OrderConstants.Daile.Online.TURNOVER + data$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineDaySortedSet = redisson.getScoredSortedSet(turnoverOnlineKeyDay);
        turnoverOnlineDaySortedSet.clear();

        String grossProfitOnlineKeyDay = OrderConstants.Daile.Online.GROSS_PROFIT + data$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitOnlineKeyDay);
        grossProfitOnlineKeyDaySortedSet.clear();

        String grossProfitKeyDay = OrderConstants.Daile.GROSS_PROFIT + data$yyMMdd;
        RScoredSortedSet<Long> grossProfitKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitKeyDay);
        grossProfitKeyDaySortedSet.clear();

        String shopOrderQuantityOnlineKeyDay = OrderConstants.Daile.Online.SHOP_ORDER_QUANTITY + data$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineKeyDaySortedSet = redisson.getScoredSortedSet(shopOrderQuantityOnlineKeyDay);
        shopOrderQuantityOnlineKeyDaySortedSet.clear();

        String grossProfitPosKeyDay = OrderConstants.Daile.Pos.GROSS_PROFIT + data$yyMMdd;
        RScoredSortedSet<Long> grossProfitPosKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitPosKeyDay);
        grossProfitPosKeyDaySortedSet.clear();

        String shopOrderQuantityPosKeyDay = OrderConstants.Daile.Pos.SHOP_ORDER_QUANTITY + data$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityPosKeyDaySortedSet = redisson.getScoredSortedSet(shopOrderQuantityPosKeyDay);
        shopOrderQuantityPosKeyDaySortedSet.clear();

        shopTaxFromOfflineList.forEach(c -> {
            BigDecimal costTotal = c.getCostTotal(); //成本
            BigDecimal totalSales = c.getTotalSales();//销售
            Long shopId = c.getShopId();
            Integer offlineVisitorNumber = c.getOfflineVisitorNumber();
            if(costTotal != null && totalSales != null && offlineVisitorNumber != null){
                BigDecimal grossProfit = totalSales.subtract(costTotal); //毛利
                totalGrossProfitRa.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                totalGrossProfitRa.addAndGet(grossProfit.doubleValue());

                totalTurnoverRaDay.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                totalTurnoverRaDay.addAndGet(totalSales.doubleValue());

                orderQuantityRaDay.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                orderQuantityRaDay.addAndGet(offlineVisitorNumber);

                orderQuantityRa.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                orderQuantityRa.addAndGet(offlineVisitorNumber);

                grossProfitSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                grossProfitSortedSet.addScore(shopId,grossProfit);

                turnoverSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                turnoverSortedSet.addScore(shopId,totalSales);


                grossProfitKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                grossProfitKeyDaySortedSet.addScore(shopId,grossProfit);

                turnoverDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                turnoverDaySortedSet.addScore(shopId,totalSales);

                turnoverDayPosSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                turnoverDayPosSortedSet.addScore(shopId,totalSales);

                turnoverOnlineDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                turnoverOnlineDaySortedSet.addScore(shopId,0);

                grossProfitOnlineKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                grossProfitOnlineKeyDaySortedSet.addScore(shopId,0);

                grossProfitPosKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                grossProfitPosKeyDaySortedSet.addScore(shopId,totalSales.subtract(costTotal));

                shopOrderQuantityPosKeyDaySortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
                shopOrderQuantityPosKeyDaySortedSet.addScore(shopId,offlineVisitorNumber);

            }
        });

        return true;
    }

    public Boolean initTodayReturn() {
        String dateTime = DateUtil.get4yMd(new Date());

        String data$yyMMdd = dateTime.substring(2);

        String totalGrossProfit = ReturnOrderConstants.Daile.TOTAL_GROSS_PROFIT + data$yyMMdd;
        RAtomicDouble totalGrossProfitRa = redisson.getAtomicDouble(totalGrossProfit);
        totalGrossProfitRa.delete();

        String totalTurnoverKeyDay = ReturnOrderConstants.Daile.TOTAL_TURNOVER + data$yyMMdd;
        RAtomicDouble totalTurnoverRaDay = redisson.getAtomicDouble(totalTurnoverKeyDay);
        totalTurnoverRaDay.delete();

        String orderQuantityKeyDay = ReturnOrderConstants.Daile.ORDER_QUANTITY + data$yyMMdd;
        RAtomicDouble orderQuantityRaDay = redisson.getAtomicDouble(orderQuantityKeyDay);
        orderQuantityRaDay.delete();

        String turnoverKeyDay = ReturnOrderConstants.Daile.TURNOVER + data$yyMMdd;
        RScoredSortedSet<Long> turnoverDaySortedSet = redisson.getScoredSortedSet(turnoverKeyDay);
        turnoverDaySortedSet.clear();

        String grossProfitKeyDay = ReturnOrderConstants.Daile.GROSS_PROFIT + data$yyMMdd;
        RScoredSortedSet<Long> grossProfitKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitKeyDay);
        grossProfitKeyDaySortedSet.clear();

        String turnoverPosKeyDay = ReturnOrderConstants.Daile.Pos.TURNOVER + data$yyMMdd;
        RScoredSortedSet<Long> turnoverDayPosSortedSet = redisson.getScoredSortedSet(turnoverPosKeyDay);
        turnoverDayPosSortedSet.clear();

        String grossProfitPosKeyDay = ReturnOrderConstants.Daile.Pos.GROSS_PROFIT + data$yyMMdd;
        RScoredSortedSet<Long> grossProfitPosKeyDaySortedSet = redisson.getScoredSortedSet(grossProfitPosKeyDay);
        grossProfitPosKeyDaySortedSet.clear();

        String shopOrderQuantityPosKeyDay = ReturnOrderConstants.Daile.Pos.SHOP_ORDER_QUANTITY + data$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityPosKeyDaySortedSet = redisson.getScoredSortedSet(shopOrderQuantityPosKeyDay);
        shopOrderQuantityPosKeyDaySortedSet.clear();

        return true;
    }
}
