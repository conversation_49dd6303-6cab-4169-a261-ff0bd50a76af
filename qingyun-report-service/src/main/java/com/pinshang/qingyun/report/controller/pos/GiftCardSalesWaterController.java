package com.pinshang.qingyun.report.controller.pos;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.RepeatSubmitBusinessCode;
import com.pinshang.qingyun.base.enums.gift.GiftSaleTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.GiftCardSalesTypeEnum;
import com.pinshang.qingyun.report.service.pos.GiftCardSalesWaterService;
import com.pinshang.qingyun.report.util.BeanUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/giftCard")
@Slf4j
public class GiftCardSalesWaterController {

    @Autowired
    private GiftCardSalesWaterService giftCardSalesWaterService;

    @Autowired
    private IRenderService renderService;

    @PostMapping("/salesWaterPage")
    @ApiOperation("礼品卡销售流水")
    public TablePageInfo<GiftCardSalesWaterPage> salesWaterPage(@RequestBody QueryGiftCardWaterDTO dto) {
        dto.setType(GiftSaleTypeEnum.GIFT_CARD.getCode());
        PageInfo<GiftCardSalesWaterPage> page = giftCardSalesWaterService.salesWaterPage(dto);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        GiftCardSalesWaterPage giftCardSalesWaterPage = null;
        if (page.getList().size() > 0) {
            giftCardSalesWaterPage = giftCardSalesWaterService.salesWaterSum(dto);
        } else {
            giftCardSalesWaterPage = new GiftCardSalesWaterPage();
        }
        tablePageInfo.setHeader(giftCardSalesWaterPage);
        return tablePageInfo;
    }


    @GetMapping(value = "/export/salesWaterPage", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("导出礼品卡销售流水")
    @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.XD_USER_PUSH_EXPORT,expireTime = 10)
    public void exportSalesWaterPage(QueryGiftCardWaterDTO dto, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(dto.getDateStart() != null && dto.getDateEnd() != null, "时间不能为空");
        dto.setType(GiftSaleTypeEnum.GIFT_CARD.getCode());
        dto.initExportPage();
        PageInfo<GiftCardSalesWaterPage> page = giftCardSalesWaterService.salesWaterPage(dto);
        List<GiftCardSalesWaterPage> dataList = new ArrayList<>();
        GiftCardSalesWaterPage salesWater = giftCardSalesWaterService.salesWaterSum(dto);
        if (null != salesWater) {
            salesWater.setShopTypeName("总计");
        }
        dataList.add(salesWater);
        for (GiftCardSalesWaterPage e: page.getList()) {
            e.setShopTypeName(ShopTypeEnums.get(e.getShopType()).getName());
            e.setCardSourceTypeName(GiftCardSalesTypeEnum.getEnumByCOde(e.getCardSourceType()).getDesc());
        }
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"礼品卡销售流水");
            EasyExcel.write(response.getOutputStream(), GiftCardSalesWaterPage.class).autoCloseStream(Boolean.FALSE).sheet("礼品卡销售流水")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("礼品卡销售流水--导出异常",e);
        }

    }

    @PostMapping("/consignmentSalesWaterPage")
    @ApiOperation("代销销售流水")
    @MethodRender
    public TablePageInfo<GiftCardSalesWaterPage> consignmentSalesWaterPage(@RequestBody QueryGiftCardWaterDTO dto) {
        PageInfo<GiftCardSalesWaterPage> page = giftCardSalesWaterService.consignmentSalesWaterPage(dto);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        GiftCardSalesWaterPage giftCardSalesWaterPage = null;
        if (page.getList().size() > 0) {
            giftCardSalesWaterPage = giftCardSalesWaterService.consignmentSalesWaterSum(dto);
        } else {
            giftCardSalesWaterPage = new GiftCardSalesWaterPage();
        }
        tablePageInfo.setHeader(giftCardSalesWaterPage);
        return tablePageInfo;
    }

    @GetMapping(value = "/exportConsignmentSalesWaterPage", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("导出代销销售流水")
    @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.XD_USER_PUSH_EXPORT,expireTime = 10)
    public void exportConsignmentSalesWaterPage(QueryGiftCardWaterDTO dto, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(dto.getDateStart() != null && dto.getDateEnd() != null, "时间不能为空");
        dto.initExportPage();
        PageInfo<GiftCardSalesWaterPage> page = giftCardSalesWaterService.consignmentSalesWaterPage(dto);
        renderService.render(page.getList(),"/giftCard/exportConsignmentSalesWaterPage");
        List<ConsignmentSalesWaterDTO> dataList = new ArrayList<>();
        GiftCardSalesWaterPage salesWater = giftCardSalesWaterService.consignmentSalesWaterSum(dto);
        if (null != salesWater) {
            ConsignmentSalesWaterDTO consignmentSalesWaterDTO = new ConsignmentSalesWaterDTO();
            consignmentSalesWaterDTO.setOrgName("合计");
            consignmentSalesWaterDTO.setNum(salesWater.getNum());
            consignmentSalesWaterDTO.setPrice(salesWater.getPrice());
            dataList.add(consignmentSalesWaterDTO);
        }

        List<ConsignmentSalesWaterDTO> pageList = BeanCloneUtils.copyTo(page.getList(), ConsignmentSalesWaterDTO.class);
        dataList.addAll(pageList);
        try {
            ExcelUtil.setFileNameAndHead( response,"代销销售流水");
            EasyExcel.write(response.getOutputStream(), ConsignmentSalesWaterDTO.class).autoCloseStream(Boolean.FALSE).sheet("代销销售流水")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("代销销售流水--导出异常",e);
        }

    }

    @PostMapping("/consignmentCommoditySalesPage")
    @ApiOperation("香烟代销销售汇总")
    @MethodRender
    public PageInfo<ConsignmentCommoditySalesReportODTO> consignmentCommoditySalesReport(@RequestBody ConsignmentCommoditySalesReportIDTO idto){
        return giftCardSalesWaterService.consignmentCommoditySalesReport(idto);
    }

    @PostMapping("/consignmentShopCommoditySalesReport")
    @ApiOperation("香烟代销门店销售汇总")
    @MethodRender
    public PageInfo<ConsignmentShopSalesReportODTO> consignmentShopCommoditySalesReport(@RequestBody ConsignmentShopSalesReportIDTO idto){
        return giftCardSalesWaterService.consignmentShopCommoditySalesReport(idto);
    }

    @ApiOperation(value = "香烟代销销售汇总-导出", notes = "香烟代销销售流水表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/consignmentCommoditySalesPage")
    public void exportConsignmentCommoditySalesPage(ConsignmentCommoditySalesReportIDTO dto, HttpServletResponse response) throws IOException {
        dto.initExportPage();

        PageInfo<ConsignmentCommoditySalesReportODTO> page = giftCardSalesWaterService.consignmentCommoditySalesReport(dto);
        if(null == page.getList()){
            page.setList(new ArrayList<>());
        }
        if(null != dto.getIsWithShop() && 1 == dto.getIsWithShop()){
            List<ConsignmentCommoditySalesReportODTO> dataList = new ArrayList<>();
            renderService.render(page.getList(), "/export/consignmentCommoditySalesPage");
            dataList.addAll(page.getList());
            try {
                ExcelUtil.setFileNameAndHead( response,"香烟代销销售汇总表");
                EasyExcel.write(response.getOutputStream(), ConsignmentCommoditySalesReportODTO.class).autoCloseStream(Boolean.FALSE).sheet("香烟代销销售汇总表")
                        .doWrite( dataList );
            } catch (Exception e) {
                ExcelUtil.setExceptionResponse( response );
                log.error("香烟代销销售汇总表--导出异常",e);
            }
        }else{
            List<ConsignmentCommoditySalesReportNoShopODTO> dataList = new ArrayList<>();
            List<ConsignmentCommoditySalesReportNoShopODTO> list = page.getList().stream().map(it -> BeanCloneUtils.copyTo(it, ConsignmentCommoditySalesReportNoShopODTO.class)).collect(Collectors.toList());
            renderService.render(list, "/export/consignmentCommoditySalesPage");
            dataList.addAll(list);
            try {
                ExcelUtil.setFileNameAndHead( response,"香烟代销销售汇总表");
                EasyExcel.write(response.getOutputStream(), ConsignmentCommoditySalesReportNoShopODTO.class).autoCloseStream(Boolean.FALSE).sheet("香烟代销销售汇总表")
                        .doWrite( dataList );
            } catch (Exception e) {
                ExcelUtil.setExceptionResponse( response );
                log.error("香烟代销销售汇总表--导出异常",e);
            }
        }

    }

    @ApiOperation(value = "香烟代销门店销售汇总-导出", notes = "香烟代销门店销售流水-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/consignmentShopCommoditySalesReport")
    public void exportConsignmentShopCommoditySalesReport(ConsignmentShopSalesReportIDTO dto, HttpServletResponse response) throws IOException {
        dto.initExportPage();

        PageInfo<ConsignmentShopSalesReportODTO> page = giftCardSalesWaterService.consignmentShopCommoditySalesReport(dto);
        if(null == page.getList()){
            page.setList(new ArrayList<>());
        }
        List<ConsignmentShopSalesReportODTO> dataList = new ArrayList<>();
        renderService.render(page.getList(), "/export/consignmentShopCommoditySalesReport");
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"香烟代销门店销售汇总");
            EasyExcel.write(response.getOutputStream(), ConsignmentShopSalesReportODTO.class).autoCloseStream(Boolean.FALSE).sheet("香烟代销门店销售汇总")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("香烟代销门店销售汇总--导出异常",e);
        }
    }
}
