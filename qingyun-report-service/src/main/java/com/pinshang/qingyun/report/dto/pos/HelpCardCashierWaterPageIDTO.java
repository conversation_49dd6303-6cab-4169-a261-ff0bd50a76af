package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HelpCardCashierWaterPageIDTO
 * <AUTHOR>
 * @Date 2023/2/22 14:47
 * @Description HelpCardCashierWaterPageIDTO
 * @Version 1.0
 */
@Data
public class HelpCardCashierWaterPageIDTO extends Pagination {
    @ApiModelProperty("开始时间 yyyy-MM-dd")
    private String beginTime;

    @ApiModelProperty("结束时间 yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty("组织code")
    private String orgCode;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("设备号")
    private String payCode;

    @ApiModelProperty("订单号")
    private String orderCode;

    @ApiModelProperty("帮困卡号")
    private String cardNo;

    @ApiModelProperty("门店id列表-后端部门code转换")
    private List<Long> shopIdList;
}
