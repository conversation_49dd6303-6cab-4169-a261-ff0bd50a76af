package com.pinshang.qingyun.report.enums;

/**
 * 鲜家加盟分类报表分析、商品报表分析类型
 */
public enum XsjmReportTypeEnum {
    FIRST_CATE_SUMMARY(1, "大类汇总"),
    SECOND_CATE_SUMMARY(2, "中类汇总"),
    THIRD_CATE_SUMMARY(3, "小类汇总"),
    COMMODITY_SUMMARY(4, "商品汇总"),
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;



    XsjmReportTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
