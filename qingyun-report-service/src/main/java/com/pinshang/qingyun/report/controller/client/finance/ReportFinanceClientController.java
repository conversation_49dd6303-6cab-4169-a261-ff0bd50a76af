package com.pinshang.qingyun.report.controller.client.finance;


import com.pinshang.qingyun.report.dto.finance.PosSalesOrderIDTO;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderODTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordIDTO;
import com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordODTO;
import com.pinshang.qingyun.report.service.BreakageEnteringService;
import com.pinshang.qingyun.report.service.pos.PosReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 12:34
 * 抓取原始数据 供大店财务 内部往来单据使用
 */
@RestController
@RequestMapping("/reportFinance/client")
@Api(value = "ReportFinanceClientController", tags = "供财务 内部往来单据提供原始数据client接口-API")
public class ReportFinanceClientController {

    @Autowired
    private PosReportService reportService;

    @Autowired
    private BreakageEnteringService breakageRecordService;

    @ApiOperation(value = "【大店】POS销售订单/退单", notes = "【大店】POS销售订单/退单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/posSalesOrder/list")
    public List<PosSalesOrderODTO> selectShopPosSalesOrderList(@RequestBody PosSalesOrderIDTO idto) {
        return reportService.selectShopPosSalesOrderList(idto);
    }

    @ApiOperation(value = "【大店】门店报损记录", notes = "门店报损记录", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/breakageRecord/list")
    public List<ShopBreakageRecordODTO> selectBreakageRecordList(@RequestBody ShopBreakageRecordIDTO idto){
        return breakageRecordService.selectBreakageRecordList(idto);
    }

}
