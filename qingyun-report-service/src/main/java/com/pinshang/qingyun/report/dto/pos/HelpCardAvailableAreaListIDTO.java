package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HelpCardAvailableAreaListIDTO
 * <AUTHOR>
 * @Date 2023/2/22 16:55
 * @Description HelpCardAvailableAreaListIDTO
 * @Version 1.0
 */
@Data
public class HelpCardAvailableAreaListIDTO {
    @ApiModelProperty("区域id列表")
    private List<Long> idList;

    @ApiModelProperty("区域名称 模糊查询")
    private String areaName;

    @ApiModelProperty("状态, 0-停用, 1-启用")
    private Integer status;
}
