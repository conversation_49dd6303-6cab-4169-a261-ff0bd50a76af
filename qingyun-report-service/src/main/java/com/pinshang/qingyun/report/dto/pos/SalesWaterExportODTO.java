package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SalesWaterExportODTO {

    @ApiModelProperty(position = 1, value ="销售方式(赠送、销售、退货)")
    private String saleType;

    @ApiModelProperty(position = 2, value ="订单号")
    private String orderCode;

    @ApiModelProperty(position = 3,  value = "收银渠道:联网收银,本地收银")
    private String cashChannel;

    @ApiModelProperty(position = 4, value ="交易时间")
    private Date saleTime;

    @ApiModelProperty(position = 5, value ="货号")
    private String commodityCode;

    @ApiModelProperty(position = 6, value ="品名")
    private String commodityName;

    @ApiModelProperty(position = 7, value ="单位")
    private String unit;

    @ApiModelProperty(position = 8, value ="规格")
    private String commoditySpec;

    @ApiModelProperty(position = 9, value ="成交价格")
    private String transactionPrice;

    @ApiModelProperty(position = 10, value ="数量")
    private String quantity;

    @ApiModelProperty(position = 40, value ="份数")
    private Integer number;

    @ApiModelProperty(position = 11, value ="成交金额")
    private String transactionAmount;

    @ApiModelProperty(position = 12, value ="零售价")
    private String retailPrice;

    @ApiModelProperty(position = 13, value ="零售金额")
    private String retailAmount;

    @ApiModelProperty(position = 14, value ="成交/零售价")
    private String transactionRetailRate;

    @ApiModelProperty(position = 15, value ="后台特价")
    private String backgroudBargainPrice;

    @ApiModelProperty(position = 16, value ="会员价")
    private String memberPrice;

    @ApiModelProperty(position = 17, value ="手动议价后")
    private String handBargainEnd;

    @ApiModelProperty(position = 18, value ="手动折扣后")
    private String handDiscountEnd;

    @ApiModelProperty(position = 19, value ="后台促销分摊后")
    private String backgroundPromotionShareEnd;

    @ApiModelProperty(position = 20, value ="整单议价分摊后")
    private String orderBargainShareEnd;

    @ApiModelProperty(position = 21, value ="整单折扣分摊后")
    private String orderDiscountShareEnd;

    @ApiModelProperty("线下优惠券分摊后")
    private String discountCouponShareEnd;

    @ApiModelProperty("线下优惠券序列号")
    private String couponOfflineSn;

    @ApiModelProperty(position = 22, value ="条形码")
    private String barCode;

    @ApiModelProperty(position = 23, value ="会员卡号")
    private String memberCardNo;

    @ApiModelProperty(position = 24, value ="会员姓名")
    private String memberName;

    @ApiModelProperty(position = 25, value ="大类")
    private String commodityFirstKind;

    @ApiModelProperty(position = 26, value ="中类")
    private String commoditySecondKind;

    @ApiModelProperty(position = 27, value ="小类")
    private String commodityThirdKind;

    @ApiModelProperty(position = 28, value ="品牌名称")
    private String brandName;

    @ApiModelProperty(position = 29, value ="收银员编号")
    private String employeeNumber;

    @ApiModelProperty(position = 30, value ="收银员姓名")
    private String createName;

    @ApiModelProperty(position = 31, value ="授权人编号")
    private String opUserCode;

    @ApiModelProperty(position = 32, value ="授权人名称")
    private String opUserName;

    @ApiModelProperty(position = 33, value ="折扣/特价码")
    private String couponCode;

    @ApiModelProperty(position = 34, value ="退货原因")
    private String returnReason;

    @ApiModelProperty(position = 35, value ="供应商名称")
    private String supplierName;

    @ApiModelProperty(position = 36, value ="原单号")
    private String originOrderCode;

    @ApiModelProperty(position = 37, value ="促销编号")
    private String promotionCode;

    @ApiModelProperty(position = 38, value ="优惠券编码")
    private String discountCouponCode;

    @ApiModelProperty(position = 39, value ="门店名称")
    private String shopName;

    @ApiModelProperty(position = 40, value ="pos类型")
    private String posTypeName;

    @ApiModelProperty(position = 41, value ="POS机号")
    private String macCode;



    @ApiModelProperty(position = 42, value ="档口名称")
    private String stallName;
}
