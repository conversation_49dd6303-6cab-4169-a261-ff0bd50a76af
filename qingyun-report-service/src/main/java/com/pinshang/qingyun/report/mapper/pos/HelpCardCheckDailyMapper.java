package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryIDTO;
import com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryODTO;
import com.pinshang.qingyun.report.model.pos.HelpCardCheckDaily;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName HelpCardCheckDailyMapper
 * <AUTHOR>
 * @Date 2023/2/28 15:29
 * @Description HelpCardCheckDailyMapper
 * @Version 1.0
 */
@Repository
public interface HelpCardCheckDailyMapper extends MyMapper<HelpCardCheckDaily> {
    List<HelpCardCheckSummaryODTO> helpCardCheckSummary(HelpCardCheckSummaryIDTO idto);

    HelpCardCheckSummaryODTO helpCardCheckSummarySum(HelpCardCheckSummaryIDTO idto);
}
