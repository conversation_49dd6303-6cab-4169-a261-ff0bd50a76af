package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DaySaleAimPageIDTO extends Pagination {

    @ApiModelProperty("门店")
    private  Long shopId;

    @ApiModelProperty("开始时间yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("结束时间yyyy-MM-dd")
    private String endDate;

    private List<Long> shopIdList;


}
