package com.pinshang.qingyun.report.controller.xd;

import com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO;
import com.pinshang.qingyun.report.service.xd.XdShopCommodityTaxService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/xdCommodityTax")
public class XdCommodityTaxController {
    public static final String CREATE_XD_SHOP_COMMODITY_TAX_LOCK = "xd_shop_commodity_tax_lock";
    public static final Long SHOP_COMMODITY_TAX_LOCK_EXPIRE_TIME = 10L;

    @Autowired
    private XdShopCommodityTaxService xdShopCommodityTaxService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 鲜道商品日汇总
     * @param timeStamp
     */
    @PostMapping("/xdShopCommodityTax")
    public void shopCommodityTax(@RequestParam(value = "timeStamp") String timeStamp) {
        String cacheKey = XdCommodityTaxController.CREATE_XD_SHOP_COMMODITY_TAX_LOCK;
        RLock lock = redissonClient.getLock(cacheKey);

        try {
            log.info("开始----------------门店报表--鲜道--商品总表+锁-------" + new Date());
            lock.lock(XdCommodityTaxController.SHOP_COMMODITY_TAX_LOCK_EXPIRE_TIME, TimeUnit.SECONDS);

            //汇总
            xdShopCommodityTaxService.xdCommodityTax(timeStamp);

        } finally {
            log.info("结束----------------门店报表--鲜道--商品总表释放锁-------" + new Date());
            lock.unlock();
        }

    }

    /**
     *
     *查询鲜道商品日汇总
     * @return
     */
    @GetMapping("/findXdCommodityTaxList")
    public List<XdCommodityTaxODTO> findXdCommodityTaxList(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime, @RequestParam(value = "shopId") Long shopId, @RequestParam(value = "commodityId") Long commodityId) {
        return xdShopCommodityTaxService.findXdCommodityTaxList(beginTime, endTime, shopId, commodityId);
    }


}
