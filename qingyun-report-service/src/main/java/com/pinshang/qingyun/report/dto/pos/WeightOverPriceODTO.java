package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.box.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WeightOverPriceODTO {

    @ApiModelProperty("门店id")
    private Long  shopId;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("交易时间")
    private Date saleTime;

    @ApiModelProperty("成交金额")
    private BigDecimal transactionAmount;
    @ApiModelProperty("后台零售价金额")
    private BigDecimal  retailAmount;
    @ApiModelProperty("超后台零售价金额")
    private BigDecimal  overAmount;


    public String getSaleTimeStr(){
        return DateUtil.getDateFormate(saleTime,"yyyy-MM-dd");
    }
}
