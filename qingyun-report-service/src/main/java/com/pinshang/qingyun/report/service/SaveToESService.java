package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.constant.RedissKeyConst;
import com.pinshang.qingyun.report.search.dto.EsDaySaleSummaryODTO;
import com.pinshang.qingyun.report.search.dto.EsDayThirdSaleSummaryODTO;
import com.pinshang.qingyun.report.search.dto.PosToEsODTO;
import com.pinshang.qingyun.report.search.service.PosReportSearchClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2022/3/23
 */
@Service
@Slf4j
public class SaveToESService {

    @Autowired
    private PosReportSearchClient posReportSearchClient;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    /**
     * pos非当日订单直接新增到ES
     * @param daySaleSummaryESList
     * @param dDayThirdSaleSummaryESList
     * @return
     */
    @Async
    public Boolean posNotCurrentDaySaveES(List<EsDaySaleSummaryODTO> daySaleSummaryESList, List<EsDayThirdSaleSummaryODTO> dDayThirdSaleSummaryESList){
        String saleTime = daySaleSummaryESList.get(0).getSaleTime();
        try{
            // 汇总数据进ES
            PosToEsODTO posToEsODTO = new PosToEsODTO();
            posToEsODTO.setDaySaleSummaryList(daySaleSummaryESList);
            posToEsODTO.setDayThirdSaleSummaryList(dDayThirdSaleSummaryESList);
            posReportSearchClient.posNotCurrentDaySaveES(posToEsODTO);
        }catch (Exception e){
            log.error("pos非当日数据保存进ES异常:{}", e);
            toEsErrorSendMessage(RedissKeyConst.POS_NOT_CURRENT + saleTime,"pos非当日数据保存进ES异常,日期" + saleTime);
        }
        return Boolean.TRUE;
    }


    /**
     * 保存近es 异常发送短信(同种类型，一天只发送一次短信)
     */
    public void toEsErrorSendMessage(String redissonKey,String content){
        RBucket<String> bucket = redissonClient.getBucket(redissonKey);
        String data = bucket.get();
        if(StringUtils.isBlank(data)){
            StringBuffer sb = new StringBuffer();
            sb.append(content);

            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
            bucket.set(redissonKey, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);
        }
    }
    /**
     * B订单实发补偿进ES
     * @param orderCodeList
     * @return
     */
    @Async
    public Boolean bOrderFixToES(List<String> orderCodeList){
        // B订单实发补偿进ES
        posReportSearchClient.bOrderFixToES(orderCodeList);
        return Boolean.TRUE;
    }
}
