package com.pinshang.qingyun.report.dto.xsOrder;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class XsOrderReportODTO {

    private Long id;
    // 下单数量
    private Integer count;
    // 订单总金额
    private BigDecimal totalAmount;
    // 平均价
    private BigDecimal averageAmount;
    // 下单用户数量
    private Integer userOrderCount;

    // 出库数量
    private Integer stockOutCount;
    // 出库总金额
    private BigDecimal stockOutTotalAmount;

    // 用户数量
    private Integer userCount;

    // 到这天为止总的注册数量
    private Integer userTotal;

    // 下单时间
    private String createTime;
}
