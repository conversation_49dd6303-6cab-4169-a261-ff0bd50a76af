package com.pinshang.qingyun.report.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportIDTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportODTO;
import com.pinshang.qingyun.report.mapper.ReportMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/3/4
 */
@Service
public class MdCheckInfoService {

    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private MdCheckService mdCheckService;


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Boolean mdCheckInfoMonthReport(String beginTime, String endTime, String yesterday){
        // 月考核信息汇总
        reportMapper.deleteMdCheckInfo(beginTime,endTime);

        MdCheckReportIDTO idto = new MdCheckReportIDTO();
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        idto.setBeginDate(beginTime);
        idto.setEndDate(endTime);
        PageInfo<MdCheckReportODTO> pageInfo = mdCheckService.getMdCheckPlanPage(idto);
        if(CollectionUtils.isNotEmpty(pageInfo.getList())){
            List<MdCheckReportODTO> list = pageInfo.getList();
            for(MdCheckReportODTO odto : list){
                // 考核方案里面的总天数
                Long planDay = com.pinshang.qingyun.report.util.DateUtils.getDiffDay(odto.getPlanBeginDate(),odto.getPlanEndDate());
                // 考核日期范围起始日期至T-1（即昨天）的总天数
                List<String> dateList = com.pinshang.qingyun.report.util.DateUtils.getBetweenDate(odto.getPlanBeginDate(),yesterday);
                if(CollectionUtils.isNotEmpty(dateList)){
                    odto.setDurationPlanAim(odto.getTotalPlanAim().multiply(new BigDecimal(dateList.size())).divide(new BigDecimal(planDay + ""),2,BigDecimal.ROUND_HALF_UP));
                    odto.setCompleteAimPercent(odto.getCompleteAim().multiply(new BigDecimal(100)).divide(odto.getDurationPlanAim(),0,BigDecimal.ROUND_HALF_UP) + "%");
                }else {
                    odto.setDurationPlanAim(null);
                    odto.setCompleteAim(null);
                    odto.setCompleteAimPercent("");
                }
            }
            reportMapper.batchInsertMdCheckInfo(list);
        }
        return Boolean.TRUE;
    }
}
