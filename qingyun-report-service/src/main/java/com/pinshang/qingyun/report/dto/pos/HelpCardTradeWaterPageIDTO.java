package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName HelpCardTradeWaterPageIDTO
 * <AUTHOR>
 * @Date 2023/3/13 11:09
 * @Description HelpCardTradeWaterPageIDTO
 * @Version 1.0
 */
@Data
public class HelpCardTradeWaterPageIDTO extends Pagination {
    @ApiModelProperty("交易日期-开始")
    private String beginTime;

    @ApiModelProperty("交易日期-结束")
    private String endTime;

    @ApiModelProperty("帮困卡号")
    private String cardNo;

    @ApiModelProperty("设备号")
    private String payCode;
}
