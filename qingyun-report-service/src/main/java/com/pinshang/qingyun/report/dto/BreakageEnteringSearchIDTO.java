package com.pinshang.qingyun.report.dto;

        import com.pinshang.qingyun.base.page.Pagination;
        import io.swagger.annotations.ApiModelProperty;
        import lombok.Data;

        import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:44
 */
@Data
public class BreakageEnteringSearchIDTO extends Pagination {

    @ApiModelProperty(position = 1, value = "报损日期开始时间yyyy-MM-dd")
    private String beginTime;
    @ApiModelProperty(position = 2, value = "报损日期结束时间yyyy-MM-dd")
    private String endTime;
    @ApiModelProperty(position = 3, value = "门店id")
    private Long shopId;
    @ApiModelProperty(position = 4, value = "大类id")
    private Long category1;
    @ApiModelProperty(position = 5, value = "中类id")
    private Long category2;
    @ApiModelProperty(position = 6, value = "小类id")
    private Long category3;
    @ApiModelProperty(position = 7, value = "商品名称")
    private String commodityName;
    @ApiModelProperty(position = 8, value = "报损原因id")
    private Long reasonId;
    @ApiModelProperty(position = 8, value = "渠道: 全部（空）/ 2-餐饮POS / 1-掌上门店PDA")
    private Integer breakageChannel;
    @ApiModelProperty(position = 8, value = "退货单号")
    private String referCode;
    @ApiModelProperty(position = 9, value = "代销商")
    private Long consignmentId;
    @ApiModelProperty(position = 10, value = "档口ID")
    private Long stallId;

    private List<Long> stallIdList;
}
