package com.pinshang.qingyun.report.etl;

import com.pinshang.qingyun.base.druid.DruidProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.core.exception.KettleException;
import org.pentaho.di.trans.Trans;
import org.pentaho.di.trans.TransMeta;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class EtlExecute {

    @Autowired
    private EtlMethod etlMethod;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${etl.filepath}")
    private String etlFilePath;

    private static final String ETL_TIME_STAMP_FLAG = "etl_time_stamp_flag:";

    public void execute(String timeStamp,final String fileName){
        RAtomicLong ral = redissonClient.getAtomicLong(ETL_TIME_STAMP_FLAG + timeStamp);
        long al = ral.incrementAndGet();
        if(al > 1){
            return;
        }
        ral.expire(1, TimeUnit.MINUTES);

        File file = new File(etlFilePath);
        if(!file.isDirectory()){
            throw new RuntimeException("etl 文件目录配置不正确");
        }
        File[] ktrs;
        if(StringUtils.isNotBlank(fileName)){
            ktrs = file.listFiles((f,n) -> n.endsWith(fileName + ".ktr"));
        }else{
            ktrs = file.listFiles((f,n) -> n.endsWith("ktr"));
        }
        etlMethod.runTransformation(ktrs,timeStamp);
    }
}
