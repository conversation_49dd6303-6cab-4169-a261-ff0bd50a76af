package com.pinshang.qingyun.report.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ClearingChargesItemExportODTO {

    //门店名称
    private String shopName;
    //门店编码
    private String shopCode;
    //现金
    private BigDecimal cash;
    //银行卡手续费
    private BigDecimal bankCardPoundage;
    //银行
    private BigDecimal bankCard;
    //银联聚合手续费
    private BigDecimal unionPoundage;
    //银联聚合
    private BigDecimal unionPay;
    //支付宝记账手续费
    private BigDecimal tallyAliPoundage;
    //支付宝记账
    private BigDecimal tallyAli;
    //微信记账手续费
    private BigDecimal tallyWechatPoundage;
    //微信记账
    private BigDecimal tallyWechat;
    //ok卡手续费
    private BigDecimal okCardPoundage;
    //ok卡收
    private BigDecimal okCard;
    //总手续费
    private BigDecimal totalPoundage;
    //总收
    private BigDecimal totalAmount;

}
