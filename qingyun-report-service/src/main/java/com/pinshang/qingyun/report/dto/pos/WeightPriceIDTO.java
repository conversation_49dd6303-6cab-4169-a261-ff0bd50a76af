package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class WeightPriceIDTO extends Pagination {

    @ApiModelProperty("销售日期yyyy-MM-dd")
    private String beginTime;

    @ApiModelProperty("销售日期yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty("门店名称")
    private Long shopId;

    private Long commodityId;
    @ApiModelProperty("商品编码/名称")
    private String commodityKey;
    @ApiModelProperty("包装类型")
    private Long commodityPackageId;

    private List<Long> shopIdList;

    @ApiModelProperty("成交价 / 后台零售价  > ? % ")
    private Double overPercent;


    @ApiModelProperty("超后台零售金额  > ")
    private Double overAmount;

}
