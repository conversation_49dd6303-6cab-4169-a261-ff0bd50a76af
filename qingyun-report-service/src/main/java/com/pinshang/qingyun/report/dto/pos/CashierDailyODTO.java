package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CashierDailyODTO {
    @ApiModelProperty(position = 1, value = "门店编码")
    private String shopCode;

    @ApiModelProperty(position = 2, value = "门店名称")
    private String shopName;

    @ApiModelProperty(position = 3, value = "收银员编号")
    private String saleNumber;

    @ApiModelProperty(position = 4, value = "收银员姓名")
    private String saleName;

    @ApiModelProperty(position = 5, value = "现金")
    private String cash;

    @ApiModelProperty(position = 6, value = "银行卡")
    private String bankCard;

    @ApiModelProperty(position = 7, value = "抹零金额")
    private String sumOfMoney;

    @ApiModelProperty(position = 8, value = "支付宝")
    private String alipay;

    @ApiModelProperty(position = 9, value = "微信")
    private String weChat;

    @ApiModelProperty(position = 10, value = "支付宝记账")
    private String alipayBookkeeping;

    @ApiModelProperty(position = 11, value = "微信记账")
    private String weChatBookkeeping;

    @ApiModelProperty(position = 12, value = "合计")
    private String total;

    @ApiModelProperty(position = 13, value = "收银日期")
    private String createTime;

    @ApiModelProperty(position = 14, value = "销售方式")
    private String saleType;

    @ApiModelProperty(position = 15, value = "收款方式")
    private String payType;

    @ApiModelProperty(position = 16, value = "金额")
    private String payAmount;

    @ApiModelProperty(position = 17, value = "销售额")
    private String salesAmount;

    @ApiModelProperty(position = 18, value = "退款额")
    private String refundAmount;

    @ApiModelProperty(position = 19, value = "pos类型")
    private String posTypeName;

    @ApiModelProperty(position = 20, value = "pos机号")
    private String macCode;

    @ApiModelProperty(position = 21, value = "银联支付宝")
    private String unionAlipay;

    @ApiModelProperty(position = 22, value = "银联微信")
    private String unionWeChat;

    @ApiModelProperty(position = 23, value = "银联刷卡")
    private String unionBank;

    @ApiModelProperty(position = 24, value = "OK卡")
    private String okCard;

    @ApiModelProperty(position = 25, value = "云闪付")
    private String unionPayQuickPass;

    private String ZF022;
    private String ZF023;
    private String ZF024;
    private String ZF025;
    private String ZF026;
    private String ZF027;
    private String ZF028;
    private String ZF029;
    private String ZF030;
    private String ZF031;
    private String ZF032;
    private String ZF033;
    private String ZF034;
    private String ZF035;
    private String ZF036;
    private String ZF037;
    private String ZF038;
    private String ZF039;
    private String ZF040;
    private String ZF041;
    private String ZF042;
    private String ZF043;
    private String ZF044;
    private String ZF045;
    private String ZF046;
    private String ZF047;
    private String ZF048;
    private String ZF049;
    private String ZF050;
    private String ZF051;
    private String ZF052;
    private String ZF053;
    private String ZF054;
    private String ZF055;
    private String ZF056;
    private String ZF057;
    private String ZF058;
    private String ZF059;
    private String ZF060;
    private String ZF061;
    private String ZF062;
    private String ZF063;
    private String ZF064;
    private String ZF065;
    private String ZF066;
    private String ZF067;
    private String ZF068;
    private String ZF069;
    private String ZF070;
    private String ZF071;
    private String ZF072;
    private String ZF073;
    private String ZF074;
    private String ZF075;
    private String ZF076;
    private String ZF077;
    private String ZF078;
    private String ZF079;
    private String ZF080;
}
