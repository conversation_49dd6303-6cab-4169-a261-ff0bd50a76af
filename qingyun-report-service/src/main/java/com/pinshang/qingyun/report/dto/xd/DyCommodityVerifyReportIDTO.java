package com.pinshang.qingyun.report.dto.xd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/05/31
 * @Version 1.0
 */
@Data
public class DyCommodityVerifyReportIDTO {
    @ApiModelProperty("t_xd_dy_order_item表id")
    private Long dyItemId;

    @ApiModelProperty("抖音券码")
    private String dyCouponCode;

    @ApiModelProperty("优惠券分类 1=满减券,2=赠品券")
    private Integer couponClass;

    @ApiModelProperty("状态:1-待支付 2-已支付 0-已退款")
    private Integer status;

    @ApiModelProperty("核销时间")
    private Date verifyTime;

    @ApiModelProperty("销来源8.云超小程序 9.云超APP 91.PDA")
    private Integer sourceType;

    @ApiModelProperty("xd/xs订单code")
    private String orderCode;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("核销门店")
    private Long shopId;

    @ApiModelProperty("关联退货单号")
    private String returnCode;

    @ApiModelProperty("抖音侧订单id")
    private String dyOrderId;

}
