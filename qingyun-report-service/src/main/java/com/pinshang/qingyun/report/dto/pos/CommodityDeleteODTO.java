package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.pos.PosTypeEnum;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CommodityDeleteODTO {

	@ExcelIgnore
	private Long refId;

	@ApiModelProperty(position = 1, value ="门店名称")
	@ExcelProperty(index = 1, value = "门店名称")
	private String shopName;

	@ApiModelProperty(position = 1, value ="客户编码")
	@ExcelProperty(index = 2, value = "客户编码")
	private String storeCode;

	@ApiModelProperty(position = 2, value ="POS机号")
	@ExcelProperty(index = 4, value = "POS机号")
	private String macCode;

	@ApiModelProperty(position = 3, value ="操作时间")
	@ExcelProperty(index = 6, value = "操作时间")
	private String operateTime;

	@ApiModelProperty(position = 4, value ="货号")
	@ExcelProperty(index = 7, value = "货号")
	private String commodityCode;

	@ApiModelProperty(position = 5, value ="商品名称")
	@ExcelProperty(index = 8, value = "品名")
	private String commodityName;

	@ApiModelProperty(position = 6, value ="商品规格")
	@ExcelProperty(index = 9, value = "规格")
	private String commoditySpec;

	@ApiModelProperty(position = 7, value ="商品单位")
	@ExcelProperty(index = 10, value = "单位")
	private String commodityUnit;

	@ApiModelProperty(position = 8, value ="数量")
	@ExcelProperty(index = 11, value = "数量")
	private BigDecimal quantity;

	@ApiModelProperty(position = 9, value ="零售价")
	@ExcelProperty(index = 12, value = "零售价")
	private BigDecimal commodityPrice;

	@ApiModelProperty(position = 10, value ="零售价金额")
	@ExcelProperty(index = 13, value = "零售价金额")
	private BigDecimal commodityAmount;

	@ApiModelProperty(position = 11, value ="收银员编码")
	@ExcelProperty(index = 14, value = "收银员编码")
	private String casherCode;

	@ApiModelProperty(position = 12, value ="收银员名称")
	@ExcelProperty(index = 15, value = "收银员名称")
	private String casherName;

	@ApiModelProperty(position = 13, value ="授权人编码")
	@ExcelIgnore
	private String authorizedCode;

	@ApiModelProperty(position = 14, value ="授权人名称")
	@ExcelIgnore
	private String authorizedName;

	@ApiModelProperty(position = 15, value ="净销售额")
	@ExcelIgnore
	private BigDecimal netSales;

	@ApiModelProperty(position = 16, value ="删除和净销额比")
	@ExcelIgnore
	private BigDecimal deleteSalesRatio;

	@ApiModelProperty(position = 17, value ="店铺id")
	@ExcelIgnore
	private Long shopId;

	@ApiModelProperty(position = 18, value ="收银员id")
	@ExcelIgnore
	private Long casherId;

	@ApiModelProperty(position = 19, value ="店铺编号")
	@ExcelProperty(index = 0, value = "门店编码")
	private String shopCode;

	@ApiModelProperty(position = 20, value ="订单编号")
	@ExcelProperty(index = 5, value = "订单号")
	private Long orderCode;

	@ApiModelProperty(position = 21, value ="授权人账号")
	@ExcelProperty(index = 16, value = "授权人账号")
	private String authorizerCode;

	@ApiModelProperty(position = 22, value ="授权人姓名")
	@ExcelProperty(index = 17, value = "授权人名称")
	private String authorizerName;

	@ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
	@ExcelIgnore
	private Integer posType;

	@ApiModelProperty("pos机类型名称")
	@ExcelProperty(index = 3, value = "POS类型")
	private String posTypeName;


	@ApiModelProperty("增加次数")
	@ExcelIgnore
	private Long addNum;

	@ApiModelProperty("增加总金额")
	@ExcelIgnore
	private BigDecimal addAmount;

	@ApiModelProperty("减少次数")
	@ExcelIgnore
	private Long reduceNum;

	@ApiModelProperty("减少总金额")
	@ExcelIgnore
	private BigDecimal reduceAmount;

	@ApiModelProperty("增加的比率")
	@ExcelIgnore
	private BigDecimal addRatio;

	@ApiModelProperty("减少的比率")
	@ExcelIgnore
	private BigDecimal reduceRatio;

	@ExcelIgnore
	private Long posMacId;

	public String getPosTypeName() {
		if (null != this.posType) {
			return PosTypeEnum.get(this.posType).getDesc();
		} else {
			return null;
		}
	}
}
