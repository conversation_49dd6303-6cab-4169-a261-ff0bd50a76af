package com.pinshang.qingyun.report.util;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.web.servlet.ModelAndView;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 报表工具.
 * <p>
 * detailed description
 *
 * <AUTHOR> Zhang
 * @version 1.0
 * @since 2018/7/20
 */
@Slf4j
public class ReportUtil {


    public static <T> void  buildData(List<T> list,Map<String, List<String>> data){
        buildData(list,data,3,DateUtil.DEFAULT_DATE_FORMAT);
    }

    /**
     * 构建报表数据
     *
     * 注意：T 中的属性的声明属性必须和要导出的报表的顺序完全一致，字段不能多也不能少
     * @param list 要导出的数据列表
     * @param <T> 报表导出的ODTO类型
     * @return
     */
    public static <T> void  buildData(List<T> list,Map<String, List<String>> data,int rowNum,String dateFormate){
        if (SpringUtil.isEmpty(list)) {
            return;
        }
        int row = rowNum;
        for (T object : list) {
            if(object == null){
                continue;
            }
            List<String> dataList = new ArrayList<>();
            Field[] fields = object.getClass().getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                field.setAccessible(true);
                Object value = null;
                try{
                    PropertyDescriptor pd = new PropertyDescriptor(field.getName(),object.getClass());
                    Method readMethod = pd.getReadMethod();
                    if(readMethod != null){
                        value = readMethod.invoke(object);
                    }
                } catch (IllegalAccessException | IntrospectionException | InvocationTargetException e) {
                    try{
                        value = field.get(object);
                    }catch (IllegalAccessException e1) {
                        log.error("buildRows error, filed:{}", field);
                    }
                }
                if (value instanceof Date) {
                    value = DateUtil.getDateFormate((Date)value, dateFormate);
                }
                String valueStr = value != null ? value.toString() : "";
                dataList.add(valueStr);
            }
            data.put("key_"+ (row++), dataList);
        }
    }

    /**
     * 构建ExcelView
     * @param sheetTitle sheet类型
     * @param data 报表数据
     * @return
     */
    public static ModelAndView buildModelAndView(ExcelSheetTitleEnum sheetTitle, Map<String, List<String>> data, String tableHeader){
        String fileName = sheetTitle.getName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName +"_"+ sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        map.put("tableHeader", tableHeader);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    /**
     * 构建ExcelView
     * @param sheetTitle sheet类型
     * @param data 报表数据
     * @return
     */
    public static ModelAndView buildModelAndView(ExcelSheetTitleEnum sheetTitle, Map<String, List<String>> data){
        String fileName = sheetTitle.getName();


        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName +"_"+ sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    public static List<List<String>> getHeaderList(Boolean isWithShop, String header) {
        List<List<String>> list = new ArrayList<List<String>>();
        if(isWithShop){
            List<String> head0 = new ArrayList<String>(Arrays.asList(header,"部门"));
            List<String> head1 = new ArrayList<String>(Arrays.asList(header,"客户编码"));
            List<String> head2 = new ArrayList<String>(Arrays.asList(header,"门店"));
            list.add(head0);
            list.add(head1);
            list.add(head2);
        }
        List<String> head0 = new ArrayList<String>(Arrays.asList(header,"工厂"));
        List<String> head1 = new ArrayList<String>(Arrays.asList(header,"生产组"));
        List<String> head2 = new ArrayList<String>(Arrays.asList(header,"商品编码"));
        List<String> head3 = new ArrayList<String>(Arrays.asList(header,"商品"));
        List<String> head4 = new ArrayList<String>(Arrays.asList(header,"规格"));
        List<String> head5 = new ArrayList<String>(Arrays.asList(header,"计量单位"));
        List<String> head6 = new ArrayList<String>(Arrays.asList(header,"商品数量"));
        List<String> head7 = new ArrayList<String>(Arrays.asList(header,"商品金额"));
        List<String> head8 = new ArrayList<String>(Arrays.asList(header,"实收金额"));
        List<String> head9 = new ArrayList<String>(Arrays.asList(header,"实发数量"));
        List<String> head10 = new ArrayList<String>(Arrays.asList(header,"C端实发金额"));
        List<String> head11 = new ArrayList<String>(Arrays.asList(header,"销售成本"));
        List<String> head12 = new ArrayList<String>(Arrays.asList(header,"条码"));
        List<String> head13 = new ArrayList<String>(Arrays.asList(header,"税率"));
        List<String> head14 = new ArrayList<String>(Arrays.asList(header,"大类"));
        List<String> head15 = new ArrayList<String>(Arrays.asList(header,"中类"));
        List<String> head16 = new ArrayList<String>(Arrays.asList(header,"小类"));
        List<String> head17 = new ArrayList<>(Arrays.asList(header,"车间"));

        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        list.add(head7);
        list.add(head8);
        list.add(head9);
        list.add(head10);
        list.add(head11);
        list.add(head12);
        list.add(head13);
        list.add(head14);
        list.add(head15);
        list.add(head16);
        list.add(head17);
        return list;
    }

    public static HorizontalCellStyleStrategy getStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        // 背景色, 设置为白色，也是默认颜色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(headWriteFont);

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        // contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);

        // 背景绿色
        //contentWriteCellStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        //设置 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置 水平居中
        // contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //设置边框样式
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }


    public static List<List<String>> getOrderSalesAnalysisHeaderList(String row1, String row2) {
        List<List<String>> list = new ArrayList<List<String>>();

        List<String> head0 = new ArrayList<String>(Arrays.asList(row1,row2,"门店"));
        List<String> head1 = new ArrayList<String>(Arrays.asList(row1,row2,"分类"));
        List<String> head2 = new ArrayList<String>(Arrays.asList(row1,row2,"订货金额"));
        List<String> head3 = new ArrayList<String>(Arrays.asList(row1,row2,"订货占比"));
        List<String> head4 = new ArrayList<String>(Arrays.asList(row1,row2,"销售金额"));
        List<String> head5 = new ArrayList<String>(Arrays.asList(row1,row2,"销售占比"));
        List<String> head6 = new ArrayList<String>(Arrays.asList(row1,row2,"退货金额"));
        List<String> head7 = new ArrayList<String>(Arrays.asList(row1,row2,"销售与订货差额"));

        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        list.add(head7);
        return list;
    }
}
