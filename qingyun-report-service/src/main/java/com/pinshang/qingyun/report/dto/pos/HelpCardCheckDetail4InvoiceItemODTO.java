package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName HelpCardheckDetail4InvoiceItem
 * <AUTHOR>
 * @Date 2023/3/9 14:55
 * @Description HelpCardheckDetail4InvoiceItem
 * @Version 1.0
 */
@Data
public class HelpCardCheckDetail4InvoiceItemODTO {
    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    private Long companyId;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("数量")
    private BigDecimal quantity;
    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;
}
