package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ConsignmentCommoditySalesReportNoShopODTO {

    @ExcelIgnore
    private Long storeId;

    @ExcelIgnore
    @ApiModelProperty("门店id")
    private Long shopId;

    @ExcelIgnore
    @ApiModelProperty("商品id")
    private Long  commodityId;

    @ApiModelProperty("客户编码")
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode , keyName = "storeId")
    private String storeCode;

    @ApiModelProperty("门店编码")
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopCode,keyName = "shopId")
    private String shopCode;

    @ApiModelProperty("门店名称")
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ApiModelProperty("条形码")
    @ExcelProperty(value = "条形码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.barCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品编码")
    @ExcelProperty(value = "商品编码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty(value = "商品名称")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityName, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("规格")
    @ExcelProperty(value = "规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId", fieldName = RenderFieldHelper.Commodity.commoditySpec)
    private String commoditySpec;

    @ApiModelProperty("计量单位名称")
    @ExcelProperty(value = "计量单位名称")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId", fieldName = RenderFieldHelper.Commodity.commodityUnit)
    private String commodityUnitName;

    @ApiModelProperty("销售数量")
    @ExcelProperty(value = "销售数量")
    private Integer salesQuantity;

    @ApiModelProperty("销售金额")
    @ExcelProperty(value = "销售金额")
    private  BigDecimal salesAmount;

    @ApiModelProperty("大类")
    @ExcelProperty("大类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
    private String commodityFirstCateName;

    @ApiModelProperty("中类")
    @ExcelProperty("中类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName,keyName = "commodityId")
    private String commoditySecondCateName;

    @ApiModelProperty("小类")
    @ExcelProperty("小类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
    private String commodityThirdCateName;
}
