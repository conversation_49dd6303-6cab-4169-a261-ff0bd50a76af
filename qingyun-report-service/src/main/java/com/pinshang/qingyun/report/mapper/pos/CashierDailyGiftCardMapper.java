package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.pos.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CashierDailyGiftCardMapper {

    /**
     * 线下营业款
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);

    /**
     * 线下营业款 总计
     * @param cashierDailyIDTO
     * @return
     */
    CashierDailySUMODTO selectSUMCashierDailyList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);


    /**
     * 线下售卡
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyGiftCardList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);
    /**
     *线下售卡总计
     * @param cashierDailyIDTO
     * @return
     */
    CashierDailySUMODTO selectSUMCashierDailyGiftCardList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);


    /**
     * 线下售卡，线下营业款
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyAndGiftCardList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);
    /**
     *线下售卡，线下营业款 总计
     * @param cashierDailyIDTO
     * @return
     */
    CashierDailySUMODTO selectSUMCashierDailyAndGiftCardList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);


    /**
     * 收银员对账
     * @param cashierReconciledIDTO
     * @return
     */
    List<CashierReconciledODTO> selectCashierReconciledList(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    CashierReconciledSUMODTO selectSUMCashierReconciledList(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    List<CashierWaterCountODTO> selectCashierWaterCount(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);

    /**
     * 收银员对账--日对账
     * @param cashierReconciledIDTO
     * @return
     */
    List<CashierReconciledODTO> selectDiurnalReconciliationList(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    CashierReconciledSUMODTO selectSUMDiurnalReconciliationList(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);

}
