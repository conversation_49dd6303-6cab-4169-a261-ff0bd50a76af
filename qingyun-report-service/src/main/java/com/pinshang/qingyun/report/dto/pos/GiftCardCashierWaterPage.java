package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GiftCardCashierWaterPage {

    @ApiModelProperty("门店类型")
    @ExcelIgnore
    private Integer shopType;

    @ExcelProperty("门店类型")
    private String shopTypeName;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店")
    private String shopName;

    @ApiModelProperty("订单号")
    @ExcelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("销售渠道 1POS 2团购 3APP")
    @ExcelIgnore
    private Integer cardSourceType;

    @ExcelProperty("销售渠道")
    private String cardSourceTypeName;

    @ApiModelProperty("流水时间")
    @ExcelProperty("收银时间")
    private Date saleTime;

    @ApiModelProperty("销售金额")
    @ExcelProperty("收款金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("支付名称")
    @ExcelProperty("收款方式")
    private String payName;

    @ApiModelProperty("员工姓名")
    @ExcelProperty("收银员")
    private String employeeName;

    @ApiModelProperty("POS机号")
    @ExcelProperty("POS机")
    private String macCode;

    @ApiModelProperty("部门")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    @ExcelIgnore
    private String orgName;

    @ExcelIgnore
    private Long shopId;

    public String getShopTypeName() {
        if (null != this.shopType) {
            return null != ShopTypeEnums.get(this.shopType) ? ShopTypeEnums.get(this.shopType).getName() : null;
        } else {
            return null;
        }
    }

    public String getCardSourceTypeName() {
        if (null != this.cardSourceType) {
            return OrderSourceTypeEnum.getMsg(this.cardSourceType);
        } else {
            return null;
        }

    }








}
