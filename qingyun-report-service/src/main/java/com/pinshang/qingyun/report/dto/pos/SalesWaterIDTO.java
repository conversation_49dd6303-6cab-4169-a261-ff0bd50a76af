package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
* 销售流水输入参数
*
* <AUTHOR>
* @since 2018-05-31
*/
@Data
public class SalesWaterIDTO extends Pagination {

	@ApiModelProperty(position = 1, value ="销售日期yyyy-MM-dd HH:mm:ss")
	private String beginTime;

	@ApiModelProperty(position = 2, value ="销售日期yyyy-MM-dd HH:mm:ss")
	private String endTime;

	@ApiModelProperty(position = 3, value ="收银员编号")
	private String employeeNumber;

	@ApiModelProperty(position = 4, value ="POS机号")
	private String macCode;

	@ApiModelProperty(position = 5, value ="门店名称")
	private Long shopId;

	@ApiModelProperty(position = 6, value ="商品名称")
	private String commodityName;

	@ApiModelProperty(position = 7, value ="订单号")
	private String orderCode;

	@ApiModelProperty(position = 8, value = "一级分类id")
	private Long category1;
	@ApiModelProperty(position = 9, value = "二级分类id")
	private Long category2;
	@ApiModelProperty(position = 10, value = "三级分类id")
	private Long category3;

	@ApiModelProperty(position = 11, value ="销售方式(赠送、销售、退货)")
	private String saleType;

	@ApiModelProperty(position = 12, value ="条形码")
	private String barCode;

	@ApiModelProperty(position = 13, value ="折扣类型")
	private String discountType;

	@ApiModelProperty(position = 14, value ="会员卡号")
	private String memberCardNo;

	@ApiModelProperty(position = 15, required = false, value = "小票金额(元)")
	private String beginAmount;

	@ApiModelProperty(position = 16, required = false, value = "小票金额(元)")
	private String endAmount;

    private List<Long> shopIdList;

	@ApiModelProperty(position = 17,  value = "收银渠道:8,联网收银 9,本地收银")
	private String cashChannel;

	@ApiModelProperty("pos机类型")
	private Integer posType;

	@ApiModelProperty("线下优惠券序列号")
	private String couponOfflineSn;

	@ApiModelProperty("档口id")
	private Long stallId;

}
