package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.pos.PosTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CommodityQuantityUpdateDTO {


    @ExcelIgnore
    private Long itemId;

    @ExcelIgnore
    private Long shopId;

    @ApiModelProperty("门店code")
    @ExcelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("POS类型")
    @ExcelProperty("POS类型")
    private String posTypeName;

    @ApiModelProperty("pos机编码")
    @ExcelProperty("POS机号")
    private String macCode;

    @ApiModelProperty("订单编码")
    @ExcelProperty("订单号")
    private String orderCode;

    @ApiModelProperty("操作时间")
    @ExcelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("商品编码")
    @ExcelProperty("货号")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("品名")
    private String commodityName;

    @ApiModelProperty("商品规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("商品单位")
    @ExcelProperty("单位")
    private String commodityUnit;

    @ApiModelProperty("数量变化")
    @ExcelProperty("数量变化")
    private String operateTypeName;

    @ApiModelProperty("数量")
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("零售价")
    @ExcelProperty("零售价")
    private BigDecimal commodityPrice;

    @ApiModelProperty("零售价金额")
    @ExcelProperty("零售价金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("收银员编码")
    @ExcelProperty("收银员编码")
    private String casherCode;

    @ApiModelProperty("收银员姓名")
    @ExcelProperty("收银员姓名")
    private String casherName;

    @ApiModelProperty("授权人账号")
    @ExcelProperty("授权人账号")
    private String authorizerCode;

    @ApiModelProperty("授权人姓名")
    @ExcelProperty("授权人姓名")
    private String authorizerName;

    /**
     * 1-增加, 2-减少
     */
    @ApiModelProperty("1-增加, 2-减少")
    @ExcelIgnore
    private Integer operateType;





    @ApiModelProperty("收银员")
    @ExcelIgnore
    private Long casherId;



    @ExcelIgnore
    private Long posMacId;

    /**
     * 1-收银pos, 2-自助pos
     */
    @ExcelIgnore
    private Integer posType;

    @ExcelIgnore
    private Date createTime;

    public String getOperateTypeName() {
        if (null != this.operateType) {
            return 1 == this.operateType? "增加":"减少";
        } else {
            return null;
        }
    }

    public String getPosTypeName() {
        if (null != this.posType) {
            return PosTypeEnum.get(this.posType).getDesc();
        } else {
            return null;
        }
    }



}
