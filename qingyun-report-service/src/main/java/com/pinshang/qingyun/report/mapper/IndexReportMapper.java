package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.report.dto.index.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IndexReportMapper {

    /**
     * 四大框
     * @param vo
     * @return
     */
    SalesDataEntry selectSalesDataHistory(HomePageVo vo);
    /**
     * 查询线下销售
     * @param vo
     * @return
     */
    SalesDataEntry selectOffLineSalesDataHistory(HomePageVo vo);

    /**
     * 线下曲线图
     * @param vo
     * @return
     */
    List<YearOnYearDataEntry> selectSelectOffLineDataGraphicsHistory(HomePageVo vo);

    /**
     * 曲线图
     * @param vo
     * @return
     */
    List<YearOnYearDataEntry> selectDataGraphicsHistory(HomePageVo vo);


    /**
     * 门店排序 线下数据
     * @param vo
     * @return
     */
    List<StoreSaleEntry> selectOffLineHistoryStoreSale(HomePageVo vo);

    /**
     * 门店排序 线上数据
     * @param vo
     * @return
     */
    List<StoreSaleEntry> selectHistoryStoreSale(HomePageVo vo);

    /**
     *根据时间查询销售数据
     * @param vo
     * @return
     */
    List<IndexEntry> compensateIndex(CompensateVo vo);

    /**
     * 根据时间段 查询销售数据
     * @param vo
     * @return
     */
    List<IndexEntry> selectOffHistorySaleByTimeSlot(CompensateVo vo);
    /**
     * 数据监控
     * @param dateTime
     * @return
     */
    Long countCommodityShopTaxByTime(@Param("dateTime") String dateTime);

    /**
     * 监控商品总表(门店)和前置仓日汇总表
     * @param dateTime
     * @return
     */
    Long countRealTimeReportByTime(@Param("dateTime") String dateTime);
}
