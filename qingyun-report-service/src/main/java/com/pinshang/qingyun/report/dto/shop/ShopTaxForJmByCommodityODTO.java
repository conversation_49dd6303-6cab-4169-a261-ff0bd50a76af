package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/07/03
 * @Version 1.0
 */
@Data
public class ShopTaxForJmByCommodityODTO {

    @ExcelProperty("门店编码")
    private String shopCode;

    @ExcelProperty("门店")
    private String shopName;


    @ExcelProperty("大类")
    private String commodityFirstKindName;

    @ExcelProperty("中类")
    private String commoditySecondKindName;

    @ExcelProperty("小类")
    private String commodityThirdKindName;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("计量单位")
    private String commodityUnitName;


    @ExcelProperty("销售金额")
    private BigDecimal totalSales=BigDecimal.ZERO;

    @ExcelProperty("进货金额")
    private BigDecimal orderTotal = BigDecimal.ZERO;

    @ExcelProperty("毛利额")
    private BigDecimal grossProfitMargin = BigDecimal.ZERO;


    @ExcelIgnore
    @ApiModelProperty("毛利率")
    private BigDecimal grossMargin = BigDecimal.ZERO;

    @ExcelProperty("毛利率")
    private String grossMarginStr ="0.00";

    @ExcelProperty("进货数量")
    private BigDecimal orderQuanty = BigDecimal.ZERO;

    @ExcelProperty("销售数量")
    private BigDecimal totalQuantity=BigDecimal.ZERO;

    @ExcelProperty("损耗数量")
    private BigDecimal lossQuantity = BigDecimal.ZERO;


}

