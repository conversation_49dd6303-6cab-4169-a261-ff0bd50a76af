package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;


/**
* 手动优惠报表
*
* <AUTHOR> Zhang
* @since 2019-07-02
*/
@Data
public class HandDiscountODTO {

	private Long shopId;

	@ApiModelProperty(position = 2, value ="门店名称")
	private String shopName;

	@ApiModelProperty(position = 4, value ="门店编码")
	private String shopCode;

	@ApiModelProperty(position = 4, value ="客户编码")
	private String storeCode;

	@ApiModelProperty(position = 3, value ="收银员姓名")
	private String casherName;

	@ApiModelProperty(position = 4, value ="收银账号")
	private String casherNumber;

	@ApiModelProperty(position = 5, value ="收银日期")
	private String casherDate;

	@ApiModelProperty(position = 6, value ="手动优惠总金额")
	private BigDecimal discountTotalAmount;

}
