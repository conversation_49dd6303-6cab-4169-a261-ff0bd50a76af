package com.pinshang.qingyun.report.service.shop;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.DaySalesCompletionRateSum;
import com.pinshang.qingyun.report.dto.SalesCompletionRateIDTO;
import com.pinshang.qingyun.report.dto.SalesCompletionRateODTO;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.shop.DaySaleAimIDTO;
import com.pinshang.qingyun.report.dto.shop.DaySaleAimODTO;
import com.pinshang.qingyun.report.dto.shop.DaySaleAimPageIDTO;
import com.pinshang.qingyun.report.dto.shop.SaleAimIDTO;
import com.pinshang.qingyun.report.mapper.shop.DaySaleAimMapper;
import com.pinshang.qingyun.report.model.shop.DaySaleAim;
import com.pinshang.qingyun.report.model.shop.Shop;
import com.pinshang.qingyun.report.realtime.RedisDataConstants;
import com.pinshang.qingyun.report.service.BaseService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class DaySaleAimService extends BaseService<DaySaleAimMapper,DaySaleAim> {

      @Autowired
      private  DaySaleAimMapper daySaleAimMapper;

      @Autowired
      private  ShopService shopService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private SMMUserClient smmUserClient;

    /**
     * 日销售完成率
     * @param idto
     * @return
     */
    public TablePageInfo<SalesCompletionRateODTO> daySalesCompletionRatePage(SalesCompletionRateIDTO idto){
        PageInfo<SalesCompletionRateODTO> page = null;

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(idto.getShopId() == null){
            idto.setShopIdList(shopIdList);
        }else{
            idto.setShopIdList(Arrays.asList(idto.getShopId()));
        }

        Integer isToday = idto.getIsToday();
        String yyMMdd = DateTimeUtil.getNowDayDate();
        if(isToday.intValue() == 1){
            idto.setBeginDate(yyMMdd);
            idto.setEndDate(yyMMdd);
        }
        page = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            daySaleAimMapper.daySalesCompletionRatePage(idto);
        });
        DaySalesCompletionRateSum hear = daySaleAimMapper.daySalesCompletionRateSum(idto);
        List<SalesCompletionRateODTO> salesCompletionRates = page.getList();
        if(isToday.intValue() == 1 && CollectionUtils.isNotEmpty(salesCompletionRates) && hear != null){
            String turnoverYyMMdd = DateUtils.get$yyMMdd(LocalDate.now());
            String turnoverKey = RedisDataConstants.Daile.TURNOVER + turnoverYyMMdd;
            RScoredSortedSet<Long> scoredSortedSet = redisson.getScoredSortedSet(turnoverKey);
            salesCompletionRates.forEach(item -> {
                Long shopId = item.getShopId();
                Double score = scoredSortedSet.getScore(shopId);
                if(score != null){
                    item.setOfflineSales(new BigDecimal(score).setScale(2, RoundingMode.HALF_UP));
                }
            });
            BigDecimal sum = BigDecimal.ZERO;
            idto.setPageSize(Integer.MAX_VALUE);
            List<Long> daySaleAimShopIdList = daySaleAimMapper.getDaySaleAimShopIdList(idto);

            for (Long it : daySaleAimShopIdList) {
                Double score = scoredSortedSet.getScore(it);
                if(score != null){
                    sum = sum.add(new BigDecimal(score));
                }
            }

            if(sum.compareTo(BigDecimal.ZERO) != 0){
                hear.setOfflineSalesSum(sum.setScale(2,RoundingMode.HALF_UP));
            }
        }

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        tablePageInfo.setHeader(hear);
        return tablePageInfo;
    }


    /**
     * 月销售完成率
     * @param idto
     * @return
     */
    public TablePageInfo<SalesCompletionRateODTO> monthSalesCompletionRatePage(SalesCompletionRateIDTO idto){
        PageInfo<SalesCompletionRateODTO> page = null;
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(idto.getShopId() == null){
            idto.setShopIdList(shopIdList);
        }else{
            idto.setShopIdList(Arrays.asList(idto.getShopId()));
        }
        page = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            daySaleAimMapper.monthSalesCompletionRatePage(idto);
        });
        DaySalesCompletionRateSum hear = daySaleAimMapper.monthSalesCompletionRateSum(idto);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        tablePageInfo.setHeader(hear);
        return tablePageInfo;
    }

    /**
     * 日销售目标管理(分页)
     * @param idto
     * @return
     */
    public PageInfo<DaySaleAimODTO> listDaySaleAimPage(DaySaleAimPageIDTO idto) {
        PageInfo<DaySaleAimODTO> pageDate = null ;
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(idto.getShopId() == null){
            idto.setShopIdList(shopIdList);
        }else{
            idto.setShopIdList(Arrays.asList(idto.getShopId()));
        }
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            daySaleAimMapper.listDaySaleAimPage(idto);
        });
        if(CollectionUtils.isNotEmpty(pageDate.getList())){
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE,-1);
            String yesterday = DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
            for(DaySaleAimODTO odto:pageDate.getList()){
                int i = DateUtils.compareDate(odto.getSaleTime(),yesterday,"yyyy-MM-dd");
                odto.setIsHistory(i <= 0 ? 1:0);
            }
        }
        return pageDate;
    }

    /**
     * 新增日销售目标管理
     * @param daySaleAimIDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
     public void saveDaySaleAim(DaySaleAimIDTO daySaleAimIDTO){
         Assert.notNull(daySaleAimIDTO.getShopId(), "门店不能为空");
         Assert.notNull(daySaleAimIDTO.getSaleTime(), "日期不能为空");
         Assert.notNull(daySaleAimIDTO.getAimSaleAmount(), "销售目标不能为空");

         //判断门店日期唯一性
         Integer count = daySaleAimMapper.countDaySaleAimByShopSaleTime(daySaleAimIDTO.getShopId(),daySaleAimIDTO.getSaleTime());
         Assert.isTrue(count == 0, "此门店的此日期已有目标！");

         //判断日期：只能选今天和今天之后的日期
         String today = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
         int i = DateUtils.compareDate(today,daySaleAimIDTO.getSaleTime(),"yyyy-MM-dd");
         Assert.isTrue(i <= 0, "只能选今天和今天之后的日期！");

         DaySaleAim daySaleAim = new DaySaleAim();
         BeanUtils.copyProperties(daySaleAimIDTO,daySaleAim);
         Shop shop = shopService.selectByPrimaryKey(daySaleAimIDTO.getShopId());
         daySaleAim.setShopCode(null !=shop ? shop.getShopCode():"");
         daySaleAim.setShopName(null !=shop ? shop.getShopName():"");
         this.insert(daySaleAim);

     }
     public DaySaleAimODTO getDaySaleAimById(Long id){
         Assert.notNull(id, "门店不能为空");

         DaySaleAimODTO dto = new DaySaleAimODTO();
         DaySaleAim daySaleAim = daySaleAimMapper.selectByPrimaryKey(id);
         BeanUtils.copyProperties(daySaleAim,dto);
         return dto;
     }
    /**
     * 修改日销售目标管理
     * @param
     * @param daySaleAimIDTO
     * @return
     */
     public void updateDaySaleAim(DaySaleAimIDTO daySaleAimIDTO) {
         Assert.notNull(daySaleAimIDTO.getId(), "id不能为空");
         Assert.notNull(daySaleAimIDTO.getAimSaleAmount(), "销售目标不能为空");
         DaySaleAim daySaleAim = new DaySaleAim();
         BeanUtils.copyProperties(daySaleAimIDTO,daySaleAim);
         this.updateByPrimaryKeySelective(daySaleAim);
     }

    /**
     * 删除日销售目标管理
     * @param id
     * @return
     */
     public int deleteDaySaleAim(Long id) {
         Assert.notNull(id, "id不能为空");
         return daySaleAimMapper.deleteByPrimaryKey(id);
     }



    /**
     * 获取shopMap
     * @return
     */
     public  Map<String,Shop> getShopMap(){
         Map<String,Shop> shopMap = new HashMap<>();
         List<Shop> shopList = shopService.selectListAll();
         for(Shop shop:shopList){
             shopMap.put(shop.getShopCode(),shop);
         }
         return  shopMap;
     }
    /**
     * 校验1-9位正整数
     * @param str
     * @return
     */
    public  boolean isNumber(String str) {
        // Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        String reg = "^[1-9]\\d{0,8}$";
        Pattern pattern = Pattern.compile(reg);
        Matcher match = pattern.matcher(str);
        return match.matches();
    }
    /**
     * 批量导入 日销售目标管理
     * @param list
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object importDaySaleAim(List<SaleAimIDTO> list)throws ParamException {
        Map<String,Object> dataMap = new HashMap<>();//定义返回map
        List<String> errorList = new ArrayList<>();//定义返回错误信息list
        errorList.add("错误提示：");
        boolean isError = false;

        List<DaySaleAim> daySaleAimList = new ArrayList<>();//新增数据
        List<String> keyList = new ArrayList<>();//判断是否和数据库重复key

        //文件内容校验
        isError = valid(list,errorList, isError, daySaleAimList,keyList);

        //数据库层面校验重复
        isError = daySaleAimValidateDB(list, errorList, isError, keyList);

        if(isError){
            dataMap.put("success",0);
            dataMap.put("data",errorList);
        }else{
            //校验通过，没有问题
            dataMap.put("success",1);
            daySaleAimMapper.insertList(daySaleAimList);
        }
        return dataMap;
    }

    /**
     * 文件内容校验
     * @param list
     * @param errorList
     * @param isError
     * @param daySaleAimList
     * @param keyList
     * @return
     */
    private boolean valid(List<SaleAimIDTO> list,List<String> errorList, boolean isError, List<DaySaleAim> daySaleAimList,List<String> keyList){
        Map<String,Shop> shopMap = getShopMap();//获取门店map
        Map<String,String> dateShopMap = new HashMap<>();//判断excel内是否重复
        for(int i=0;i<list.size();i++){
            SaleAimIDTO idto = list.get(i);
            DaySaleAim  daySaleAim = new DaySaleAim();

            //校验日期
            if(!isDate(idto.getSaleTime())){
                isError = true;
                errorList.add("第"+(i+2)+"行,日期格式有误，请参考模板！");
            }else{
                idto.setSaleTime(DateUtil.getDateFormate(DateUtil.parseDate(idto.getSaleTime(),"yyyy-MM-dd"),"yyyy-MM-dd"));
                String today = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
                int j = DateUtils.compareDate(today,idto.getSaleTime(),"yyyy-MM-dd");
                if(j > 0){
                    isError = true;
                    errorList.add("第"+(i+2)+"行,只能填今天和今天之后的日期！");
                }
            }
            daySaleAim.setSaleTime(idto.getSaleTime());

            //校验门店
            Shop shop = shopMap.get(idto.getShopCode());
            if(null == shop){
                isError = true;
                errorList.add("第"+(i+2)+"行,没有匹配到门店！");
            }else{
                daySaleAim.setShopId(shop.getId());
                daySaleAim.setShopCode(shop.getShopCode());
                daySaleAim.setShopName(shop.getShopName());
            }
            //校验目标销售金额
            if(!isNumber(idto.getAimSaleAmount())){
                isError = true;
                errorList.add("第"+(i+2)+"行,销售目标金额格式有误，销售目标的输入范围：1~999999999！");
            }else{
                daySaleAim.setAimSaleAmount(new BigDecimal(idto.getAimSaleAmount()));
            }

            //校验备注
            if(StringUtils.isNotBlank(idto.getRemark()) && idto.getRemark().length() > 15){
                isError = true;
                errorList.add("第"+(i+2)+"行,备注最多输入15个字！");
            }
            daySaleAim.setRemark(idto.getRemark());

            if(dateShopMap.get(idto.getShopCode()+idto.getSaleTime()) != null){
                isError=true;
                errorList.add("第"+(i+2)+"行,文件内重复,相同门店相同日期只能有一个目标！");
            }
            dateShopMap.put(idto.getShopCode()+idto.getSaleTime(),idto.getShopCode()+idto.getSaleTime());
            keyList.add(idto.getShopCode()+idto.getSaleTime());

            //daySaleAim.setId(PosIdWorker.getId());
            daySaleAim.setCreateId(idto.getCreateId());
            daySaleAim.setUpdateId(idto.getCreateId());
            daySaleAim.setCreateTime(new Date());
            daySaleAim.setUpdateTime(new Date());
            daySaleAimList.add(daySaleAim);
        }
        return isError;
    }

    /**
     * 数据库层面校验重复
     * @param list
     * @param errorList
     * @param isError
     * @param keyList
     * @return
     */
    private boolean daySaleAimValidateDB(List<SaleAimIDTO> list, List<String> errorList, boolean isError, List<String> keyList) {
        List<String> repeatList = daySaleAimMapper.queryDayKeyListRepeat(keyList);
        Map<String,String> keyMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(repeatList)){
            for(String s:repeatList){
                keyMap.put(s,s);
            }
        }
        for(int i=0;i<list.size();i++){
            SaleAimIDTO idto = list.get(i);
            if(keyMap.get(idto.getShopCode()+idto.getSaleTime()) != null){
                isError = true;
                errorList.add("第"+(i+2)+"行,数据重复,相同门店相同日期只能有一个目标！");
            }
        }
        return isError;
    }

    /**
     * 判断“YYYY-MM-DD”类型 时间
     * @param date
     * @return
     */
    public static boolean isDate(String date){
        String reg = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))?$";
        Pattern p = Pattern.compile(reg);
        return p.matcher(date).matches();
    }
}
