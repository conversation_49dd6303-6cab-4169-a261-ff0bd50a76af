package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.HandDiscountIDTO;
import com.pinshang.qingyun.report.dto.HandDiscountODTO;
import com.pinshang.qingyun.report.model.HandDiscount;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
* 手动优惠报表
*
* <AUTHOR>
* @since 2019-07-02
*/
@Repository
public interface HandDiscountMapper extends MyMapper<HandDiscount> {

    List<HandDiscountODTO> handDiscountReport(@Param("idto") HandDiscountIDTO idto);

    HandDiscountODTO handDiscountReportHeader(@Param("idto") HandDiscountIDTO idto);

    Integer findCount(@Param("shopId")Long shopId, @Param("userId")Long userId, @Param("cashDate")String cashDate);

    void updateDiscountAmount(@Param("shopId")Long shopId, @Param("userId")Long userId, @Param("cashDate")String cashDate, @Param("promotionPrice")BigDecimal promotionPrice);

    void insertDiscountAmount(@Param("shopId")Long shopId, @Param("userId")Long userId, @Param("cashDate")String cashDate, @Param("promotionPrice")BigDecimal promotionPrice);
}
