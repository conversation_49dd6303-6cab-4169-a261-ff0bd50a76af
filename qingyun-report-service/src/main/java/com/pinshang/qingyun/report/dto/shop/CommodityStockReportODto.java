package com.pinshang.qingyun.report.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品库存报表(出参)
 * Created by mengday.zhang on 2018/3/29.
 */
@Data
public class CommodityStockReportODto {

    @ApiModelProperty("门店")
    private String shopName;

    @ApiModelProperty("大类")
    private String commodityFirstKind;
    @ApiModelProperty("中类")
    private String commoditySecondKind;
    @ApiModelProperty("小类")
    private String commodityThirdKind;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("商品状态")
    private String commodityState;

    @ApiModelProperty("上下架状态")
    private String appStatus;

    @ApiModelProperty("移动平均价")
    private BigDecimal weightPrice;

    @ApiModelProperty("实时库存")
    private BigDecimal stockQuantity;

    @ApiModelProperty("库存金额")
    private BigDecimal stockPrice;

    @ApiModelProperty("本月期初库存")
    private BigDecimal monthInitStock;

    @ApiModelProperty("本月入库数量")
    private BigDecimal currentMonthInStorageQuantity;

    @ApiModelProperty("当前进价")
    private BigDecimal commodityPrice;

    /** 已下单未收获的商品数量, 有延长收获的天数配置 */
    @ApiModelProperty("在途库存数")
    private BigDecimal enRouteQuantity;

    @ApiModelProperty("前7天销售数量")
    private BigDecimal previous7DayQuantity;

    @ApiModelProperty("前30天销售数量")
    private BigDecimal previous30DayQuantity;

    @ApiModelProperty("子码列表")
    private String barCodes;
}
