package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CashierReconciledODTO {

    private Long shopId;

    @ApiModelProperty(position = 1, value = "门店编码")
    private String shopCode;

    @ApiModelProperty(position = 2, value = "门店名称")
    private String shopName;

    @ApiModelProperty(position = 3, value = "收银员编号")
    private String saleNumber;

    @ApiModelProperty(position = 4, value = "收银员姓名")
    private String saleName;

    @ApiModelProperty(position = 5, value = "销售方式")
    private String saleType;

    @ApiModelProperty(position = 6, value = "收款方式")
    private String payType;

    @ApiModelProperty("收款方式")
    private String payTypeName;

    @ApiModelProperty(position = 7, value = "金额")
    private String payAmount;

    @ApiModelProperty(position = 8, value = "销售金额")
    private String salesAmount;

    @ApiModelProperty(position = 9, value = "退款金额")
    private String refundAmount;

    @ApiModelProperty(position = 10, value = "收银机号")
    private String posMacId;

    @ApiModelProperty(position = 11, value = "对账时间")
    private Date createTime;

    @ApiModelProperty(position = 12, value = "对账渠道")
    private String sourceType;

    @ApiModelProperty(position = 13, value = "首笔交易时间")
    private Date beginTime;

    @ApiModelProperty(position = 14, value = "末笔交易时间")
    private Date endTime;

    @ApiModelProperty(position = 15, value = "交易金额")
    private String balanceAmount;

    private String newDate;

    @ApiModelProperty("POS机号")
    private String macCode;

    @ApiModelProperty("销售笔数")
    private Long saleCount;
    @ApiModelProperty("退回笔数")
    private Long returnCount;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

    @ApiModelProperty("pos机类型名称")
    private String posTypeName;


    /** 合计总金额 */
    private BigDecimal totalAmount;
    /** 现金小计金额 */
    private  BigDecimal cashTotalAmount;
    /** 银行卡小计金额 */
    private BigDecimal bankTotalAmount;
    /** OK卡小计金额 */
    private BigDecimal okTotalAmount;
    /** 记账(聚合)小计金额 */
    private BigDecimal chargeConvergeTotalAmount;
    /** 记账(索迪斯)小计金额 */
    private BigDecimal chargeSdsTotalAmount;
    /** 聚合支付小计金额 */
    private BigDecimal convergeTotalAmount;
    /** 索迪斯小计金额 */
    private BigDecimal sdsTotalAmount;
    /** 积分支付小计金额 */
    private BigDecimal jfTotalAmount;
    /** 抹零小计金额 */
    private BigDecimal moLinTotalAmount;


    /** 售卡合计总金额 */
    private BigDecimal giftCardTotalAmount;
    /** 售卡聚合支付小计金额 */
    private BigDecimal giftCardConvergeTotalAmount;
    /** 售卡现金小计金额*/
    private BigDecimal giftCardCashTotalAmount;


    /** 清美卡 */
    private BigDecimal qmCardAmount;
    /** 清美支付 */
    private BigDecimal qmPayAmount;
    /** 记账(清美) */
    private BigDecimal qmAccountAmount;

    /** 换货(票)金额 */
    private BigDecimal swapTicketAmount;

    /** 数字货币金额 */
    private BigDecimal digitalCurrencyAmount;

    /**
     * 帮困卡
     */
    private BigDecimal helpCardAmount;

    /**
     * 代销总金额
     */
    private BigDecimal consignmentAmount;

    /**
     * 代销现金总金额
     */
    private BigDecimal consignmentCashAmount;

    /**
     * 代销聚合支付总金额
     */
    private BigDecimal consignmentAggregationAmount;

    /**
     * 聚合现金+售卡现金+现金
     */
    @ApiModelProperty("聚合现金+售卡现金+现金")
    private BigDecimal totalCashAmount;

    @ApiModelProperty("悦享金额")
    private BigDecimal joyPayAmout;

    @ApiModelProperty("OK支付金额")
    private BigDecimal okPayAmount;

    public BigDecimal getCashTotal(){
        BigDecimal cashTotalAmount0 = cashTotalAmount != null ? cashTotalAmount : BigDecimal.ZERO;
        BigDecimal giftCardCashTotalAmount0 = giftCardCashTotalAmount != null ? giftCardCashTotalAmount : BigDecimal.ZERO;
        return cashTotalAmount0.add(giftCardCashTotalAmount0);
    }
}
