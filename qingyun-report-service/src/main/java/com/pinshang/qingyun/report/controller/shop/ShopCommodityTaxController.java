package com.pinshang.qingyun.report.controller.shop;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.annotation.NotAdvice;
import com.pinshang.qingyun.report.dto.OnToOffAnalyzeDTO;
import com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.shop.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.enums.XsjmReportTypeEnum;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxService;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxTransService;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/shopCommodityTax")
public class ShopCommodityTaxController {
    public static final String CREATE_XS_SHOP_COMMODITY_TAX_LOCK = "xs_shop_commodity_tax_lock";
    public static final Long SHOP_COMMODITY_TAX_LOCK_EXPIRE_TIME = 60L;

    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private ShopCommodityTaxTransService shopCommodityTaxTransService;

    @Autowired
    private IRenderService renderService;

    /**
     * 商品总表汇总
     * @param timeStamp
     */
    @PostMapping("/shopCommodityTax")
    public void shopCommodityTax(@RequestParam(value = "timeStamp") String timeStamp) {
        String cacheKey = ShopCommodityTaxController.CREATE_XS_SHOP_COMMODITY_TAX_LOCK;
        RLock lock = redissonClient.getLock(cacheKey);

        try {
            log.info("开始----------------门店报表-商品总表+锁-------" + new Date());
            lock.lock(ShopCommodityTaxController.SHOP_COMMODITY_TAX_LOCK_EXPIRE_TIME, TimeUnit.SECONDS);

            //汇总
            shopCommodityTaxTransService.commodityTax(timeStamp);

        } finally {
            log.info("结束----------------门店报表-商品总表释放锁-------" + new Date());
            lock.unlock();
        }

    }


    @GetMapping("/queryShopCommoditySaleList/{shopId}")
    @NotAdvice
    public List<ShopCommoditySaleStatisticsODTO> queryShopCommoditySaleList(@PathVariable("shopId") Long shopId) {
        return shopCommodityTaxService.queryShopCommoditySaleList(shopId);
    }

    /**
     * pda 手持订货查询 月均销量
     *
     * @return
     */
    @GetMapping("/findMonthSalesQuanty")
    @NotAdvice
    public List<CommodityOrderSalesODTO> findMonthSalesQuanty(@RequestParam("commodityId") Long commodityId, @RequestParam("storeId") Long storeId, @RequestParam("inBegin") String inBegin, @RequestParam("yesterday") String yesterday, @RequestParam("flag") int flag) {
        return shopCommodityTaxService.findMonthSalesQuanty(commodityId, storeId, inBegin, yesterday, flag);
    }


    /**
     * 上个月的销量top5(自动订货)
     * @param shopAutoCommodityTaxDTO
     * @return
     */
    @PostMapping("/queryTopShopCommoditySale")
    @NotAdvice
    public List<Long> queryTopShopCommoditySale(@RequestBody ShopAutoCommodityTaxDTO shopAutoCommodityTaxDTO) {
        return shopCommodityTaxService.queryTopShopCommoditySale(shopAutoCommodityTaxDTO);
    }


    /**
     * 商品总表汇总job(只保留31天数据，专为PDA订货用)
     * @param timeStamp
     */
    @PostMapping("/shopCommodityTaxMonth")
    public Boolean shopCommodityTaxMonth(@RequestParam(value = "timeStamp") String timeStamp) {
        return  shopCommodityTaxService.commodityTaxMonth(timeStamp);
    }

    @PostMapping("/onToOffAnalyze")
    @ApiOperation("线上线下客单分析")
    public List<OnToOffAnalyzeDTO> onToOffAnalyze(@RequestBody OnToOffAnalyzeIDTO dto) {
        return shopCommodityTaxService.onToOffAnalyze(dto);
    }

    @ApiOperation("导出线上线下客单分析")
    @GetMapping("/exportOnToOffAnalyze")
    public ModelAndView exportOnToOffAnalyze(OnToOffAnalyzeIDTO dto) {
        List<OnToOffAnalyzeDTO> list = shopCommodityTaxService.onToOffAnalyze(dto);
        Map<String, List<String>> data = new HashMap<>();

        if (CollectionUtils.isNotEmpty(list)) {
            List<String> dataLst = null;
            int i = 0;
            for (OnToOffAnalyzeDTO analyze : list) {
                dataLst = new ArrayList<>();
                dataLst.add(analyze.getChannel().equals(1) ? "线上": "线下");
                dataLst.add(analyze.getTimeName());
                dataLst.add(analyze.getSales().toString());
                dataLst.add(analyze.getVisitorNumber().toString());
                dataLst.add(analyze.getAverageAmount().toString());
                dataLst.add(analyze.getShopNum().toString());
                dataLst.add(analyze.getSingleAverageSale().toString());
                dataLst.add(analyze.getSingleAverageVisitor().toString());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        String fileName = "线上线下客单分析" + DateUtil.get4yMdHms(new Date());

        map.put("sheetTitle", ExcelSheetTitleEnum.ON_TO_OFF_ANALYZE);

        // 参数设置
        map.put("filename", fileName);
        map.put("data", data);

        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "维护t_md_commodity_shop_tax的online_visitor_number和online_average_amount，跑job")
    @GetMapping("/insertOrUpdateOnline")
    public Integer insertOrUpdateOnline(@RequestParam("time") String time) {
        return shopCommodityTaxService.insertOrUpdateOnline(time);
    }

    @ApiOperation(value = "维护t_md_commodity_first_tax的online_sales, total_sales，跑job")
    @GetMapping("/insertOrUpdateCategorySalesOnLine")
    public Boolean insertOrUpdateCategorySalesOnLine(@RequestParam("time") String time) {
        return shopCommodityTaxService.insertOrUpdateCategorySalesOnLine(time);
    }

    @ApiOperation(value = "鲜家加盟-分类数据报表、商品分析表")
    @PostMapping("/shopTaxForJmByCate")
    @MethodRender
    public TablePageInfo<ShopTaxForJmByCateODTO> shopTaxForJmByCate(@RequestBody ShopTaxForJmByCateIDTO idto){
        return shopCommodityTaxService.shopTaxForJmByCate(idto);
    }


    @ApiOperation(value = "鲜家加盟-分类数据报表、商品分析表-导出")
    @GetMapping("/shopTaxForJmByCate/export")
    public void shopTaxForJmByCate(ShopTaxForJmByCateIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        TablePageInfo<ShopTaxForJmByCateODTO> pageInfo = shopCommodityTaxService.shopTaxForJmByCate(idto);
        List<ShopTaxForJmByCateODTO> list =  new ArrayList<>();

        if(CollectionUtils.isNotEmpty(pageInfo.getList())) {
            ShopTaxForJmByCateODTO header = (ShopTaxForJmByCateODTO) pageInfo.getHeader();
            if(XsjmReportTypeEnum.COMMODITY_SUMMARY.getCode().equals(idto.getType())) {
                header.setShopCode("合计");
            }else {
                header.setShopName("合计");
            }
            list.add(header);
            list.addAll(pageInfo.getList());
        }

        //renderService.render(list, "/shopTaxForJmByCate/export");
        if(XsjmReportTypeEnum.FIRST_CATE_SUMMARY.getCode().equals(idto.getType())){
            List<ShopTaxForJmByCateFirstODTO> exportList = BeanCloneUtils.copyTo(list, ShopTaxForJmByCateFirstODTO.class);
            exportList = exportList.stream()
                    .peek(it -> {
                        if(null != it.getGrossMargin()) {
                            it.setGrossMarginStr(it.getGrossMargin().setScale(2, RoundingMode.HALF_UP) + "%");
                        }else{
                            it.setGrossMarginStr("");
                        }
                    })
                    .collect(Collectors.toList());
            ExcelUtil.setFileNameAndHead(response, "分类数据报表");
            EasyExcel.write(response.getOutputStream(), ShopTaxForJmByCateFirstODTO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet("分类数据报表").doWrite(exportList);
        }else if(XsjmReportTypeEnum.SECOND_CATE_SUMMARY.getCode().equals(idto.getType())){
            List<ShopTaxForJmByCateSecondODTO> exportList = BeanCloneUtils.copyTo(list, ShopTaxForJmByCateSecondODTO.class);
            exportList = exportList.stream()
                    .peek(it -> {
                        if(null != it.getGrossMargin()) {
                            it.setGrossMarginStr(it.getGrossMargin().setScale(2, RoundingMode.HALF_UP) + "%");
                        }else{
                            it.setGrossMarginStr("");
                        }
                    })
                    .collect(Collectors.toList());
            ExcelUtil.setFileNameAndHead(response, "分类数据报表");
            EasyExcel.write(response.getOutputStream(), ShopTaxForJmByCateSecondODTO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet("分类数据报表").doWrite(exportList);
        }else if(XsjmReportTypeEnum.THIRD_CATE_SUMMARY.getCode().equals(idto.getType())){
            List<ShopTaxForJmByCateThirdODTO> exportList = BeanCloneUtils.copyTo(list, ShopTaxForJmByCateThirdODTO.class);
            exportList = exportList.stream()
                    .peek(it -> {
                        if(null != it.getGrossMargin()) {
                            it.setGrossMarginStr(it.getGrossMargin().setScale(2, RoundingMode.HALF_UP) + "%");
                        }else{
                            it.setGrossMarginStr("");
                        }
                    })
                    .collect(Collectors.toList());
            ExcelUtil.setFileNameAndHead(response, "分类数据报表");
            EasyExcel.write(response.getOutputStream(), ShopTaxForJmByCateThirdODTO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet("分类数据报表").doWrite(exportList);
        }else if(XsjmReportTypeEnum.COMMODITY_SUMMARY.getCode().equals(idto.getType())){
            List<ShopTaxForJmByCommodityODTO> exportList = BeanCloneUtils.copyTo(list, ShopTaxForJmByCommodityODTO.class);
            exportList = exportList.stream()
                    .peek(it -> {
                        if(null != it.getGrossMargin()) {
                            it.setGrossMarginStr(it.getGrossMargin().setScale(2, RoundingMode.HALF_UP) + "%");
                        }else{
                            it.setGrossMarginStr("");
                        }
                    })
                    .collect(Collectors.toList());
            ExcelUtil.setFileNameAndHead(response, "商品分析表");
            EasyExcel.write(response.getOutputStream(), ShopTaxForJmByCommodityODTO.class)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet("商品分析表").doWrite(exportList);
        }

    }

}
