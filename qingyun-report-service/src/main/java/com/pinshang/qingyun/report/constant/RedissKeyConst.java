package com.pinshang.qingyun.report.constant;

/**
 * @Author: sk
 * @Date: 2022/6/7
 */
public class RedissKeyConst {
    // 修复商品总表(pos非当日数据)
    public static final String FIX_POS_DAY_REPORT = "fixPosDayReport";

    // pos非当日数据保存进ES异常 key = posNotCurrent + orderTime
    public static final String POS_NOT_CURRENT = "posNotCurrent";


    // 修复品类实发报表(B订单非当日数据)
    public static final String FIX_B_ORDER_REPORT = "fixBOrderReport";

    // 修复短交报表ES(B订单非当日数据)
    public static final String FIX_B_ORDER_REPORT_ES = "fixBOrderReportES";

    // 修复 xd-order送货日期之后配送成功、失败。进行数据补偿
    public static final String FIX_XD_ORDER_DELAY_COMPLETE= "fixXdorderDelayComplete";

    // 是否启用数据路由
    public static final String ENABLE_DATA_QUERY = "report:enableDataQuery";
}
