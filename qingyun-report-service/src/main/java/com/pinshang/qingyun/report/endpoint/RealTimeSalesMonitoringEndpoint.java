package com.pinshang.qingyun.report.endpoint;


import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.realtime.RealTimeData;
import com.pinshang.qingyun.report.realtime.RealTimeReportService;
import com.pinshang.qingyun.report.util.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.CopyOnWriteArraySet;

@Slf4j
@ServerEndpoint(value = "/ws/realTimeSalesMonitoring")
@Component
public class RealTimeSalesMonitoringEndpoint{

    private static CopyOnWriteArraySet<RealTimeSalesMonitoringEndpoint> webSocketSet = new CopyOnWriteArraySet<RealTimeSalesMonitoringEndpoint>();

    private Session session;

    @Autowired
    private RealTimeReportService realTimeReportService;

    @Scheduled(fixedRate = 2000)
    public void sendMessage() throws IOException {
        if(webSocketSet.size() > 0) {
            log.info("sendMessage begin");
            RealTimeData allData = realTimeReportService.getAllData();
            for (RealTimeSalesMonitoringEndpoint r : webSocketSet) {
                r.session.getBasicRemote().sendText(JsonUtil.java2json(allData));
            }
            log.info("sendMessage end");
        }
    }

    @Scheduled(fixedRate = 3000)
    public void sendMapMessage() throws IOException {
        if(webSocketSet.size() > 0){
            log.info("sendMapMessage begin");
            RealTimeData mapData = realTimeReportService.getMapData();
            log.info("sendMapMessage data load");
            for(RealTimeSalesMonitoringEndpoint r : webSocketSet){
                Session session = r.session;
                log.info("send to" + session.getId());
                session.getBasicRemote().sendText(JsonUtil.java2json(mapData));
            }
            log.info("sendMapMessage end");
        }
    }

    @OnClose
    public void onClose(){
        webSocketSet.remove(this);
        log.info("客户端 sessionId：{} 移除",session.getId());
    }

    @OnOpen
    public void onOpen(Session session) throws IOException {
        this.session = session;
        webSocketSet.add(this);
        RealTimeReportService realTimeReportService = SpringUtils.getBean(RealTimeReportService.class);
        session.getBasicRemote().sendText(JsonUtil.java2json(realTimeReportService.getAllData()));
        session.getBasicRemote().sendText(JsonUtil.java2json(realTimeReportService.getMapData()));
        log.info("客户端 sessionId：{} 加入",session.getId());
    }

    @OnMessage
    public void message(Session session, String message) throws IOException{

    }

    public void clear(){

    }

}
