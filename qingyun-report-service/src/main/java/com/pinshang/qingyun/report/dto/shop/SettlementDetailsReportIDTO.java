package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by hhf on 2018/6/12.
 * 结算明细报表
 */
@Data
@AllArgsConstructor
public class SettlementDetailsReportIDTO extends Pagination {

    public SettlementDetailsReportIDTO(){};
    /**门店**/
    @ApiModelProperty(position = 1, value = "门店id")
    private Long shopId;

    /**送货日期-开始**/
    @ApiModelProperty(position = 2, value = "送货日期-开始")
    private String deliveryTimeStart;

    /**送货日期-结束**/
    @ApiModelProperty(position = 3, value = "送货日期-结束")
    private String deliveryTimeEnd;

    /**结算日期-开始**/
    @ApiModelProperty(position = 4, value = "结算日期-开始")
    private String settleTimeStart;

    /**结算日期-结束**/
    @ApiModelProperty(position = 5, value = "结算日期-结束")
    private String settleTimeEnd;

    /**物流模式 0-直送, 1-配送, 2-直通**/
    @ApiModelProperty(position = 6, value = "物流模式 0-直送, 1-配送, 2-直通")
    private Integer logisticsModel;

    /**分类**/
    @ApiModelProperty(position = 7, value = "分类")
    private Long categoryId;

    /**条码**/
    @ApiModelProperty(position = 8, value = "条码")
    private String barCode;

    /**商品**/
    @ApiModelProperty(position = 9, value = "商品")
    private String commodityCode;

    /**订单编号**/
    @ApiModelProperty(position = 10, value = "订单编号")
    private String subOrderCode;

    /**预订单编号**/
    @ApiModelProperty(position = 11, value = "预订单编号")
    private String preOrderCode;

    /**客户类型**/
    @ApiModelProperty(position = 12, value = "客户类型")
    private Long storeTypeId;
}
