package com.pinshang.qingyun.report.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.report.dto.BreakageEnteringIDTO;
import com.pinshang.qingyun.report.dto.OrderInfoODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.PosReportSubtypeEnum;
import com.pinshang.qingyun.report.service.BreakageEnteringService;
import com.pinshang.qingyun.report.service.ShopReportService;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import com.pinshang.qingyun.report.service.pos.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
@OnlineSwitchWatcher
public class ReportListener extends BaseKafkaOnlineSwitchProcessor {
    @Autowired
    private PosReportService reportService;

    @Autowired
     private OrderDeleteService orderDeleteService;

    @Autowired
    private MemberAccountExecptionService memberAccountExecptionService;

    @Autowired
    private GiftCardCashierWaterService giftCardCashierWaterService;

    @Autowired
    private GiftCardSalesWaterService giftCardSalesWaterService;

    @Autowired
    private ShopReportService shopReportService;

    @Autowired
    private ReceiptDifferenceService receiptDifferenceService;

    @Autowired
    private BreakageEnteringService breakageEnteringService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private PosReportCommodityQuantityUpdateService posReportCommodityQuantityUpdateService;

    /**
     * 单品删除监听
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.SINGLE_ITEM_DELETION,topics = {"${application.name.switch}" + KafkaTopicConstant.SINGLE_ITEM_DELETION})
    public void commodityDeleteListen(String message) {
        try{
            log.info("topic:{}, message:{}", KafkaTopicConstant.SINGLE_ITEM_DELETION, message);
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            CommodityDeleteMessage vo = JsonUtil.json2java(messageWrapper.getData().toString(), CommodityDeleteMessage.class);
            if(null != vo){
                Long keyId;
                if(messageWrapper.getKeyId() != null && !"0".equals(messageWrapper.getKeyId())){
                    keyId = Long.parseLong(messageWrapper.getKeyId());
                }else{
                    keyId = IdWorker.getId();
                }
                vo.setId(keyId);
                orderDeleteService.saveCommodityDelete(vo);
            }
        }catch (Exception e){
            log.error("单品删除监听异常:{}", e);

            StringBuffer sb = new StringBuffer();
            sb.append("单品删除监听");
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }

    /**
     * 单品修改数量监听
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.POS_ITEM_QUANTITY_UPDATE_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.POS_ITEM_QUANTITY_UPDATE_TOPIC})
    public void commodityQuantityUpdateListen(String message) {
        try{
            log.info("topic:{}, message:{}", KafkaTopicConstant.POS_ITEM_QUANTITY_UPDATE_TOPIC, message);
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            PosReportCommodityQuantityUpdateMessage vo = JsonUtil.json2java(messageWrapper.getData().toString(), PosReportCommodityQuantityUpdateMessage.class);
            if(null != vo){
                Long keyId = messageWrapper.getKeyId() == null ? null : Long.parseLong(messageWrapper.getKeyId());
                posReportCommodityQuantityUpdateService.insertQuantityUpdate(keyId, vo);
            }
        }catch (Exception e){
            log.error("单品修改数量监听:{}", e);
        }
    }

    /**
     * 订单作废监听
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.ORDER_DELETION,topics = {"${application.name.switch}" + KafkaTopicConstant.ORDER_DELETION})
    public void orderDeleteListen(String message) {
        try{
            log.info("topic:{}, message:{}", KafkaTopicConstant.ORDER_DELETION, message);
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            OrderDeleteMessage vo = JsonUtil.json2java(messageWrapper.getData().toString(), OrderDeleteMessage.class);
            if(null != vo ){
                Long keyId = messageWrapper.getKeyId() == null ? null : Long.parseLong(messageWrapper.getKeyId());
                vo.setId(keyId);
                orderDeleteService.saveOrderDelete(vo);
            }
        }catch (Exception e){
            log.error("订单作废监听异常:{}", e);

            StringBuffer sb = new StringBuffer();
            sb.append("订单作废监听");
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }

    /**
     * 会员-异常报表 和 手动优惠报表
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.POS_ACCOUNT_ORDER_EXECPTION_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.POS_ACCOUNT_ORDER_EXECPTION_TOPIC})
    public void posExecptionReportListen(String message) {
        log.info("topic:{}, message:{}", KafkaTopicConstant.POS_ACCOUNT_ORDER_EXECPTION_TOPIC, message);
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            Integer type = jsonObject.getInteger("type");
            Long shopId = jsonObject.getLong("shopId");
            // 年月日 yyyy-MM-dd
            String cashDate = jsonObject.getString("cashDate");
            if (PosReportSubtypeEnum.MEMBER.getCode().equals(type)) {
                Long userId = jsonObject.getLong("userId");
                Long saleCount = jsonObject.getLong("saleCount");

                // 会员-异常报表
                memberAccountExecptionService.insertExecptionMemberReport(shopId, userId, cashDate, saleCount);
            } else if (PosReportSubtypeEnum.PROMOTION_PRICE.getCode().equals(type)) {
                Long employeeId = jsonObject.getLong("employeeId");
                BigDecimal promotionPrice = jsonObject.getBigDecimal("promotionPrice");
                // 检查
                reportService.insertHandDiscountReport(shopId, employeeId, cashDate, promotionPrice);
            }
        } catch (Exception e) {
            log.error("kafka report consum is error, message is {}, exception is:{}", message, e);

            StringBuffer sb = new StringBuffer();
            sb.append("会员-异常报表和手动优惠报表异常");
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }

    /**
     *礼品卡收银流水
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.POS_GIFT_CARD_REPORT_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.POS_GIFT_CARD_REPORT_TOPIC})
    public void giftCardCashierWaterListen(String message) {
        log.info("gift-card-cashier-water------");
        log.info("topic:{}, message:{}", KafkaTopicConstant.POS_GIFT_CARD_REPORT_TOPIC, message);
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        GiftCardCashierWaterMessage vo = JsonUtil.json2java(messageWrapper.getData().toString(), GiftCardCashierWaterMessage.class);
        if(null != vo ){
            giftCardCashierWaterService.addCashierWater(vo);
        }
    }

    /**
     *B订单实发补偿
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.B_ORDER_FIX_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.B_ORDER_FIX_TOPIC})
    public void BOrderFixListen(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        List<OrderInfoODTO> subOrderList = JSON.parseArray(messageWrapper.getData().toString(), OrderInfoODTO.class);
        if(SpringUtil.isEmpty(subOrderList)){
            return;
        }

        try {
            shopReportService.bOrderFix(subOrderList);
        } catch (Exception e) {
            log.error("B订单实发补偿异常:{}", e);
            StringBuffer sb = new StringBuffer();
            sb.append("B订单实发补偿异常 " + subOrderList.get(0).getOrderTime());
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }

    /**
     *xd-order送货日期之后配送成功、失败。进行数据补偿
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.XD_ORDER_DELAY_COMPLETE_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.XD_ORDER_DELAY_COMPLETE_TOPIC})
    public void xdOrderDelayCompleteListen(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        String toShopDate = messageWrapper.getData().toString();
        try {
            if(StringUtils.isNotBlank(toShopDate)){
                shopReportService.setXdOrderDelayRedisValue(toShopDate);
            }
        } catch (Exception e) {
            log.error("xd-order送货日期之后配送成功、失败。进行数据补偿异常", e);
        }
    }

    /**
     * 对账差异记录
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.POS_REPORT_RECEIPT_DIFFERENCE_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.POS_REPORT_RECEIPT_DIFFERENCE_TOPIC})
    public void receiptDifference(String message) {
        log.info("topic:{}, message:{}", KafkaTopicConstant.POS_REPORT_RECEIPT_DIFFERENCE_TOPIC, message);
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        if (null != messageWrapper.getData() && "" != messageWrapper.getData()) {
            ReceiptDifferenceDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), ReceiptDifferenceDTO.class);
            receiptDifferenceService.insert(dto);
        }

    }

    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.SINGLE_ITEM_DELETION,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ORDER_DELETION,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.POS_ACCOUNT_ORDER_EXECPTION_TOPIC,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.B_ORDER_FIX_TOPIC
        );
    }


    /**
     * 对账差异记录
     * @param message
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.POS_BREAKAGE_ENTERING_VOLCANO_TOPIC,topics = {"${application.name.switch}" + KafkaTopicConstant.POS_BREAKAGE_ENTERING_VOLCANO_TOPIC})
    public void saveBreakageEnting(String message){
        log.info("topic:{}, message:{}", KafkaTopicConstant.POS_BREAKAGE_ENTERING_VOLCANO_TOPIC, message);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            if (null != messageWrapper.getData() && "" != messageWrapper.getData()) {
                BreakageEnteringIDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), BreakageEnteringIDTO.class);
                TokenInfo tokenInfo = new TokenInfo();
                tokenInfo.setUserId(dto.getCreateId());
                tokenInfo.setShopId(dto.getShopId());
                tokenInfo.setRealName(dto.getCreateName());
                FastThreadLocalUtil.setQY(tokenInfo);
                breakageEnteringService.saveBreakageEnting(dto);
            }
        } catch (Exception e) {
            log.error("报损，kafkaTopic={}，消息消费异常：{}",KafkaTopicConstant.POS_BREAKAGE_ENTERING_VOLCANO_TOPIC, e);

            StringBuffer sb = new StringBuffer();
            sb.append("对账差异记录异常");
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }

    }
}
