package com.pinshang.qingyun.report.model;

import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 品类
 *
 * <AUTHOR>
 *
 * @date 2019.5.28
 */
@Data
@NoArgsConstructor
@Table(name="t_pos_blend_collect_report")
public class BlendCollect extends BasePO {

    /** 客户id */
    private Long storeId;

    /** 门店id */
	private Long shopId;

    /** 日期 */
	private Date collectDate;

    /** 收款项 1: 聚合  2:现金 */
	private Integer collectType;

    /** 实收金额 */
	private BigDecimal collectAmount;

    /** 手续费 */
	private BigDecimal feeAmount;

    /** 创建姓名 */
	private String createName;

}