package com.pinshang.qingyun.report.mapper.settle;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.settle.ShopStockInReportODTO;
import com.pinshang.qingyun.report.dto.settle.ShopStockInReportQueryIDTO;
import com.pinshang.qingyun.report.model.settle.StockInCommodity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface StockInCommodityMapper extends MyMapper<StockInCommodity>{

    List<ShopStockInReportODTO> shopStockInReport(@Param("vo") ShopStockInReportQueryIDTO vo);

    ShopStockInReportODTO shopStockInReportSum(@Param("vo") ShopStockInReportQueryIDTO vo);
}
