package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-01-23
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class ShopActualSummaryODTO {

    @ApiModelProperty(position = 1, value ="门店名称")
    private String shopName;

    @ApiModelProperty(position = 2, value ="现金")
    private BigDecimal cash;

    @ApiModelProperty(position = 3, value ="银行卡")
    private BigDecimal bankCard;

    @ApiModelProperty(position = 4, value ="聚合")
    private BigDecimal unionPay;

    @ApiModelProperty(position = 5, value ="OK")
    private BigDecimal okCard;

    @ApiModelProperty(position = 6, value ="支付宝（记账）")
    private BigDecimal tallyAli;

    @ApiModelProperty(position = 7, value ="微信（记账）")
    private BigDecimal tallyWechat;

    @ApiModelProperty(position = 8, value ="合计")
    private BigDecimal totalAmount;
}