package com.pinshang.qingyun.report.enums;

public enum OrderDeleteTypeEnum {
    HAND_DRIVE(10,"整单取消"),
    AUTOMATIC(20,"挂单未结算"),
    SUSPEND(30,"挂单删除");
    private Integer code;
    private String name;
    OrderDeleteTypeEnum(Integer code,String name){
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
