package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportQueryIDTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportQueryIDTO;
import com.pinshang.qingyun.report.model.settle.StockInCommodity;
import com.pinshang.qingyun.xd.report.dto.CloudCommodityInfoXDReportODTO;
import com.pinshang.qingyun.xd.report.dto.CloudCommodityReportSummaryODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/3/7
 */
@Repository
public interface CloudCommodityReportMapper {

    List<CloudCommodityReportODTO> realDeliveryReport2(@Param("vo") CloudCommodityReportQueryIDTO vo);

    CloudCommodityReportODTO realDeliveryReport2Sum(@Param("vo") CloudCommodityReportQueryIDTO vo);

    void deleteCloudCommodityReportSummary(@Param("orderTime") String orderTime);

    void batchInsertCloudCommodityReportSummary(@Param("summaryList") List<CloudCommodityReportSummaryODTO> items);


    void deleteCloudCommodityInfoReport(@Param("orderTime") String orderTime);
    void batchInsertCloudCommodityInfoReport(@Param("summaryList") List<CloudCommodityInfoXDReportODTO> items);

    List<CloudCommodityInfoReportODTO> cloudCommodityReport(@Param("vo") CloudCommodityInfoReportQueryIDTO vo);
    CloudCommodityInfoReportODTO cloudCommodityReportSum(@Param("vo") CloudCommodityInfoReportQueryIDTO vo);

    List<StockInCommodity> getCloudInReport(@Param("day") String day);
}
