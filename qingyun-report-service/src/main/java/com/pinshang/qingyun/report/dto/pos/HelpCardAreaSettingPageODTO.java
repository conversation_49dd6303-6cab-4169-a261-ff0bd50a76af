package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName HelpCardAreaSettingPageODTO
 * <AUTHOR>
 * @Date 2023/2/17 15:42
 * @Description HelpCardAreaSettingPageODTO
 * @Version 1.0
 */
@Data
public class HelpCardAreaSettingPageODTO {
    @ApiModelProperty("区域设置表主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("开票主体id-公司id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long invoiceSubjectId;

    @ApiModelProperty("抬头")
    private String invoiceTitle;

    @ApiModelProperty("开票主体")
    private String invoiceSubjectName;

    @ApiModelProperty("发票类型 1-普票、2-专票")
    private Integer invoiceType;

    @ApiModelProperty("税号")
    private String taxIdentificationNumber;

    @ApiModelProperty("地址电话")
    private String addressPhone;

    @ApiModelProperty("开户账户")
    private String account;

    @ApiModelProperty("手续费比例")
    private String serviceChargeRatio;

    @ApiModelProperty("状态 0-停用, 1-启用")
    private Integer status;

    @ApiModelProperty("状态")
    private String statusStr;

    @ApiModelProperty("发票类型")
    private String invoiceTypeStr;
}
