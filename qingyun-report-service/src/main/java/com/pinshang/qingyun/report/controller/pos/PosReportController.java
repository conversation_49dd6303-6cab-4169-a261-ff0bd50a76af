package com.pinshang.qingyun.report.controller.pos;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.controller.BaseController;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.annotation.NotAdvice;
import com.pinshang.qingyun.report.client.report.service.PosReportClient;
import com.pinshang.qingyun.report.dto.HandDiscountIDTO;
import com.pinshang.qingyun.report.dto.HandDiscountODTO;
import com.pinshang.qingyun.report.dto.export.CashierDiscountExportRespVo;
import com.pinshang.qingyun.report.dto.export.GuestListSummaryExportRespVo;
import com.pinshang.qingyun.report.dto.export.SalesPromotionExportRespVo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.enums.PosReportGuestListEnum;
import com.pinshang.qingyun.report.enums.PosReportSummaryTypeEnum;
import com.pinshang.qingyun.report.enums.PosReportWithShopEnum;
import com.pinshang.qingyun.report.service.pos.*;
import com.pinshang.qingyun.report.util.ReflectionUtil;
import com.pinshang.qingyun.report.util.ReportUtil;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/posReports")
public class PosReportController extends BaseController {

    @Autowired
    private PosReportService reportService;
    @Autowired
    private CashierDailyService cashierDailyService;
    @Autowired
    private SalesSummaryReportService salesSummaryReportService;

    @Autowired
    private CashierDiscountReportService cashierDiscountReportService;

    @Autowired
    private GuestListSummaryReportService guestListSummaryReportService;

    @Autowired
    private MemberAccountExecptionService memberAccountExecptionService;

    @Autowired
    private PosReportClient posReportClient;

    @Autowired
    private IRenderService renderService;

    /**
     * {
     * 	"pageNo": 1,
     * 	"pageSize": 10,
     * 	"beginTime": "2018-06-01 10:10:10",
     * 	"endTime": "2018-06-05 10:10:10",
     * 	"employeeNumber": "0001",
     * 	"macCode": "003101",
     * 	"shopId": "64",
     * 	"commodityName": "统一茉香.蜜茶",
     * 	"saleType": "1",
     * 	"orderCode": "****************",
     * 	"category1": "996135984224522798",
     * 	"barCode": "123122",
     * 	"discountType": 23
     * }
     * @param salesWaterIDTO
     * @return
     */
    @ApiOperation(value = "销售流水报表", notes = "销售流水报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/salesWater")
    @MethodRender
    public TablePageInfo<SalesWaterODTO> listSalesWaterIDTOReport(SalesWaterIDTO salesWaterIDTO) {
        QYAssert.isTrue(StringUtils.isNotBlank(salesWaterIDTO.getBeginTime()), "请选择销售日期");
        QYAssert.isTrue(StringUtils.isNotBlank(salesWaterIDTO.getEndTime()), "请选择销售日期");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(salesWaterIDTO.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(salesWaterIDTO.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 2, "销售日期的跨度不能超过3天");
        return reportService.listSalesWaterIDTOReport(salesWaterIDTO);
    }

    @ApiOperation(value = "销售流水报表导出", notes = "收银流水报表导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/exportInfo/salesWater")
    public ModelAndView exportSalesWaterIDTOReport (SalesWaterIDTO salesWaterIDTO){
        QYAssert.isTrue(StringUtils.isNotBlank(salesWaterIDTO.getBeginTime()), "请选择销售日期");
        QYAssert.isTrue(StringUtils.isNotBlank(salesWaterIDTO.getEndTime()), "请选择销售日期");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(salesWaterIDTO.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(salesWaterIDTO.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 2, "销售日期的跨度不能超过3天");

        salesWaterIDTO.setPageNo(1);
        salesWaterIDTO.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<SalesWaterODTO> page = reportService.listSalesWaterIDTOReport(salesWaterIDTO);
        renderService.render(page.getList(), "/exportInfo/salesWater");
        //        List<SalesWaterODTO> list = page.getList();
        List<SalesWaterExportODTO> list = BeanCloneUtils.copyTo(page.getList(), SalesWaterExportODTO.class);
        Map<String, List<String>> data=new HashMap<>();


        if(CollectionUtils.isNotEmpty(list) && null != page.getHeader() ){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(10,getFieldValueByName("quantity",page.getHeader()));
            totalMap.put(11,getFieldValueByName("number",page.getHeader()));
            totalMap.put(12,getFieldValueByName("transactionAmount",page.getHeader()));
            totalMap.put(14,getFieldValueByName("retailAmount",page.getHeader()));
            setTotalRow(data, totalMap,ExcelSheetTitleEnum.SALES_WATER_REPORT.getTitles().length);
        }
        ReportUtil.buildData(list,data);

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.SALES_WATER_REPORT, data);
    }



    /**
     * {
     * 	"pageNo": 1,
     * 	"pageSize": 10,
     * 	"beginTime": "2018-06-01 10:10:10",
     * 	"endTime": "2018-06-05 10:10:10",
     * 	"employeeNumber": "0001",
     * 	"macCode": "003901",
     * 	"shopId": "64",
     * 	"saleType": "1",
     * 	"orderCode": "****************",
     * 	"payType": "ZF024"
     * }
     * @param cashierWaterIDTO
     * @return
     */
    @ApiOperation(value = "收银流水报表", notes = "收银流水报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/cashierWater")
    public TablePageInfo<CashierWaterODTO> listCashierWaterReport(CashierWaterIDTO cashierWaterIDTO) {
        QYAssert.isTrue(StringUtils.isNotBlank(cashierWaterIDTO.getBeginTime()), "请选择销售日期");
        QYAssert.isTrue(StringUtils.isNotBlank(cashierWaterIDTO.getEndTime()), "请选择销售日期");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(cashierWaterIDTO.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(cashierWaterIDTO.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 2, "收银日期的跨度不能超过3天");
        TablePageInfo<CashierWaterODTO> pageInfo = reportService.listCashierWaterReport(cashierWaterIDTO);
        return pageInfo;
    }

    @ApiOperation(value = "收银流水报表-导出", notes = "收银流水报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/exportInfo/cashierWater")
    public ModelAndView exportCashierWaterReport (CashierWaterIDTO cashierWaterIDTO){
        QYAssert.isTrue(StringUtils.isNotBlank(cashierWaterIDTO.getBeginTime()), "请选择销售日期");
        QYAssert.isTrue(StringUtils.isNotBlank(cashierWaterIDTO.getEndTime()), "请选择销售日期");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(cashierWaterIDTO.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(cashierWaterIDTO.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 2, "收银日期的跨度不能超过3天");

        cashierWaterIDTO.setPageNo(1);
        cashierWaterIDTO.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<CashierWaterODTO> pageInfo = reportService.listCashierWaterReport(cashierWaterIDTO);
        List<CashierWaterODTO> list = pageInfo.getList();
        Map<String, List<String>> data=new HashMap<>();
        if(CollectionUtils.isNotEmpty(list) && null != pageInfo.getHeader()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(8,getFieldValueByName("payAmount",pageInfo.getHeader()));
            setTotalRow(data, totalMap,ExcelSheetTitleEnum.CASHIER_WATER_REPORT.getTitles().length);
        }
        ReportUtil.buildData(list,data);

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.CASHIER_WATER_REPORT, data);
    }


    //ceshi
   /* @ApiOperation(value = "添加测试数据", notes = "添加测试数据")
    @ApiOperation(value = "添加测试数据", notes = "添加测试数据")
    @RequestMapping(value = "/addTestData", method = RequestMethod.GET)
    public void addTestData(@RequestParam(value="orderCode", required=false) String orderCode) {
        listenerService.salesSummaryReport(orderCode);
    }*/

    /**
     * 应该暂时不用了，转es了
     * @param salesSummaryReportOrderIDTO
     * @return
     */
    @ApiOperation(value = "商品销售汇总报表、类别销售汇总报表", notes = "商品销售汇总报表、类别销售汇总报表")
    @RequestMapping(value = "/commoditySalesSummaryReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @MethodRender
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,0);
    }

    @ApiOperation(value = "门店销售汇总分析报表，类别销售汇总分析报表，商品销售汇总分析报表", notes = "门店销售汇总分析报表，类别销售汇总分析报表，商品销售汇总分析报表")
    @RequestMapping(value = "/commoditySalesSummary2Report", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @MethodRender
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummary2Report(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,1);
    }

    @ApiOperation(value = "商品销售汇总报表、类别销售汇总报表(job)", notes = "商品销售汇总报表、类别销售汇总报表(job)")
    @RequestMapping(value = "/commodityDaySalesSummaryReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean commodityDaySalesSummaryReport(@RequestParam("saleTime") String saleTime){
        salesSummaryReportService.commodityDaySalesSummaryReport(saleTime);
        return Boolean.TRUE;
    }

    @ApiOperation(value = "商品销售汇总，类别销售汇总报表导出", notes = "商品销售汇总，类别销售汇总报表导出")
    @RequestMapping(value = "/exportInfo/commoditySalesSummaryReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "POS_SR_COMMODITY")
    public void commoditySalesSummaryReport(@RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="beginDate", required=false) String beginDate, @RequestParam(value="endDate", required=false) String endDate,
                                                  @RequestParam(value="cateId1", required=false) Long cateId1, @RequestParam(value="cateId2", required=false) Long cateId2, @RequestParam(value="cateId3", required=false) Long cateId3,
                                                  @RequestParam(value="commodityKey", required=false) String commodityKey, @RequestParam(value="barCode", required=false) String barCode,
                                                  @RequestParam(value="summaryType", required=false) Integer summaryType,@RequestParam(value="isWithShop", required=false) Integer isWithShop,@RequestParam(value="commodityId", required=false) Long commodityId,
                                            HttpServletResponse response) throws IOException {

        SalesSummaryReportOrderIDTO idto=new SalesSummaryReportOrderIDTO();
        //设置参数
        setSalesSummaryParam(shopId, beginDate, endDate, cateId1, cateId2, cateId3, commodityKey, barCode, summaryType, isWithShop,null, commodityId, null, null,idto);
        TablePageInfo<SalesSummaryReportOrderODTO> result=salesSummaryReportService.commoditySalesSummaryReport(idto,0);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            renderService.render(list, "/exportInfo/commoditySalesSummaryReport");
            //设置总行
            setCommodityTotalRow(summaryType, isWithShop, result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setDataLst(summaryType, isWithShop, dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode() || summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            fileName="商品销售汇总报表";
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            fileName="类别销售汇总报表";
        }
        ExcelSheetTitleEnum sheetTitle = getExcelSheetTitle(summaryType, isWithShop);

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        fileName = String.format("%s_%s", fileName, new DateTime().toString("yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

        /* 已重构, 后续稳定后删除
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
        */
    }

    private void setCommodityTotalRow(@RequestParam(value = "summaryType", required = false) Integer summaryType, @RequestParam(value = "isWithShop", required = false) Integer isWithShop, TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(10,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGiveAmount",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(17,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(18,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,20);
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(8,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalGiveAmount",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,19);
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(2,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(3,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
    }

    private void setSalesSummaryParam(Long shopId,String beginDate,String endDate,Long cateId1, Long cateId2,Long cateId3,String commodityKey,String barCode,Integer summaryType,Integer isWithShop, Integer isSortByAmount,Long commodityId,Integer shopType,Long provinceId, SalesSummaryReportOrderIDTO idto) {
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(null !=cateId1) {
            idto.setCateId1(cateId1);
        }
        if(null !=cateId2) {
            idto.setCateId2(cateId2);
        }
        if(null !=cateId3) {
            idto.setCateId3(cateId3);
        }
        if(!StringUtils.isEmpty(commodityKey)) {
            idto.setCommodityKey(commodityKey);
        }
        if(!StringUtils.isEmpty(barCode)) {
            idto.setBarCode(barCode);
        }
        if(null !=summaryType) {
            idto.setSummaryType(summaryType);
        }
        if(null !=isWithShop) {
            idto.setIsWithShop(isWithShop);
        }
        if(null !=isSortByAmount) {
            idto.setIsSortByAmount(isSortByAmount);
        }
        if(null !=commodityId) {
            idto.setCommodityId(commodityId);
        }
        if(null != shopType){
            idto.setShopType(shopType);
        }
        if(null != provinceId){
            idto.setProvinceId(provinceId);
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
    }

    private ExcelSheetTitleEnum getExcelSheetTitle(Integer summaryType,Integer isWithShop) {
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_NOSHOP;
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SALES_SUMMARY_WITHSHOP;
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SALES_SUMMARY_NOSHOP;
        }
        return sheetTitle;
    }

    private void setDataLst(Integer summaryType,Integer isWithShop, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
            dataLst.add(dto.getCommodityPrice()+"");
            dataLst.add(dto.getSaleQuantity()+"");
            dataLst.add(dto.getReturnQuantity()+"");
            dataLst.add(dto.getGiveQuantity()+"");
            dataLst.add(dto.getSaleAmount()+"");
            dataLst.add(dto.getReturnAmount()+"");
            dataLst.add(dto.getGiveAmount()+"");
            dataLst.add(dto.getDiscountAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getSaleQuantity()+"");
            dataLst.add(dto.getSaleAmount()+"");
            dataLst.add(dto.getReturnQuantity()+"");
            dataLst.add(dto.getReturnAmount()+"");
            dataLst.add(dto.getGiveQuantity()+"");
            dataLst.add(dto.getDiscountAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
            dataLst.add(dto.getSalesAmountPercent());
        }
    }

    @ApiOperation(value = "门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  报表导出", notes = "门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  报表导出")
    @RequestMapping(value = "/exportInfo/commoditySalesSummaryAnalysisReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "POS_SR_SHOP_COMMODITY")
    public void commoditySalesSummaryAnalysisReport(HttpServletResponse response,
                                                  @RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="beginDate", required=false) String beginDate, @RequestParam(value="endDate", required=false) String endDate,
                                                  @RequestParam(value="cateId1", required=false) Long cateId1, @RequestParam(value="cateId2", required=false) Long cateId2, @RequestParam(value="cateId3", required=false) Long cateId3,
                                                  @RequestParam(value="commodityKey", required=false) String commodityKey, @RequestParam(value="barCode", required=false) String barCode,
                                                  @RequestParam(value="summaryType", required=false) Integer summaryType,@RequestParam(value="isWithShop", required=false) Integer isWithShop,@RequestParam(value="isSortByAmount", required=false) Integer isSortByAmount,@RequestParam(value="commodityId", required=false) Long commodityId) throws IOException {

        SalesSummaryReportOrderIDTO idto=new SalesSummaryReportOrderIDTO();
        //设置参数
        setSalesSummaryParam(shopId, beginDate, endDate, cateId1, cateId2, cateId3, commodityKey, barCode, summaryType, isWithShop,isSortByAmount,commodityId,null,null,idto);
        TablePageInfo<SalesSummaryReportOrderODTO> result=salesSummaryReportService.commoditySalesSummaryReport(idto,1);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            renderService.render(list, "/exportInfo/commoditySalesSummaryAnalysisReport");
            //设置总行
            setCommodityAnalysisTotalRow(summaryType, isWithShop, result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setDataLstAnalysis(summaryType, isWithShop, dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            fileName="商品销售汇总分析报表";
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            fileName="类别销售汇总分析报表";
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode() || summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            fileName="门店销售汇总分析报表";
        }

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        ExcelSheetTitleEnum sheetTitle = getAnalysisExcelSheetTitle(summaryType, isWithShop);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        fileName = String.format("%s_%s", fileName, new DateTime().toString("yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

        /* 已重构, 后续稳定后删除
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
        */
    }
    //门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  导出合计行设置
    private void setCommodityAnalysisTotalRow(Integer summaryType,Integer isWithShop, TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(9,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,15);
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,13);
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(3,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(3,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,9);
        }
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
    }

    private ExcelSheetTitleEnum getAnalysisExcelSheetTitle(Integer summaryType,Integer isWithShop) {
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if( (summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()) && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            sheetTitle=ExcelSheetTitleEnum.SHOP_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            sheetTitle=ExcelSheetTitleEnum.SHOPDAY_SUMMARY_ANALYSIS_NOSHOP;
        }
        return sheetTitle;
    }

    private void setDataLstAnalysis(Integer summaryType,Integer isWithShop, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            dataLst.add(dto.getSaleTime());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()  || summaryType==PosReportSummaryTypeEnum.SIX.getCode() || summaryType==PosReportSummaryTypeEnum.SEVEN.getCode() || ( null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getCateCode());
            dataLst.add(dto.getCommodityFirstName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
        }else{
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
        }
        dataLst.add(dto.getWeightAmount()+"");
        dataLst.add(dto.getGrossprofitmarginAmount()+"");
        dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
        dataLst.add(dto.getNoTaxSaleAmount()+"");
        dataLst.add(dto.getNoTaxWeightAmount()+"");
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getSalesAmountPercent());
        }
    }

    @ApiOperation(value = "畅销品排行报表", notes = "畅销品排行报表")
    @RequestMapping(value = "/bestSellerReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<SalesSummaryReportOrderODTO> bestSellerReport(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.bestSellerReport(salesSummaryReportOrderIDTO);
    }

    @ApiOperation(value = "畅销品排行报表导出", notes = "畅销品排行报表导出")
    @RequestMapping(value = "/exportInfo/bestSellerReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "POS_SR_BEST_SELLER")
    public void bestSellerReport(HttpServletResponse response,
                                           @RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="beginDate", required=false) String beginDate, @RequestParam(value="endDate", required=false) String endDate,
                                                            @RequestParam(value="cateId1", required=false) Long cateId1, @RequestParam(value="cateId2", required=false) Long cateId2, @RequestParam(value="cateId3", required=false) Long cateId3,
                                                            @RequestParam(value="commodityKey", required=false) String commodityKey, @RequestParam(value="barCode", required=false) String barCode,
                                                            @RequestParam(value="summaryType", required=false) Integer summaryType,@RequestParam(value="isWithShop", required=false) Integer isWithShop,
                                                            @RequestParam(value="commodityId", required=false) Long commodityId, @RequestParam(value="shopType", required=false) Integer shopType,
                                                            @RequestParam(value="provinceId", required=false) Long provinceId, @RequestParam(value = "orgCode", required = false) String orgCode) throws IOException {

        SalesSummaryReportOrderIDTO idto=new SalesSummaryReportOrderIDTO();
        //设置参数
        setSalesSummaryParam(shopId, beginDate, endDate, cateId1, cateId2, cateId3, commodityKey, barCode, summaryType, isWithShop,null, commodityId, shopType, provinceId,idto);
        if (null != orgCode) {
            idto.setOrgCode(orgCode);
        }
        TablePageInfo<SalesSummaryReportOrderODTO> pageDate=salesSummaryReportService.bestSellerReport(idto);
        List<SalesSummaryReportOrderODTO> list= pageDate.getList();

        //转excel数据
        List<SalesSummaryReportOrderExcelODTO> excelDtoList = list.parallelStream().map(dtoData -> {
            SalesSummaryReportOrderExcelODTO excelDto = new SalesSummaryReportOrderExcelODTO();
            BeanUtils.copyProperties(dtoData, excelDto);
            return excelDto;
        }).collect(Collectors.toList());
        //设置首行合计数据
        SalesSummaryReportOrderExcelODTO sumExcelDto = new SalesSummaryReportOrderExcelODTO();
        if (pageDate.getHeader() instanceof TotalSalesSummaryODTO){
            TotalSalesSummaryODTO totalSalesSummaryODTO = (TotalSalesSummaryODTO) pageDate.getHeader();
            sumExcelDto.setCommodityFirstName("合计");
            sumExcelDto.setTatalQuantity(totalSalesSummaryODTO.getTotalSaleQuantity());
            sumExcelDto.setTatalAmount(totalSalesSummaryODTO.getTotalSaleAmount());
            sumExcelDto.setWeightAmount(totalSalesSummaryODTO.getTotalSaleWeight());
            sumExcelDto.setGrossprofitmarginAmount(totalSalesSummaryODTO.getTotalGrossprofitmarginAmount());
        }
        excelDtoList.add(0, sumExcelDto);

        //输出excel
        String fileName = String.format("畅销品排行报表_%s", new DateTime().toString(DatePattern.PURE_DATETIME_PATTERN));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        EasyExcel.write(response.getOutputStream(), SalesSummaryReportOrderExcelODTO.class)
                .autoCloseStream(Boolean.TRUE).sheet("畅销品排行").doWrite(excelDtoList);
        /* 已重构; 后续稳定后可删除代码
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",pageDate.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",pageDate.getHeader()));
            totalMap.put(9,getFieldValueByName("totalSaleWeight",pageDate.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGrossprofitmarginAmount",pageDate.getHeader()));
            setTotalRow(data, totalMap,12);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnit());
                dataLst.add(dto.getTatalQuantity()+"");
                dataLst.add(dto.getTatalAmount()+"");
                dataLst.add(dto.getWeightAmount()+"");
                dataLst.add(dto.getGrossprofitmarginAmount()+"");
                dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
                dataLst.add(dto.getAvgSalePrice() == null ? "" : (dto.getAvgSalePrice()+""));
                data.put("key_"+ i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "畅销品排行报表"+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.BEST_SELLS_REPORT);
        map.put("data", data);
        map.put("title", "畅销品排行报表");
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
        */
    }
    @ApiOperation(value = "促销销售报表", notes = "促销销售报表")
    @RequestMapping(value = "/salesPromotionReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<SalesSummaryReportOrderODTO> salesPromotionReport(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return salesSummaryReportService.salesPromotionReport(salesSummaryReportOrderIDTO);
    }

    @ApiOperation(value = "促销销售报表导出", notes = "促销销售报表导出")
    @RequestMapping(value = "/exportInfo/salesPromotionReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "SALES_PROMOTION_REPORT")
    public void salesPromotionReport(@RequestParam(value="shopId", required=false) Long shopId, @RequestParam(value="beginDate", required=false) String beginDate,  @RequestParam(value="endDate", required=false) String endDate,
                                     @RequestParam(value="cateId1", required=false) Long cateId1,  @RequestParam(value="cateId2", required=false) Long cateId2,  @RequestParam(value="cateId3", required=false) Long cateId3,
                                     @RequestParam(value="commodityKey", required=false) String commodityKey,  @RequestParam(value="barCode", required=false) String barCode,
                                     @RequestParam(value="summaryType", required=false) Integer summaryType, @RequestParam(value="isWithShop", required=false) Integer isWithShop,
                                     @RequestParam(value="promotionType", required=false) String promotionType, @RequestParam(value="promotionKey", required=false) String promotionKey,
                                     @RequestParam(value="commodityId", required=false) Long commodityId,@RequestParam(value="shopType", required=false) Integer shopType,
                                     @RequestParam(value="provinceId", required=false) Long provinceId,
                                     HttpServletResponse response) throws IOException {

        SalesSummaryReportOrderIDTO idto=new SalesSummaryReportOrderIDTO();
        if(!StringUtils.isEmpty(promotionType)) {
            idto.setPromotionType(promotionType);
        }
        if(!StringUtils.isEmpty(promotionKey)) {
            idto.setPromotionKey(promotionKey);
        }
        //设置参数
        setSalesSummaryParam(shopId, beginDate, endDate, cateId1, cateId2, cateId3, commodityKey, barCode, summaryType, isWithShop,null, commodityId,shopType, provinceId, idto);
        TablePageInfo<SalesSummaryReportOrderODTO> result = salesSummaryReportService.salesPromotionReport(idto);
        List<SalesSummaryReportOrderODTO> list = result.getList();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            List<SalesPromotionExportRespVo> exportList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                TotalSalesSummaryODTO total = (TotalSalesSummaryODTO) result.getHeader();
                exportList = BeanCloneUtils.copyTo(list, SalesPromotionExportRespVo.class);

                SalesPromotionExportRespVo totalRespVo = new SalesPromotionExportRespVo();
                totalRespVo.setShopName("合计");
                totalRespVo.setTatalQuantity(total.getTotalSaleQuantity());
                totalRespVo.setTatalAmount(total.getTotalSaleAmount());
                totalRespVo.setDiscountAmount(total.getTotalDiscountAmount());
                totalRespVo.setGiveAmount(total.getTotalGiveAmount());
                exportList.add(0,totalRespVo);
            }
            ExcelUtil.setFileNameAndHead(response,  "促销销售报表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream(), SalesPromotionExportRespVo.class)
                    .autoCloseStream(Boolean.TRUE).sheet("促销销售报表").doWrite(exportList);
        }catch (Exception e){
            log.error("促销销售报表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

        /*Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(9,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGiveAmount",result.getHeader()));
            setTotalRow(data, totalMap,13);

            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getCommodityFirstName());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnit());
                dataLst.add(dto.getSalePrice()+"");
                dataLst.add(dto.getTatalQuantity()+"");
                dataLst.add(dto.getTatalAmount()+"");
                dataLst.add(dto.getDiscountAmount()+"");
                dataLst.add(dto.getGiveAmount()+"");
                dataLst.add(dto.getPromotionType());
                data.put("key_"+ i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "促销销售报表"+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.SALES_PROMOTION_REPORT);
        map.put("data", data);
        map.put("title", "促销销售报表");
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);*/
    }

    @ApiOperation(value = "收银员让利报表", notes = "收银员让利报表")
    @RequestMapping(value = "/cashierDiscountReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<CashierDiscountReportOrderODTO> cashierDiscountReport(CashierDiscountReportOrderIDTO cashierDiscountReportOrderIDTO){
        return cashierDiscountReportService.cashierDiscountReport(cashierDiscountReportOrderIDTO);
    }

    @ApiOperation(value = "收银员让利报表导出", notes = "收银员让利报表导出")
    @RequestMapping(value = "/exportInfo/cashierDiscountReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "CASHIER_DISCOUNT_REPORT")
    public void cashierDiscountReport(@RequestParam(value="shopId", required=false) Long shopId, @RequestParam(value="beginDate", required=false) String beginDate,  @RequestParam(value="endDate", required=false) String endDate,
                                       @RequestParam(value="cashierKey", required=false) String cashierKey,
                                       @RequestParam(value="shopType", required=false) Integer shopType,
                                      @RequestParam(value="provinceId", required=false) Long provinceId,
                                       HttpServletResponse response) throws IOException{

        CashierDiscountReportOrderIDTO idto=new CashierDiscountReportOrderIDTO();
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(!StringUtils.isEmpty(cashierKey)) {
            idto.setCashierKey(cashierKey);
        }
        if(null != shopType){
            idto.setShopType(shopType);
        }
        if(null != provinceId){
            idto.setProvinceId(provinceId);
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        //设置参数
        TablePageInfo<CashierDiscountReportOrderODTO> result = cashierDiscountReportService.cashierDiscountReport(idto);
        List<CashierDiscountReportOrderODTO> list = result.getList();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            List<CashierDiscountExportRespVo> exportList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                TotalCashierDiscountODTO total = (TotalCashierDiscountODTO) result.getHeader();
                exportList = BeanCloneUtils.copyTo(list, CashierDiscountExportRespVo.class);

                CashierDiscountExportRespVo totalRespVo = new CashierDiscountExportRespVo();
                totalRespVo.setSaleTime("合计");
                totalRespVo.setRealTotalAmount(total.getTotalFromAmount());
                totalRespVo.setTotalAmount(total.getTotalToAmount());
                totalRespVo.setDiscountAmount(total.getTotalDiscount());
                exportList.add(0,totalRespVo);
            }
            ExcelUtil.setFileNameAndHead(response,  "收银员让利报表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream(), CashierDiscountExportRespVo.class)
                    .autoCloseStream(Boolean.TRUE).sheet("收银员让利报表").doWrite(exportList);
        }catch (Exception e){
            log.error("收银员让利报表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

       /* Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(6,getFieldValueByName("totalFromAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalToAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalDiscount",result.getHeader()));
            setTotalRow(data, totalMap,8);
            for(CashierDiscountReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                dataLst.add(dto.getSaleTime());
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getCashierNo());
                dataLst.add(dto.getCashierName());
                dataLst.add(dto.getRealTotalAmount()+"");
                dataLst.add(dto.getTotalAmount()+"");
                dataLst.add(dto.getDiscountAmount()+"");
                data.put("key_"+ i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "收银员让利报表"+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.CASHIER_DISCOUNT_REPORT);
        map.put("data", data);
        map.put("title", "收银员让利报表");
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);*/
    }

    @ApiOperation(value = "客单分析报表", notes = "客单分析报表")
    @RequestMapping(value = "/guestSummaryReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<GuestListSummaryReportOrderODTO> guestSummaryReport(GuestListSummaryReportOrderIDTO guestListSummaryReportOrderIDTO){
        if(guestListSummaryReportOrderIDTO.getShopId() == null
        && SpringUtil.isEmpty(guestListSummaryReportOrderIDTO.getShopIdList())){
            //全部门店跨度不能超过3天
            int diff = DateUtil.getDayDif(DateUtil.parseDate(guestListSummaryReportOrderIDTO.getEndDate(), "yyyy-MM-dd"), DateUtil.parseDate(guestListSummaryReportOrderIDTO.getBeginDate(), "yyyy-MM-dd"));
            QYAssert.isTrue(diff <= 2, "销售日期的跨度不能超过3天");
        }
        return guestListSummaryReportService.guestSummaryReport(guestListSummaryReportOrderIDTO);
    }

    @ApiOperation(value = "客单分析报表导出", notes = "客单分析报表导出")
    @RequestMapping(value = "/exportInfo/guestSummaryReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "POS_SR_GUEST")
    public void guestSummaryReport(@RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="beginDate", required=false) String beginDate, @RequestParam(value="endDate", required=false) String endDate,
                                              @RequestParam(value="macCode", required=false) String macCode,@RequestParam(value="summaryType", required=false) Integer summaryType,
                                              @RequestParam(value="shopType", required=false) Integer shopType,  @RequestParam(value="provinceId", required=false) Long provinceId,
                                           HttpServletResponse response) throws IOException {

        GuestListSummaryReportOrderIDTO idto=new GuestListSummaryReportOrderIDTO();
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(!StringUtils.isEmpty(macCode)) {
            idto.setMacCode(macCode);
        }
        if(null!=summaryType) {
            idto.setSummaryType(summaryType);
        }
        if(null != shopType){
            idto.setShopType(shopType);
        }
        if(null != provinceId){
            idto.setProvinceId(provinceId);
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        //设置参数
        TablePageInfo<GuestListSummaryReportOrderODTO> result=guestListSummaryReportService.guestSummaryReport(idto);
        List<GuestListSummaryReportOrderODTO> list=result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            if(summaryType== PosReportGuestListEnum.ONE.getCode()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(6,getFieldValueByName("totalGuestQuantity",result.getHeader()));
                totalMap.put(7,getFieldValueByName("totalGuestAmount",result.getHeader()));
                totalMap.put(12,getFieldValueByName("avgGuestPrice",result.getHeader()));
                setTotalRow(data, totalMap,12);
            }
            if(summaryType== PosReportGuestListEnum.TWO.getCode()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(3,getFieldValueByName("totalGuestQuantity",result.getHeader()));
                totalMap.put(4,getFieldValueByName("totalGuestAmount",result.getHeader()));
                totalMap.put(5,getFieldValueByName("avgGuestPrice",result.getHeader()));
                setTotalRow(data, totalMap,5);
            }
            for(GuestListSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                dataLst.add(dto.getSaleTime());
                if(summaryType== PosReportGuestListEnum.ONE.getCode()){
                    dataLst.add(dto.getShopCode());
                    dataLst.add(dto.getShopName());
                    dataLst.add(dto.getMacCode());
                    dataLst.add(dto.getMacName());
                    dataLst.add(dto.getGuestQuantity()+"");
                    dataLst.add(dto.getGuestAmount()+"");
                    dataLst.add(dto.getTotalGuestQuantity()+"");
                    dataLst.add(dto.getTotalGuestAmount()+"");
                    dataLst.add(dto.getAvgAmount());
                    dataLst.add(dto.getAvgQuantity());
                    dataLst.add(dto.getAvgGuestPrice()+"");
                }
                if(summaryType== PosReportGuestListEnum.TWO.getCode()){
                    dataLst.add(dto.getShopName());
                    dataLst.add(dto.getGuestQuantity()+"");
                    dataLst.add(dto.getGuestAmount()+"");
                    dataLst.add(dto.getAvgGuestPrice()+"");
                    dataLst.add(dto.getJointRate()+"");
                }
                data.put("key_"+ i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "客单分析报表"+"_"+ sdf.format(new Date());
        // 参数设置
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportGuestListEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.GUEST_SUMMARY_BY_POS_REPORT;
        }
        if(summaryType ==PosReportGuestListEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.GUEST_SUMMARY_BY_SHOP_REPORT;
        }
        if (sheetTitle == null){
            QYAssert.isFalse("不支持的类型");
        }

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        ExcelUtil.setFileNameAndHead(response,  filename);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

        /* 已重构, 稳定后删除
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", "客单分析报表");
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
        */
    }

    @ApiOperation(value = "客单时段分析报表", notes = "客单时段分析报表")
    @RequestMapping(value = "/guestSummaryHourReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<GuestListSummaryReportOrderODTO> guestSummaryHourReport(GuestListSummaryReportOrderIDTO guestListSummaryReportOrderIDTO){
        return guestListSummaryReportService.guestSummaryHourReport(guestListSummaryReportOrderIDTO);
    }

    @ApiOperation(value = "客单时段分析报表导出", notes = "客单时段分析报表导出")
    @RequestMapping(value = "/exportInfo/guestSummaryHourReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "GUEST_SUMMARY_HOUR_REPORT")
    public void exportStock(@RequestParam(value="shopId", required=false) Long shopId,
                            @RequestParam(value="beginDate", required=false) String beginDate,
                            @RequestParam(value="endDate", required=false) String endDate,
                            @RequestParam(value="shopType", required=false) Integer shopType,
                            @RequestParam(value="provinceId", required=false) Long provinceId,
                            HttpServletResponse response) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

        GuestListSummaryReportOrderIDTO idto=new GuestListSummaryReportOrderIDTO();
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(null != shopType){
            idto.setShopType(shopType);
        }
        if(null != provinceId){
            idto.setProvinceId(provinceId);
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        //设置参数
        TablePageInfo<GuestListSummaryReportOrderODTO> result=guestListSummaryReportService.guestSummaryHourReport(idto);
        List<GuestListSummaryReportOrderODTO> list = result.getList();
        try {
            List<GuestListSummaryExportRespVo> exportList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                TotalGuestListODTO total = (TotalGuestListODTO) result.getHeader();
                exportList = BeanCloneUtils.copyTo(list, GuestListSummaryExportRespVo.class);

                GuestListSummaryExportRespVo totalRespVo = new GuestListSummaryExportRespVo();
                totalRespVo.setHourTimeStr("合计");
                totalRespVo.setGuestQuantity(total.getTotalGuestQuantity());
                totalRespVo.setGuestAmount(total.getTotalGuestAmount());
                totalRespVo.setAvgGuestPrice(total.getAvgGuestPrice());
                exportList.add(0,totalRespVo);
            }
            ExcelUtil.setFileNameAndHead(response,  "客单时段分析报表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream(), GuestListSummaryExportRespVo.class)
                    .autoCloseStream(Boolean.TRUE).sheet("客单时段分析报表").doWrite(exportList);
        }catch (Exception e){
            log.error("客单时段分析报表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

    }


    private void setTotalRow(Map<String, List<String>> data, Map<Integer, String> totalMap,Integer length) {
        List<String> dataLst = new ArrayList<>();
        totalMap.put(1,"合计");
        for(int j=1;j<=length;j++){
            dataLst.add(totalMap.get(j));
        }
        data.put("key_0", dataLst);
    }

    private void setTableHeadRow(Map<String, List<String>> data, Map<Integer, String> totalMap,Integer length) {
        List<String> dataLst = new ArrayList<>();
        for(int j=0;j<=length;j++){
            dataLst.add(totalMap.get(j));
        }
        data.put("key_0", dataLst);
    }


    private String getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value==null?"":value.toString();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return null;
        }
    }

    @ApiOperation(value = "导出收银日报", notes = "导出收银日报")
    @RequestMapping(value = "/exportInfo/cashierDailyExcel", method = RequestMethod.GET)
    public ModelAndView exportInfoCashierDailyExcel(
            @RequestParam(value="shopCode", required=false) String shopCode,
            @RequestParam(value="startTime", required=true) String startTime,
            @RequestParam(value="EndTime", required=true) String EndTime,
            @RequestParam(value="saleNumber", required=false) String saleNumber,
            @RequestParam(value="macCode", required=false) String macCode,
            @RequestParam(value="saleType", required=false) Integer saleType,
            @RequestParam(value="searchCriteria", required=true) String searchCriteria,
            @RequestParam(value="moneyType", required=false) Integer moneyType,
            @RequestParam(value="posType", required=false) Integer posType
    ){
        CashierDailyIDTO cashierDailyIDTO = new CashierDailyIDTO();
        cashierDailyIDTO.setPageNo(1);
        cashierDailyIDTO.setPageSize(Integer.MAX_VALUE);
        if(null!=shopCode) {
            cashierDailyIDTO.setShopCode(shopCode);
        }
        if(null!=startTime) {
            cashierDailyIDTO.setStartTime(startTime+" 00:00:00");
        }
        if(null!=EndTime) {
            cashierDailyIDTO.setEndTime(EndTime+" 23:59:59");
        }
        if(null!=saleNumber) {
            cashierDailyIDTO.setSaleNumber(saleNumber);
        }
        if(null!=macCode) {
            cashierDailyIDTO.setMacCode(macCode);
        }
        if(null!=saleType) {
            cashierDailyIDTO.setSaleType(saleType);
        }
        if(null!=searchCriteria) {
            cashierDailyIDTO.setSearchCriteria(searchCriteria);
        }
        if(null!= moneyType) {
            cashierDailyIDTO.setMoneyType(moneyType);
        }
        if(null!= posType) {
            cashierDailyIDTO.setPosType(posType);
        }
        List<PayTypeODTO> payTypeList = cashierDailyService.getPayTypeList();
        cashierDailyIDTO.setPayTypeList(payTypeList);

        TablePageInfo<CashierDailyODTO> ss = cashierDailyService.selectCashierDailyList(cashierDailyIDTO);
        ViewExcel viewExcel = null;
        Map<String, Object> map = new HashMap<>();
        if("0".equals(searchCriteria) || cashierDailyIDTO.getSearchCriteria().equals("1")){
            //收银日报
            Map<String, List<String>> data = new HashMap<>();
            List<String> dataList = new ArrayList<>();
            int i = 1;
            List<CashierDailyODTO> list =ss.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();

                // 合计行
                totalMap.put(0, "合计");
                List<TableColumnODTO> tableColumns = ss.getTableColumns();
                for (int index = 1; index < tableColumns.size(); index++) {
                    totalMap.put(index, StringUtil.changeToString(tableColumns.get(index).getSum()));
                }

                setTableHeadRow(data, totalMap,tableColumns.size());

                // 数据行
                for (CashierDailyODTO dto : list) {
                    dataList = new ArrayList<>();
                    dataList.add(dto.getShopCode());
                    dataList.add(dto.getShopName());
                    if ("1".equals(searchCriteria)) {
                        dataList.add(dto.getPosTypeName());
                    }
                    dataList.add(dto.getSaleNumber());
                    dataList.add(dto.getSaleName());
                    if ("0".equals(searchCriteria)) {
                        dataList.add(dto.getPosTypeName());
                        dataList.add(dto.getMacCode());
                        dataList.add(dto.getSaleType());
                    }

                    dataList.add(dto.getTotal());

                    // 循环每一行数据的所有支付方式
                    Map<String, Object> itemMap = ReflectionUtil.getKeyAndValue(dto);
                    for (PayTypeODTO payType : payTypeList) {
                        String value = (String) itemMap.get(payType.getPayTypeCode());
                        dataList.add(value);
                    }

                    data.put("key_"+ i++, dataList);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = null;
            ExcelSheetTitleEnum excelSheetTitle = null;
            if ("0".equals(searchCriteria)) {
                filename = "收银日报按POS机"+"_"+ sdf.format(new Date());
                excelSheetTitle = addAllPayTypeNameToTitles(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_POS_DY_QUERY, payTypeList, searchCriteria);
                map.put("title", "收银日报按POS机");
            } else {
                filename = "收银日报按收银员汇总"+"_"+ sdf.format(new Date());
                map.put("title", "收银日报按收银员汇总");
                excelSheetTitle = addAllPayTypeNameToTitles(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_DY_QUERY, payTypeList, searchCriteria);
                map.put("sheetTitle", excelSheetTitle);
            }
            map.put("sheetTitle", excelSheetTitle);

            // 参数设置
            map.put("filename", filename);
            map.put("data", data);
            viewExcel = new ViewExcel();
        }else if(cashierDailyIDTO.getSearchCriteria().equals("2")){ //---------------------------
            //收银日报按门店
            Map<String, List<String>> data = new HashMap<>();
            List<String> dataList = new ArrayList<>();
            int i = 1;
            List<CashierDailyODTO> list =ss.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(0, "合计");
                List<TableColumnODTO> tableColumns = ss.getTableColumns();
                for (int index = 1; index < tableColumns.size(); index++) {
                    totalMap.put(index, StringUtil.changeToString(tableColumns.get(index).getSum()));
                }
                setTableHeadRow(data, totalMap,tableColumns.size());

                for (CashierDailyODTO dto : list) {
                    dataList = new ArrayList<>();
                    dataList.add(dto.getShopCode());
                    dataList.add(dto.getShopName());
                    dataList.add(dto.getPosTypeName());
                    dataList.add(dto.getTotal());

                    // 循环每一行数据的所有支付方式
                    Map<String, Object> itemMap = ReflectionUtil.getKeyAndValue(dto);
                    for (PayTypeODTO payType : payTypeList) {
                        String value = (String) itemMap.get(payType.getPayTypeCode());
                        dataList.add(value);
                    }
                    data.put("key_"+ i++, dataList);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = "收银日报按门店"+"_"+ sdf.format(new Date());
            // 参数设置
            map.put("filename", filename);
            map.put("sheetTitle", addAllPayTypeNameToTitles(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_SHOP_DY_QUERY, payTypeList, searchCriteria));
            map.put("data", data);
            map.put("title", "收银日报按门店");
            viewExcel = new ViewExcel();
        }else if(cashierDailyIDTO.getSearchCriteria().equals("3")){
            //收银日报按日期
            Map<String, List<String>> data = new HashMap<>();
            List<String> dataList = new ArrayList<>();
            int i = 1;
            List<CashierDailyODTO> list =ss.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(0, "合计");
                List<TableColumnODTO> tableColumns = ss.getTableColumns();
                for (int index = 1; index < tableColumns.size(); index++) {
                    totalMap.put(index, StringUtil.changeToString(tableColumns.get(index).getSum()));
                }
                setTableHeadRow(data, totalMap,tableColumns.size());

                for (CashierDailyODTO dto : list) {
                    dataList = new ArrayList<>();
                    dataList.add(dto.getShopCode());
                    dataList.add(dto.getShopName());
                    dataList.add(dto.getCreateTime());
                    dataList.add(dto.getPosTypeName());
                    dataList.add(dto.getTotal());

                    // 循环每一行数据的所有支付方式
                    Map<String, Object> itemMap = ReflectionUtil.getKeyAndValue(dto);
                    for (PayTypeODTO payType : payTypeList) {
                        String value = (String) itemMap.get(payType.getPayTypeCode());
                        dataList.add(value);
                    }

                    data.put("key_"+ i++, dataList);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = "收银日报按日期"+"_"+ sdf.format(new Date());
            map.put("filename", filename);
            map.put("sheetTitle", addAllPayTypeNameToTitles(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_DATE_DY_QUERY, payTypeList, searchCriteria));
            map.put("data", data);
            map.put("title", "收银日报按日期");
            viewExcel = new ViewExcel();
        }else if(cashierDailyIDTO.getSearchCriteria().equals("4")){
            //收银日报按付款方式
            Map<String, List<String>> data = new HashMap<>();
            List<String> dataLst = new ArrayList<>();
            int i = 1;
            List<CashierDailyODTO> list =ss.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(8,getFieldValueByName("payAmount",ss.getHeader()));
                totalMap.put(9,getFieldValueByName("salesAmount",ss.getHeader()));
                totalMap.put(10,getFieldValueByName("refundAmount",ss.getHeader()));
                setTotalRow(data, totalMap,10);

                for (CashierDailyODTO dto : list) {
                    dataLst = new ArrayList<>();
                    dataLst.add(dto.getShopCode());
                    dataLst.add(dto.getShopName());
                    dataLst.add(dto.getPosTypeName());
                    dataLst.add(dto.getSaleNumber());
                    dataLst.add(dto.getSaleName());
                    dataLst.add(dto.getSaleType());
                    dataLst.add(dto.getPayType());
                    dataLst.add(dto.getPayAmount());
                    dataLst.add(dto.getSalesAmount());
                    dataLst.add(dto.getRefundAmount());
                    data.put("key_"+ i++, dataLst);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = "收银日报按付款方式"+"_"+ sdf.format(new Date());
            // 参数设置
            map.put("filename", filename);
            map.put("sheetTitle", ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_PAYMENT);
            map.put("data", data);
            map.put("title", "收银日报按付款方式");
            viewExcel = new ViewExcel();
        }
        return new ModelAndView(viewExcel, map);
    }//posReport/CollectingSilver/cashierReconciledExcel
    @ApiOperation(value = "收银员对账导出", notes = "收银员对账导出")
    @RequestMapping(value = "/exportInfo/cashierReconciledExcel", method = RequestMethod.GET)
    public ModelAndView exportInfoCashierReconciledExcel(
            @RequestParam(value="shopCode", required=false) String shopCode,
            @RequestParam(value="startTime", required=true) String startTime,
            @RequestParam(value="EndTime", required=true) String EndTime,
            @RequestParam(value="saleNumber", required=false) String saleNumber,
            @RequestParam(value="payType", required=false) String payType,
            @RequestParam(value="sumOfMoney", required=false) String sumOfMoney,
            @RequestParam(value="searchCriteria", required=true) String searchCriteria,
            @RequestParam(value="saleType", required=false) Integer saleType,
            @RequestParam(value="sourceType", required=false) Integer sourceType,
            @RequestParam(value="posType", required=false) Integer posType
    ){
        CashierReconciledIDTO cashierReconciledIDTO = new CashierReconciledIDTO();
        cashierReconciledIDTO.setPageNo(1);
        cashierReconciledIDTO.setPageSize(Integer.MAX_VALUE);
        if(null!=shopCode) {
            cashierReconciledIDTO.setShopCode(shopCode);
        }
        if(null!=startTime) {
            cashierReconciledIDTO.setStartTime(startTime+" 00:00:00");
        }
        if(null!=EndTime) {
            cashierReconciledIDTO.setEndTime(EndTime+" 23:59:59");
        }
        if(null!=saleNumber) {
            cashierReconciledIDTO.setSaleNumber(saleNumber);
        }
        if(null!=payType) {
            cashierReconciledIDTO.setPayType(payType);
        }
        if(null!=sumOfMoney) {
            cashierReconciledIDTO.setSumOfMoney(sumOfMoney);
        }
        if(null!=searchCriteria) {
            cashierReconciledIDTO.setSearchCriteria(searchCriteria);
        }
        if(null!=saleType) {
            cashierReconciledIDTO.setSaleType(saleType);
        }
        if(null!=sourceType) {
            cashierReconciledIDTO.setSourceType(sourceType);
        }
        if(null!=posType) {
            cashierReconciledIDTO.setPosType(posType);
        }

        ViewExcel viewExcel = null;
        Map<String, Object> map = new HashMap<>();
        SimpleDateFormat ssf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if(cashierReconciledIDTO.getSearchCriteria().equals("1")){
            //收银员对账
            TablePageInfo<CashierReconciledODTO> result = cashierDailyService.selectCashierReconciledList(cashierReconciledIDTO);
            Map<String, List<String>> data = new HashMap<>();
            List<String> dataLst = new ArrayList<>();
            int i = 1;
            List<CashierReconciledODTO> list =result.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(8,getFieldValueByName("payAmount",result.getHeader()));
                totalMap.put(9,getFieldValueByName("salesAmount",result.getHeader()));
                totalMap.put(10,getFieldValueByName("saleCount",result.getHeader()));
                totalMap.put(11,getFieldValueByName("refundAmount",result.getHeader()));
                totalMap.put(12,getFieldValueByName("returnCount",result.getHeader()));
                setTotalRow(data, totalMap,12);

                for (CashierReconciledODTO dto : list) {
                    dataLst = new ArrayList<>();
                    dataLst.add(dto.getShopCode());
                    dataLst.add(dto.getShopName());
                    dataLst.add(dto.getSaleNumber());
                    dataLst.add(dto.getSaleName());
                    dataLst.add(dto.getPosTypeName());
                    dataLst.add(dto.getMacCode());
                    dataLst.add(dto.getPayTypeName());
                    dataLst.add(dto.getPayAmount());
                    dataLst.add(dto.getSalesAmount());
                    dataLst.add(dto.getSaleCount()+"");
                    dataLst.add(dto.getRefundAmount());
                    dataLst.add(dto.getReturnCount()+"");
                    data.put("key_"+ i++, dataLst);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = "收银员对账"+"_"+ sdf.format(new Date());
            // 参数设置
            map.put("filename", filename);
            map.put("sheetTitle", ExcelSheetTitleEnum.CASHIER_RECONCILED_LIST);
            map.put("data", data);
            map.put("title", "收银员对账");
            viewExcel = new ViewExcel();
        }else if(cashierReconciledIDTO.getSearchCriteria().equals("2")){
            //收银员对账--对账记录
            TablePageInfo<CashierReconciledODTO> result = cashierDailyService.selectAccountRecordList(cashierReconciledIDTO);
            JSONObject jsonObject = JSONObject.parseObject(result.getHeader().toString().replaceAll("=",":"));

            Map<String, List<String>> data = new HashMap<>();
            List<String> dataLst = new ArrayList<>();
            int i = 1;
            List<CashierReconciledODTO> list =result.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(11,jsonObject.getString("totalAmount"));
                totalMap.put(12,jsonObject.getString("cashTotalAmount"));

                totalMap.put(13,jsonObject.getString("bankTotalAmount"));
                totalMap.put(14,jsonObject.getString("okTotalAmount"));
                totalMap.put(15,jsonObject.getString("chargeConvergeTotalAmount"));
                totalMap.put(16,jsonObject.getString("chargeSdsTotalAmount"));
                totalMap.put(17,jsonObject.getString("convergeTotalAmount"));
                totalMap.put(18,jsonObject.getString("digitalCurrencyAmount"));
                totalMap.put(19,jsonObject.getString("sdsTotalAmount"));

                totalMap.put(20,jsonObject.getString("joyPayAmout"));
                totalMap.put(21,jsonObject.getString("okPayAmount"));

                totalMap.put(22,jsonObject.getString("jfTotalAmount"));
                totalMap.put(23,jsonObject.getString("moLinTotalAmount"));

                totalMap.put(24,jsonObject.getString("qmCardAmount"));
                totalMap.put(25,jsonObject.getString("qmPayAmount"));
                totalMap.put(26,jsonObject.getString("helpCardAmount"));
                totalMap.put(27,jsonObject.getString("qmAccountAmount"));

                totalMap.put(28,jsonObject.getString("swapTicketAmount"));

                totalMap.put(29,jsonObject.getString("giftCardTotalAmount"));
                totalMap.put(30,jsonObject.getString("giftCardConvergeTotalAmount"));
                totalMap.put(31,jsonObject.getString("giftCardCashTotalAmount"));

                totalMap.put(32,jsonObject.getString("consignmentAmount"));
                totalMap.put(33,jsonObject.getString("consignmentAggregationAmount"));
                totalMap.put(34,jsonObject.getString("consignmentCashAmount"));

                totalMap.put(35,jsonObject.getString("totalCashAmount"));

                setTotalRow(data, totalMap,35);

                for (CashierReconciledODTO dto : list) {
                    dataLst = new ArrayList<>();
                    dataLst.add(dto.getShopCode());
                    dataLst.add(dto.getShopName());
                    dataLst.add( dto.getPosTypeName());
                    dataLst.add( dto.getPosMacId());
                    dataLst.add(dto.getSaleNumber());
                    dataLst.add(dto.getSaleName());
                    dataLst.add(ssf.format(dto.getCreateTime()));
                    dataLst.add(dto.getSourceType());
                    dataLst.add(ssf.format(dto.getBeginTime()));
                    dataLst.add(ssf.format(dto.getEndTime()));
                    dataLst.add(null == dto.getTotalAmount() ? "" : dto.getTotalAmount()+"");
                    dataLst.add(null == dto.getCashTotalAmount() ? "" : dto.getCashTotalAmount()+"");

                    dataLst.add(null == dto.getBankTotalAmount() ? "" : dto.getBankTotalAmount()+"");
                    dataLst.add(null == dto.getOkTotalAmount() ? "" : dto.getOkTotalAmount()+"");
                    dataLst.add(null == dto.getChargeConvergeTotalAmount() ? "" : dto.getChargeConvergeTotalAmount()+"");
                    dataLst.add(null == dto.getChargeSdsTotalAmount() ? "" : dto.getChargeSdsTotalAmount()+"");
                    dataLst.add(null == dto.getConvergeTotalAmount() ? "" : dto.getConvergeTotalAmount()+"");
                    dataLst.add(null == dto.getDigitalCurrencyAmount() ? "" : dto.getDigitalCurrencyAmount()+"");
                    dataLst.add(null == dto.getSdsTotalAmount() ? "" : dto.getSdsTotalAmount()+"");

                    dataLst.add(null == dto.getJoyPayAmout() ? "" : dto.getJoyPayAmout()+"");
                    dataLst.add(null == dto.getOkPayAmount() ? "" : dto.getOkPayAmount()+"");

                    dataLst.add(null == dto.getJfTotalAmount() ? "" : dto.getJfTotalAmount()+"");
                    dataLst.add(null == dto.getMoLinTotalAmount() ? "" : dto.getMoLinTotalAmount()+"");

                    dataLst.add(null == dto.getQmCardAmount() ? "" : dto.getQmCardAmount()+"");
                    dataLst.add(null == dto.getQmPayAmount() ? "" : dto.getQmPayAmount()+"");
                    dataLst.add(null == dto.getHelpCardAmount() ? "" : dto.getHelpCardAmount()+"");
                    dataLst.add(null == dto.getQmAccountAmount() ? "" : dto.getQmAccountAmount()+"");
                    dataLst.add(null == dto.getSwapTicketAmount() ? "" : dto.getSwapTicketAmount()+"");

                    dataLst.add(null == dto.getGiftCardTotalAmount() ? "" : dto.getGiftCardTotalAmount()+"");
                    dataLst.add(null == dto.getGiftCardConvergeTotalAmount() ? "" : dto.getGiftCardConvergeTotalAmount()+"");
                    dataLst.add(null == dto.getGiftCardCashTotalAmount() ? "" : dto.getGiftCardCashTotalAmount()+"");

                    dataLst.add(null == dto.getConsignmentAmount() ? "" : dto.getConsignmentAmount()+"");
                    dataLst.add(null == dto.getConsignmentAggregationAmount() ? "" : dto.getConsignmentAggregationAmount()+"");
                    dataLst.add(null == dto.getConsignmentCashAmount() ? "" : dto.getConsignmentCashAmount()+"");

                    dataLst.add(dto.getTotalCashAmount()+"");
                    data.put("key_"+ i++, dataLst);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = "前台收银对账记录"+"_"+ sdf.format(new Date());
            // 参数设置
            map.put("filename", filename);
            map.put("sheetTitle", ExcelSheetTitleEnum.ACCOUNT_RECORD_LIST);
            map.put("data", data);
            map.put("title", "前台收银对账记录");
            viewExcel = new ViewExcel();
        }else if(cashierReconciledIDTO.getSearchCriteria().equals("3")){
            //收银员对账--日对账
            TablePageInfo<CashierReconciledODTO> result = cashierDailyService.selectDiurnalReconciliationList(cashierReconciledIDTO);
            Map<String, List<String>> data = new HashMap<>();
            List<String> dataLst = new ArrayList<>();
            int i = 1;
            List<CashierReconciledODTO> list =result.getList();
            if(null !=list && !list.isEmpty()){
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(8,getFieldValueByName("salesAmount",result.getHeader()));
                setTotalRow(data, totalMap,8);

                for (CashierReconciledODTO dto : list) {
                    dataLst = new ArrayList<>();
                    dataLst.add(dto.getShopCode());
                    dataLst.add(dto.getShopName());
                    dataLst.add(dto.getNewDate());
                    dataLst.add(dto.getSaleNumber());
                    dataLst.add(dto.getSaleName());
                    dataLst.add(dto.getSaleType());
                    dataLst.add(dto.getPayType());
                    dataLst.add(dto.getSalesAmount());
                    data.put("key_"+ i++, dataLst);
                }
            }
            // 文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String filename = "收银员日对账"+"_"+ sdf.format(new Date());
            // 参数设置
            map.put("filename", filename);
            map.put("sheetTitle", ExcelSheetTitleEnum.DIURNAL_RECONCILIATION_LIST);
            map.put("data", data);
            map.put("title", "收银员日对账");
            viewExcel = new ViewExcel();
        }
        return new ModelAndView(viewExcel, map);
    }

    private ExcelSheetTitleEnum addAllPayTypeNameToTitles(ExcelSheetTitleEnum excelSheetTitle, List<PayTypeODTO> payTypeList, String searchCriteria) {
        List<String> titleList = new ArrayList<String>(Arrays.asList(excelSheetTitle.getTitles()));

        payTypeList.forEach(item -> {
            titleList.add(item.getPayTypeName());
        });

        String[] titles = new String[titleList.size()];
        titleList.toArray(titles);

        ExcelSheetTitleEnum sheetTitle = null;
        if ("0".equals(searchCriteria)) {
            sheetTitle = ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_POS_DY_REPORT;
        } else if ("1".equals(searchCriteria)) {
            sheetTitle = ExcelSheetTitleEnum.CASHIER_DAILY_LIST_DY_REPORT;
        } else if ("2".equals(searchCriteria)) {
            sheetTitle = ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_SHOP_DY_REPORT;
        } else if ("3".equals(searchCriteria)) {
            sheetTitle = ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_DATE_DY_REPORT;
        }
        sheetTitle.setTitles(titles);

        return sheetTitle;
    }

    @ApiOperation(value = "会员号异常使用报表", notes = "会员号异常使用报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/memeberAccountExecptionUser")
    public PageInfo<MemberExceptionUseODTO> memeberAccountExecptionUser(MemberExceptionUseIDTO idto) {
        PageInfo<MemberExceptionUseODTO> pageInfo = memberAccountExecptionService.listMemeberAccountExecptionUser(idto);

        return pageInfo;
    }


    @ApiOperation(value = "导出会员号异常使用报表", notes = "会员号异常使用报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/exportInfo/exportMemeberAccountExecptionUser")
    public ModelAndView exportMemeberAccountExecptionUser (MemberExceptionUseIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        PageInfo<MemberExceptionUseODTO> pageInfo = memberAccountExecptionService.listMemeberAccountExecptionUser(idto);
        List<MemberExceptionUseODTO> list = pageInfo.getList();
        List<MemberExceptionUseExport> exportList = new ArrayList<>();
        MemberExceptionUseExport export = null;
        for (MemberExceptionUseODTO memberExceptionUseODTO : list) {
            export = BeanCloneUtils.copyTo(memberExceptionUseODTO, MemberExceptionUseExport.class);
            export.setCashDate(DateUtil.get4yMd(memberExceptionUseODTO.getCashDate()));
            exportList.add(export);
        }
        Map<String, List<String>> data=new HashMap<>();
        ReportUtil.buildData(exportList,data);

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.MEMBER_ACCOUNT_EXECEPTION_USER_REPORT, data);
    }


    /**
     * 收银流水日汇总(job)
     * @param saleTime
     */
    @ApiOperation(value = "收银流水日汇总(job)", notes = "收银流水日汇总(job)")
    @RequestMapping(value = "/cashierWaterDayReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean cashierWaterDayReport(@RequestParam("saleTime") String saleTime){
        reportService.cashierWaterDayReport(saleTime);
        return Boolean.TRUE;
    }

    /**
     *查询收银流水日汇总(收银月报使用)
     * @param beginTime
     * @param endTime
     * @param type: 1,对账总表  0，对账明细
     * @return
     */
    @NotAdvice
    @ApiOperation(value = "查询收银流水日汇总(收银月报使用)", notes = "查询收银流水日汇总(收银月报使用)")
    @RequestMapping(value = "/queryCashierWaterDayReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CashierWaterDayReportODTO> queryCashierWaterDayReport(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime,@RequestParam("type") Integer type){
        return reportService.queryCashierWaterDayReport(shopId,beginTime,endTime,type);
    }

    @ApiOperation(value = "查询单品删除", notes = "查询单品删除", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/signalCommodityDelete")
    public TablePageInfo<CommodityDeleteODTO> signalCommodityDelete(CommodityDeleteIDTO idto) {
        return reportService.signalCommodityDelete(idto);
    }

    @ApiOperation(value = "查询单品删除-导出excel", notes = "")
    @FileCacheQuery(bizCode = "POS_ERROR_COMMODITY_DEL")
    @GetMapping("/exportInfo/signalCommodityDelete")
    public void exportExcelSignalCommodityDelete(CommodityDeleteIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        reportService.exportExcelSignalCommodityDelete(idto, response);
    }

    @ApiOperation(value = "查询单品删除统计", notes = "查询单品删除统计", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/signalCommodityDeleteTotal")
    public TablePageInfo<CommodityDeleteODTO> signalCommodityDeleteTotal(CommodityDeleteIDTO idto) {
        return reportService.signalCommodityDeleteTotal(idto);
    }

    @ApiOperation(value = "查询单品删除统计报表", notes = "查询单品删除统计报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/signalCommodityDeleteReport")
    public TablePageInfo<CommodityDeleteODTO> signalCommodityDeleteReport(CommodityDeleteIDTO idto) {
        idto.setPageSize(Integer.MAX_VALUE);
        return reportService.signalCommodityDeleteTotal(idto);
    }

    @ApiOperation(value = "手动优惠报表", notes = "手动优惠报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/handDiscountReport")
    public TablePageInfo<HandDiscountODTO> handDiscountReport(HandDiscountIDTO idto) {
        if(idto.getCasherId() == null){
            String dateFormate = "yyyy-MM-dd";
            QYAssert.isTrue(StringUtils.isNotBlank(idto.getBeginTime()) && StringUtils.isNotBlank(idto.getEndTime()), "请选择收银日期");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), dateFormate), DateUtil.parseDate(idto.getBeginTime(), dateFormate));
            QYAssert.isTrue(diff <= 91, "收银日期的跨度不能超过92天");
        }

        TablePageInfo<HandDiscountODTO> pageInfo = reportService.handDiscountReport(idto);

        return pageInfo;
    }


    @ApiOperation(value = "pos称重商品价格报表", notes = "pos称重商品价格报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/weightPrice")
    public TablePageInfo<WeightPriceODTO> listWeightPriceReport(WeightPriceIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (idto.getShopId() == null) {
            idto.setShopId(tokenInfo.getShopId());
        }
        return reportService.listWeightPriceReport(idto);
    }


    @ApiOperation(value = "门店自称重高价统计", notes = "门店自称重高价统计", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/weightOverPrice")
    public TablePageInfo<WeightOverPriceODTO> listWeightOverPriceReport(WeightPriceIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (idto.getShopId() == null) {
            idto.setShopId(tokenInfo.getShopId());
        }
        return reportService.listWeightOverPriceReport(idto);
    }
    @ApiOperation(value = "门店自称重高价统计(明细)", notes = "门店自称重高价统计(明细)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/weightOverPriceDetail")
    public List<WeightOverPriceODTO> listWeightOverPriceReportDetail(@RequestParam("shopId") Long shopId,@RequestParam("beginTime")  String beginTime,@RequestParam("endTime")  String endTime) {
        return reportService.listWeightOverPriceReportDetail(shopId,beginTime,endTime);
    }


    @ApiOperation(value = "手动优惠报表明细", notes = "手动优惠报表明细", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/handDiscountDetail")
    public TablePageInfo<HandDiscountDetailODTO> handDiscountDetailReport(HandDiscountDetailIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (tokenInfo.getShopId() != null) {
            idto.setShopId(tokenInfo.getShopId());
        }
        return reportService.handDiscountDetailReport(idto);
    }
    @ApiOperation(value = "手动优惠报表明细导出", notes = "手动优惠报表明细导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/exportInfo/handDiscountDetail")
    public ModelAndView exportHandDiscountDetailReport (HandDiscountDetailIDTO idto){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (tokenInfo.getShopId() != null) {
            idto.setShopId(tokenInfo.getShopId());
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<HandDiscountDetailODTO> page = reportService.handDiscountDetailReport(idto);
        List<HandDiscountDetailODTO> list = page.getList();

        List<HandDiscountDetailExportODTO> exportList = new ArrayList<>();
        Map<String, List<String>> data = new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            for(HandDiscountDetailODTO odto : list){
                HandDiscountDetailExportODTO exportOdto = new HandDiscountDetailExportODTO();
                BeanUtils.copyProperties(odto,exportOdto);
                exportOdto.setCashierTime(odto.getCashierTimeStr());
                exportOdto.setHandleDiscountTypeName(odto.getHandDiscountTypeName());
                exportList.add(exportOdto);
            }

            Map<Integer,String> totalMap = new HashMap<>();
            totalMap.put(15,getFieldValueByName("quantity",page.getHeader()));
            totalMap.put(16,getFieldValueByName("retailAmount",page.getHeader()));
            totalMap.put(18,getFieldValueByName("handleDiscountAmount",page.getHeader()));
            totalMap.put(19,getFieldValueByName("transactionAmount",page.getHeader()));
            totalMap.put(20,getFieldValueByName("handleRetailPercent",page.getHeader()));
            setTotalRow(data, totalMap,ExcelSheetTitleEnum.HANDLE_DISCOUNT_DETAIL_REPORT.getTitles().length);
        }
        ReportUtil.buildData(exportList,data);

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.HANDLE_DISCOUNT_DETAIL_REPORT, data);
    }


    @ApiOperation(value = "云超配货>查询大类销售额", notes = "云超配货>查询大类销售额", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/queryPosCateSummary")
    public List<CloudAllocationODTO> queryPosCateSummary(@RequestBody CloudAllocationIDTO idto) {
        return reportService.queryPosCateSummary(idto);
    }


    @ApiOperation(value = "订货参考>查询pos销售数量", notes = "订货参考>查询pos销售数量", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/queryPosSalesWaterData")
    public List<PosSaleWaterODTO> queryPosSalesWaterData(@RequestBody PosSaleWaterQueryIDTO idto) {
        return reportService.queryPosSalesWaterData(idto);
    }

    @ApiOperation(value = "商品销售汇总和收银流水金额对比(job)", notes = "商品销售汇总和收银流水金额对比(job)")
    @RequestMapping(value = "/comparePosReportAmount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean comparePosReportAmount(@RequestParam("saleTime") String saleTime){
        salesSummaryReportService.comparePosReportAmount(saleTime);
        return Boolean.TRUE;
    }

    /**
     * 云集-结算数据
     *提供代销商类型订单数据：
     * 3、POS线下订单，成交金额（包含退货，退货成交金额为负）
     */
    @PostMapping("/queryConsignmentOrderSettle")
    @ApiOperation(value = "云集-结算数据,查询订单信息", notes = "云集-结算数据,查询订单信息")
    public List<ConsignmentOrderODTO> queryConsignmentOrderSettle(@RequestBody ConsignmentOrderIDTO idto){
        return reportService.queryConsignmentOrderSettle(idto);
    }


    /**
     * 会员号异常使用、手动优惠报表(job)
     * @param saleTime
     */
    @ApiOperation(value = "会员号异常使用、手动优惠报表(job)", notes = "会员号异常使用、手动优惠报表(job)")
    @RequestMapping(value = "/exceptionHandleReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean exceptionHandleReport(@RequestParam("saleTime") String saleTime){
        return memberAccountExecptionService.exceptionHandleReport(saleTime);
    }

    /**
     * 查询t_pos_report_sales_water 销售流水报表某天特定类型商品的变动总数量
     * 查询参数，日期、商品特定分类
     */
    @PostMapping("/queryPosReportShopCommoditySumQuantity")
    @ApiOperation(value = "销售流水报表某天特定类型商品的变动总数量", notes = "销售流水报表某天特定类型商品的变动总数量")
    public List<InventoryCompPosReportODTO> queryPosReportShopCommoditySumQuantity(@RequestBody InventoryCompPosReportIDTO idto){
        return reportService.queryPosReportShopCommoditySumQuantity(idto);
    }


}
