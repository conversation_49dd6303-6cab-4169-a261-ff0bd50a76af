package com.pinshang.qingyun.report.dto.pos.joinShop;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SingleProductSalesExportDTO {

    @ExcelProperty("大类")
    private String commodityFirstName;

    @ExcelProperty("中类")
    private String commoditySecondName;

    @ExcelProperty("小类")
    private String commodityThirdName;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("条码")
    private String barCode;

    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ApiModelProperty("销售数量 = 销售数量合计 + 赠品数量合计 - 退货数量合计")
    @ExcelProperty("销售数量")
    private BigDecimal saleQuantity;

    @ApiModelProperty("销售额 = 销售成交金额合计-退货金额合计")
    @ExcelProperty("销售额")
    private BigDecimal saleAmount;

    @ApiModelProperty("原价金额 = 销售的原价金额合计-退货的原价金额合计")
    @ExcelProperty("原价金额")
    private BigDecimal commodityPriceAmount;

    @ApiModelProperty("折扣额 = 原价金额合计-成交金额合计")
    @ExcelProperty("折扣额")
    private BigDecimal discountAmount;

    @ApiModelProperty("折扣率 = 折扣额/原价金额，%格式，%号前面四舍五入进位 保留两个小数")
    @ExcelProperty("折扣率")
    private String discountRate;
}
