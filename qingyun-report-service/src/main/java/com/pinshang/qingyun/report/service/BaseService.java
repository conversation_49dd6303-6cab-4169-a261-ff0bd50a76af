package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.report.model.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * Created by crell
 * 2018/5/7
 */
public abstract class BaseService<M extends Mapper<T>, T extends BaseModel<T>> {
    @Autowired
    protected M mapper;

    public void setMapper(M mapper) {
        this.mapper = mapper;
    }

    public T selectOne(T entity) {
        return mapper.selectOne(entity);
    }


    public T selectByPrimaryKey(Object id) {
        return mapper.selectByPrimaryKey(id);
    }


    public List<T> selectList(T entity) {
        return mapper.select(entity);
    }


    public List<T> selectListAll() {
        return mapper.selectAll();
    }


    public int selectCount(T entity) {
        return mapper.selectCount(entity);
    }


    public int insert(T entity) {
        entity.preInsert();
        return mapper.insert(entity);
    }


    public int insertSelective(T entity) {
        entity.preInsert();
        return mapper.insertSelective(entity);
    }

    public long insertSelective(Object source, Class<T> clazz) {
        T entity = getInstance(source, clazz);
        entity.preInsert();
        mapper.insertSelective(entity);

        return entity.getId();
    }


    public int delete(T entity) {
        return mapper.delete(entity);
    }


    public int deleteByPrimaryKey(Object id) {
        return mapper.deleteByPrimaryKey(id);
    }


    public int updateByPrimaryKey(T entity) {
        entity.preUpdate();
        return mapper.updateByPrimaryKey(entity);
    }


    public int updateByPrimaryKeySelective(T entity) {
        entity.preUpdate();
        return mapper.updateByPrimaryKeySelective(entity);
    }

    public int updateByPrimaryKeySelective(Object dto, Class<T> clazz) {
        T entity = getInstance(dto, clazz);
        entity.preUpdate();
        return mapper.updateByPrimaryKeySelective(entity);
    }

    public List<T> selectByExample(Object example) {
        return mapper.selectByExample(example);
    }

    public int selectCountByExample(Object example) {
        return mapper.selectCountByExample(example);
    }

    private T getInstance(Object dto, Class<T> clazz){
        T entity = null;
        try {
            entity = clazz.newInstance();
            BeanUtils.copyProperties(dto, entity);
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return entity;
    }

}
