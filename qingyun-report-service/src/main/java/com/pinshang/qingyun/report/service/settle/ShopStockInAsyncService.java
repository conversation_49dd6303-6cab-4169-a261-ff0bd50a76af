package com.pinshang.qingyun.report.service.settle;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2022/6/10
 */
@Service
@Slf4j
public class ShopStockInAsyncService {

    @Autowired
    private ShopStockInService shopStockInService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    /**
     * 门店实际入库汇总
     * @param day
     */
    @Async
    public void shopStockInTax(String day) {
        try {

            shopStockInService.shopStockInTax(day);

            // 由于到货日期和取消日期不同，导致统计不到。多跑一天数据
            Date beforeOneDate = DateUtil.addDay(DateUtil.parseDate(day, "yyyy-MM-dd"),-1);
            String beforeOneDateStr = DateUtil.getDateFormate(beforeOneDate, "yyyy-MM-dd");

            shopStockInService.shopStockInTax(beforeOneDateStr);

        }catch (Exception e){
            log.error("门店实际入库汇总异常: ", e);
            StringBuffer sb = new StringBuffer();
            sb.append("门店实际入库汇总" + day);

            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
    }
}
