package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.pos.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface PosOperationRecordMapper {

    List<CommodityDeleteODTO> signalCommodityDelete(@Param("idto") CommodityDeleteIDTO idto);

    CommodityDeleteODTO signalCommodityDeleteSum(@Param("idto") CommodityDeleteIDTO idto);

    List<CommodityDeleteODTO> signalCommodityDeleteTotal(@Param("idto") CommodityDeleteIDTO idto);

    CommodityDeleteODTO signalCommodityDeleteTotalSum(@Param("idto") CommodityDeleteIDTO idto);
}
