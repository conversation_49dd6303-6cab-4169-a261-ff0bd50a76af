package com.pinshang.qingyun.report.dto.shop;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommodityTaxReportODto {

	private Long shopId;
	private String shopName;
	private String barCode;
	private Long commodityId;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;

	private Long cateId;
	private String cateName;
	//订单
	private BigDecimal totalSales;//总销售额
	private BigDecimal totalSalesQuantity; //总销售数量
	private BigDecimal onlineSales;//线上销售额
	private BigDecimal offlineSales;//线下销售额
	private BigDecimal offlineRatio;//线下销售额占比
	private BigDecimal grossProfitRate;//毛利率

	//收货
	private BigDecimal orderTotal;//进货金额
	//退货
	private BigDecimal saleReturnOrderTotal;//退货金额
	//库存调整
	private BigDecimal stockAdjustAmountTotal;//库存更正金额

	private BigDecimal weightPrice; //移动成本价
	private BigDecimal costTotal; //商品的成本合计

	//库存
	/*private BigDecimal stockQuantityPrice;//库存金额
	private BigDecimal stockQuantityDay;//库存天数
	private BigDecimal stockQuantity;//库存
	private BigDecimal negativeStockQuantity;//负库存*/

	//线下
	private BigDecimal offlineVisitorNumber;//线下来客数
	private BigDecimal offlineAverageAmount;//线下客单价
	private String barCodes;	// 子码列表
}
