package com.pinshang.qingyun.report.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/19.
 */
public enum ExcelSheetTitle {

    CATEGORY_SALES_PERCENT_REPORT("category_sales_percent", "类别销售周同比报表",new String[] {"大类", "今日销售额", "今日销售额占比", "上周销售额", "销售额同比"}),
    SHOP_SALES_PERCENT_REPORT("shop_sales_percent", "门店销售周同比报表",new String[] {"门店", "今日销售额", "上周销售额", "销售额同比", "今日来客","上周来客","来客同比","今日客单价","上周客单价","客单价同比"}),


    SHOP_SHORT_DELIVERY("shop_short_delivery","短交报表", new String[] {"门店名称","送货日期","订单号","商品分类","条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","发货数量","差异数量","短交比例","工厂","生产组", "客户编号","下单人"}),
    SHOP_STOCK_ADJUST("shop_stock_adjust","库存调整报表", new String[] { "调整日期","调整单号","分类","条形码","商品编码","商品名称","规格","单位","调整类型","调整数量","调整原因"}),
    RETURN_ORDER_DETAILS_REPORT_LIST("order_details_report_list","退单明细报表",new String[] {"门店", "退货日期","退单编号","商品分类","条形码","商品编码","商品名称","规格","单位","单价","退货数量","退货金额","退货原因","备注"}),

    SHOP_COMMODITY_TAX_SHOP("shop_commodity_tax","商品总表（含税）", new String[] { "门店名称","总销售额","线上销售额","线下销售额","线下销售额占比(%)","毛利率","进货金额","退货金额","库存金额","库存天数","库存更正金额","负库存个数","线下来客数","线下客单价"}),
    SHOP_COMMODITY_TAX("shop_commodity_tax","商品总表（含税）", new String[] { "门店名称","条形码","商品编码","商品名称","规格","总销售额","线上销售额","线下销售额","线下销售额占比(%)","毛利率","进货金额","退货金额","库存金额","库存天数","库存更正金额","库存"}),
    SHOP_COMMODITY_TAX_CATE("shop_commodity_tax","商品总表（含税）", new String[] { "门店名称","分类","总销售额","线上销售额","线下销售额","线下销售额占比(%)","毛利率","进货金额","退货金额","库存金额","库存天数","库存更正金额","负库存个数"}),

    ACTUAL_RECEIPT_ANALYSIS_REPORT("actual_receipt_analysis_report","门店实收商品分析表",new String[] {"门店名称","一级分类","条形码","商品编码","商品名称","规格","订货数量","实收数量","实收数量差异","当前库存","供货价","供货金额","供应商","采购员"}),

    SETTLEMENT_DETAIL_REPORT("settlement_detail_report","结算明细报表",new String[]{"门店","送货日期","结算日期","订单编号","预订单编号","分类","条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","实发数量","结算金额"}),

    OPERATING_REPORT_LIST("operating_report_list","运营报表",new String[] {}),//此title是动态设置的，慎用

    APP_OPERATE_REPORT("app_operate_report","app运营报表",new String[] {"门店","下单总用户数","新用户数","老用户数","下单订单数","下单订单总额","出库订单数","出库净额","单均价","退货商品个数","退货金额"}),

    SHOP_COMMODITY_ONLINE_REPORT("shop_commodity_online_report","商品销售明细表", new String[] { "门店名称","大类","中类","小类","条形码","商品编码","商品名称","规格","品牌","出库数量","出库净额","退货数量","退货金额","销售额","毛利率%","基准价","平均售价","顾客数","抵用券金额","满减金额","满折金额"}),
    COMMODITY_STOCK_REPORT("commodity_stock_report","商品库存情况表", new String[] {"门店", "大类", "中类", "小类", "条形码", "商品编码", "商品名称", "规格", "品牌", "商品状态", "上下架状态", "移动平均价", "实时库存", "库存金额", "本月期初库存", "本月入库数量", "当前进价"}),

    SHOP_CATE_USER("shop_cate_user","分类顾客数报表", new String[] { "门店","分类","顾客数","销售数量","销售额","件单价"}),

    COMMODITY_DETAIL_REPORT("commodity_detail_report", "商品信息明细报表", new String[]{"门店", "大类", "中类", "小类", "条形码", "商品编码", "商品名称", "规格", "品牌", "副标题", "商品状态", "上下架状态", "包装类型", "是否称重", "起卖重量(g)", "计量单位", "税率", "是否即食", "存储条件", "保质期", "产地", "物流模式", "默认供应商", "销售箱规", "采购箱规", "默认主图", "主图数量", "长图", "移动平均价", "进价", "基准价", "线上特价", "前7天销售数量", "前30天销售数量", "年至今销售数量", "当前库存"}),
    ORDER_SALES_ANALYSIS_REPORT("order_sales_analysis_report","订货销售分析报表",new String[]{"门店","分类","订货金额","订货占比","销售金额","销售占比","退货金额","销售与订货差额"}),
    CATE_AVERAGE_AMOUNT("cate_average_amount","类别客单周同比",new String[]{"门店","分类","今日客单量","上周客单量","客单量同比","今日客单价","上周客单价","客单量同比"}),
    MONTH_CASHIER_REPORT("monthCashierReport","收银对账月报表",new String[]{"门店编码","门店名称","现金实收","银行卡手续费","银行卡实收","聚合手续费","聚合实收","支付宝（记账）手续费","支付宝（记账）实收","微信（记账）手续费","微信（记账）实收","ok卡手续费","ok卡实收","手续费合计","实收合计"}),
    SHOP_ACTUAL_SUMMARY("shopActualSummary","门店实收汇总表",new String[]{"门店", "现金", "银行卡", "聚合", "OK卡", "支付宝（记账）", "微信（记账）", "合计"}),
    RECONCILIATION_OF_RURNOVER("reconciliationOfRurnover","门店营业款对账明细",new String[]{"日期","现金","","","","银行卡","","","","聚合","","","","OK卡","","","","支付宝（记账）","","","","微信（记账）","","","","合计"}),
    EXPORT_RECONCILIATION_STATEMENT("exportReconciliationStatement","门店营业款对账总表",new String[]{"门店","现金","","","","银行卡","","","","聚合","","","","OK卡","","","","支付宝（记账）","","","","微信（记账）","","","","合计"}),

    SHOP_REAL_DELIVERY("shop_real_delivery","商品实发汇总报表", new String[] {"商品编码","商品名称","规格","条码","一级品类","计量单位","订货数量","实发数量","实发金额","差异数量","工厂","生产组","车间"}),

    ;
    private String code;
    private String name;
    private String[] titles;

    private ExcelSheetTitle(String code, String name, String[] titles) {
        this.code = code;
        this.name = name;
        this.titles = titles;
    }



    /**
     * 动态设置title
     * @param code
     * @param titles
     */
    public static void setTitles(String code, String[] titles) {
        for (ExcelSheetTitle est : ExcelSheetTitle.values()) {
            if (code.equals(est.getCode())) {
                est.setTitles(titles);
                break;
            }
        }
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String[] getTitles() {
        return titles;
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
    }
}
