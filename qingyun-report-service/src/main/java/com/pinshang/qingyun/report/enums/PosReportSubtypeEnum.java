package com.pinshang.qingyun.report.enums;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-07-05
 */
public enum PosReportSubtypeEnum {
    MEMBER(10, "会员"),
    PROMOTION_PRICE(20, "手动优惠报表");

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;

    PosReportSubtypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
