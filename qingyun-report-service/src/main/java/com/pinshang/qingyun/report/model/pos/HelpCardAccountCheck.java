package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PosHelpCardAccountCheckingODTO
 * <AUTHOR>
 * @Date 2023/2/23 17:55
 * @Description PosHelpCardAccountCheckingODTO
 * @Version 1.0
 */
@Data
@Table(name = "t_pos_help_card_account_check")
public class HelpCardAccountCheck extends BaseModel<HelpCardAccountCheck> {
    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("对账期间开始 yyyy-MM-dd")
    private Date checkDateBegin;

    @ApiModelProperty("对账期间结束 yyyy-MM-dd")
    private Date checkDateEnd;

    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("对账金额")
    private BigDecimal checkAmount;

    @ApiModelProperty("品鲜交易金额")
    private BigDecimal pxTraderAmount;

    @ApiModelProperty("差异金额")
    private BigDecimal diffAmount;

    @ApiModelProperty("对账状态, 1-未对账, 2-已对账, 3-已取消")
    private Integer status;

}
