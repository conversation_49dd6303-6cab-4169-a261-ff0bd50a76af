package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.dto.pos.HelpCardAmountByDate;
import com.pinshang.qingyun.report.mapper.pos.HelpCardCheckDailyMapper;
import com.pinshang.qingyun.report.mapper.pos.HelpCardTraderSummaryMapper;
import com.pinshang.qingyun.report.mapper.pos.HelpCardTransactionFlowMapper;
import com.pinshang.qingyun.report.model.pos.HelpCardCheckDaily;
import com.pinshang.qingyun.report.model.pos.HelpCardTransactionFlow;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import com.pinshang.qingyun.report.util.SftpUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName HelpCardTradeService
 * <AUTHOR>
 * @Date 2023/2/23 9:35
 * @Description HelpCardTradeService
 * @Version 1.0
 */
@Service
@Slf4j
public class PosHelpCardTradeService {
    @Autowired
    private HelpCardTransactionFlowMapper helpCardTransactionFlowMapper;

    @Autowired
    private HelpCardTraderSummaryMapper helpCardTraderSummaryMapper;

    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Autowired
    private HelpCardCheckDailyMapper helpCardCheckDailyMapper;

    @Value("${report.helpcard.flowfile.ip:*************}")
    private String zfFlowFileIp;


    @Value("${report.helpcard.flowfile.port:31022}")
    private Integer zfFlowFilePort;


    @Transactional(rollbackFor = Exception.class)
    public Boolean helpCardAutoCheckJob(String date) throws IOException {
        if(StringUtil.isBlank(date)){
            Date nowDate = new Date();
            Date yesterday = DateUtil.addDay(nowDate, -1);
            date = DateUtil.get4yMd(yesterday);
        }
        this.readFile(date);
        this.autoCheck(date);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void readFile(String date) throws IOException {
        List<HelpCardTransactionFlow> flowList = new ArrayList<>();
        Date nowDate = DateUtil.parseDate(date, "yyyy-MM-dd");
        // 获取前一日日期拼接文件名称
        String fileName = "/upload/SHQMJT" + DateUtil.getDateFormate(nowDate, "yyyyMMdd") +"01.list";
        List<String> stringList = SftpUtils.readFile(zfFlowFileIp, zfFlowFilePort, "sftpuser", "mHj47V9a7i", fileName);
        if(null== stringList || stringList.size() < 2){
            log.info("当日未拉取到数据"+fileName);
            return;
        }
        for(int i = 1; i < stringList.size(); i++){
            String line = stringList.get(i);
            if(StringUtils.isNotBlank(line)){
                String[] info = line.split("\\|");
                if(info.length > 0){
                    HelpCardTransactionFlow flow = new HelpCardTransactionFlow();
                    flow.setTransactionDate(DateUtil.parseDate(info[0], "yyyyMMdd"));
                    flow.setPayCode(info[1]);
                    flow.setTransactionDateTime(DateUtil.parseDate(info[2], "yyyyMMddHHmmss"));
                    flow.setSerialNumber(info[3]);
                    flow.setCardNo(info[4]);
                    // 分转化为元
                    flow.setAmount(new BigDecimal(info[5]).divide(new BigDecimal(100),2,  RoundingMode.HALF_UP));
                    flow.setAmountCent(new BigDecimal(info[5]));
                    flow.setTypeName(info[6]);
                    flow.setReturnCode(info[7]);
                    flow.preInsert();
                    flowList.add(flow);
                    System.out.println(flow);
                }
            }
        }
        if(SpringUtil.isNotEmpty(flowList)) {
            Example flowExample = new Example(HelpCardTransactionFlow.class);
            flowExample.createCriteria().andEqualTo("transactionDate", flowList.get(0).getTransactionDate());
            helpCardTransactionFlowMapper.deleteByExample(flowExample);
            helpCardTransactionFlowMapper.insertList(flowList);
        }
    }

    /**
     * 自动对账, 并且落表每日
     * @param date
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean autoCheck(String date){
        //默认前一天
        Date nowDate = new Date();
        if(StringUtil.isBlank(date)){
            Date yesterday = DateUtil.addDay(nowDate, -1);
            date = DateUtil.get4yMd(yesterday);
        }
        Date transactionDate = DateUtil.parseDate(date, "yyyy-MM-dd");
        String beginTime = date + " 00:00:00";
        String endTime = date +"23:59:59";
        // 根据设备号 查询昨日品鲜系统
        List<HelpCardAmountByDate> pxList = helpCardTraderSummaryMapper.selectAmountByDate(beginTime, endTime);
        // 根据设备号查询政府对账
        List<HelpCardAmountByDate> zfList = helpCardTransactionFlowMapper.selectAmountByDate(beginTime, endTime, null);

        if(SpringUtil.isEmpty(pxList)){
            return true;
        }
        if(zfList.isEmpty()){
            weChatSendMessageService.sendWeChatMessage("自动对账, 第三方信息为空");
        }
        Map<String, BigDecimal> zfMap = zfList.stream().peek(it -> {
            if(null == it.getAmount()){it.setAmount(BigDecimal.ZERO);}
                })
                .collect(Collectors.toMap(HelpCardAmountByDate::getPayCode, HelpCardAmountByDate::getAmount));
        StringBuffer sb = new StringBuffer();
        List<HelpCardCheckDaily> dailyList = new ArrayList<>(pxList.size());
        pxList.forEach(it -> {
            if(null == it.getAmount()){
                it.setAmount(BigDecimal.ZERO);
            }
            HelpCardCheckDaily daily = new HelpCardCheckDaily();
            daily.setAreaId(it.getAreaId());
            daily.setAreaName(it.getAreaName());
            daily.setShopId(it.getShopId());
            daily.setPayCode(it.getPayCode());
            daily.setTransactionDate(transactionDate);
            daily.setPxTraderAmount(it.getAmount());
            BigDecimal zfAmount = zfMap.get(it.getPayCode());
            if(null == zfAmount){
                daily.setConfirmAmount(BigDecimal.ZERO);
            }else{
                daily.setConfirmAmount(zfAmount);
            }
            daily.setDiffAmount(daily.getConfirmAmount().subtract(daily.getPxTraderAmount()));
            if(daily.getDiffAmount().compareTo(BigDecimal.ZERO) != 0){
                sb.append(it.getPayCode());
            }
            daily.setCreateTime(nowDate);
            daily.setCreateId(-1L);
            dailyList.add(daily);
        });
        if(StringUtil.isNotBlank(sb.toString())){
            weChatSendMessageService.sendWeChatMessage(sb + "自动对账有异常");
        }
        if(SpringUtil.isNotEmpty(dailyList)){
            Example dailyRecord = new Example(HelpCardCheckDaily.class);
            dailyRecord.createCriteria().andEqualTo("transactionDate",transactionDate);
            helpCardCheckDailyMapper.deleteByExample(dailyRecord);

            helpCardCheckDailyMapper.insertList(dailyList);
        }
        return true;
    }
}
