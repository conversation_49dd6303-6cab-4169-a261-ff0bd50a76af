package com.pinshang.qingyun.report.controller.shop;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.dto.shop.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.shop.MonthSaleAimService;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/monthSaleAim")
public class MonthSaleAimController {

    @Autowired
    private MonthSaleAimService monthSaleAimService;

    /**
     * 月销售目标管理(分页)
     * @param idto
     * @return
     */
    @ApiOperation(value = "月销售目标管理列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("listMonthSaleAimPage")
    public PageInfo<MonthSaleAimODTO> listMonthSaleAimPage(MonthSaleAimPageIDTO idto) {
        return monthSaleAimService.listMonthSaleAimPage(idto);
    }

    /**
     * 新增月销售目标管理
     * @param monthSaleAimIDTO
     * @return
     */
    @ApiOperation(value = "新增", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "monthSaleAimIDTO", value = "新增月销售目标管理", required = true, paramType = "body", dataType = "MonthSaleAimIDTO")
    @PostMapping("savMonthSaleAim")
    public void saveMonthSaleAim(@RequestBody MonthSaleAimIDTO monthSaleAimIDTO){
        monthSaleAimService.saveMonthSaleAim(monthSaleAimIDTO);

    }

    @ApiOperation(value = "根据id查询月销售目标管理", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "id", value = "根据id查询月销售目标管理", required = true, paramType = "query")
    @GetMapping("getMonthSaleAimById")
    public MonthSaleAimODTO getMonthSaleAimById(@RequestParam("id") Long id) {
        return monthSaleAimService.getMonthSaleAimById(id);
    }

    /**
     * 修改月销售目标管理
     * @param
     * @param monthSaleAimIDTO
     * @return
     */
    @ApiOperation(value = "修改", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "monthSaleAimIDTO", value = "修改月销售目标管理", required = true, paramType = "body", dataType = "MonthSaleAimIDTO")
    @PutMapping("updateMonthSaleAim")
    public void updateMonthSaleAim(@RequestBody MonthSaleAimIDTO monthSaleAimIDTO){
        monthSaleAimService.updateMonthSaleAim(monthSaleAimIDTO);
    }

    /**
     * 删除月销售目标管理
     * @param id
     * @return
     */
    @ApiOperation(value = "删除", notes = "删除", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "path")
    @DeleteMapping("/deleteMonthSaleAim/{id}")
    public int deleteMonthSaleAim(@PathVariable("id") Long id) {
        return monthSaleAimService.deleteMonthSaleAim(id);
    }


    /**
     * 批量导入 月销售目标管理
     * @param list
     * @return
     */
    @RequestMapping(value = {"/import/importMonthSaleAim"}, method = {RequestMethod.POST})
    public Object importMonthSaleAim(@RequestBody List<SaleAimIDTO> list)throws ParamException {
        return monthSaleAimService.importMonthSaleAim(list);
    }

    @ApiOperation(value = "月销售目标管理-导出", notes = "月销售目标管理-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/monthSaleAimReport", method = RequestMethod.GET)
    public ModelAndView exportInfoMonthSaleAimReportReport(MonthSaleAimPageIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (idto.getShopId() == null) {
            idto.setShopId(tokenInfo.getShopId());
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        PageInfo<MonthSaleAimODTO> result = monthSaleAimService.listMonthSaleAimPage(idto);
        List<MonthSaleAimODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        if (null != list && !list.isEmpty()) {
            for (MonthSaleAimODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getSaleTime());
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getAimSaleAmount()+"");
                dataLst.add(dto.getRemark());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "月销售目标管理报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.MONTH_SAIL_AIM);
        map.put("data", data);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }
}
