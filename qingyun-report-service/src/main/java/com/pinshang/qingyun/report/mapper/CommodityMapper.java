package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.report.dto.CategoryODTO;
import com.pinshang.qingyun.report.dto.CommodityInfoODTO;
import com.pinshang.qingyun.report.dto.pos.CommodityPackageSpecDTO;
import com.pinshang.qingyun.report.model.Commodity;
import com.pinshang.qingyun.report.model.shop.CommodityTax;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface CommodityMapper{

    List<CategoryODTO> findAllFirstCategory();

    List<Commodity> findCommodityBarCodeByParam(@Param("commodityIdList") List<Long> commodityIdList);

    Commodity getCommodityByComodityId(@Param("commodityId") Long commodityId);

    CommodityInfoODTO findCommodityInfoForHand(@Param("barCode") String barcode);

    Commodity findCommodityById(@Param("commodityId") Long commodityId);

    List<CommodityTax> getCommodityListByIds(@Param("commodityIdSet")Set<Long> commodityIdSet);

    List<CommodityPackageSpecDTO> getCommodityListByCodes(@Param("commodityCodeSet")Set<String> commodityCodeSet);

    List<Commodity> getCommodityByCommodityCodes(@Param("commodityCodeList")List<String> commodityCodeList);

    List<Commodity> findCommodityByIdList(@Param("commodityIdList") List<Long> commodityIdList);
}
