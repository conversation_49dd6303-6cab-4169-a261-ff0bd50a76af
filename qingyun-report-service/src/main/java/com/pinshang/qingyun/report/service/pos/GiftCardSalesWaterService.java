package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.mapper.pos.GiftCardSalesWaterMapper;
import com.pinshang.qingyun.report.model.pos.GiftCardSalesWater;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.SpringUtils;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class GiftCardSalesWaterService {

    @Autowired
    private GiftCardSalesWaterMapper giftCardSalesWaterMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopClient shopClient;

    public Integer addSalesWater(List<GiftCardSalesWater> list) {
        return giftCardSalesWaterMapper.insertList(list);
    }

    /**
     * 销售流水
     * @param dto
     * @return
     */
    public PageInfo<GiftCardSalesWaterPage> salesWaterPage(QueryGiftCardWaterDTO dto) {
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if(!CollectionUtils.isEmpty(shopIdList)){
                dto.setShopIdList(shopIdList);
            }
        }
        PageInfo<GiftCardSalesWaterPage> page = PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() ->{
            giftCardSalesWaterMapper.salesWaterPage(dto);
        });
//        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
//        GiftCardSalesWaterPage giftCardSalesWaterPage = giftCardSalesWaterMapper.salesWaterSum(dto);
//        tablePageInfo.setHeader(giftCardSalesWaterPage);
        return page;
    };

    public GiftCardSalesWaterPage salesWaterSum(QueryGiftCardWaterDTO dto) {
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if(!CollectionUtils.isEmpty(shopIdList)){
                dto.setShopIdList(shopIdList);
            }
        }
        return giftCardSalesWaterMapper.salesWaterSum(dto);
    }

    /**
     * 代销销售流水
     * @param dto
     * @return
     */
    public PageInfo<GiftCardSalesWaterPage> consignmentSalesWaterPage(QueryGiftCardWaterDTO dto) {
        QYAssert.isTrue(null != dto.getType(), "销售类型不能为空");
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)) {
                return new PageInfo<>();
            }
            dto.setShopIdList(shopIdList);
            if (StringUtils.isNotBlank(dto.getOrgNum())) {
                List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(dto.getOrgNum());
                if (org.apache.commons.collections.CollectionUtils.isEmpty(shopDtos)) {
                    return new PageInfo<>();
                }
                dto.setShopIdList(shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList()));
            }
        }
        PageInfo<GiftCardSalesWaterPage> page = PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() ->{
            giftCardSalesWaterMapper.salesWaterPage(dto);
        });
        return page;
    };

    public GiftCardSalesWaterPage consignmentSalesWaterSum(QueryGiftCardWaterDTO dto) {
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)) {
                return new GiftCardSalesWaterPage();
            }
            dto.setShopIdList(shopIdList);
            if (StringUtils.isNotBlank(dto.getOrgNum())) {
                List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(dto.getOrgNum());
                if (org.apache.commons.collections.CollectionUtils.isEmpty(shopDtos)) {
                    return new GiftCardSalesWaterPage();
                }
                dto.setShopIdList(shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList()));
            }
        }
        return giftCardSalesWaterMapper.salesWaterSum(dto);
    }

    public List<GiftCardSalesWater> listByCode(String orderCode) {
        Example example = new Example(GiftCardSalesWater.class);
        example.createCriteria().andEqualTo("orderCode", orderCode);
        return giftCardSalesWaterMapper.selectByExample(example);
    }

    public PageInfo<ConsignmentCommoditySalesReportODTO> consignmentCommoditySalesReport(ConsignmentCommoditySalesReportIDTO idto){
        QYAssert.isTrue(StringUtils.isNotEmpty(idto.getBeginDate()) && StringUtils.isNotEmpty(idto.getEndDate()), "销售日期必填");
        idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
        idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), idto.getOrgCode(), null));
        idto.setShopIdList(shopIdList);
        if(CollectionUtils.isEmpty(shopIdList)){
            return new PageInfo<>();
        }
        PageInfo<ConsignmentCommoditySalesReportODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            giftCardSalesWaterMapper.selectConsignmentCommoditySales(idto);
        });
        return pageInfo;
    }

    public PageInfo<ConsignmentShopSalesReportODTO> consignmentShopCommoditySalesReport(ConsignmentShopSalesReportIDTO idto){
        QYAssert.isTrue(StringUtils.isNotEmpty(idto.getBeginDate()) && StringUtils.isNotEmpty(idto.getEndDate()), "销售日期必填");
        idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
        idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), idto.getOrgCode(), null));
        idto.setShopIdList(shopIdList);
        if(CollectionUtils.isEmpty(shopIdList)){
            return new PageInfo<>();
        }
        PageInfo<ConsignmentShopSalesReportODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            giftCardSalesWaterMapper.selectConsignmentShopCommoditySales(idto);
        });
        return pageInfo;
    }

}
