package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.client.report.service.PosAdminClient;
import com.pinshang.qingyun.report.dto.pos.AIMonitorCommodityDTO;
import com.pinshang.qingyun.report.dto.pos.TradeODTO;
import com.pinshang.qingyun.report.mapper.pos.CommodityDeleteMapper;
import com.pinshang.qingyun.report.mapper.pos.OrderDeleteMapper;
import com.pinshang.qingyun.report.mapper.pos.PosReportCommodityQuantityUpdateMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.pos.CommodityDelete;
import com.pinshang.qingyun.report.model.pos.OrderDelete;
import com.pinshang.qingyun.report.model.shop.Shop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AIMonitorService {

    @Autowired
    private CommodityDeleteMapper commodityDeleteMapper;

    @Autowired
    private OrderDeleteMapper orderDeleteMapper;

    @Autowired
    private PosAdminClient posAdminClient;

    @Autowired
    private PosReportCommodityQuantityUpdateMapper posReportCommodityQuantityUpdateMapper;

    @Autowired
    private ShopMapper shopMapper;

    /**
     * AI异常操作查询相关的订单信息
     * 提供给AI那边
     * @param orderCode
     * @param operateType
     * @return
     */
    public TradeODTO abnormalReceipt(String orderCode, Integer operateType) {
        List<AIMonitorCommodityDTO> commodityList = null;
        TradeODTO dto = null;

        switch (operateType){
            case 1:
                //清购物车
                dto = clearShoppingCart(orderCode);
                break;
            case 2:
                //挂单
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                if (null == dto || null == dto.getOrderCode()) {
                    dto = wholeOrderCancel(orderCode);
                }
                break;
            case 3:
                //整单作废
                dto = wholeOrderCancel(orderCode);
                break;
            case 4:
                //现金退货
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                break;
            case 5:
                //删除商品+现金支付
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                commodityList = commodityDeleteMapper.listCommodityByOrderCode(orderCode);
                if (null != dto && SpringUtil.isNotEmpty(dto.getList())) {
                    dto.getList().addAll(commodityList);
                }
                break;
            case 6:
                //减少商品+现金支付
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                commodityList = posReportCommodityQuantityUpdateMapper.updateCommodityInfo(orderCode);
                if (null != dto && SpringUtil.isNotEmpty(dto.getList()) && SpringUtil.isNotEmpty(commodityList)) {
                    Map<Long, BigDecimal> map = commodityList.stream().collect(Collectors.toMap(AIMonitorCommodityDTO::getItemId, AIMonitorCommodityDTO::getQuantity));
                    dto.getList().forEach(e -> {
                        if (map.containsKey(e.getItemId())) {
                            e.setOperateType(2);
                            e.setOldQuantity( e.getQuantity().add(map.get(e.getItemId())) );
                        }
                    });
                }
                break;
            case 7:
                //手动折扣+现金支付
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                if (null != dto && SpringUtil.isNotEmpty(dto.getList()) ) {
                    for (AIMonitorCommodityDTO commodity : dto.getList()) {
                        if (null != commodity.getPromotionType()) {
                            if (21 == commodity.getPromotionType() || 22 == commodity.getPromotionType()) {
                                commodity.setOperateType(3);
                            } else if (24 == commodity.getPromotionType() || 25 == commodity.getPromotionType()) {
                                dto.setOperateType(1);
                            }
                        }
                    }
                }
                break;
            case 8:
                //查对账+手动开钱箱 无订单
                break;
            case 9:
                //手动开钱箱+查对账 无订单
                break;
            case 11:
                //开钱箱+删除商品+现金支付
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                commodityList = commodityDeleteMapper.listCommodityByOrderCode(orderCode);
                if (null != dto && SpringUtil.isNotEmpty(dto.getList())) {
                    dto.getList().addAll(commodityList);
                }
                break;
            case 12:
                //开钱箱+减商品+现金支付
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                commodityList = posReportCommodityQuantityUpdateMapper.updateCommodityInfo(orderCode);
                if (null != dto && SpringUtil.isNotEmpty(dto.getList())) {
                    Map<Long, BigDecimal> map = commodityList.stream().collect(Collectors.toMap(AIMonitorCommodityDTO::getItemId, AIMonitorCommodityDTO::getQuantity));
                    dto.getList().forEach(e -> {
                        if (map.containsKey(e.getItemId())) {
                            e.setOperateType(2);
                            e.setOldQuantity( e.getQuantity().add(map.get(e.getItemId())) );
                        }
                    });
                }
                break;
            case 13:
                //开钱箱+手动折扣+现金支付
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                if (null != dto && SpringUtil.isNotEmpty(dto.getList()) ) {
                    for (AIMonitorCommodityDTO commodity : dto.getList()) {
                        if (null != commodity.getPromotionType()) {
                            if (21 == commodity.getPromotionType() || 22 == commodity.getPromotionType()) {
                                commodity.setOperateType(3);
                            } else if (24 == commodity.getPromotionType() || 25 == commodity.getPromotionType()) {
                                dto.setOperateType(1);
                            }
                        }
                    }
                }

                break;
            case 21:
                //用静态码,清空购物车
                dto = clearShoppingCart(orderCode);
                break;
            case 22:
                //用静态码,挂单
                dto = posAdminClient.queryPrint(Long.valueOf(orderCode));
                if (null == dto || null == dto.getOrderCode()) {
                    dto = wholeOrderCancel(orderCode);
                }
                break;
            case 23:
                //用静态码,整单取消
                dto = wholeOrderCancel(orderCode);
                break;
            default:
                log.warn("订单={}没有对应的类型={}", orderCode, operateType);
        }

        if (null != dto && SpringUtil.isNotEmpty(dto.getList()) ) {
            dto.getList().sort(Comparator.comparing(AIMonitorCommodityDTO::getOperateType,Comparator.nullsLast(Integer::compareTo) ));
        }
        return dto;
    }

    /**
     * 清空购物车
     * @param orderCode
     * @return
     */
    public TradeODTO clearShoppingCart(String orderCode) {
        TradeODTO dto = null;
        List<AIMonitorCommodityDTO> commodityList = commodityDeleteMapper.listCommodityByOrderCode(orderCode);
        if (SpringUtil.isNotEmpty(commodityList)) {
            Example example = new Example(CommodityDelete.class);
            example.createCriteria().andEqualTo("orderCode", orderCode);
            example.orderBy("operateTime");
            List<CommodityDelete> list = commodityDeleteMapper.selectByExample(example);
            //称重品数量
            Long num = commodityList.stream().filter(e -> YesOrNoEnums.YES.getCode().equals(e.getIsWeight())).count();
            BigDecimal quantity = commodityList.stream().filter(e -> YesOrNoEnums.NO.getCode().equals(e.getIsWeight())).map(AIMonitorCommodityDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer totalNum = num.intValue() + quantity.intValue();

            Shop shop = shopMapper.selectByPrimaryKey(list.get(0).getShopId());

            dto = new TradeODTO(Long.valueOf(orderCode), list.get(0).getCasherCode(), list.get(0).getCasherName(), list.get(0).getOperateTime(), shop.getShopCode(), list.get(0).getShopName(), totalNum,null,null, null, null, null, null);
            dto.setList(commodityList);
        }
        return dto;
    }

    /**
     * 整单作废   挂单删除   挂单未结算
     * @param orderCode
     * @return
     */
    public TradeODTO wholeOrderCancel(String orderCode) {
        TradeODTO dto = null;
        List<AIMonitorCommodityDTO> commodityList = orderDeleteMapper.listCommodityByOrderCode(orderCode);
        if (SpringUtil.isNotEmpty(commodityList)) {
            Example example = new Example(OrderDelete.class);
            example.createCriteria().andEqualTo("orderCode", orderCode);
            List<OrderDelete> orderDeletes = orderDeleteMapper.selectByExample(example);
            dto = new TradeODTO(Long.valueOf(orderCode), orderDeletes.get(0).getOperateCode(), orderDeletes.get(0).getOperateName(), orderDeletes.get(0).getOperateTime(), orderDeletes.get(0).getShopCode(), orderDeletes.get(0).getShopName(), orderDeletes.get(0).getQuantity().intValue(), null, null, null, null, null, null);
            dto.setList(commodityList);
        }
        return dto;
    }
}
