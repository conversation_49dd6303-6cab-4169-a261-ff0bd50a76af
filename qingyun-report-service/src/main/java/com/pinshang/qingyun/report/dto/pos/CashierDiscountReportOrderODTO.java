package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CashierDiscountReportOrderODTO {
    /** 订单编码*/
    private  Long orderCode;
    /** 门店ID*/
    @ApiModelProperty("门店ID")
    private Long shopId;

    /** 门店code*/
    @ApiModelProperty("门店code")
    private String shopCode;

    /** 门店名称*/
    @ApiModelProperty("门店名称")
    private String shopName;

    /** 原价金额*/
    @ApiModelProperty("原价金额")
    private BigDecimal realTotalAmount;

    /** 实收金额*/
    @ApiModelProperty("实收金额")
    private BigDecimal totalAmount;

    /** 让利金额*/
    @ApiModelProperty("让利金额")
    private BigDecimal discountAmount;

    /** 销售日期*/
    @ApiModelProperty("销售日期")
    private String saleTime;

    /** 收银员编号*/
    @ApiModelProperty("收银员编号")
    private String cashierNo;

    /** 收银员名称*/
    @ApiModelProperty("收银员名称")
    private String cashierName;

    private Date createTime;

    private Long createId;

    private Date updateTime;

    private Long updateId;
}
