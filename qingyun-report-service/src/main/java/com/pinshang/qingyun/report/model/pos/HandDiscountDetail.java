package com.pinshang.qingyun.report.model.pos;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2021/7/6
 */
@Data
@Entity
@Table(name = "t_pos_report_hand_discount_detail")
public class HandDiscountDetail  {
    @Id
    private Long id;

    private Long shopId;//门店id
    private String shopCode;
    private String shopName;

    private String  cashierNumber;//收银员账号
    private String  cashierName;//收银员名称
    private String  orderCode;//订单号

    private Long commodityId;//商品id
    private String commodityCode;//商品编码
    private String commodityName;//商品名称
    private String commoditySpec;//规格
    private String commodityUnit;//单位

    private Long commodityFirstId;//一级大类ID
    private String commodityFirstName;
    private Long commoditySecondId;//二级大类ID
    private String commoditySecondName;
    private Long commodityThirdId;//三级大类ID
    private String commodityThirdName;

    private BigDecimal retailPrice;//零售价
    private BigDecimal retailAmount;//零售金额
    private BigDecimal quantity;//数量

    private BigDecimal backgroundDiscountAmount;//后台自动优惠金额
    private BigDecimal handleDiscountAmount;//手动优惠金额
    private BigDecimal transactionAmount;//成交金额
    private String handleRetailPercent;//手动/零售价
    private Integer handleDiscountType;//手动优惠类型

    private String opUserCode;//授权人账号
    private String opUserName;//授权人姓名
    private Long createId;
    private Date createTime;
}
