package com.pinshang.qingyun.report.model.pos;

import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "t_pos_report_cashier_water_day")
@Data
public class CashierWaterDay {

    private Long id;

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    private Date createTime;

    private BigDecimal payAmount = BigDecimal.ZERO;

    private Integer saleType;

    private String payType;

    private String payName;

    private String employeeNumber;

    private String createName;

    private String macCode;

    /** pos机id **/
    private Long posMacId;

    /** pos机类型 1-收银pos, 2-自助pos **/
    private Integer posType;
}
