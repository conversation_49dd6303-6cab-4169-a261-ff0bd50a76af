package com.pinshang.qingyun.report.service.shop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.mdCheck.DynamicShopCheckSummaryReportODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportIDTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportODTO;
import com.pinshang.qingyun.report.dto.mdCheck.ShopCheckSummaryReportODTO;
import com.pinshang.qingyun.report.dto.shop.CheckCommodityGroupCompleteIDTO;
import com.pinshang.qingyun.report.service.MdCheckService;
import com.pinshang.qingyun.shop.dto.ShopColorDTO;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.mdCheck.MdCheckPlanDetailIDTO;
import com.pinshang.qingyun.shop.dto.mdCheck.MdCheckPlanODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopMapColorClient;
import com.pinshang.qingyun.shop.service.mdCheck.MdCheckReportClient;
import com.pinshang.qingyun.smm.dto.org.SelectShopParentOrgInfoIDTO;
import com.pinshang.qingyun.smm.dto.org.ShopAndShopParentOrgInfoODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopCheckReportService {

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private ShopMapColorClient shopMapColorClient;

    @Autowired
    private MdCheckService mdCheckService;

    @Autowired
    private MdCheckReportClient mdCheckReportClient;

    @Autowired
    private SMMUserClient smmUserClient;

    public PageInfo<MdCheckReportODTO> checkCommodityGroupCompleteRate(CheckCommodityGroupCompleteIDTO idto) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return new PageInfo();
        }

        // 1. 根据所属部门编码查询门店list, 当即选择所属部门就要和用户门店范围列表取交集
        if (!StringUtil.isNullOrEmpty(idto.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(idto.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (CollectionUtils.isEmpty(shopIdList)) {
                    return new PageInfo();
                }
            }
        }

        if (!StringUtil.isNullOrEmpty(idto.getOrgCode()) && idto.getShopId() != null && !shopIdList.contains(idto.getShopId())) {
            return new PageInfo();
        }

        // 2. 根据考核商品组、考核类型、考核方案 查找 考核方案id
        List<Long> planIdList = null;
        if (idto.getCheckGroupId() != null || idto.getCheckType() != null || !StringUtil.isNullOrEmpty(idto.getCheckPlanCodeOrName())) {
            MdCheckPlanDetailIDTO planIdto = new MdCheckPlanDetailIDTO();
            planIdto.setPlanBeginDate(idto.getPlanBeginDate());
            planIdto.setPlanEndDate(idto.getPlanEndDate());
            planIdto.setCheckGroupId(idto.getCheckGroupId());
            planIdto.setCheckType(idto.getCheckType());
            planIdto.setCheckPlanCodeOrName(idto.getCheckPlanCodeOrName());
            List<MdCheckPlanODTO> shopCheckPlanList = mdCheckReportClient.findShopCheckPlanList(planIdto);
            if (CollectionUtils.isNotEmpty(shopCheckPlanList)) {
                planIdList = shopCheckPlanList.stream().map(MdCheckPlanODTO::getId).collect(Collectors.toList());
            } else {
                return new PageInfo();
            }
        }

        // 3. 查询数据
        MdCheckReportIDTO param = new MdCheckReportIDTO();
        param.setPageNo(idto.getPageNo());
        param.setPageSize(idto.getPageSize());
        param.setBeginDate(idto.getPlanBeginDate());
        param.setEndDate(idto.getPlanEndDate());
        param.setShopIdList(shopIdList);
        param.setShopId(idto.getShopId());
        param.setPlanIdList(planIdList);
        PageInfo<MdCheckReportODTO> pageInfo = mdCheckService.getMdCheckPlanPage(param);

        // 4. 关联出每个组织名称（如 运营一部、运营二部）
        List<MdCheckReportODTO> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> idList = list.stream().map(MdCheckReportODTO::getShopId).collect(Collectors.toList());
            SelectShopParentOrgInfoIDTO shopParentOrgInfoIDTO = new SelectShopParentOrgInfoIDTO();
            shopParentOrgInfoIDTO.setShopIdList(idList);
            List<ShopAndShopParentOrgInfoODTO> orgInfoList = orgClient.selectShopAndShopParentOrgInfoList(shopParentOrgInfoIDTO);
            if(SpringUtil.isNotEmpty(orgInfoList)) {
                Map<Long, ShopAndShopParentOrgInfoODTO> orgInfoMap = orgInfoList.stream().collect(Collectors.toMap(ShopAndShopParentOrgInfoODTO::getShopId, orgInfo -> orgInfo));
                for (int i = 0; i < list.size(); i++) {
                    // 找不到指定结构丢弃数据
                    MdCheckReportODTO item = list.get(i);
                    ShopAndShopParentOrgInfoODTO orgInfo = orgInfoMap.get(item.getShopId());
                    if (orgInfo == null) {
                        list.remove(i);
                        continue;
                    }
                    item.setOrgCode(orgInfo.getParentOrgCode());
                    item.setOrgName(orgInfo.getParentOrgName());
                }
            }
        }

        return pageInfo;
    }


    public DynamicShopCheckSummaryReportODTO checkSummaryReport(String beginTime, String endTime) {
        List<ShopCheckSummaryReportODTO> returnList = new ArrayList<>();

        // 1. 查询门店地图启用的运营结构（动态列名）
        List<ShopColorDTO> shopColorDTOS = shopMapColorClient.startList();

        // 2. 查询 考核商品组完成率
        CheckCommodityGroupCompleteIDTO param = new CheckCommodityGroupCompleteIDTO();
        param.setPageNo(1);
        param.setPageSize(Integer.MAX_VALUE);
        param.setPlanBeginDate(beginTime);
        param.setPlanEndDate(endTime);
        PageInfo<MdCheckReportODTO> pageInfo = this.checkCommodityGroupCompleteRate(param);
        List<MdCheckReportODTO> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, ShopColorDTO> orgCodeAndWeightMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopColorDTOS)) {
                orgCodeAndWeightMap = shopColorDTOS.stream().collect(Collectors.toMap(ShopColorDTO::getOrgCode, Function.identity()));
            }

            // 3. 根据考核商品组名（面包组、牛奶组）
            Map<String, List<MdCheckReportODTO>> checkCommodityGroupMap = list.stream().collect(Collectors.groupingBy(item -> item.getCheckGroupName()));
            Map<String, ShopColorDTO> finalOrgCodeAndWeightMap = orgCodeAndWeightMap;
            checkCommodityGroupMap.forEach((checkGroupName, itemList) -> {
                ShopCheckSummaryReportODTO item = new ShopCheckSummaryReportODTO();
                item.setCheckGroupName(checkGroupName);

                // 每个考核商品组所有的完成和期间考核
                double totalOrgCompleteAim = 0;
                double totalOrgDurationPlanAim = 0;
                List<ShopCheckSummaryReportODTO.OrgItem> orgCompleteRateList = new ArrayList<>();
                // 4. 根据组织结构（如运营一部、运营二部、运营三部）进行分组
                Map<String, List<MdCheckReportODTO>> orgCodeGroupMap = itemList.stream().collect(Collectors.groupingBy(i -> i.getOrgCode()));
                for (Map.Entry<String, List<MdCheckReportODTO>> entry : orgCodeGroupMap.entrySet()) {
                    String orgCode = entry.getKey();
                    List<MdCheckReportODTO> items = entry.getValue();
                    // 期间实际完成（分组求和）
                    double totalCompleteAim = items.stream().collect(Collectors.summingDouble(i -> i.getCompleteAim().doubleValue()));
                    // 期间考核目标（分组求和）
                    double totalDurationPlanAim = items.stream().collect(Collectors.summingDouble(i -> i.getDurationPlanAim().doubleValue()));
                    // 完成率
                    BigDecimal totalCompleteRate = BigDecimal.valueOf(totalCompleteAim).divide(BigDecimal.valueOf(totalDurationPlanAim), 3, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));

                    // 设置运营机构的排序值，如果组织结构不在门店地图配置范围列表中就丢弃这条数据
                    ShopColorDTO shopColorDTO = finalOrgCodeAndWeightMap.get(orgCode);
                    ShopCheckSummaryReportODTO.OrgItem orgCompleteRate = new ShopCheckSummaryReportODTO.OrgItem();
                    orgCompleteRate.setCompleteRate(totalCompleteRate.setScale(0, BigDecimal.ROUND_HALF_UP).intValue() + "%");
                    if (shopColorDTO != null) {
                        orgCompleteRate.setOrgName(shopColorDTO.getOrgName());
                        orgCompleteRate.setSort(shopColorDTO.getWeight());
                        orgCompleteRateList.add(orgCompleteRate);

                        totalOrgCompleteAim += totalCompleteAim;
                        totalOrgDurationPlanAim += totalDurationPlanAim;
                    }
                }
                orgCompleteRateList.sort((x, y) -> Integer.compare(x.getSort(), y.getSort()));
                item.setOrgCompleteRateList(orgCompleteRateList);

                // 合计完成率
                BigDecimal totalOrgCompleteRate = BigDecimal.ZERO;
                if (totalOrgDurationPlanAim > 0) {
                    totalOrgCompleteRate = BigDecimal.valueOf(totalOrgCompleteAim).divide(BigDecimal.valueOf(totalOrgDurationPlanAim), 3, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                }
                item.setTotalCompleteRate(totalOrgCompleteRate.setScale( 0, BigDecimal.ROUND_HALF_UP).intValue());
                returnList.add(item);
            });
        }

        // 根据门店地图配置的顺序对运营部门的数据进行排序
        returnList.sort((x, y) -> Integer.compare(y.getTotalCompleteRate(), x.getTotalCompleteRate()));
        log.info(JsonUtil.java2json(returnList));

        // 组装数据，动态列名
        List<String> dynamicClumns = shopColorDTOS.stream().sorted((x, y) -> Integer.compare(x.getWeight(), y.getWeight())).map(ShopColorDTO::getOrgName).collect(Collectors.toList());
        List<String> columns = new ArrayList<>();
        columns.add("考核商品组");
        columns.addAll(dynamicClumns);
        columns.add("合计完成率");

        // 组装每一行数据
        List<List<String>> rows = new ArrayList<>();
        returnList.forEach(item -> {
            List<String> row = new ArrayList<>();
            // 第一列：考核组组名
            String checkGroupName = item.getCheckGroupName();
            row.add(checkGroupName);

            // 动态列值（如运营一部、运营二部、运营三部）
            shopColorDTOS.forEach(org -> {
                List<ShopCheckSummaryReportODTO.OrgItem> orgCompleteRateList = item.getOrgCompleteRateList().stream().filter(i -> org.getOrgName().equals(i.getOrgName())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orgCompleteRateList)) {
                    row.add(orgCompleteRateList.get(0).getCompleteRate());
                } else {
                    row.add("0%");
                }
            });
            // 最后一列：合计完成率
            row.add(item.getTotalCompleteRate().toString());
            rows.add(row);
        });

        DynamicShopCheckSummaryReportODTO dynamic = new DynamicShopCheckSummaryReportODTO();
        dynamic.setColumnTitles(columns);
        dynamic.setRows(rows);
        return dynamic;
    }
}
