package com.pinshang.qingyun.report.xdrealtime;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.mapper.RealTimeReportByDateMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.shop.Shop;
import com.pinshang.qingyun.report.model.xd.OrderTypeConstants;
import com.pinshang.qingyun.report.model.xd.RealTimeReportByDate;
import com.pinshang.qingyun.report.model.xd.SourceTypeConstants;
import com.pinshang.qingyun.report.util.SpringUtils;
import com.pinshang.qingyun.xd.order.dto.RealTimeReportByDateODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class XdRealTimeReport {

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private RedissonClient redisClient;

    @Autowired
    private RealTimeReportByDateMapper realTimeReportByDateMapper;

    @Autowired
    private XdOrderClient xdOrderClient;

    public void report(String dateTime) {

        List<Shop> allShopList = shopMapper.getAllShopList();

        if (CollectionUtils.isEmpty(allShopList)) {
            return;
        }
        String current$yyMMdd = dateTime;
        Date now = new Date();
        if(StringUtils.isEmpty(dateTime)){
            now = DateUtils.addDays(now, -1);
            current$yyMMdd = DateFormatUtils.format(now, "yy-MM-dd");
        }else{
            now = DateUtil.parseDate(dateTime, "yy-MM-dd");
            current$yyMMdd = DateFormatUtils.format(now, "yy-MM-dd");
        }

        // APP
        {
            String turnoverOnlineKey = OrderConstants.App.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.App.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.App.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.App.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.App.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.App.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.App, OrderTypeConstants.Normal);
        }

        // MINI
        {
            String turnoverOnlineKey = OrderConstants.Mini.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.Mini.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.Mini.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.Mini.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.Mini.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.Mini.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.Mini, OrderTypeConstants.Normal);
        }
        // elm
        {
            String turnoverOnlineKey = OrderConstants.Elm.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.Elm.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.Elm.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.Elm.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.Elm.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.Elm.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.Elm, OrderTypeConstants.Normal);
        }

        // jddj
        {
            String turnoverOnlineKey = OrderConstants.Jddj.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.Jddj.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.Jddj.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.Jddj.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.Jddj.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.Jddj.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.Jddj, OrderTypeConstants.Normal);
        }

        // group
        {
            String turnoverOnlineKey = OrderConstants.Group.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.Group.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.Group.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.Group.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.Group.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.Group.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.Mini, OrderTypeConstants.Group);
        }

        // cloud
        {
            String turnoverOnlineKey = OrderConstants.Cloud.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.Cloud.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.Cloud.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.Cloud.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.Cloud.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.Cloud.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.Cloud_Mini, OrderTypeConstants.Cloud);
        }

        // cloud_app
        {
            String turnoverOnlineKey = OrderConstants.Cloud_App.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
            //门店月毛利额
            String grossProfitOnlineKey = OrderConstants.Cloud_App.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
            //门店订单数量
            String shopOrderQuantityOnlineKey = OrderConstants.Cloud_App.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);

            //门店月销售
            String returnTurnoverOnlineKey = ReturnOrderConstants.Cloud_App.TURNOVER + current$yyMMdd;
            RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
            //门店月毛利额
            String returnGrossProfitOnlineKey = ReturnOrderConstants.Cloud_App.GROSS_PROFIT + current$yyMMdd;
            RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
            //门店订单数量
            String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.Cloud_App.SHOP_ORDER_QUANTITY + current$yyMMdd;
            RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

            insert(allShopList, now, turnoverOnlineSet, grossProfitOnlineSet, shopOrderQuantityOnlineSet, returnTurnoverOnlineSet, returnGrossProfitOnlineSet, returnShopOrderQuantityOnlineSet, SourceTypeConstants.Cloud_App, OrderTypeConstants.Cloud);
        }

    }

    /**
     * mysql查取日销售报告
     * @param dateTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void reportByDb(String dateTime){
        String current$yyMMdd;
        Date now = new Date();
        if(StringUtils.isEmpty(dateTime)){
            now = DateUtils.addDays(now, -1);
            current$yyMMdd = DateFormatUtils.format(now, "yyyy-MM-dd");
        }else{
            now = DateUtil.parseDate(dateTime, "yyyy-MM-dd");
            current$yyMMdd = DateFormatUtils.format(now, "yyyy-MM-dd");
        }
        List<RealTimeReportByDateODTO> list = xdOrderClient.realTimeReportByDate(current$yyMMdd, current$yyMMdd);
        if(CollectionUtils.isNotEmpty(list)) {
            List<RealTimeReportByDate> realTimeReportByDateList = list.parallelStream().map(it -> {
                RealTimeReportByDate realTimeReportByDate = new RealTimeReportByDate();
                BeanUtils.copyProperties(it, realTimeReportByDate);
                realTimeReportByDate.setReturnAverageAmount(BigDecimal.ZERO);
                realTimeReportByDate.setReturnSales(BigDecimal.ZERO);
                realTimeReportByDate.setReturnCost(BigDecimal.ZERO);
                realTimeReportByDate.setReturnQuantity(BigDecimal.ZERO);
                return realTimeReportByDate;
            }).collect(Collectors.toList());
            realTimeReportByDateMapper.insertList(realTimeReportByDateList);
        }
    }

    private void insert(List<Shop> allShopList, Date now, RScoredSortedSet<Long> turnoverOnlineSet, RScoredSortedSet<Long> grossProfitOnlineSet, RScoredSortedSet<Long> shopOrderQuantityOnlineSet,
                        RScoredSortedSet<Long> returnTurnoverOnlineSet, RScoredSortedSet<Long> returnGrossProfitOnlineSet, RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet, int sourceType, int orderType) {
        for (Shop shop : allShopList) {
            Long shopId = shop.getId();
            Double sales = turnoverOnlineSet.getScore(shopId);
            Double quantity = shopOrderQuantityOnlineSet.getScore(shopId);
            Double profit = grossProfitOnlineSet.getScore(shopId);
            Double returnSales = returnTurnoverOnlineSet.getScore(shopId);
            Double returnQuantity = returnShopOrderQuantityOnlineSet.getScore(shopId);
            Double returnProfit = returnGrossProfitOnlineSet.getScore(shopId);

            if (
                    sales == null &&
                            quantity == null &&
                            profit == null &&
                            returnSales == null &&
                            returnQuantity == null &&
                            returnProfit == null
            ) {
                continue;
            }
            if (sales == null) {
                sales = 0.0;
            }
            if (quantity == null) {
                quantity = 0.0;
            }
            if (profit == null) {
                profit = 0.0;
            }
            if (returnSales == null) {
                returnSales = 0.0;
            }
            if (returnQuantity == null) {
                returnQuantity = 0.0;
            }
            if (returnProfit == null) {
                returnProfit = 0.0;
            }

            RealTimeReportByDate realTimeReportByDate = new RealTimeReportByDate();
            realTimeReportByDate.setDateTime(now);
            realTimeReportByDate.setShopId(shopId);
            realTimeReportByDate.setSourceType(sourceType);
            realTimeReportByDate.setOrderType(orderType);
            realTimeReportByDate.setSales(BigDecimal.valueOf(sales));
            realTimeReportByDate.setQuantity(BigDecimal.valueOf(quantity));
            realTimeReportByDate.setCost(BigDecimal.valueOf(sales - profit));
            if (quantity.compareTo(Double.valueOf(0.0)) > 0) {
                realTimeReportByDate.setAverageAmount(BigDecimal.valueOf(sales / quantity));
            } else {
                realTimeReportByDate.setAverageAmount(BigDecimal.valueOf(0.0));
            }
            realTimeReportByDate.setReturnSales(BigDecimal.valueOf(returnSales));
            realTimeReportByDate.setReturnQuantity(BigDecimal.valueOf(returnQuantity));
            realTimeReportByDate.setReturnCost(BigDecimal.valueOf(returnSales - returnProfit));
            if (returnQuantity.compareTo(Double.valueOf(0.0)) > 0) {
                realTimeReportByDate.setReturnAverageAmount(BigDecimal.valueOf(returnSales / returnQuantity));
            } else {
                realTimeReportByDate.setReturnAverageAmount(BigDecimal.valueOf(0.0));
            }
            realTimeReportByDateMapper.insert(realTimeReportByDate);
        }
    }


    public boolean xdRedisInit() {
        Date now = new Date();
        String current$yyMMdd = DateFormatUtils.format(now, "yy-MM-dd");
        RealTimeReportByDate realTimeReportByDate = new RealTimeReportByDate();
        realTimeReportByDate.setDateTime(DateUtil.getNowDate());
        List<RealTimeReportByDate> realTimeReportByDates = realTimeReportByDateMapper.select(realTimeReportByDate);

        String turnoverKey = OrderConstants.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverSet = redisClient.getScoredSortedSet(turnoverKey);
        turnoverSet.clear();
        String shopOrderQuantity = OrderConstants.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantitySet = redisClient.getScoredSortedSet(shopOrderQuantity);
        shopOrderQuantitySet.clear();

        //APP
        String turnoverOnlineKey = OrderConstants.App.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineSet = redisClient.getScoredSortedSet(turnoverOnlineKey);
        //门店月毛利额
        String grossProfitOnlineKey = OrderConstants.App.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineSet = redisClient.getScoredSortedSet(grossProfitOnlineKey);
        //门店订单数量
        String shopOrderQuantityOnlineKey = OrderConstants.App.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineKey);
        //门店月销售
        String returnTurnoverOnlineKey = ReturnOrderConstants.App.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineSet = redisClient.getScoredSortedSet(returnTurnoverOnlineKey);
        //门店月毛利额
        String returnGrossProfitOnlineKey = ReturnOrderConstants.App.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineKey = ReturnOrderConstants.App.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineKey);

        turnoverOnlineSet.clear();
        grossProfitOnlineSet.clear();
        shopOrderQuantityOnlineSet.clear();
        returnTurnoverOnlineSet.clear();
        returnGrossProfitOnlineSet.clear();
        returnShopOrderQuantityOnlineSet.clear();

        //Elm
        String turnoverOnlineElmKey = OrderConstants.Elm.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineElmSet = redisClient.getScoredSortedSet(turnoverOnlineElmKey);
        //门店月毛利额
        String grossProfitOnlineElmKey = OrderConstants.Elm.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineElmSet = redisClient.getScoredSortedSet(grossProfitOnlineElmKey);
        //门店订单数量
        String shopOrderQuantityOnlineElmKey = OrderConstants.Elm.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineElmSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineElmKey);
        //门店月销售
        String returnTurnoverOnlineElmKey = ReturnOrderConstants.Elm.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineElmSet = redisClient.getScoredSortedSet(returnTurnoverOnlineElmKey);
        //门店月毛利额
        String returnGrossProfitOnlineElmKey = ReturnOrderConstants.Elm.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineElmSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineElmKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineElmKey = ReturnOrderConstants.Elm.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineElmSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineElmKey);

        turnoverOnlineElmSet.clear();
        grossProfitOnlineElmSet.clear();
        shopOrderQuantityOnlineElmSet.clear();
        returnTurnoverOnlineElmSet.clear();
        returnGrossProfitOnlineElmSet.clear();
        returnShopOrderQuantityOnlineElmSet.clear();

        //Mini
        String turnoverOnlineMiniKey = OrderConstants.Mini.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineMiniSet = redisClient.getScoredSortedSet(turnoverOnlineMiniKey);
        //门店月毛利额
        String grossProfitOnlineMiniKey = OrderConstants.Mini.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineMiniSet = redisClient.getScoredSortedSet(grossProfitOnlineMiniKey);
        //门店订单数量
        String shopOrderQuantityOnlineMiniKey = OrderConstants.Mini.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineMiniSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineMiniKey);

        //门店月销售
        String returnTurnoverOnlineMiniKey = ReturnOrderConstants.Mini.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineMiniSet = redisClient.getScoredSortedSet(returnTurnoverOnlineMiniKey);
        //门店月毛利额
        String returnGrossProfitOnlineMiniKey = ReturnOrderConstants.Mini.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineMiniSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineMiniKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineMiniKey = ReturnOrderConstants.Mini.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineMiniSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineMiniKey);

        turnoverOnlineMiniSet.clear();
        grossProfitOnlineMiniSet.clear();
        shopOrderQuantityOnlineMiniSet.clear();
        returnTurnoverOnlineMiniSet.clear();
        returnGrossProfitOnlineMiniSet.clear();
        returnShopOrderQuantityOnlineMiniSet.clear();

        //Jddj
        String turnoverOnlineJddjKey = OrderConstants.Jddj.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineJddjSet = redisClient.getScoredSortedSet(turnoverOnlineJddjKey);
        //门店月毛利额
        String grossProfitOnlineJddjKey = OrderConstants.Jddj.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineJddjSet = redisClient.getScoredSortedSet(grossProfitOnlineJddjKey);
        //门店订单数量
        String shopOrderQuantityOnlineJddjKey = OrderConstants.Jddj.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineJddjSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineJddjKey);
        //门店月销售
        String returnTurnoverOnlineJddjKey = ReturnOrderConstants.Jddj.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineJddjSet = redisClient.getScoredSortedSet(returnTurnoverOnlineJddjKey);
        //门店月毛利额
        String returnGrossProfitOnlineJddjKey = ReturnOrderConstants.Jddj.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineJddjSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineJddjKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineJddjKey = ReturnOrderConstants.Jddj.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineJddjSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineJddjKey);

        turnoverOnlineJddjSet.clear();
        grossProfitOnlineJddjSet.clear();
        shopOrderQuantityOnlineJddjSet.clear();
        returnTurnoverOnlineJddjSet.clear();
        returnGrossProfitOnlineJddjSet.clear();
        returnShopOrderQuantityOnlineJddjSet.clear();

        //Group
        String turnoverOnlineGroupKey = OrderConstants.Group.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineGroupSet = redisClient.getScoredSortedSet(turnoverOnlineGroupKey);
        //门店月毛利额
        String grossProfitOnlineGroupKey = OrderConstants.Group.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineGroupSet = redisClient.getScoredSortedSet(grossProfitOnlineGroupKey);
        //门店订单数量
        String shopOrderQuantityOnlineGroupKey = OrderConstants.Group.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineGroupSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineGroupKey);
        //门店月销售
        String returnTurnoverOnlineGroupKey = ReturnOrderConstants.Group.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineGroupSet = redisClient.getScoredSortedSet(returnTurnoverOnlineGroupKey);
        //门店月毛利额
        String returnGrossProfitOnlineGroupKey = ReturnOrderConstants.Group.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineGroupSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineGroupKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineGroupKey = ReturnOrderConstants.Group.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineGroupSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineGroupKey);

        turnoverOnlineGroupSet.clear();
        grossProfitOnlineGroupSet.clear();
        shopOrderQuantityOnlineGroupSet.clear();
        returnTurnoverOnlineGroupSet.clear();
        returnGrossProfitOnlineGroupSet.clear();
        returnShopOrderQuantityOnlineGroupSet.clear();

        //Cloud
        String turnoverOnlineCloudKey = OrderConstants.Cloud.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineCloudSet = redisClient.getScoredSortedSet(turnoverOnlineCloudKey);
        //门店月毛利额
        String grossProfitOnlineCloudKey = OrderConstants.Cloud.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineCloudSet = redisClient.getScoredSortedSet(grossProfitOnlineCloudKey);
        //门店订单数量
        String shopOrderQuantityOnlineCloudKey = OrderConstants.Cloud.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineCloudSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineCloudKey);
        //门店月销售
        String returnTurnoverOnlineCloudKey = ReturnOrderConstants.Cloud.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineCloudSet = redisClient.getScoredSortedSet(returnTurnoverOnlineCloudKey);
        //门店月毛利额
        String returnGrossProfitOnlineCloudKey = ReturnOrderConstants.Cloud.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineCloudSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineCloudKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineCloudKey = ReturnOrderConstants.Cloud.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineCloudSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineCloudKey);

        turnoverOnlineCloudSet.clear();
        grossProfitOnlineCloudSet.clear();
        shopOrderQuantityOnlineCloudSet.clear();
        returnTurnoverOnlineCloudSet.clear();
        returnGrossProfitOnlineCloudSet.clear();
        returnShopOrderQuantityOnlineCloudSet.clear();

        //CloudApp
        String turnoverOnlineCloudAppKey = OrderConstants.Cloud_App.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineCloudAppSet = redisClient.getScoredSortedSet(turnoverOnlineCloudAppKey);
        //门店月毛利额
        String grossProfitOnlineCloudAppKey = OrderConstants.Cloud_App.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineCloudAppSet = redisClient.getScoredSortedSet(grossProfitOnlineCloudAppKey);
        //门店订单数量
        String shopOrderQuantityOnlineCloudAppKey = OrderConstants.Cloud_App.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineCloudAppSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineCloudAppKey);
        //门店月销售
        String returnTurnoverOnlineCloudAppKey = ReturnOrderConstants.Cloud_App.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineCloudAppSet = redisClient.getScoredSortedSet(returnTurnoverOnlineCloudAppKey);
        //门店月毛利额
        String returnGrossProfitOnlineCloudAppKey = ReturnOrderConstants.Cloud_App.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineCloudAppSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineCloudAppKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineCloudAppKey = ReturnOrderConstants.Cloud_App.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineCloudAppSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineCloudAppKey);

        turnoverOnlineCloudAppSet.clear();
        grossProfitOnlineCloudAppSet.clear();
        shopOrderQuantityOnlineCloudAppSet.clear();
        returnTurnoverOnlineCloudAppSet.clear();
        returnGrossProfitOnlineCloudAppSet.clear();
        returnShopOrderQuantityOnlineCloudAppSet.clear();

        //Quick
        String turnoverOnlineQuickKey = OrderConstants.Quick.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineQuickSet = redisClient.getScoredSortedSet(turnoverOnlineQuickKey);
        //门店月毛利额
        String grossProfitOnlineQuickKey = OrderConstants.Quick.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineQuickSet = redisClient.getScoredSortedSet(grossProfitOnlineQuickKey);
        //门店订单数量
        String shopOrderQuantityOnlineQuickKey = OrderConstants.Quick.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineQuickSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineQuickKey);
        //门店月销售
        String returnTurnoverOnlineQuickKey = ReturnOrderConstants.Quick.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineQuickSet = redisClient.getScoredSortedSet(returnTurnoverOnlineQuickKey);
        //门店月毛利额
        String returnGrossProfitOnlineQuickKey = ReturnOrderConstants.Quick.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineQuickSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineQuickKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineQuickKey = ReturnOrderConstants.Quick.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineQuickSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineQuickKey);

        turnoverOnlineQuickSet.clear();
        grossProfitOnlineQuickSet.clear();
        shopOrderQuantityOnlineQuickSet.clear();
        returnTurnoverOnlineQuickSet.clear();
        returnGrossProfitOnlineQuickSet.clear();
        returnShopOrderQuantityOnlineQuickSet.clear();

        //Warehouse
        String turnoverOnlineWarehouseKey = OrderConstants.Warehouse.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineWarehouseSet = redisClient.getScoredSortedSet(turnoverOnlineWarehouseKey);
        //门店月毛利额
        String grossProfitOnlineWarehouseKey = OrderConstants.Warehouse.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineWarehouseSet = redisClient.getScoredSortedSet(grossProfitOnlineWarehouseKey);
        //门店订单数量
        String shopOrderQuantityOnlineWarehouseKey = OrderConstants.Warehouse.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineWarehouseSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineWarehouseKey);
        //门店月销售
        String returnTurnoverOnlineWarehouseKey = ReturnOrderConstants.Warehouse.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineWarehouseSet = redisClient.getScoredSortedSet(returnTurnoverOnlineWarehouseKey);
        //门店月毛利额
        String returnGrossProfitOnlineWarehouseKey = ReturnOrderConstants.Warehouse.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineWarehouseSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineWarehouseKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineWarehouseKey = ReturnOrderConstants.Warehouse.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineWarehouseSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineWarehouseKey);

        turnoverOnlineWarehouseSet.clear();
        grossProfitOnlineWarehouseSet.clear();
        shopOrderQuantityOnlineWarehouseSet.clear();
        returnTurnoverOnlineWarehouseSet.clear();
        returnGrossProfitOnlineWarehouseSet.clear();
        returnShopOrderQuantityOnlineWarehouseSet.clear();

        //WarehouseApp
        String turnoverOnlineWarehouseAppKey = OrderConstants.Warehouse_App.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> turnoverOnlineWarehouseAppSet = redisClient.getScoredSortedSet(turnoverOnlineWarehouseAppKey);
        //门店月毛利额
        String grossProfitOnlineWarehouseAppKey = OrderConstants.Warehouse_App.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> grossProfitOnlineWarehouseAppSet = redisClient.getScoredSortedSet(grossProfitOnlineWarehouseAppKey);
        //门店订单数量
        String shopOrderQuantityOnlineWarehouseAppKey = OrderConstants.Warehouse_App.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> shopOrderQuantityOnlineWarehouseAppSet = redisClient.getScoredSortedSet(shopOrderQuantityOnlineWarehouseAppKey);
        //门店月销售
        String returnTurnoverOnlineWarehouseAppKey = ReturnOrderConstants.Warehouse_App.TURNOVER + current$yyMMdd;
        RScoredSortedSet<Long> returnTurnoverOnlineWarehouseAppSet = redisClient.getScoredSortedSet(returnTurnoverOnlineWarehouseAppKey);
        //门店月毛利额
        String returnGrossProfitOnlineWarehouseAppKey = ReturnOrderConstants.Warehouse_App.GROSS_PROFIT + current$yyMMdd;
        RScoredSortedSet<Long> returnGrossProfitOnlineWarehouseAppSet = redisClient.getScoredSortedSet(returnGrossProfitOnlineWarehouseAppKey);
        //门店订单数量
        String returnShopOrderQuantityOnlineWarehouseAppKey = ReturnOrderConstants.Warehouse_App.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Long> returnShopOrderQuantityOnlineWarehouseAppSet = redisClient.getScoredSortedSet(returnShopOrderQuantityOnlineWarehouseAppKey);

        turnoverOnlineWarehouseAppSet.clear();
        grossProfitOnlineWarehouseAppSet.clear();
        shopOrderQuantityOnlineWarehouseAppSet.clear();
        returnTurnoverOnlineWarehouseAppSet.clear();
        returnGrossProfitOnlineWarehouseAppSet.clear();
        returnShopOrderQuantityOnlineWarehouseAppSet.clear();

        for (int i = 0; i < realTimeReportByDates.size(); i++) {
            RealTimeReportByDate it = realTimeReportByDates.get(i);
            BigDecimal sales = it.getSales();
            BigDecimal cost = it.getCost();
            BigDecimal quantity = it.getQuantity();
            BigDecimal returnCost = it.getReturnCost();
            BigDecimal returnSales = it.getReturnSales();
            BigDecimal returnQuantity = it.getReturnQuantity();
            Long shopId = it.getShopId();
            //BigDecimal ttSales = sales.subtract(cost).add(returnSales);
            BigDecimal ttlQuantity = quantity.add(returnQuantity);
            turnoverSet.addScore(shopId, sales);

            shopOrderQuantitySet.addScore(shopId,ttlQuantity);

            if (it.getSourceType().equals(SourceTypeConstants.App)
               && it.getOrderType().equals(OrderTypeConstants.Normal)) {
                // APP
                {
                    turnoverOnlineSet.addScore(shopId, sales);
                    grossProfitOnlineSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineSet.addScore(shopId, quantity);
                    returnTurnoverOnlineSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getSourceType().equals(SourceTypeConstants.Elm)){
                {
                    turnoverOnlineElmSet.addScore(shopId, sales);
                    grossProfitOnlineElmSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineElmSet.addScore(shopId, quantity);
                    returnTurnoverOnlineElmSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineElmSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineElmSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getSourceType().equals(SourceTypeConstants.Mini)
                    && it.getOrderType().equals(OrderTypeConstants.Normal)){
                // MINI
                {
                    turnoverOnlineMiniSet.addScore(shopId, sales);
                    grossProfitOnlineMiniSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineMiniSet.addScore(shopId, quantity);
                    returnTurnoverOnlineMiniSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineMiniSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineMiniSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getSourceType().equals(SourceTypeConstants.Jddj)){
                {
                    turnoverOnlineJddjSet.addScore(shopId, sales);
                    grossProfitOnlineJddjSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineJddjSet.addScore(shopId, quantity);
                    returnTurnoverOnlineJddjSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineJddjSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineJddjSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getOrderType().equals(OrderTypeConstants.Group)){
                //Group
                {
                    turnoverOnlineGroupSet.addScore(shopId, sales);
                    grossProfitOnlineGroupSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineGroupSet.addScore(shopId, quantity);
                    returnTurnoverOnlineGroupSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineGroupSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineGroupSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getSourceType().equals(SourceTypeConstants.Cloud_App)){
                //Cloud_App
                {
                    turnoverOnlineCloudAppSet.addScore(shopId, sales);
                    grossProfitOnlineCloudAppSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineCloudAppSet.addScore(shopId, quantity);
                    returnTurnoverOnlineCloudAppSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineCloudAppSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineCloudAppSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getSourceType().equals(SourceTypeConstants.Cloud_Mini)){
                // Cloud_Mini
                {
                    turnoverOnlineCloudSet.addScore(shopId, sales);
                    grossProfitOnlineCloudSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineCloudSet.addScore(shopId, quantity);
                    returnTurnoverOnlineCloudSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineCloudSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineCloudSet.addScore(shopId, returnQuantity);
                }
            }else if(it.getOrderType().equals(OrderTypeConstants.Quick)){
                //Quick
                {
                    turnoverOnlineQuickSet.addScore(shopId, sales);
                    grossProfitOnlineQuickSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineQuickSet.addScore(shopId, quantity);
                    returnTurnoverOnlineQuickSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineQuickSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineQuickSet.addScore(shopId, returnQuantity);
                }
            }else if (it.getSourceType().equals(SourceTypeConstants.App)
                    && it.getOrderType().equals(OrderTypeConstants.Warehouse)) {
                // Warehouse_APP
                {
                    turnoverOnlineWarehouseAppSet.addScore(shopId, sales);
                    grossProfitOnlineWarehouseAppSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineWarehouseAppSet.addScore(shopId, quantity);
                    returnTurnoverOnlineWarehouseAppSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineWarehouseAppSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineWarehouseAppSet.addScore(shopId, returnQuantity);
                }
            }else if (it.getSourceType().equals(SourceTypeConstants.Mini)
                    && it.getOrderType().equals(OrderTypeConstants.Warehouse)) {
                // Warehouse
                {
                    turnoverOnlineWarehouseSet.addScore(shopId, sales);
                    grossProfitOnlineWarehouseSet.addScore(shopId, sales.subtract(cost));
                    shopOrderQuantityOnlineWarehouseSet.addScore(shopId, quantity);
                    returnTurnoverOnlineWarehouseSet.addScore(shopId, returnSales);
                    returnGrossProfitOnlineWarehouseSet.addScore(shopId, returnSales.subtract(returnCost));
                    returnShopOrderQuantityOnlineWarehouseSet.addScore(shopId, returnQuantity);
                }
            }
        }
        return true;
    }

}


/**
 * create table real_time_report_by_date
 * (
 * id                        bigint(32) auto_increment
 * primary key,
 * shop_id                   bigint(32)             not null comment '门店编号',
 * source_type            int(5)                  not null comment  '渠道',
 * sales               decimal(12, 2)          null comment '销售额',
 * quantity              decimal(12, 3)         null comment '订单数量',
 * cost               decimal(12, 2)         null comment '成本',
 * average_amount    decimal(12, 2)         not null comment '客单价',
 * return_sales       decimal(12, 2)         null comment '退单金额',
 * return_quantity              decimal(12, 3)         null comment '退单数量',
 * return_cost               decimal(12, 2)         null comment '成本',
 * return_average_amount    decimal(12, 2)         not null comment '客单价',
 * date_time                 datetime not null comment '时间'
 * );
 * <p>
 * create index idx_date_time
 * on real_time_report_history_by_date (date_time);
 * <p>
 * create index idx_shop_id
 * on real_time_report_history_by_date (shop_id);
 **/