package com.pinshang.qingyun.report.dto.settle;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class ShopStockInReportODTO {

    @ApiModelProperty("组织名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    private Long shopId;
    private String shopCode;
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;
    @ApiModelProperty("客户编码")
    private String storeCode;


    private Long commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    private String commodityUnitName;
    private String barCode;
    @ApiModelProperty("商品条形码")
    private List<String> barCodeList;

    @ApiModelProperty("订货数量")
    private BigDecimal orderQuantity;
    @ApiModelProperty("订货金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("实发数量")
    private BigDecimal deliveryQuantity;
    @ApiModelProperty("实发金额")
    private BigDecimal deliveryAmount;
    @ApiModelProperty("调拨入库数量")
    private BigDecimal allotInQuantity;
    @ApiModelProperty("调拨入库金额")
    private BigDecimal allotInAmount;
    @ApiModelProperty("调拨出库数量")
    private BigDecimal allotOutQuantity;
    @ApiModelProperty("调拨出库金额")
    private BigDecimal allotOutAmount;
    @ApiModelProperty("实退数量")
    private BigDecimal returnQuantity;
    @ApiModelProperty("实退金额")
    private BigDecimal returnAmount;

    @ApiModelProperty("云超转入数量")
    private BigDecimal cloudInQuantity;
    @ApiModelProperty("云超转入金额")
    private BigDecimal cloudInAmount;

    @ApiModelProperty("发货数量总计")
    private BigDecimal totalQuantity;
    @ApiModelProperty("发货金额总计")
    private BigDecimal totalAmount;

    @ApiModelProperty("大类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
    private String commodityFirstCateName;

    @ApiModelProperty("中类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName,keyName = "commodityId")
    private String commoditySecondCateName;

    @ApiModelProperty("小类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
    private String commodityThirdCateName;

    private Long commodityFirstId;
    private Long commoditySecondId;
    private Long commodityThirdId;

}
