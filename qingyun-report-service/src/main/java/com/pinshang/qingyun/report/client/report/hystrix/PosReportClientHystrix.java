package com.pinshang.qingyun.report.client.report.hystrix;

import com.pinshang.qingyun.report.client.report.service.PosReportClient;
import com.pinshang.qingyun.report.dto.ExceptionMemberODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PosReportClientHystrix implements FallbackFactory<PosReportClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	@Override
	public PosReportClient create(Throwable throwable) {
		return new PosReportClient() {

			@Override
			public List<PayTypeODTO> getAllPayTypeList() {
				return null;
			}

			@Override
			public TablePageInfo<CashierReconciledODTO> selectAccountRecordList(CashierReconciledIDTO cashierReconciledIDTO) {
				return null;
			}

			@Override
			public ExceptionMemberODTO findMemberAccountExecptionInfo(Long shopId, Long userId, String cashDate) {
				return null;
			}

			@Override
			public List<OrderCodeODTO> findOrderCodeByReturnCode(List<Long> returnCodeList) {
				return null;
			}
		};
	}
}
