package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.GiftCardSalesWater;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface GiftCardSalesWaterMapper extends MyMapper<GiftCardSalesWater> {

    List<GiftCardSalesWaterPage> salesWaterPage(@Param("dto") QueryGiftCardWaterDTO dto);

    GiftCardSalesWaterPage salesWaterSum(@Param("dto") QueryGiftCardWaterDTO dto);

    List<ConsignmentCommoditySalesReportODTO> selectConsignmentCommoditySales(ConsignmentCommoditySalesReportIDTO dto);

        List<ConsignmentShopSalesReportODTO> selectConsignmentShopCommoditySales(ConsignmentShopSalesReportIDTO dto);
}
