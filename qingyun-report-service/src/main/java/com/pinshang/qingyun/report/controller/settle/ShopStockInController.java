package com.pinshang.qingyun.report.controller.settle;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.export.ShopStockInCommodityExportRespVo;
import com.pinshang.qingyun.report.dto.export.ShopStockInSummaryExportRespVo;
import com.pinshang.qingyun.report.dto.settle.ShopStockInReportODTO;
import com.pinshang.qingyun.report.dto.settle.ShopStockInReportQueryIDTO;
import com.pinshang.qingyun.report.service.settle.ShopStockInAsyncService;
import com.pinshang.qingyun.report.service.settle.ShopStockInService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2022/5/24
 */

@Slf4j
@RestController
@RequestMapping("/shopStockIn")
public class ShopStockInController {
    public static final String CREATE_SHOP_STOCK_IN_COMMODITY_LOCK = "shop_stock_in_commodity_lock";
    public static final Long SHOP_STOCK_IN_COMMODITY_LOCK_EXPIRE_TIME = 60L;

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private ShopStockInService shopStockInService;
    @Autowired
    private ShopStockInAsyncService shopStockInAsyncService;

    @Autowired
    private IRenderService renderService;
    /**
     * 门店实际入库汇总
     * @param timeStamp
     */
    @PostMapping("/shopStockInTax")
    public void shopStockInTax(@RequestParam(value = "timeStamp") String timeStamp) {
        String cacheKey = ShopStockInController.CREATE_SHOP_STOCK_IN_COMMODITY_LOCK;
        RLock lock = redissonClient.getLock(cacheKey);

        try {
            log.info("开始----------------门店实际入库+锁-------" + new Date());
            lock.lock(ShopStockInController.SHOP_STOCK_IN_COMMODITY_LOCK_EXPIRE_TIME, TimeUnit.SECONDS);

            //汇总
            shopStockInAsyncService.shopStockInTax(timeStamp);

        } finally {
            log.info("结束----------------门店实际入库释放锁-------" + new Date());
            lock.unlock();
        }

    }


    @ApiOperation(value = "门店商品结算报表", notes = "门店商品结算报表")
    @PostMapping("/shopStockInReport")
    @MethodRender
    public TablePageInfo<ShopStockInReportODTO> shopStockInReport(@RequestBody ShopStockInReportQueryIDTO vo) {
        return shopStockInService.shopStockInReport(vo);
    }


    @ApiOperation(value = "导出门店实收结算汇总表", notes = "导出门店实收结算汇总表")
    @GetMapping("/exportShopStockInReport")
    @FileCacheQuery(bizCode = "SHOP_STOCK_IN_REPORT")
    public void exportShopStockInReport(ShopStockInReportQueryIDTO vo, HttpServletResponse response) throws IOException {
        Assert.notNull(vo.getReportType(), "参数错误");
        vo.initExportPage();
        TablePageInfo<ShopStockInReportODTO> result = shopStockInService.shopStockInReport(vo);
        List<ShopStockInReportODTO> list = result.getList();
        try {
            List<ShopStockInSummaryExportRespVo> exportList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                renderService.render(list, "/exportShopStockInReport");
                ShopStockInReportODTO total = (ShopStockInReportODTO) result.getHeader();
                exportList = BeanCloneUtils.copyTo(list, ShopStockInSummaryExportRespVo.class);

                ShopStockInSummaryExportRespVo totalRespVo = BeanCloneUtils.copyTo(total,ShopStockInSummaryExportRespVo.class);
                totalRespVo.setOrgName("合计");
                exportList.add(0,totalRespVo);
            }
            ExcelUtil.setFileNameAndHead(response,  "门店实收结算汇总表");
            EasyExcel.write(response.getOutputStream(), ShopStockInSummaryExportRespVo.class)
                    .autoCloseStream(Boolean.TRUE).sheet("门店实收结算汇总表").doWrite(exportList);

        }catch (Exception e){
            log.error("门店实收结算汇总表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

        /*Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        if (null != list && !list.isEmpty()) {
            renderService.render(list, "/exportShopStockInReport");
            ShopStockInReportODTO sumODTO = (ShopStockInReportODTO) result.getHeader();
            sumODTO.setOrgName("合计");
            list.add(0, sumODTO);
            for (ShopStockInReportODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getOrgName());
                dataLst.add(dto.getStoreCode());
                dataLst.add(dto.getShopName());

                if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.COMMODITY)){
                    dataLst.add(dto.getCommodityCode());
                    dataLst.add(dto.getCommodityName());
                    dataLst.add(dto.getBarCode());
                    dataLst.add(dto.getCommoditySpec());
                    dataLst.add(dto.getCommodityUnitName());

                    dataLst.add(dto.getOrderQuantity() + "");
                    dataLst.add(dto.getOrderAmount() + "");
                    dataLst.add(dto.getDeliveryQuantity() + "");
                    dataLst.add(dto.getDeliveryAmount() + "");
                    dataLst.add(dto.getAllotInQuantity() + "");
                    dataLst.add(dto.getAllotInAmount() + "");
                    dataLst.add(dto.getCloudInQuantity() + "");
                    dataLst.add(dto.getCloudInAmount() + "");
                    dataLst.add(dto.getReturnQuantity() + "");
                    dataLst.add(dto.getReturnAmount() + "");
                    dataLst.add(dto.getAllotOutQuantity() + "");
                    dataLst.add(dto.getAllotOutAmount() + "");
                    dataLst.add(dto.getTotalQuantity() + "");
                    dataLst.add(dto.getTotalAmount() + "");
                    dataLst.add(dto.getCommodityFirstCateName());
                    dataLst.add(dto.getCommoditySecondCateName());
                    dataLst.add(dto.getCommodityThirdCateName());
                }

                if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.FIRST_CATE)
                    || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.SECOND_CATE)
                    || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.THIRD_CATE)){
                    dataLst.add(dto.getOrderAmount() + "");
                    dataLst.add(dto.getDeliveryAmount() + "");
                    dataLst.add(dto.getAllotInAmount() + "");
                    dataLst.add(dto.getCloudInAmount() + "");
                    dataLst.add(dto.getReturnAmount() + "");
                    dataLst.add(dto.getAllotOutAmount() + "");
                    dataLst.add(dto.getTotalAmount() + "");
                    dataLst.add(dto.getCommodityFirstCateName());
                    dataLst.add(dto.getCommoditySecondCateName());
                    dataLst.add(dto.getCommodityThirdCateName());
                }
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        String fileName = "";
        if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.COMMODITY)){
            fileName = "门店商品实收结算表";
            map.put("sheetTitle", ExcelSheetTitleEnum.STOCK_IN_REPORT_COMMODITY);
        }

        if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.FIRST_CATE)
          || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.SECOND_CATE)
          || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.THIRD_CATE)){
            fileName = "门店实收结算汇总表" ;
            map.put("sheetTitle", ExcelSheetTitleEnum.STOCK_IN_REPORT_CATE);
        }

        // 参数设置
        map.put("filename", fileName);
        map.put("data", data);

        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);*/
    }

    @ApiOperation(value = "导出门店实收结算商品表", notes = "导出门店实收结算商品表")
    @PostMapping("/exportShopStockInReportV2")
    @FileCacheQuery(bizCode = "SHOP_STOCK_IN_REPORT")
    public void exportShopStockInReportV2(@RequestBody ShopStockInReportQueryIDTO vo, HttpServletResponse response) throws IOException {
        Assert.notNull(vo.getReportType(), "参数错误");
        vo.initExportPage();
        TablePageInfo<ShopStockInReportODTO> result = shopStockInService.shopStockInReport(vo);
        List<ShopStockInReportODTO> list = result.getList();
        try {
            List<ShopStockInCommodityExportRespVo> exportList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                renderService.render(result.getList(), "/exportShopStockInReportV2");
                ShopStockInReportODTO total = (ShopStockInReportODTO) result.getHeader();

                exportList = BeanCloneUtils.copyTo(list, ShopStockInCommodityExportRespVo.class);

                ShopStockInCommodityExportRespVo totalRespVo = BeanCloneUtils.copyTo(total,ShopStockInCommodityExportRespVo.class);
                totalRespVo.setOrgName("合计");
                exportList.add(0,totalRespVo);
            }
            ExcelUtil.setFileNameAndHead(response,  "门店实收结算商品表");
            EasyExcel.write(response.getOutputStream(), ShopStockInCommodityExportRespVo.class)
                    .autoCloseStream(Boolean.TRUE).sheet("门店实收结算商品表").doWrite(exportList);

        }catch (Exception e){
            log.error("门店实收结算商品表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }
        /*List<ShopStockInReportODTO> list = new ArrayList<>();

        // 通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.setSheet(0);
        //参数为true时只导出有别名的
        writer.setOnlyAlias(true);
        writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
        Workbook workbook = writer.getWorkbook();
        StyleSet styleSet = writer.getStyleSet();
        DataFormat dataFormat = workbook.createDataFormat();
        CellStyle cellStyleForDate = styleSet.getCellStyleForDate();
        cellStyleForDate.setDataFormat(dataFormat.getFormat("yyyy-MM-dd HH:mm:ss"));

        //自定义标题别名
        if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.COMMODITY)) {
            writer.addHeaderAlias("orgName", "部门");
            writer.addHeaderAlias("storeCode", "客户编码");
            writer.addHeaderAlias("shopName", "门店名称");
            writer.addHeaderAlias("commodityCode", "商品编码");
            writer.addHeaderAlias("commodityName", "商品名称");
            writer.addHeaderAlias("barCode", "条码");
            writer.addHeaderAlias("commoditySpec", "规格");
            writer.addHeaderAlias("commodityUnitName", "计量单位");
            writer.addHeaderAlias("orderQuantity", "订货数量");
            writer.addHeaderAlias("orderAmount", "订货金额");
            writer.addHeaderAlias("deliveryQuantity", "实发数量");
            writer.addHeaderAlias("deliveryAmount", "实发金额");
            writer.addHeaderAlias("allotInQuantity", "调拨入库数量");
            writer.addHeaderAlias("allotInAmount", "调拨入库金额");
            writer.addHeaderAlias("cloudInQuantity", "云超转入数量");
            writer.addHeaderAlias("cloudInAmount", "云超转入金额");
            writer.addHeaderAlias("returnQuantity", "实退数量");
            writer.addHeaderAlias("returnAmount", "实退金额");
            writer.addHeaderAlias("allotOutQuantity", "调拨出库数量");
            writer.addHeaderAlias("allotOutAmount", "调拨出库金额");
            writer.addHeaderAlias("totalQuantity", "实收数量总计");
            writer.addHeaderAlias("totalAmount", "实收金额总计");
            writer.addHeaderAlias("commodityFirstCateName", "后台大类");
            writer.addHeaderAlias("commoditySecondCateName", "后台中类");
            writer.addHeaderAlias("commodityThirdCateName", "后台小类");
        }

        if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.FIRST_CATE)
                || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.SECOND_CATE)
                || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.THIRD_CATE)){
            writer.addHeaderAlias("orgName", "部门");
            writer.addHeaderAlias("storeCode", "客户编码");
            writer.addHeaderAlias("shopName", "门店名称");
            writer.addHeaderAlias("orderAmount", "订货金额");
            writer.addHeaderAlias("deliveryAmount", "实发金额");
            writer.addHeaderAlias("allotInAmount", "调拨入库金额");
            writer.addHeaderAlias("cloudInAmount", "云超调入金额");
            writer.addHeaderAlias("returnAmount", "实退金额");
            writer.addHeaderAlias("allotOutAmount", "调拨出库金额");
            writer.addHeaderAlias("totalAmount", "实收金额总计");
            writer.addHeaderAlias("commodityFirstCateName", "后台大类");
            writer.addHeaderAlias("commoditySecondCateName", "后台中类");
            writer.addHeaderAlias("commodityThirdCateName", "后台小类");
        }
        ShopStockInReportODTO sumODTO = (ShopStockInReportODTO) result.getHeader();
        sumODTO.setOrgName("合计");
        list.add(sumODTO);
        list.addAll(result.getList());

        writer.write(list, true);
        Sheet sheet = writer.getSheet();
        int cellNum = sheet.getRow(0).getPhysicalNumberOfCells();
        for (int i = 0; i < cellNum; i++) {
            // 调整每一列宽度
            sheet.autoSizeColumn((short) i);
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 17 / 10);
        }
        String fileName = "";
        if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.COMMODITY)){
            fileName =  "门店商品实收结算表.xlsx";
        }
        if(vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.FIRST_CATE)
                || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.SECOND_CATE)
                || vo.getReportType().equals(ShopStockInReportQueryIDTO.ReportType.THIRD_CATE)){
            fileName = "门店实收结算汇总表.xlsx";
        }

        fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        //写出倒流
        OutputStream out = null;
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            log.error("获取输出流异常");
            e.printStackTrace();
        }
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
        IoUtil.close(out);*/
    }
}
