package com.pinshang.qingyun.report.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by mengday.zhang on 2018/3/21.
 */
@Data
public class CommodityDetailReportODto {

    @ApiModelProperty("门店")
    private String shopName;

    @ApiModelProperty("大类")
    private String commodityFirstKind;
    @ApiModelProperty("中类")
    private String commoditySecondKind;
    @ApiModelProperty("小类")
    private String commodityThirdKind;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("副标题")
    private String commoditySubName;

    @ApiModelProperty("商品状态")
    private String commodityState;

    @ApiModelProperty("上架状态")
    private String appStatus;

    @ApiModelProperty("包装类型")
    private String commodityPackageKind;

    @ApiModelProperty("是否称重")
    private String isWeight;

    @ApiModelProperty("起卖重量(克)")
    private Integer sellWeight;

    @ApiModelProperty("计量单位")
    private String commodityUnit;

    @ApiModelProperty("税率")
    private String taxRate;

    @ApiModelProperty("是否即食")
    private String commodityIsInstant;

    @ApiModelProperty("贮存条件")
    private String storageCondition;

    @ApiModelProperty("保质期天数")
    private Double qualityDays;

    @ApiModelProperty("产地")
    private String origin;

    @ApiModelProperty("物流配送模式")
    private String logisticsModel;

    @ApiModelProperty("默认供应商")
    private String supplierName;

    @ApiModelProperty("销售箱装量")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty("采购箱规")
    private BigDecimal boxCapacity;

    @ApiModelProperty("默认主图")
    private String defaultPicUrl;

    /** 主图数量pic_urlN 有值的数量 */
    @ApiModelProperty("主图数量")
    private Integer picQuantity;

    @ApiModelProperty("长图")
    private String imgTextPicUrl;

    @ApiModelProperty("移动平均价")
    private BigDecimal weightPrice;

    @ApiModelProperty("进价")
    /** t_sub_order_item.price */
    private BigDecimal purchasePrice;

    @ApiModelProperty("基准价")
    private BigDecimal basePrice;

    /** 线上特价(t_xs_promotion_list) */
    @ApiModelProperty("线上特价")
    private BigDecimal commodityPrice;

    @ApiModelProperty("前7天销售数量")
    private Long previous7DayQuantity;

    @ApiModelProperty("前30天销售数量")
    private Long previous30DayQuantity;

    @ApiModelProperty("年至今销售数量")
    private Long yearToTodayQuantity;

    @ApiModelProperty("库存数量")
    private Integer stockQuantity;

    @ApiModelProperty("子码")
    private String barCodes;

    private Long shopId;
    private Long commodityId;
}
