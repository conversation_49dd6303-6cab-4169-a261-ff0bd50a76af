package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderDeleteODTO {

    private Long refId;

    @ApiModelProperty("门店ID")
    private  Long shopId;

    @ApiModelProperty("门店code")
    private String shopCode;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("pos机号")
    private String macCode;

    @ApiModelProperty("订单号")
    private Long orderCode;

    @ApiModelProperty("商品数量")
    private BigDecimal quantity;

    @ApiModelProperty("订单金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("操作类型：1挂单删除 2挂单未结算 3整单取消")
    private Integer operateType;

    @ApiModelProperty("操作类型")
    private String operateTypeName;

    @ApiModelProperty("挂单时间")
    private Date orderRestTime;

    @ApiModelProperty("挂单人账号")
    private String orderRestCasherCode;

    @ApiModelProperty("挂单人姓名")
    private String orderRestCasherName;

    @ApiModelProperty("作废时间")
    private Date orderDeleteTime;

    @ApiModelProperty("作废人账号")
    private String orderDeleteCasherCode;

    @ApiModelProperty("作废人姓名")
    private String orderDeleteCasherName;

    @ApiModelProperty("操作人姓名")
    private  String operateName;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("授权人账号")
    private String authorizerCode;

    @ApiModelProperty("授权人姓名")
    private String authorizerName;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

    @ApiModelProperty("pos机类型名称")
    private String posTypeName;
}
