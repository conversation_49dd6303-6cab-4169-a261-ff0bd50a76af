package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderCancelIDTO extends Pagination {

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("收银日期开始时间yyyy-MM-dd HH:mm:ss")
    private String beginDate;

    @ApiModelProperty("收银日期结束时间yyyy-MM-dd HH:mm:ss")
    private String endDate;

    @ApiModelProperty("收银员")
    private Integer casherId;

    @ApiModelProperty("门店编码")
    private String shopCode;

    private List<Long> shopIdList;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

    private BigDecimal cancelMorethanException = BigDecimal.ZERO;
}
