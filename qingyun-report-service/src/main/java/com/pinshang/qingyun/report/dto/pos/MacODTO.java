package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MacODTO {
    private Long id;
    /** 门店ID*/
    @ApiModelProperty("门店ID")
    private Long shopId;

    /** 门店名称*/
    @ApiModelProperty("门店名称")
    private String shopName;

    /**门店code*/
    @ApiModelProperty("门店code")
    private String shopCode;

    /**机器号*/
    @ApiModelProperty("机器号")
    private String macCode;

    /**机器名称*/
    @ApiModelProperty("机器名称")
    private String macName;

    /**安卓机mac标识*/
    @ApiModelProperty("安卓机mac标识")
    private String macId;

    /**绑定状态:0-未绑定,1-已绑定*/
    @ApiModelProperty("绑定状态:0-未绑定,1-已绑定")
    private Integer bindStatus;

    /**使用状态:0-未使用,1-培训模式,2-正式模式*/
    @ApiModelProperty("使用状态:0-未使用,1-培训模式,2-正式模式")
    private Integer useStatus;

    /**备注*/
    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;
}
