package com.pinshang.qingyun.report.controller.pos;

import com.pinshang.qingyun.base.controller.BaseController;
import com.pinshang.qingyun.report.annotation.NotAdvice;
import com.pinshang.qingyun.report.dto.pos.ComparePosReportODTO;
import com.pinshang.qingyun.report.dto.pos.ConsumeReportIDTO;
import com.pinshang.qingyun.report.dto.pos.WeightPriceODTO;
import com.pinshang.qingyun.report.enums.ReportTypeEnum;
import com.pinshang.qingyun.report.model.pos.*;
import com.pinshang.qingyun.report.service.pos.PosReportFixService;
import com.pinshang.qingyun.report.service.pos.SalesSummaryReportService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/posReportFix")
public class PosReportFixController extends BaseController {

    @Autowired
    private PosReportFixService posReportFixService;
    @Autowired
    private SalesSummaryReportService salesSummaryReportService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * pos报表消费
     * @param consumeReportIDTO
     */
    @ApiOperation(value = "pos报表消费", notes = "pos报表消费", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/consumeReport")
    public Boolean insertConsumeReport(@RequestBody ConsumeReportIDTO consumeReportIDTO) throws InterruptedException {

        String orderCodeKey = "REPORT:POS:CONSUME:"+ consumeReportIDTO.getOrderCode();
        RLock lock = redissonClient.getLock(orderCodeKey);
        if (lock.tryLock()) {
            try {
                return posReportFixService.insertConsumeReport(consumeReportIDTO);
            }finally{
                lock.unlock();
            }
        }
        return Boolean.TRUE;
    }

    /**
     * pos 称重品消费
     * @param weightPriceList
     */
    @ApiOperation(value = "pos 称重品消费", notes = "pos 称重品消费", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/posWeightPriceReport")
    public Boolean posWeightPriceReport(@RequestBody List<WeightPriceODTO> weightPriceList){
        return posReportFixService.posWeightPriceReport(weightPriceList);
    }
    /**
     * 保存收银流水
     * @param cashierWaterReports
     */
    @ApiOperation(value = "保存收银流水", notes = "保存收银流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertCashierWaterReports")
    public void insertCashierWaterReports(@RequestBody List<ReportCashierWater> cashierWaterReports){
        posReportFixService.insertCashierWaterReports(cashierWaterReports);
    }

    /**
     * 保存销售流水
     * @param salesWaters
     */
    @ApiOperation(value = "保存销售流水", notes = "保存销售流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertSalesOfTheWaterReport")
    public void insertSalesOfTheWaterReport(@RequestBody List<ReportSalesWater> salesWaters){
        posReportFixService.insertSalesOfTheWaterReport(salesWaters);
    }

    /**
     * 保存商品销售汇总
     * @param salesSummaryList
     */
    @ApiOperation(value = "保存商品销售汇总", notes = "保存商品销售汇总", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertSalesSummaryReport")
    public void insertSalesSummaryReport(@RequestBody List<SalesSummaryReport> salesSummaryList){
        posReportFixService.insertSalesSummaryReport(salesSummaryList);
    }

    /**
     * 保存客单分析
     * @param guestSummaryList
     */
    @ApiOperation(value = "保存客单分析", notes = "保存客单分析", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertGuestListSummaryReport")
    public void insertGuestListSummaryReport(@RequestBody List<GuestListSummaryReport> guestSummaryList){
        posReportFixService.insertGuestListSummaryReport(guestSummaryList);
    }

    /**
     * 保存收银员让利
     * @param cashierDiscountList
     */
    @ApiOperation(value = "保存收银员让利", notes = "保存收银员让利", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertCashierDiscountReport")
    public void insertCashierDiscountReport(@RequestBody List<CashierDiscountReport> cashierDiscountList){
        posReportFixService.insertCashierDiscountReport(cashierDiscountList);
    }

    /**
     * 保存手动优惠
     * @param handleDiscountList
     */
    @ApiOperation(value = "保存手动优惠", notes = "保存手动优惠", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertHandleDiscountReport")
    public void insertHandleDiscountReport(@RequestBody List<HandDiscountDetail> handleDiscountList){
        posReportFixService.insertHandleDiscountReport(handleDiscountList);
    }

    /**
     * 新增pos报表(收银流水报表,销售流水报表,销售汇总,客单分析,收银员让利,手动优惠)
     */
    @ApiOperation(value = "新增pos报表", notes = "新增pos报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/insertPosReport")
    public Boolean insertPosReport(@RequestBody ConsumeReportIDTO consumeReportIDTO){
        return posReportFixService.insertPosReport(consumeReportIDTO);
    }



    @PostMapping("/deleteCashierWaterReportByOrderCodes")
    public void deleteCashierWaterReportByOrderCodes(@RequestParam("orderCode") String orderCode){
        posReportFixService.deleteCashierWaterReportByOrderCodes(orderCode);
    }
    @PostMapping("/deleteSalesWaterReportByOrderCodes")
    public void deleteSalesWaterReportByOrderCodes(@RequestParam("orderCode") String orderCode){
        posReportFixService.deleteSalesWaterReportByOrderCodes(orderCode);
    }
    @PostMapping("/deleteSalesSummaryReportByOrderCodes")
    public void deleteSalesSummaryReportByOrderCodes(@RequestParam("orderCode") Long orderCode){
        posReportFixService.deleteSalesSummaryReportByOrderCodes(orderCode);
    }
    @PostMapping("/deleteGuestListSummaryReportByOrderCodes")
    public void deleteGuestListSummaryReportByOrderCodes(@RequestParam("orderCode") Long orderCode){
        posReportFixService.deleteGuestListSummaryReportByOrderCodes(orderCode);
    }
    @PostMapping("/deleteCashierDiscountReportByOrderCodes")
    public void deleteCashierDiscountReportByOrderCodes(@RequestParam("orderCode") Long orderCode){
        posReportFixService.deleteCashierDiscountReportByOrderCodes(orderCode);
    }
    @PostMapping("/deleteHandleDiscountReportByOrderCodes")
    public void deleteHandleDiscountReportByOrderCodes(@RequestParam("orderCode") Long orderCode){
        posReportFixService.deleteHandleDiscountReportByOrderCodes(orderCode);
    }
    /**
     * 根据订单号删除pos报表(收银流水报表,销售流水报表,销售汇总,客单分析,收银员让利,手动优惠)
     */
    @PostMapping("/deletePosReportByOrderCode")
    public Boolean deletePosReportByOrderCode(@RequestParam("orderCode") String orderCode){
        return posReportFixService.deletePosReportByOrderCode(orderCode);
    }


    @PostMapping("/deleteCashierWaterReportByTimeRange")
    public void deleteCashierWaterReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        posReportFixService.deleteCashierWaterReportByTimeRange(shopId,beginTime,endTime);
    }
    @PostMapping("/deleteSalesWaterReportByTimeRange")
    public void deleteSalesWaterReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        posReportFixService.deleteSalesWaterReportByTimeRange(shopId,beginTime,endTime);
    }
    @PostMapping("/deleteSalesSummaryReportByTimeRange")
    public void deleteSalesSummaryReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        posReportFixService.deleteSalesSummaryReportByTimeRange(shopId,beginTime,endTime);
    }
    @PostMapping("/deleteGuestListSummaryReportByTimeRange")
    public void deleteGuestListSummaryReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        posReportFixService.deleteGuestListSummaryReportByTimeRange(shopId,beginTime,endTime);
    }
    @PostMapping("/deleteCashierDiscountReportByTimeRange")
    public void deleteCashierDiscountReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        posReportFixService.deleteCashierDiscountReportByTimeRange(shopId,beginTime,endTime);
    }
    @PostMapping("/deleteHandleDiscountReportByTimeRange")
    public void deleteHandleDiscountReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        posReportFixService.deleteHandleDiscountReportByTimeRange(shopId,beginTime,endTime);
    }
    /**
     * 根据时间范围删除pos报表(收银流水报表,销售流水报表,销售汇总,客单分析,收银员让利,手动优惠)
     */
    @PostMapping("/deletePosReportReportByTimeRange")
    public Boolean deletePosReportReportByTimeRange(@RequestParam("shopId") Long shopId,@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        return posReportFixService.deletePosReportReportByTimeRange(shopId,beginTime,endTime);
    }


    @GetMapping("/getLastDateOrder")
    @NotAdvice
    public List<Payment> getLastDateOrder(@RequestParam("lastDate")String lastDate){
        return posReportFixService.getLastDateOrder(lastDate);
    }
    @GetMapping("/getLastSaleOrder")
    @NotAdvice
    public List<Payment> getLastSaleOrder(@RequestParam("lastDate")String lastDate){
        return posReportFixService.getLastSaleOrder(lastDate);
    }
    @GetMapping("/getLastSaleWaterOrder")
    @NotAdvice
    public List<Payment> getLastSaleWaterOrder(@RequestParam("lastDate")String lastDate){
        return posReportFixService.getLastSaleWaterOrder(lastDate);
    }
    @GetMapping("/getLastCashierWaterOrder")
    @NotAdvice
    public List<Payment> getLastCashierWaterOrder(@RequestParam("lastDate")String lastDate){
        return posReportFixService.getLastCashierWaterOrder(lastDate);
    }

    @GetMapping("/selectReportData")
    @NotAdvice
    public Boolean selectReportData(@RequestParam("reportTypeEnum") ReportTypeEnum reportTypeEnum, @RequestParam("orderCode") String orderCode){
        return posReportFixService.selectReportData(reportTypeEnum,orderCode);
    }

    @PostMapping("/thirdCateDaySummary")
    public Boolean thirdCateDaySummary(@RequestParam(value = "timeStamp") String timeStamp){
        return salesSummaryReportService.thirdCateDaySummary(timeStamp);
    }

    /**
     * 比较payment订单量和pos几张报表订单量
     * @return
     */
    @GetMapping("/comparePosReportOrder")
    @NotAdvice
    public ComparePosReportODTO comparePosReportOrder(@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime){
        return posReportFixService.comparePosReportOrder(beginTime,endTime);
    }


    /**
     * pos原单号退款，原单号没记录上
     * @param timeStamp
     * @return
     */
    @PostMapping("/fixOriginOrderCode")
    public Boolean fixOriginOrderCode(@RequestParam(value = "timeStamp") String timeStamp){
        return posReportFixService.fixOriginOrderCode(timeStamp);
    }
}
