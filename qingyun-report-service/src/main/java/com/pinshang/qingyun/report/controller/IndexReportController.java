package com.pinshang.qingyun.report.controller;

import com.pinshang.qingyun.report.annotation.NotAdvice;
import com.pinshang.qingyun.report.dto.index.*;
import com.pinshang.qingyun.report.service.IndexReportService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/indexReport")
public class IndexReportController {

    private final IndexReportService indexReportService;

    public IndexReportController(IndexReportService indexReportService) {
        this.indexReportService = indexReportService;
    }


    /**
     * 查询年月周的四大框数据
     * @param vo
     * @return
     */
    @RequestMapping(value = "/selectSalesDataHistory", method = RequestMethod.POST)
    @NotAdvice
    public SalesDataEntry selectSalesDataHistory(@RequestBody HomePageVo vo){
        return indexReportService.selectSalesDataHistory(vo);
    }

    /**
     * 曲线图周月年
     * @param vo
     * @return
     */
    @RequestMapping(value = "/selectSelectDataGraphicsHistory")
    @NotAdvice
    public List<YearOnYearDataEntry> selectSelectDataGraphicsHistory(@RequestBody HomePageVo vo){
        return indexReportService.selectSelectDataGraphicsHistory(vo);
    }

    /**
     * 门店排序周月年
     * @param vo
     * @return
     */
    @RequestMapping(value = "/selectHistoryStoreSale")
    @NotAdvice
    public List<StoreSaleEntry> selectHistoryStoreSale(@RequestBody HomePageVo vo){
        return indexReportService.selectHistoryStoreSale(vo);
    }

    /**
     * 数据补偿
     * @param vo
     * @return
     */
    @RequestMapping(value = "/compensateIndex")
    @NotAdvice
    public List<IndexEntry> compensateIndex(@RequestBody CompensateVo vo){
        return indexReportService.compensateIndex(vo);
    }

    @RequestMapping(value = "/selectOffHistorySaleByTimeSlot")
    @NotAdvice
    public List<IndexEntry> selectOffHistorySaleByTimeSlot(@RequestBody CompensateVo vo){
        return indexReportService.selectOffHistorySaleByTimeSlot(vo);
    }
    @ApiOperation(value = "监控商品总表(门店)和前置仓日汇总表", notes = "监控商品总表(门店)和前置仓日汇总表")
    @RequestMapping(value = "/monitorXsXdReportDay", method = RequestMethod.POST)
    @NotAdvice
    public Boolean monitorXsXdReportDay(@RequestParam("dateTime") String dateTime){
         indexReportService.monitorXsXdReportDay(dateTime);
        return Boolean.TRUE ;
    }
}
