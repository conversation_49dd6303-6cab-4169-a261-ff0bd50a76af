package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GuestListSummaryReportOrderODTO {
    /** 订单编码*/
    private  Long orderCode;
    /** 门店ID*/
    @ApiModelProperty("门店ID")
    private Long shopId;

    /** 门店code*/
    @ApiModelProperty("门店code")
    private String shopCode;

    /** 门店名称*/
    @ApiModelProperty("门店名称")
    private String shopName;

    private Long macId;

    /** 机器号*/
    @ApiModelProperty("机器号")
    private String macCode;

    /** 机器名称*/
    @ApiModelProperty("机器名称")
    private String macName;

    /** 客单量*/
    @ApiModelProperty("客单量")
    private BigDecimal guestQuantity;

    /** 客单金额*/
    @ApiModelProperty("客单金额")
    private BigDecimal guestAmount;

    @ApiModelProperty("客单总量")
    private BigDecimal totalGuestQuantity;//客单总量

    @ApiModelProperty("客单总金额")
    private BigDecimal totalGuestAmount;//客单总金额

    @ApiModelProperty("平均客单价")
    private BigDecimal avgGuestPrice;//平均客单价

    @ApiModelProperty("金额占比")
    private String avgAmount;//金额占比

    @ApiModelProperty("客单占比")
    private String avgQuantity;//客单占比

    /** 销售日期*/
    @ApiModelProperty("销售日期")
    private String saleTime;

    /** 时间段*/
    @ApiModelProperty("时间段")
    private Integer hourTime;

    @ApiModelProperty("时间段5：00—6：00")
    private String hourTimeStr;

    private Date createTime;

    private Long createId;

    private Date updateTime;

    private Long updateId;

    @ApiModelProperty("连带率")
    private String jointRate;
}
