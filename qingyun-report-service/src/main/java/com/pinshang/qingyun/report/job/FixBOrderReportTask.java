package com.pinshang.qingyun.report.job;

import com.pinshang.qingyun.report.service.ShopReportService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2020/10/10
 */
@Component
@Slf4j
public class FixBOrderReportTask {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ShopReportService shopReportService;

    /**
     * B端订单晚发货：修复 门店实际入库汇总（门店实收结算商品表、门店实收结算汇总表）
     *
     * 修复 xd-order送货日期之后配送成功、失败。进行数据补偿
     */
    // 每30分钟执行一次
    @Scheduled(cron ="00 0/30 * * * ?")
    //@Scheduled(cron ="0 0 0/2 * * ?") // 每2个小时跑一次
    public void scheduleJobs(){

        String lockKey = "fixBOrderReport_lock";
         RLock lock = redissonClient.getLock(lockKey);
        if (!lock.isLocked()) {
            lock.lock(10L, TimeUnit.MINUTES);
            try{
                // 修复 门店实际入库汇总（门店实收结算商品表、门店实收结算汇总表）
                shopReportService.fixBOrderReport();

                // 修复 xd-order送货日期之后配送成功、失败。进行数据补偿
                shopReportService.fixXdOrderDelayComplete();
            }finally{
                lock.unlock();
            }
        }
    }
}
