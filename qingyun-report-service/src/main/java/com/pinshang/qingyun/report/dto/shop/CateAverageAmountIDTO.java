package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CateAverageAmountIDTO extends Pagination {

    @Change(value = DataQueryConstant.NOW)
    private String date;

    /** 1 = 大类 2 = 中类 */
    private Integer summaryType;
    private Long shopId;
    private List<Long> shopIds = new ArrayList<>();
    private String lastweek;
    private Integer shopType;

    private Long provinceId;

}
