package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ShopOrderGoodReportIDto extends Pagination{

	@ApiModelProperty("门店类型")
	private Integer shopType;

	@ApiModelProperty("门店id")
	private Long shopId;

	@ApiModelProperty("送货日期开始 yyyy-MM-dd")
	private String beginDate;

	@ApiModelProperty("送货日期结束 yyyy-MM-dd")
	private String endDate;

	@ApiModelProperty("大类id")
	private Long cate1;

	@ApiModelProperty("中类id")
	private Long cate2;

	@ApiModelProperty("小类id")
	private Long cate3;

	@ApiModelProperty("商品idlist")
	private List<String> commodityIdList;

	@ApiModelProperty("条形码")
	private String barCode;

	@ApiModelProperty("工厂id")
	private Long factoryId;

	@ApiModelProperty("true:查询差异数据")
	private Boolean differ;
}
