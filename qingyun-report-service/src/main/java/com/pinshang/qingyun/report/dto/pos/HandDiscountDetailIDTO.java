package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.box.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2021/7/8
 */
@Data
public class HandDiscountDetailIDTO extends Pagination {

    @ApiModelProperty("所属部门编码")
    private String orgCode;

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("销售日期yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @ApiModelProperty("销售日期yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty("收银员编码")
    private String employeeNumber;

    @ApiModelProperty("后台大类id")
    private Long cateId1;

    @ApiModelProperty("手动优惠类型 21:手工议价 22:手工折扣 23:赠送 24:整单议价 25:整单折扣")
    private Integer handleDiscountType;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("开始小时")
    private String hourBegin;
    @ApiModelProperty("结束小时")
    private String hourEnd;
    @ApiModelProperty("手动优惠金额≥")
    private String compareOrderAmount;

    public void checkParam(){
        QYAssert.isTrue(StringUtils.isNotBlank(beginTime), "日期不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(endTime), "日期不能为空");

        int diff = DateUtil.getDayDif(DateUtil.parseDate(endTime, DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(beginTime, DateUtil.DEFAULT_DATE_FORMAT));

        if(StringUtils.isBlank(orgCode)
                && shopId == null
                && commodityId == null
                && StringUtils.isBlank(barCode)){
            QYAssert.isTrue(false, "门店、商品、条形码，不能都为空！");
        }
        if((shopId != null || StringUtils.isNotBlank(orgCode))
                && commodityId == null && StringUtils.isBlank(barCode)){
            QYAssert.isTrue(diff <= 30, "收银日期的跨度不能超过31天");
        }
        if(shopId == null
                && StringUtils.isBlank(orgCode)
                && (commodityId != null || StringUtils.isNotBlank(barCode))){
            QYAssert.isTrue(diff <= 7, "收银日期的跨度不能超过8天");
        }
    }
}
