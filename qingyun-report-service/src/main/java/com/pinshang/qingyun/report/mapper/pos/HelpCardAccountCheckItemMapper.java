package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceIDTO;
import com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceODTO;
import com.pinshang.qingyun.report.model.pos.HelpCardAccountCheckItem;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName HelpCardAccountCheckItemMapper
 * <AUTHOR>
 * @Date 2023/3/10 16:31
 * @Description HelpCardAccountCheckItemMapper
 * @Version 1.0
 */
@Repository
public interface HelpCardAccountCheckItemMapper extends MyMapper<HelpCardAccountCheckItem> {
    List<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByCompany(HelpCardCheckDetail4InvoiceIDTO idto);

    List<HelpCardCheckDetail4InvoiceODTO> selectCheckDetail4InvoiceByArea(HelpCardCheckDetail4InvoiceIDTO idto);
}
