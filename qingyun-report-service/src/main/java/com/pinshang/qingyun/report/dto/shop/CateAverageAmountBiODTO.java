package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @see ExcelSheetTitleEnum#CATE_AVERAGE_AMOUNT
 */
@Data
public class CateAverageAmountBiODTO {
    private Long shopId;
    private String shopNo;
    //门店
    String shopName;
    //分类
    String cateName;

    private Long categoryFirstId;
    private Long categorySecondId;

    private BigDecimal offlineSales = BigDecimal.ZERO;
    private BigDecimal lastOfflineSales = BigDecimal.ZERO;

    @ApiModelProperty("客单量")
    Long offlineVisitorNumber = 0L;
    @ApiModelProperty("上周客单量")
    Long lastweekOfflineVisitorNumber = 0L;

    @ApiModelProperty("客单价")
    BigDecimal offlineAverageAmount = BigDecimal.ZERO;
    @ApiModelProperty("上周客单价")
    BigDecimal lastweekOfflineAverageAmount = BigDecimal.ZERO;


}
