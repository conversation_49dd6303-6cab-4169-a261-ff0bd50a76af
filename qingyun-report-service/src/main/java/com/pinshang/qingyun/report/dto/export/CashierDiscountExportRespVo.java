package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CashierDiscountExportRespVo {

    @ExcelProperty("销售日期")
    private String saleTime;

    @ExcelProperty("门店编码")
    private String shopCode;

    @ExcelProperty("门店")
    private String shopName;

    @ExcelProperty("收银员编号")
    private String cashierNo;

    @ExcelProperty("收银员名称")
    private String cashierName;

    @ExcelProperty("原价金额")
    private BigDecimal realTotalAmount;

    @ExcelProperty("实收金额")
    private BigDecimal totalAmount;

    @ExcelProperty("让利金额")
    private BigDecimal discountAmount;
}
