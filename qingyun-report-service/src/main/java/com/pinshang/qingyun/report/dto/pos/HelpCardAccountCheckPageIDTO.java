package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName PosHelpCardAccountCheckingPageIDTO
 * <AUTHOR>
 * @Date 2023/2/24 10:25
 * @Description PosHelpCardAccountCheckingPageIDTO
 * @Version 1.0
 */
@Data
public class HelpCardAccountCheckPageIDTO extends Pagination {
    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("对账状态, 1-未对账, 2-已对账, 3-已取消")
    private Integer status;
}
