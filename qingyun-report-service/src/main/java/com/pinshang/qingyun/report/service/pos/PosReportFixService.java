package com.pinshang.qingyun.report.service.pos;


import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.commodity.CommodityPackageTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.client.report.service.PosReportClient;
import com.pinshang.qingyun.report.dto.pos.ComparePosReportODTO;
import com.pinshang.qingyun.report.dto.pos.ConsumeReportIDTO;
import com.pinshang.qingyun.report.dto.pos.OrderCodeODTO;
import com.pinshang.qingyun.report.dto.pos.WeightPriceODTO;
import com.pinshang.qingyun.report.enums.ReportTypeEnum;
import com.pinshang.qingyun.report.mapper.GuestListSummaryReport10DayMapper;
import com.pinshang.qingyun.report.mapper.pos.*;
import com.pinshang.qingyun.report.model.pos.*;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxService;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxTransService;
import com.pinshang.qingyun.report.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PosReportFixService {

    @Autowired
    private CashierWaterReportMapper cashierWaterReportMapper;

    @Autowired
    private SalesWaterReportMapper salesWaterReportMapper;

    @Autowired
    private SalesSummaryReportMapper salesSummaryReportMapper;

    @Autowired
    private GuestListSummaryReportMapper guestListSummaryReportMapper;
    @Autowired
    private GuestListSummaryReport10DayMapper guestListSummaryReport10DayMapper;
    @Autowired
    private CashierDiscountReportMapper cashierDiscountReportMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ShopCommodityTaxTransService shopCommodityTaxTransService;

    @Autowired
    private WeightPriceMapper weightPriceMapper;
    @Autowired
    private WeightOverPriceMapper weightOverPriceMapper;
    @Autowired
    private HandleDiscountMapper handleDiscountMapper;
    @Lazy
    @Autowired
    private PosNotCurrentDayService posNotCurrentDayService;
    @Autowired
    private PosReportClient posReportClient;
    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;
    @Autowired
    private HelpCardTraderSummaryMapper helpCardTraderSummaryMapper;

    /**
     * pos报表消费
     * @param consumeReportIDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertConsumeReport(ConsumeReportIDTO consumeReportIDTO) {
        QYAssert.notNull(consumeReportIDTO, "pos报表消费参数错误");
        String orderCode = consumeReportIDTO.getOrderCode();
        QYAssert.notNull(orderCode, "订单号不能为空");

        log.info("begin consumeReportIDTO orderCode={} is consume message", orderCode);

        Boolean isExist = selectReportData(ReportTypeEnum.SALES_WATER, orderCode);
        if (isExist) {
            log.warn("pos报表orderCode={}已存在, 忽略该消息", orderCode);
            return false;
        }

        // 用数据库主键判断重复
        cashierWaterReportMapper.insertConsumeRepeat(Long.valueOf(orderCode));
        insertCashierWaterReports(consumeReportIDTO.getCashierWaterReports());
        insertSalesOfTheWaterReport(consumeReportIDTO.getSalesWaters());
        insertSalesSummaryReport(consumeReportIDTO.getSalesSummaryList());
        insertGuestListSummaryReport(consumeReportIDTO.getGuestList());

        List<CashierDiscountReport> cashierDiscountList = consumeReportIDTO.getCashierDiscountList();
        if(CollectionUtils.isNotEmpty(cashierDiscountList)){
            insertCashierDiscountReport(cashierDiscountList);
        }

        //帮困卡
        List<HelpCardTraderSummary> helpCardTraderSummaryList = consumeReportIDTO.getHelpCardTraderSummaryList();
        if(CollectionUtils.isNotEmpty(helpCardTraderSummaryList)) {
            helpCardTraderSummaryMapper.insertList(helpCardTraderSummaryList);
        }

        List<HandDiscountDetail> handleDiscountList = consumeReportIDTO.getHandleDiscountList();
        if(CollectionUtils.isNotEmpty(handleDiscountList)){
            insertHandleDiscountReport(handleDiscountList);
        }

        // 非当日订单
        if(!isCurrentDay(orderCode)){
            // 非当日订单，将日期放入redisson 每2个小时执行一次，跑商品总表
            posNotCurrentDayService.setDateListToRedisson(orderCode);

            // 非当日订单,直接新增到pos报表和ES中
            posNotCurrentDayService.consumePosNotCurrentDayReport(consumeReportIDTO);
        }

        // 异步判断订单sum(收银流水.pay_amount) 是否等于 sum(销售流水.transaction_amount)
        // 不相等则发送微信消息
        posNotCurrentDayService.checkAmount(consumeReportIDTO);
        log.info("fished consumeReportIDTO  orderCode={} is consume message", orderCode);
        return true;
    }

    /**
     *  pos 称重品消费
     * @param weightPriceList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean posWeightPriceReport(List<WeightPriceODTO> weightPriceList){
        String orderCodeKey = "POS:WEIGHTPRICEREPORT";
        RLock lock = redissonClient.getLock(orderCodeKey);
        lock.lock();
        try {
            List<Long> commodityIdList = weightPriceList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<String> orderCodeList = weightPriceList.stream().map(item -> item.getOrderCode()).collect(Collectors.toList());

            List<String> orderCodeNewList = new  ArrayList<>();
            for(String orderCode : orderCodeList){
                if(!orderCodeNewList.contains(orderCode)){
                    orderCodeNewList.add(orderCode);
                }
            }

            List<WeightPrice> insertWeightPriceList = new ArrayList<>();
            List<WeightOverPrice> insertWeightOverPriceList = new ArrayList<>();
            // 查询是否存在
            List<WeightPriceODTO> existList = salesWaterReportMapper.getWeightPriceList(orderCodeNewList,commodityIdList);
            if(CollectionUtils.isEmpty(existList)){
                for(WeightPriceODTO weightPriceODTO : weightPriceList){
                    setWeightPrice(insertWeightPriceList, insertWeightOverPriceList, weightPriceODTO);
                }
            }else {
                Map<String, String> existMap = existList.stream().collect(Collectors.toMap(WeightPriceODTO::getOrderCode,WeightPriceODTO::getOrderCode,(key1 , key2)-> key2));
                for(WeightPriceODTO weightPriceODTO : weightPriceList){
                    if(existMap.get(weightPriceODTO.getOrderCode() + "" + weightPriceODTO.getCommodityId()) == null){
                        setWeightPrice(insertWeightPriceList, insertWeightOverPriceList, weightPriceODTO);
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(insertWeightPriceList)){
                weightPriceMapper.insertList(insertWeightPriceList);
            }

            if(CollectionUtils.isNotEmpty(insertWeightOverPriceList)){
                weightOverPriceMapper.insertList(insertWeightOverPriceList);
            }
        }finally {
            lock.unlock();
        }
        return true;
    }

    private void setWeightPrice(List<WeightPrice> insertWeightPriceList, List<WeightOverPrice> insertWeightOverPriceList, WeightPriceODTO weightPriceODTO) {
        WeightPrice weightPrice = new WeightPrice();
        BeanUtils.copyProperties(weightPriceODTO, weightPrice);
        weightPrice.setCreateId(1L);
        weightPrice.setCreateTime(new Date());
        insertWeightPriceList.add(weightPrice);

        // 如果成交金额大于零售金额并且是销售订单，则进 pos门店自称重高价统计表
        if (weightPriceODTO.getTransactionAmount().compareTo(weightPriceODTO.getRetailPrice().multiply(weightPriceODTO.getQuantity())) > 0
             && weightPriceODTO.getSaleType().equals(1)
             && CommodityPackageTypeEnums.散装.getName().equals(weightPriceODTO.getCommodityPackageName())) {
            WeightOverPrice weightOverPrice = new WeightOverPrice();
            BeanUtils.copyProperties(weightPriceODTO, weightOverPrice);
            weightOverPrice.setRetailAmount(weightPriceODTO.getRetailPrice().multiply(weightPriceODTO.getQuantity()));
            weightOverPrice.setOverAmount(weightOverPrice.getTransactionAmount().subtract(weightOverPrice.getRetailAmount()));
            weightOverPrice.setCreateTime(new Date());
            insertWeightOverPriceList.add(weightOverPrice);
        }
    }



    /**
     * 修复商品总表(pos非当日数据)
     * @param dayList
     */
    public void fixDayReport(List<String> dayList) {
        List<String> newDayList =  dayList.stream().distinct().collect(Collectors.toList());

        for(String day : newDayList){
            // 商品总表
            shopCommodityTaxService.commodityTax(day);

            // 维护t_md_commodity_shop_tax的online_visitor_number和online_average_amount
            shopCommodityTaxService.insertOrUpdateOnline(day);

            // 维护t_md_commodity_first_tax的online_sales, total_sales
            shopCommodityTaxService.insertOrUpdateCategorySalesOnLine(day);
        }
    }

    /**
     * 判断是否当天订单
     * @param orderCode
     * @return
     */
    public Boolean isCurrentDay(String orderCode){
        if(DateUtils.isBetweenNowDate("00:00", "02:00")){
            return true;
        }
        String currentDate = DateUtil.getDateFormate(new Date(), "yyMMdd");
        String dateString = orderCode.substring(7, 13);
        boolean isCurrentDay = currentDate.equals(dateString);
        return isCurrentDay;
    }
    /**
     * 保存收银流水
     * @param cashierWaterReports
     */
    public void insertCashierWaterReports(List<ReportCashierWater> cashierWaterReports) {
        cashierWaterReportMapper.insertCashierWaterReports(cashierWaterReports);
    }

    /**
     * 保存销售流水
     * @param salesWaters
     */
    public void insertSalesOfTheWaterReport(List<ReportSalesWater> salesWaters) {
        salesWaterReportMapper.insertSalesOfTheWaterReport(salesWaters);
    }

    /**
     * 保存商品销售汇总
     * @param salesSummaryList
     */
    public void insertSalesSummaryReport(List<SalesSummaryReport> salesSummaryList) {
        List<SalesSummaryReport> checkList = salesSummaryList.stream()
                .filter(it -> it.getCommodityFirstId() == null)
                .collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(checkList)){
            throw new BizLogicException("保存商品销售汇总异常");
        }
        salesSummaryReportMapper.insertList(salesSummaryList);
    }

    /**
     * 保存客单分析
     * @param guestSummaryList
     */
    public void insertGuestListSummaryReport(List<GuestListSummaryReport> guestSummaryList) {
        guestListSummaryReportMapper.insertList(guestSummaryList);

        //  t_pos_report_guest_list_summary_10Day 保留10天数据，供大屏查询使用
        /*List<GuestListSummaryReport10Day> guestSummary10DayList = BeanCloneUtils.copyTo(guestSummaryList, GuestListSummaryReport10Day.class);
        guestListSummaryReport10DayMapper.insertList(guestSummary10DayList);*/
    }

    /**
     * 保存收银员让利
     * @param cashierDiscountList
     */
    public void insertCashierDiscountReport(List<CashierDiscountReport> cashierDiscountList) {
        cashierDiscountReportMapper.insertList(cashierDiscountList);
    }

    /**
     * 保存手动优惠
     * @param
     */
    public void insertHandleDiscountReport(List<HandDiscountDetail> handleDiscountList) {
        handleDiscountMapper.insertList(handleDiscountList);
    }

    /**
     * 新增pos报表(收银流水报表,销售流水报表,销售汇总,客单分析,收银员让利,手动优惠)
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertPosReport(ConsumeReportIDTO consumeReportIDTO) {
        // 收银流水报表
        cashierWaterReportMapper.insertCashierWaterReports(consumeReportIDTO.getCashierWaterReports());

        // 销售流水报表
        salesWaterReportMapper.insertSalesOfTheWaterReport(consumeReportIDTO.getSalesWaters());

        // 销售汇总
        List<SalesSummaryReport> checkList = consumeReportIDTO.getSalesSummaryList().stream()
                .filter(it -> it.getCommodityFirstId() == null)
                .collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(checkList)){
            throw new BizLogicException("保存商品销售汇总异常");
        }
        salesSummaryReportMapper.insertList(consumeReportIDTO.getSalesSummaryList());

        // 客单分析
        guestListSummaryReportMapper.insertList(consumeReportIDTO.getGuestList());

        // 收银员让利
        List<CashierDiscountReport> cashierDiscountList = consumeReportIDTO.getCashierDiscountList();
        if(CollectionUtils.isNotEmpty(cashierDiscountList)){
            cashierDiscountReportMapper.insertList(cashierDiscountList);
        }

        // 手动优惠
        List<HandDiscountDetail> handleDiscountList = consumeReportIDTO.getHandleDiscountList();
        if(CollectionUtils.isNotEmpty(handleDiscountList)){
            handleDiscountMapper.insertList(handleDiscountList);
        }
        return Boolean.TRUE;
    }

    // ------------------- 删除开始  根据订单号删除

    public void deleteCashierWaterReportByOrderCodes(String orderCode) {
        cashierWaterReportMapper.deleteCashierWaterReportByOrderCodes(orderCode);
    }

    public void deleteSalesWaterReportByOrderCodes(String orderCode) {
        salesWaterReportMapper.deleteSalesWaterReportByOrderCodes(orderCode);
    }

    public void deleteSalesSummaryReportByOrderCodes(Long orderCode) {
        salesSummaryReportMapper.deleteSalesSummaryReportByOrderCodes(orderCode);
    }

    public void deleteGuestListSummaryReportByOrderCodes(Long orderCode) {
        guestListSummaryReportMapper.deleteGuestListSummaryReportByOrderCodes(orderCode);
    }

    public void deleteCashierDiscountReportByOrderCodes(Long orderCode) {
        cashierDiscountReportMapper.deleteCashierDiscountReportByOrderCodes(orderCode);
    }

    public void deleteHandleDiscountReportByOrderCodes(Long orderCode) {
        handleDiscountMapper.deleteHandleDiscountReportByOrderCodes(orderCode + "");
    }
    /**
     * 根据订单号删除pos报表(收银流水报表,销售流水报表,销售汇总,客单分析,收银员让利,手动优惠)
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePosReportByOrderCode(String orderCode) {
        // 收银流水报表
        cashierWaterReportMapper.deleteCashierWaterReportByOrderCodes(orderCode);

        // 销售流水报表
        salesWaterReportMapper.deleteSalesWaterReportByOrderCodes(orderCode);

        // 销售汇总
        salesSummaryReportMapper.deleteSalesSummaryReportByOrderCodes(Long.valueOf(orderCode));

        // 客单分析
        guestListSummaryReportMapper.deleteGuestListSummaryReportByOrderCodes(Long.valueOf(orderCode));

        // 收银员让利
        cashierDiscountReportMapper.deleteCashierDiscountReportByOrderCodes(Long.valueOf(orderCode));

        // 手动优惠
        handleDiscountMapper.deleteHandleDiscountReportByOrderCodes(orderCode);

        // 删除帮困卡
        helpCardTraderSummaryMapper.deleteHelpCardTraderSummaryByOrderCode(orderCode);
        return Boolean.TRUE;
    }

    // ------------------- 删除开始 根据时间范围，门店删除

    public void deleteCashierWaterReportByTimeRange(Long shopId,String beginTime,String endTime) {
        cashierWaterReportMapper.deleteCashierWaterReportByTimeRange(shopId,beginTime,endTime);
    }

    public void deleteSalesWaterReportByTimeRange(Long shopId,String beginTime,String endTime) {
        salesWaterReportMapper.deleteSalesWaterReportByTimeRange(shopId,beginTime,endTime);
    }

    public void deleteSalesSummaryReportByTimeRange(Long shopId,String beginTime,String endTime) {
        salesSummaryReportMapper.deleteSalesSummaryReportByTimeRange(beginTime.substring(0,10),shopId,beginTime,endTime);
    }

    public void deleteGuestListSummaryReportByTimeRange(Long shopId,String beginTime,String endTime) {
        guestListSummaryReportMapper.deleteGuestListSummaryReportByTimeRange(beginTime.substring(0,10),shopId,beginTime,endTime);
    }

    public void deleteCashierDiscountReportByTimeRange(Long shopId,String beginTime,String endTime) {
        cashierDiscountReportMapper.deleteCashierDiscountReportByTimeRange(beginTime.substring(0,10),shopId,beginTime,endTime);
    }
    public void deleteHandleDiscountReportByTimeRange(Long shopId,String beginTime,String endTime) {
        handleDiscountMapper.deleteHandleDiscountReportByTimeRange(shopId,beginTime,endTime);
    }
    /**
     * 根据时间范围删除pos报表(收银流水报表,销售流水报表,销售汇总,客单分析,收银员让利,手动优惠)
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePosReportReportByTimeRange(Long shopId,String beginTime,String endTime) {
        // 收银流水报表
        cashierWaterReportMapper.deleteCashierWaterReportByTimeRange(shopId,beginTime,endTime);

        // 销售流水报表
        salesWaterReportMapper.deleteSalesWaterReportByTimeRange(shopId,beginTime,endTime);

        // 销售汇总
        salesSummaryReportMapper.deleteSalesSummaryReportByTimeRange(beginTime.substring(0,10),shopId,beginTime,endTime);

        // 客单分析
        guestListSummaryReportMapper.deleteGuestListSummaryReportByTimeRange(beginTime.substring(0,10),shopId,beginTime,endTime);

        // 收银员让利
        cashierDiscountReportMapper.deleteCashierDiscountReportByTimeRange(beginTime.substring(0,10),shopId,beginTime,endTime);

        // 手动优惠
        handleDiscountMapper.deleteHandleDiscountReportByTimeRange(shopId,beginTime,endTime);
        return Boolean.TRUE;
    }

    //  消费昨日漏掉的订单 各订单号
    public List<Payment> getLastDateOrder(String lastDate){
        return cashierWaterReportMapper.getLastDateOrder(lastDate,null,null);
    }
    public  List<Payment> getLastSaleOrder(String lastDate){
        return cashierWaterReportMapper.getLastSaleOrder(lastDate,null,null);
    }
    public  List<Payment> getLastSaleWaterOrder(String lastDate){
        return cashierWaterReportMapper.getLastSaleWaterOrder(lastDate+" 00:00:00",lastDate+" 23:59:59");
    }
    public  List<Payment> getLastCashierWaterOrder(String lastDate){
        return cashierWaterReportMapper.getLastCashierWaterOrder(lastDate+" 00:00:00",lastDate+" 23:59:59");
    }

    public Boolean selectReportData(ReportTypeEnum reportTypeEnum, String orderCode) {
        String tableName = reportTypeEnum.getTable();
        int count =  cashierWaterReportMapper.selectReportData(tableName, orderCode);
        return count > 0;
    }


    /**
     *比较payment订单量和pos几张报表订单量
     * @return
     */
    public ComparePosReportODTO comparePosReportOrder(String beginTime,String endTime) {
        ComparePosReportODTO comparePosReportODTO = new  ComparePosReportODTO();

        //comparePosReportODTO.setGuestOrderCount(cashierWaterReportMapper.getLastGuestOrderCount(beginTime.substring(0,10),beginTime,endTime));
        //comparePosReportODTO.setDiscountOrderCount(cashierWaterReportMapper.getLastDiscountOrderCount(beginTime.substring(0,10),beginTime,endTime));
        //comparePosReportODTO.setSaleSummaryOrderCount(cashierWaterReportMapper.getLastSaleOrderCount(beginTime.substring(0,10),beginTime,endTime));
        //comparePosReportODTO.setSaleWaterOrderCount(cashierWaterReportMapper.getLastSaleWaterOrderCount(beginTime,endTime));
        comparePosReportODTO.setCashWaterOrderCount(cashierWaterReportMapper.getLastCashierWaterOrderCount(beginTime,endTime));

        Boolean isCurrentDay = DateUtils.isCurrentDay(beginTime,endTime);
        if(isCurrentDay){
            comparePosReportODTO.setCashierWaterDayAmount(cashierWaterReportMapper.getCashierWaterCurrentDayAmount(beginTime,endTime));
            comparePosReportODTO.setGuestListSummaryDayAmount(cashierWaterReportMapper.getGuestListSummaryCurrentDayAmount(beginTime.substring(0,10)));
            comparePosReportODTO.setSalesSummaryDayAmount(cashierWaterReportMapper.getSalesSummaryCurrentDayAmount(beginTime.substring(0,10)));
        }else {
            comparePosReportODTO.setCashierWaterDayAmount(cashierWaterReportMapper.getCashierWaterDayAmount(beginTime,endTime));
            comparePosReportODTO.setGuestListSummaryDayAmount(cashierWaterReportMapper.getGuestListSummaryDayAmount(beginTime.substring(0,10)));
            comparePosReportODTO.setSalesSummaryDayAmount(cashierWaterReportMapper.getSalesSummaryDayAmount(beginTime.substring(0,10)));
        }
        return comparePosReportODTO;
    }


    /**
     * pos原单号退款，原单号没记录上
     * @param timeStamp
     * @return
     */
    @Transactional
    public Boolean fixOriginOrderCode(String timeStamp) {

        // 查询销售流水收银流水为退单，原单号为空的订单号
        List<Long> returnCodeList = salesWaterReportMapper.selectOriginOrderNUll(timeStamp + " 00:00:00", timeStamp + " 23:59:59");
        if(CollectionUtils.isNotEmpty(returnCodeList)){

            // 查询退货单的原单号
            List<OrderCodeODTO> list = posReportClient.findOrderCodeByReturnCode(returnCodeList);
            if(CollectionUtils.isNotEmpty(list)){
                salesWaterReportMapper.batchUpdateSaleWaterOriginOrder(list);
                cashierWaterReportMapper.batchUpdateCashierWaterOriginOrder(list);
            }
        }
        return Boolean.TRUE;
    }
}
