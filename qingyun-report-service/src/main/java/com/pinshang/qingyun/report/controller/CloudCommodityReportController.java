package com.pinshang.qingyun.report.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportQueryIDTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportQueryIDTO;
import com.pinshang.qingyun.report.dto.export.*;
import com.pinshang.qingyun.report.service.CloudCommodityReportService;
import com.pinshang.qingyun.report.util.ReportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/3/7
 */
@Slf4j
@RestController
@RequestMapping("/commodityReport")
@Api(value = "云超商品报表", tags = "CloudCommodityReportController")
public class CloudCommodityReportController {

    @Autowired
    private CloudCommodityReportService cloudCommodityReportService;

    @Autowired
    private IRenderService renderService;

    @ApiOperation(value = "云超实发汇总信息(job调用)", notes = "云超实发汇总信息(job调用)")
    @PostMapping("/cloudCommodityReportSummary")
    public Boolean cloudCommodityReportSummary(@RequestParam("orderTime") String orderTime) {
        return cloudCommodityReportService.cloudCommodityReportSummary(orderTime);
    }

    @ApiOperation(value = "云超商品报表汇总(job调用)", notes = "云超商品报表汇总(job调用)")
    @PostMapping("/cloudCommodityInfoReport")
    public Boolean cloudCommodityInfoReport(@RequestParam("orderTime") String orderTime) {
        return cloudCommodityReportService.cloudCommodityInfoReport(orderTime);
    }

    @ApiOperation(value = "云超实发汇总报表", notes = "云超实发汇总报表")
    @ApiImplicitParam(name="vo", value="请求参数", required = true, paramType = "body", dataType ="CloudCommodityReportQueryIDTO")
    @PostMapping("/realDeliveryReport")
    @MethodRender
    public TablePageInfo<CloudCommodityReportODTO> realDeliveryReport(@RequestBody CloudCommodityReportQueryIDTO vo) {
        return cloudCommodityReportService.realDeliveryReport(vo);
    }


    @ApiOperation(value = "客户端云超实发汇总报表", notes = "客户端云超实发汇总报表")
    @ApiImplicitParam(name="vo", value="请求参数", required = true, paramType = "body", dataType ="CloudCommodityReportQueryIDTO")
    @PostMapping("/client/realDeliveryReport")
    @MethodRender
    public List<CloudCommodityReportODTO> clientRealDeliveryReport(@RequestBody CloudCommodityReportQueryIDTO vo) {
        return cloudCommodityReportService.clientRealDeliveryReport(vo);
    }


    @ApiOperation(value = "导出云超实发汇总报表", notes = "导出云超实发汇总报表")
    @GetMapping("/exportRealDeliveryReport")
    @FileCacheQuery(bizCode = "CLOUD_REAL_DELIVERY_REPORT")
    public void exportRealDeliveryReport(CloudCommodityReportQueryIDTO vo, HttpServletResponse response) throws IOException {
        vo.initExportPage();
        TablePageInfo<CloudCommodityReportODTO> result = cloudCommodityReportService.realDeliveryReport(vo);
        Boolean isWithShop =  vo.getIsWithShop() != null && vo.getIsWithShop() == 1;
        List<CloudCommodityReportODTO> list = result.getList();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            List<List<String>> headList = new ArrayList<>();
            List<CloudRealDeliveryWithShopRespVo> exportList = new ArrayList<>();
            List<CloudRealDeliveryNoShopRespVo> exportList2 = new ArrayList<>();

            if(CollectionUtils.isNotEmpty(list)){
                renderService.render(list,"/exportRealDeliveryReport");
                CloudCommodityReportODTO total = (CloudCommodityReportODTO) result.getHeader();
                String header = "订单金额合计:" + total.getRealAmount() + "元  "
                        + "C端实发金额合计:" + total.getPackageAmount() + "元  "
                        + "供货金额合计:" + total.getWeightAmount() + "元";
                if(isWithShop){
                    exportList = BeanCloneUtils.copyTo(list, CloudRealDeliveryWithShopRespVo.class);
                    headList = ReportUtil.getHeaderList(isWithShop, header);
                }else {
                    exportList2 = BeanCloneUtils.copyTo(list, CloudRealDeliveryNoShopRespVo.class);
                    headList = ReportUtil.getHeaderList(isWithShop, header);
                }
            }
            ExcelUtil.setFileNameAndHead(response,  "云超实发汇总表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream()).registerWriteHandler(ReportUtil.getStyleStrategy()).head(headList)
                    .autoCloseStream(Boolean.FALSE).sheet("云超实发汇总表").doWrite(isWithShop ? exportList : exportList2);

        }catch (Exception e){
            log.error("云超实发汇总表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }
       /* Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        CloudCommodityReportODTO sumODTO = new CloudCommodityReportODTO();
        if (null != list && !list.isEmpty()) {
            renderService.render(list,"/exportRealDeliveryReport");
            sumODTO = (CloudCommodityReportODTO) result.getHeader();
            for (CloudCommodityReportODTO dto : list) {
                dataLst = new ArrayList<>();
                if(isWithShop){
                    dataLst.add(dto.getOrgName());
                    dataLst.add(dto.getStoreCode());
                    dataLst.add(dto.getShopName());
                }

                dataLst.add(dto.getCommodityFactoryName());
                dataLst.add(dto.getCommodityWorkshopName());
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnitName());

                dataLst.add(dto.getQuantity() == null ? "" : dto.getQuantity().toString());
                dataLst.add(dto.getAmount() == null ? "" : dto.getAmount().toString());
                dataLst.add(dto.getRealAmount() == null ? "" : dto.getRealAmount().toString());
                dataLst.add(dto.getPackageQuantity() == null ? "" : dto.getPackageQuantity().toString());
                dataLst.add(dto.getPackageAmount()== null ? "" : dto.getPackageAmount().toString());
                dataLst.add(dto.getWeightAmount()== null ? "" : dto.getWeightAmount().toString());

                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getTaxRate() == null ? "" : dto.getTaxRate().toString());
                dataLst.add(dto.getCommodityFirstKindName());
                dataLst.add(dto.getCommoditySecondKindName());
                dataLst.add(dto.getCommodityThirdKindName());
                dataLst.add(dto.getCommodityFlowshopName());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "云超实发汇总表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        if(isWithShop){
            map.put("sheetTitle", ExcelSheetTitleEnum.COMMODITY_REAL_DELIVERY_WITH_SHOP);
        }else {
            map.put("sheetTitle", ExcelSheetTitleEnum.COMMODITY_REAL_DELIVERY);
        }

        map.put("data", data);
        //map.put("title", "商品实发汇总报表");
        //map.put("titleCells", (short)3);
        map.put("tableHeader", "订单金额合计:" + sumODTO.getRealAmount() + "元  "
                + "C端实发金额合计:" + sumODTO.getPackageAmount() + "元  "
                + "供货金额合计:" + sumODTO.getWeightAmount() + "元");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);*/
    }


    @ApiOperation(value = "云超报表", notes = "云超报表")
    @ApiImplicitParam(name="vo", value="请求参数", required = true, paramType = "body", dataType ="CloudCommodityInfoReportQueryIDTO")
    @PostMapping("/cloudCommodityReport")
    @MethodRender
    public TablePageInfo<CloudCommodityInfoReportODTO> cloudCommodityReport(@RequestBody CloudCommodityInfoReportQueryIDTO vo) {
        return cloudCommodityReportService.cloudCommodityReport(vo);
    }


    @ApiOperation(value = "导出云超报表", notes = "导出云超报表")
    @GetMapping("/exportCloudCommodityReport")
    @FileCacheQuery(bizCode = "CLOUD_COMMODITY_REPORT")
    public void exportRealDeliveryReport(CloudCommodityInfoReportQueryIDTO vo, HttpServletResponse response) throws IOException {
        String time = vo.getOrderTimeBegin() + "_" + vo.getOrderTimeEnd();
        Assert.notNull(vo.getReportType(), "参数错误");
        vo.initExportPage();
        TablePageInfo<CloudCommodityInfoReportODTO> result = cloudCommodityReportService.cloudCommodityReport(vo);

        List<CloudCommodityInfoReportODTO> list = result.getList();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                renderService.render(list,"/exportCloudCommodityReport");

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.CANCEL)){
                    List<CloudOrderCancelExportRespVo>  exportList = BeanCloneUtils.copyTo(list, CloudOrderCancelExportRespVo.class);
                    ExcelUtil.setFileNameAndHead(response,  "云超订单取消明细表" + "_" + time);
                    EasyExcel.write(response.getOutputStream(), CloudOrderCancelExportRespVo.class)
                            .autoCloseStream(Boolean.TRUE).sheet("云超订单取消明细表").doWrite(exportList);
                }

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.DIFF_RETURN)){
                    List<CloudDiffReturnExportRespVo> exportList = BeanCloneUtils.copyTo(list, CloudDiffReturnExportRespVo.class);
                    ExcelUtil.setFileNameAndHead(response,  "云超补差退款明细表" + "_" + time);
                    EasyExcel.write(response.getOutputStream(), CloudDiffReturnExportRespVo.class)
                            .autoCloseStream(Boolean.TRUE).sheet("云超补差退款明细表").doWrite(exportList);
                }

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.FAIL)){
                    List<CloudDeliveryFailExportRespVo>  exportList = BeanCloneUtils.copyTo(list, CloudDeliveryFailExportRespVo.class);
                    ExcelUtil.setFileNameAndHead(response,  "云超配送失败明细表" + "_" + time);
                    EasyExcel.write(response.getOutputStream(), CloudDeliveryFailExportRespVo.class)
                            .autoCloseStream(Boolean.TRUE).sheet("云超配送失败明细表").doWrite(exportList);
                }
            }

        }catch (Exception e){
            log.error("云超报表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }
        /*Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        CloudCommodityInfoReportODTO sumODTO = new CloudCommodityInfoReportODTO();
        if (null != list && !list.isEmpty()) {
            renderService.render(list,"/exportCloudCommodityReport");
            sumODTO = (CloudCommodityInfoReportODTO) result.getHeader();
            for (CloudCommodityInfoReportODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getOrgName());
                dataLst.add(dto.getStoreCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getOrderCode());
                dataLst.add(dto.getSourceType());

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.DIFF_RETURN)
                     || vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.FAIL)){
                    dataLst.add(dto.getToShopDateStr());
                }

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.FAIL)){
                    dataLst.add(dto.getBillTimeStr());
                }
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnitName());

                dataLst.add(dto.getQuantity() == null ? "" : dto.getQuantity() + "");
                dataLst.add(dto.getAmount() == null ? "" : dto.getAmount() + "");
                dataLst.add(dto.getRealAmount() == null ? "" : dto.getRealAmount() + "");
                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.CANCEL)
                  || vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.FAIL)){
                    dataLst.add(dto.getWeightAmount() == null ? "" : dto.getWeightAmount() + "");
                }
                dataLst.add(dto.getPackageQuantity() == null ? "" : dto.getPackageQuantity() + "");
                dataLst.add(dto.getPackageAmount() == null ? "" : dto.getPackageAmount() + "");

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.DIFF_RETURN)){
                    dataLst.add(dto.getRealDiffAmount() == null ? "" : dto.getRealDiffAmount() + "");
                }

                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityFirstKindName());
                dataLst.add(dto.getCommoditySecondKindName());
                dataLst.add(dto.getCommodityThirdKindName());
                dataLst.add(dto.getTaxRate() == null ? "" : dto.getTaxRate());

                if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.CANCEL)
                    || vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.DIFF_RETURN)){
                    dataLst.add(dto.getBillTimeStr());
                }

                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "";
        if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.CANCEL)){
            fileName = "云超订单取消明细表" + "_" + time;
            map.put("sheetTitle", ExcelSheetTitleEnum.CLOUD_COMMODITY_CANCEL);
            //map.put("tableHeader", "实收金额合计:" + sumODTO.getRealAmount() + "元  ");
        }
        if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.DIFF_RETURN)){
            fileName = "云超补差退款明细表" + "_" + time;
            map.put("sheetTitle", ExcelSheetTitleEnum.CLOUD_COMMODITY_DIFF_RETURN);
            //map.put("tableHeader", "差异退款金额合计:" + sumODTO.getRealDiffAmount() + "元  ");
        }
        if(vo.getReportType().equals(CloudCommodityInfoReportQueryIDTO.ReportType.FAIL)){
            fileName = "云超配送失败明细表" + "_" + time;
            map.put("sheetTitle", ExcelSheetTitleEnum.CLOUD_COMMODITY_FAIL);
            *//*map.put("tableHeader", "商品金额合计:" + sumODTO.getAmount() + "元  "
                    + "实收金额合计:" + sumODTO.getRealAmount()+ "元  "
                    + "实发金额合计:" + sumODTO.getPackageAmount() + "元 "
                    + "销售成本金额合计:" + sumODTO.getWeightAmount() + "元");*//*
        }

        // 参数设置
        map.put("filename", fileName);
        map.put("data", data);

        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);*/
    }
}
