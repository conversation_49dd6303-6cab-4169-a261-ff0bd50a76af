package com.pinshang.qingyun.report.util;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.RandomUtils;
import java.util.*;

/**
 * 生成随机集合工具类
 *
 */
public class RandomDataUtil {

    public final static int TWO=2;

    public static <T> List<T> generateRandomDataNoRepeat(List<T> list,Integer generateNum){
        if(generateNum.intValue() >= list.size()) {
            return list;
        }
        List<T> tList = new ArrayList<T>();
        if(CollectionUtils.isNotEmpty(list)) {
            for (Integer num : generateRandomNoRepeat(list.size(), generateNum)) {
                tList.add(list.get(num));
            }
        }
        return tList;
    }

    public static <T> List<T> generateRandomPermutation(List<T> list,Integer generateNum){
        if(CollectionUtils.isNotEmpty(list)) {
            checkParams(list.size(),generateNum);
            List<T> randomAllList = randomPermutation(list, generateNum);
            int initPosition=interceptPosition(list.size(),generateNum);
            return randomAllList.subList(initPosition,initPosition+generateNum);
        }
        return Collections.emptyList();
    }


    private static <T> List<T> randomPermutation(List<T> list,Integer generateNum){
        for (int i = 0; i < list.size(); i++) {
            Integer random=RandomUtils.nextInt(list.size());
            T t = list.get(random);
            list.set(random,list.get(i));
            list.set(i,t);
        }
        return list;
    }


    private static Integer interceptPosition(Integer totalCount,Integer generateNum){
        int num=RandomUtils.nextInt(totalCount);
        if(num+generateNum>totalCount){
            num=num-generateNum;
        }
        return  num;
    }

    public static Set<Integer> generateRandomNoRepeat(Integer totalCount,Integer generateNum){
        if(isLessThanHalfTotalCount(totalCount,generateNum)){
            return getRandomNoRepeat(totalCount,generateNum);
        }
        return getReverseRandomNoRepeat(totalCount,generateNum);
    }


    private static void checkParams(Integer totalCount,Integer generateNum){
        if(totalCount<generateNum){
            throw new IllegalArgumentException("generateNum is out of totalCount");
        }
    }


    private static Boolean isLessThanHalfTotalCount(Integer totalCount,Integer generateNum){
        if(generateNum<totalCount/TWO){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    private static Set<Integer> getRandomNoRepeat(Integer totalCount,Integer generateNum){
        Set<Integer> set = new HashSet<Integer>();
        while (true) {
            set.add(RandomUtils.nextInt(totalCount));
            if(set.size() == generateNum){
                return set;
            }
        }
    }


    private static Set<Integer> getReverseRandomNoRepeat(Integer totalCount,Integer generateNum){
        Set<Integer> set = new HashSet<Integer>();
        while (true) {
            set.add(RandomUtils.nextInt(totalCount));
            if(set.size() == totalCount-generateNum){
                Set<Integer> setALL=getSetALL(totalCount);
                setALL.removeAll(set);
                return setALL;
            }
        }
    }


    private static Set<Integer> getSetALL(Integer totalCount){
        Set<Integer> set = new HashSet<Integer>();
        for(int i=0;i<totalCount;i++){
            set.add(i);
        }
        return set;
    }

}