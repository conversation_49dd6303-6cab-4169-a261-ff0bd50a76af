package com.pinshang.qingyun.report.dto.cloud;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/2/23
 */
@Data
public class CloudCommodityReportQueryIDTO extends Pagination {

    @ApiModelProperty("组织code")
    private String orgCode;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty(value = "送货日期开始时间 yyyy-MM-dd")
    private String orderTimeBegin;
    @ApiModelProperty(value = "送货日期结束时间 yyyy-MM-dd")
    private String orderTimeEnd;

    @ApiModelProperty(value = "配送批次")
    private Integer deliveryBatch;

    @ApiModelProperty(value = "商品工厂ID")
    private Long commodityFactoryId;

    @ApiModelProperty(value = "商品生产组ID")
    private Long commodityWorkshopId;

    @ApiModelProperty(value = "商品车间ID")
    private Long commodityFlowshopId;

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品ID集合")
    private List<Long> commodityIdList;

    @ApiModelProperty(value = "商品一级分类ID")
    private Long cateId1;
    @ApiModelProperty(value = "商品二级分类ID")
    private Long cateId2;
    @ApiModelProperty(value = "商品三级分类ID")
    private Long cateId3;

    private List<Long> factoryIdList;

    private List<Long> shopIdList;

    @ApiModelProperty(value = "是否区分门店 1 区分 0 不区分")
    private Integer isWithShop = 0;

    @ApiModelProperty("门店类型List")
    private List<Integer> shopTypeList;

    @ApiModelProperty("经营模式List：1-直营、2-外包")
    private List<Integer> managementModeList;
}
