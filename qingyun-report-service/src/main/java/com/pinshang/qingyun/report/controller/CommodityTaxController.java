package com.pinshang.qingyun.report.controller;

import com.pinshang.qingyun.report.dto.LastMonthSaleDTO;
import com.pinshang.qingyun.report.dto.shop.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.service.CommodityTaxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/commodityTax")
public class CommodityTaxController {

    @Autowired
    private CommodityTaxService commodityTaxService;

    /**
     * 上个月销售品项，shopId为空，查询所有门店
     * @return
     */
    @PostMapping("lastMonthSale")
    public List<ShopCommoditySaleStatisticsODTO> lastMonthSale(@RequestBody LastMonthSaleDTO lastMonthSaleDTO) {
        return commodityTaxService.lastMonthSale(lastMonthSaleDTO);
    }
}
