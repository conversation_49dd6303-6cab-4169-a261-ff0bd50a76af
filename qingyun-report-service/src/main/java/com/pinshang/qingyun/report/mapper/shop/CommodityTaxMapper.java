package com.pinshang.qingyun.report.mapper.shop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.LastMonthSaleDTO;
import com.pinshang.qingyun.report.dto.shop.CommodityOrderSalesODTO;
import com.pinshang.qingyun.report.dto.shop.ShopAutoCommodityTaxDTO;
import com.pinshang.qingyun.report.dto.shop.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.model.shop.CommodityTax;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CommodityTaxMapper extends MyMapper<CommodityTax>{

    List<ShopCommoditySaleStatisticsODTO> queryShopCommoditySaleList(@Param("shopId") Long shopId);

    List<CommodityOrderSalesODTO> findMonthSalesQuanty(@Param("commodityId")Long commodityId, @Param("storeId")Long storeId, @Param("inBegin") String inBegin, @Param("yesterday") String yesterday, @Param("flag") int i);

    List<Long> queryTopShopCommoditySale(@Param("vo") ShopAutoCommodityTaxDTO shopAutoCommodityTaxDTO);

    /**
     * 上个月有销售的品项
     * @param startTime
     * @param endTime
     * @param shopIds
     * @return
     */
    List<ShopCommoditySaleStatisticsODTO> lastMonthSale(@Param("lastMonthSaleDTO") LastMonthSaleDTO lastMonthSaleDTO);

    int deleteCommodityTaxMonth(@Param("day") String day);
    void insertCommodityTaxMonth(@Param("timeStamp") String beginDateTime);
    int deleteCommodityTaxMonth30(@Param("day") String day);
}
