package com.pinshang.qingyun.report.enums;


public enum AbnormalQueryTypeEnum {
    SHOP(1, "按门店维度"),
    SHOP_DATE(2,"按门店+日期维度"),
    SHOP_DATE_CASHIER(3,"按门店+日期+收银员维度")
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;


    AbnormalQueryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
