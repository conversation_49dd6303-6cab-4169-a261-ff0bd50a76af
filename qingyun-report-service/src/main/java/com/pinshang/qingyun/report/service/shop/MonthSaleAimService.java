package com.pinshang.qingyun.report.service.shop;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.shop.MonthSaleAimIDTO;
import com.pinshang.qingyun.report.dto.shop.MonthSaleAimODTO;
import com.pinshang.qingyun.report.dto.shop.MonthSaleAimPageIDTO;
import com.pinshang.qingyun.report.dto.shop.SaleAimIDTO;
import com.pinshang.qingyun.report.mapper.shop.MonthSaleAimMapper;
import com.pinshang.qingyun.report.model.shop.MonthSaleAim;
import com.pinshang.qingyun.report.model.shop.Shop;
import com.pinshang.qingyun.report.service.BaseService;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class MonthSaleAimService extends BaseService<MonthSaleAimMapper,MonthSaleAim> {

      @Autowired
      private  MonthSaleAimMapper monthSaleAimMapper;

      @Autowired
      private  ShopService shopService;

      @Autowired
      private SMMUserClient smmUserClient;

      /**
       * 月销售目标管理(分页)
       * @param idto
       * @return
       */
      public PageInfo<MonthSaleAimODTO> listMonthSaleAimPage(MonthSaleAimPageIDTO idto) {
            PageInfo<MonthSaleAimODTO> pageDate = null ;
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if(idto.getShopId() == null){
                  idto.setShopIdList(shopIdList);
            }else{
                  idto.setShopIdList(Arrays.asList(idto.getShopId()));
            }
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                  monthSaleAimMapper.listMonthSaleAimPage(idto);
            });
            if(CollectionUtils.isNotEmpty(pageDate.getList())){
                  Calendar calendar = Calendar.getInstance();
                  calendar.add(Calendar.MONTH,-1);
                  String lastMonth = DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM");
                  for(MonthSaleAimODTO odto:pageDate.getList()){
                        int i = DateUtils.compareDate(odto.getSaleTime(),lastMonth,"yyyy-MM");
                        odto.setIsHistory(i <= 0 ? 1:0);
                  }
            }
            return pageDate;
      }

      /**
       * 新增月销售目标管理
       * @param monthSaleAimIDTO
       * @return
       */
      @Transactional(rollbackFor = Exception.class)
      public void saveMonthSaleAim(MonthSaleAimIDTO monthSaleAimIDTO){
            Assert.notNull(monthSaleAimIDTO.getShopId(), "门店不能为空");
            Assert.notNull(monthSaleAimIDTO.getSaleTime(), "月份不能为空");
            Assert.notNull(monthSaleAimIDTO.getAimSaleAmount(), "销售目标不能为空");

            //判断门店日期唯一性
            Integer count = monthSaleAimMapper.countMonthSaleAimByShopSaleTime(monthSaleAimIDTO.getShopId(),monthSaleAimIDTO.getSaleTime());
            Assert.isTrue(count == 0, "此门店的此月份已有目标！");

            //判断日期：只能选本月和本月之后的日期
            String today = DateUtil.getDateFormate(new Date(),"yyyy-MM");
            int i = DateUtils.compareDate(today,monthSaleAimIDTO.getSaleTime(),"yyyy-MM");
            Assert.isTrue(i <= 0, "只能选本月及本月之后的月份！");

            MonthSaleAim monthSaleAim = new MonthSaleAim();
            BeanUtils.copyProperties(monthSaleAimIDTO,monthSaleAim);
            Shop shop = shopService.selectByPrimaryKey(monthSaleAimIDTO.getShopId());
            monthSaleAim.setShopCode(null !=shop ? shop.getShopCode():"");
            monthSaleAim.setShopName(null !=shop ? shop.getShopName():"");
            this.insert(monthSaleAim);

      }

      public MonthSaleAimODTO getMonthSaleAimById(Long id){
            Assert.notNull(id, "门店不能为空");

            MonthSaleAimODTO dto = new MonthSaleAimODTO();
            MonthSaleAim monthSaleAim = monthSaleAimMapper.selectByPrimaryKey(id);
            BeanUtils.copyProperties(monthSaleAim,dto);
            return dto;
      }

      /**
       * 修改月销售目标管理
       * @param
       * @param monthSaleAimIDTO
       * @return
       */
      public void updateMonthSaleAim(MonthSaleAimIDTO monthSaleAimIDTO) {
            Assert.notNull(monthSaleAimIDTO.getId(), "id不能为空");
            Assert.notNull(monthSaleAimIDTO.getAimSaleAmount(), "销售目标不能为空");
            MonthSaleAim monthSaleAim = new MonthSaleAim();
            BeanUtils.copyProperties(monthSaleAimIDTO,monthSaleAim);
            this.updateByPrimaryKeySelective(monthSaleAim);
      }

      /**
       * 删除月销售目标管理
       * @param id
       * @return
       */
      public int deleteMonthSaleAim(Long id) {
            Assert.notNull(id, "id不能为空");
            return this.deleteByPrimaryKey(id);
      }


      /**
       * 获取shopMap
       * @return
       */
      public Map<String,Shop> getShopMap(){
            Map<String,Shop> shopMap = new HashMap<>();
            List<Shop> shopList = shopService.selectListAll();
            for(Shop shop:shopList){
                  shopMap.put(shop.getShopCode(),shop);
            }
            return  shopMap;
      }
      /**
       * 校验1-9位正整数
       * @param str
       * @return
       */
      public  boolean isNumber(String str) {
            // Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
            String reg = "^[1-9]\\d{0,8}$";
            Pattern pattern = Pattern.compile(reg);
            Matcher match = pattern.matcher(str);
            return match.matches();
      }
      /**
       * 批量导入 月销售目标管理
       * @param list
       * @return
       */
      @Transactional(rollbackFor = Exception.class)
      public Object importMonthSaleAim(List<SaleAimIDTO> list)throws ParamException {
            Map<String,Object> dataMap = new HashMap<>();//定义返回map
            List<String> errorList = new ArrayList<>();//定义返回错误信息list
            errorList.add("错误提示：");
            boolean isError = false;

            List<MonthSaleAim> monthSaleAimList = new ArrayList<>();//新增数据
            List<String> keyList = new ArrayList<>();//判断是否和数据库重复key

            //文件内容校验
            isError = valid(list,errorList, isError, monthSaleAimList,keyList);

            //数据库层面校验重复
            isError = monthSaleAimValidateDB(list, errorList, isError, keyList);

            if(isError){
                  dataMap.put("success",0);
                  dataMap.put("data",errorList);
            }else{
                  //校验通过，没有问题
                  dataMap.put("success",1);
                  monthSaleAimMapper.insertList(monthSaleAimList);
            }
            return dataMap;
      }

      /**
       * 文件内容校验
       * @param list
       * @param errorList
       * @param isError
       * @param monthSaleAimList
       * @param keyList
       * @return
       */
      private boolean valid(List<SaleAimIDTO> list,List<String> errorList, boolean isError, List<MonthSaleAim> monthSaleAimList,List<String> keyList) {
            Map<String,Shop> shopMap = getShopMap();//获取门店map
            Map<String,String> dateShopMap = new HashMap<>();//判断excel内是否重复
            for(int i=0;i<list.size();i++){
                  SaleAimIDTO idto = list.get(i);
                  MonthSaleAim  monthSaleAim = new MonthSaleAim();

                  //校验日期
                  if(!isDate(idto.getSaleTime()+"-01")){
                        isError = true;
                        errorList.add("第"+(i+2)+"行,月份格式有误，请参考模板！");
                  }else{
                        idto.setSaleTime(DateUtil.getDateFormate(DateUtil.parseDate(idto.getSaleTime(),"yyyy-MM"),"yyyy-MM"));
                        String today = DateUtil.getDateFormate(new Date(),"yyyy-MM");
                        int j = DateUtils.compareDate(today,idto.getSaleTime(),"yyyy-MM");
                        if(j > 0){
                              isError = true;
                              errorList.add("第"+(i+2)+"行,只能填本月及本月之后的月份！");
                        }
                  }
                  monthSaleAim.setSaleTime(idto.getSaleTime());

                  //校验门店
                  Shop shop = shopMap.get(idto.getShopCode());
                  if(null == shop){
                        isError = true;
                        errorList.add("第"+(i+2)+"行,没有匹配到门店！");
                  }else{
                        monthSaleAim.setShopId(shop.getId());
                        monthSaleAim.setShopCode(shop.getShopCode());
                        monthSaleAim.setShopName(shop.getShopName());
                  }
                  //校验目标销售金额
                  if(!isNumber(idto.getAimSaleAmount())){
                        isError = true;
                        errorList.add("第"+(i+2)+"行,销售目标金额格式有误，销售目标的输入范围：1~999999999！");
                  }else{
                        monthSaleAim.setAimSaleAmount(new BigDecimal(idto.getAimSaleAmount()));
                  }

                  //校验备注
                  if(StringUtils.isNotBlank(idto.getRemark()) && idto.getRemark().length() > 15){
                        isError = true;
                        errorList.add("第"+(i+2)+"行,备注最多输入15个字！");
                  }
                  monthSaleAim.setRemark(idto.getRemark());

                  if(dateShopMap.get(idto.getShopCode()+idto.getSaleTime()) != null){
                        isError=true;
                        errorList.add("第"+(i+2)+"行,文件内重复,相同门店相同日期只能有一个目标！");
                  }
                  dateShopMap.put(idto.getShopCode()+idto.getSaleTime(),idto.getShopCode()+idto.getSaleTime());
                  keyList.add(idto.getShopCode()+idto.getSaleTime());

                  monthSaleAim.setCreateId(idto.getCreateId());
                  monthSaleAim.setUpdateId(idto.getCreateId());
                  monthSaleAim.setCreateTime(new Date());
                  monthSaleAim.setUpdateTime(new Date());
                  monthSaleAimList.add(monthSaleAim);
            }
            return isError;
      }

      /**
       * 数据库层面校验重复
       * @param list
       * @param errorList
       * @param isError
       * @param keyList
       * @return
       */
      private boolean monthSaleAimValidateDB(List<SaleAimIDTO> list, List<String> errorList, boolean isError, List<String> keyList) {
            List<String> repeatList = monthSaleAimMapper.queryMonthKeyListRepeat(keyList);
            Map<String,String> keyMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(repeatList)){
                  for(String s:repeatList){
                        keyMap.put(s,s);
                  }
            }
            for(int i=0;i<list.size();i++){
                  SaleAimIDTO idto = list.get(i);
                  if(keyMap.get(idto.getShopCode()+idto.getSaleTime()) != null){
                        isError = true;
                        errorList.add("第"+(i+2)+"行,数据重复,相同门店相同月份只能有一个目标！");
                  }
            }
            return isError;
      }

      /**
       * 判断“YYYY-MM-DD”类型 时间
       * @param date
       * @return
       */
      public static boolean isDate(String date){
            String reg = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))?$";
            Pattern p = Pattern.compile(reg);
            return p.matcher(date).matches();
      }
}
