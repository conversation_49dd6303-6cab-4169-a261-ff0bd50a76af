package com.pinshang.qingyun.report.model.xd;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@Table(name="t_real_time_report_by_date")
public class RealTimeReportByDate extends BaseIDPO {
    private Long shopId;
    private Integer sourceType;
    private Integer orderType;
    private BigDecimal sales;
    private BigDecimal quantity;
    private BigDecimal cost;
    private BigDecimal averageAmount;
    private BigDecimal returnSales;
    private BigDecimal returnQuantity;
    private BigDecimal returnCost;
    private BigDecimal returnAverageAmount;
    private Date dateTime;
}