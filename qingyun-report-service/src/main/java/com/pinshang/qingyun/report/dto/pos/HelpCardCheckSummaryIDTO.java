package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HelpCardCommoditySummaryIDTO
 * <AUTHOR>
 * @Date 2023/2/27 10:35
 * @Description HelpCardCommoditySummaryIDTO
 * @Version 1.0
 */
@Data
public class HelpCardCheckSummaryIDTO extends Pagination {
    @ApiModelProperty("交易日期-开始")
    private String beginTime;

    @ApiModelProperty("交易日期-结束")
    private String endTime;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("设备号")
    private String payCode;

    @ApiModelProperty("仅显示差异")
    private Boolean isDiff;

    private List<Long> shopIdList;

}
