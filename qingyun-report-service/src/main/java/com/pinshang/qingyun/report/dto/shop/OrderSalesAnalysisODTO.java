package com.pinshang.qingyun.report.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
public class OrderSalesAnalysisODTO {
    @ApiModelProperty("订货金额")
    BigDecimal orderTotal;
    BigDecimal allOrderTotal;
    @ApiModelProperty("订货占比")
    String orderTotalRatio;
    @ApiModelProperty("销售金额")
    BigDecimal offlineSales;
    BigDecimal allOfflineSales;
    @ApiModelProperty("销售占比")
    String offlineSalesRatio;
    @ApiModelProperty("门店")
    String shopName;
    @ApiModelProperty("类名")
    String cateName;
    @ApiModelProperty("退货金额")
    BigDecimal saleReturnOrderTotal;
    @ApiModelProperty("差异")
    BigDecimal difference;

    private Long shopId;
    public String getOrderTotalRatio() {
        BigDecimal allOrderTotal =  this.allOrderTotal;
        BigDecimal orderTotal = this.orderTotal;
        if(StringUtils.isBlank(shopName)) {
            return "";
        }
        if(allOrderTotal == null || BigDecimal.ZERO.compareTo(allOrderTotal) >= 0){
            this.orderTotalRatio = "100%";
            return orderTotalRatio;
        }else{
            BigDecimal orderTotalRatioBd = orderTotal.multiply(BigDecimal.valueOf(100)).divide(allOrderTotal, 2, RoundingMode.HALF_UP);
            return orderTotalRatioBd.toString() + "%";
        }
    }

    public String getOfflineSalesRatio() {
        BigDecimal allOfflineSales =  this.allOfflineSales;
        BigDecimal offlineSales = this.offlineSales;
        if(StringUtils.isBlank(shopName)) {
            return "";
        }
        if(allOfflineSales == null || BigDecimal.ZERO.compareTo(allOfflineSales) >= 0 ){
            this.offlineSalesRatio = "100%";
            return offlineSalesRatio;
        }else{
            BigDecimal offlineSalesRatioBd = offlineSales.multiply(BigDecimal.valueOf(100)).divide(allOfflineSales, 2, RoundingMode.HALF_UP);
            return offlineSalesRatioBd.toString() + "%";
        }
    }

    public BigDecimal getDifference() {
        if(offlineSales != null && orderTotal != null && saleReturnOrderTotal!=null){
            return offlineSales.subtract(orderTotal.subtract(saleReturnOrderTotal));
        }else{
            return null;
        }
    }
}
