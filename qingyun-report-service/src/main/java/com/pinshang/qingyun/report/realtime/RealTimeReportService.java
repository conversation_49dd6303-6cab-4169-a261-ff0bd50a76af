package com.pinshang.qingyun.report.realtime;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.CategoryODTO;
import com.pinshang.qingyun.report.dto.FirstCateSumODTO;
import com.pinshang.qingyun.report.dto.pos.ShopODTO;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.mapper.ReportMapper;
import com.pinshang.qingyun.report.mapper.ShopReportMapper;
import com.pinshang.qingyun.report.mapper.shop.CommodityShopTaxMapper;
import com.pinshang.qingyun.report.model.shop.CommodityShopTax;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.report.util.RandomDataUtil;
import lombok.extern.java.Log;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicDouble;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

@Log
@Service
public class RealTimeReportService {

    //非强一致性数据  不要求线程安全
    private static Map<Long,ShopODTO> shopNameMap = new HashMap<>();
    private static Map<Long,String> categoryNameMap = new HashMap<>();

    @Autowired
    private CommodityShopTaxMapper commodityShopTaxMapper;


    private final static String SHOP_ID = ":shopId:";

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private ShopReportMapper shopReportMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private ReportMapper reportMapper;

    //从redis去取好了
    //销售
    public long turnoverBaseNumber = 3_000_000;
    //毛利额
    public long grossProfitBaseNumber = 1_500_000;

    private static final BigDecimal unit = new BigDecimal(10000);

    /**
     * 30秒同步一次shopName 和 categoryName
     */
    @Scheduled(fixedDelay = 1 * 1000 * 30)
    public void reLoadShopName(){
        List<ShopODTO> shops = shopReportMapper.getAllShopNameList();
        shops.forEach(s -> shopNameMap.put(s.getId(),s));
        List<CategoryODTO> firstCategorys = commodityMapper.findAllFirstCategory();
        firstCategorys.forEach(f -> categoryNameMap.put(f.getId(),f.getCateName()));
    }

    public RealTimeData getChartData(){
        RealTimeData realTimeData = new RealTimeData();
        return getChartData(realTimeData);
    }

    public RealTimeData getData(){
        RealTimeData realTimeData = new RealTimeData();
        getData(realTimeData);
        getMapData(realTimeData);
        return realTimeData;
    }

    private RealTimeData getData(RealTimeData realTimeData){
        //年总
        Data7 data7 = new Data7();
        data7.setYearTotalTurnover(StringUtils.leftPad(BigDecimal.valueOf(getYearTotalTurnover()).setScale(0,RoundingMode.HALF_UP).toPlainString(),12,'0'));
        realTimeData.setData7(data7);
        //3个贴
        Data8 data8 = new Data8();
        Long monthlyOrderQuantity = getMonthlyOrderQuantity();
        Double monthlyTotalTurnoverDouble = getMonthlyTotalTurnover();
        Long monthlyTotalTurnover = new BigDecimal(monthlyTotalTurnoverDouble).setScale(0, RoundingMode.HALF_UP).longValue();
        BigDecimal averageCustomerPrice = new BigDecimal(monthlyTotalTurnover).divide(new BigDecimal(monthlyOrderQuantity), 2, RoundingMode.HALF_UP);
        data8.setAverageCustomerPrice(averageCustomerPrice.toPlainString());
        data8.setMonthlyOrderQuantity(monthlyOrderQuantity.toString());
        data8.setMonthlyTotalTurnover(monthlyTotalTurnover.toString());
        realTimeData.setData8(data8);

        //地图


        return realTimeData;
    }

    private RealTimeData getChartData(RealTimeData realTimeData){
        //营业额月排名
        RealData realData1 = new RealData();
        realData1.setTitle("营业额月排名");
        realData1.setType("horizontal_bar");
        realData1.setDataList(getTurnoverTopN(7));
        realTimeData.setData1(realData1);

        //营业额月趋势图
        RealData realData2 = new RealData();
        realData2.setType("graph");
        realData2.setTitle("营业额月趋势图 (单位:万)");
        SeriesAndxaxisList monthTurnoverDayByDay = getMonthTurnoverDayByDay();
        realData2.setSeries(monthTurnoverDayByDay.getSeries());
        realData2.setXAxis(monthTurnoverDayByDay.getXAxis());
        realTimeData.setData2(realData2);

        //毛利额月排名
        RealData realData3 = new RealData();
        realData3.setTitle("毛利额月排名");
        realData3.setType("horizontal_bar");
        realData3.setDataList(getGrossProfitTopN(7));
        realTimeData.setData3(realData3);

        //毛利额月趋势
        RealData realData4 = new RealData();
        realData4.setType("graph");
        realData4.setTitle("毛利额月趋势 (单位:万)");
        SeriesAndxaxisList monthGrossProfitDayByDay = getMonthGrossProfitDayByDay();
        realData4.setSeries(monthGrossProfitDayByDay.getSeries());
        realData4.setXAxis(monthGrossProfitDayByDay.getXAxis());
        realTimeData.setData4(realData4);

        //商品销售占比
        RealData realData5 = new RealData();
        realData5.setTitle("商品销售占比");
        realData5.setType("pie_chart");
        realData5.setDataList(getCategorySalesTopN(4));
        realTimeData.setData5(realData5);

        //客单量趋势图
        RealData realData6 = new RealData();
        realData6.setTitle("客单量月趋势图 (单位:万单)");
        realData6.setType("graph");
        SeriesAndxaxisList orderQuantityDayByDay = getOrderQuantityDayByDay();
        realData6.setSeries(orderQuantityDayByDay.getSeries());
        realData6.setXAxis(orderQuantityDayByDay.getXAxis());
        realTimeData.setData6(realData6);
        return realTimeData;
    }


    public RealTimeData getMapData(){
        RealTimeData realTimeData = new RealTimeData();
        getMapData(realTimeData);
        MapCharWrapper data9 = realTimeData.getData9();
        List<MapChart> dataList = data9.getDataList().stream().filter(m -> !m.getOrders().equals("0")).collect(toList());
        List<MapChart> mapCharts;
        if(CollectionUtils.isNotEmpty(dataList)){
            int num = (dataList.size() / 10) + 1;
            num = num <= 15 ? num : 15;
            int size = dataList.size();
            log.info("在"+ size +"大小中的list中选取" + num);
            mapCharts = RandomDataUtil.generateRandomDataNoRepeat(dataList, num);
            log.info("选取结束");
        }else{
            mapCharts = Collections.emptyList();
        }
        data9.setDataList(mapCharts);
        realTimeData.setData10(data9);
        realTimeData.setData9(null);
        return realTimeData;
    }

    private RealTimeData getMapData(RealTimeData realTimeData){

        final String current$yyMMdd =  DateUtils.get$yyMMdd(LocalDate.now());
        String shopOrderQuantitySnapshotKey = RedisDataConstants.Daile.SHOP_ORDER_QUANTITY_SNAPSHOT + current$yyMMdd;
        RScoredSortedSet<Object> shopOrderQuantitySnapshotSet = redisson.getScoredSortedSet(shopOrderQuantitySnapshotKey);
        Collection<ScoredEntry<Object>> shopOrderQuantitySnapshotEntries = shopOrderQuantitySnapshotSet.entryRange(0, -1);
        Map<Long,Double> shopOrderQuantitySnapshotMap = new HashMap<>();
        shopOrderQuantitySnapshotEntries.forEach(a -> {
            Double score = a.getScore();
            Object value = a.getValue();
            shopOrderQuantitySnapshotMap.put((Long)value,score);
        });

        String shopOrderQuantityKey = RedisDataConstants.Daile.SHOP_ORDER_QUANTITY + current$yyMMdd;
        RScoredSortedSet<Object> shopOrderQuantitySet = redisson.getScoredSortedSet(shopOrderQuantityKey);
        Collection<ScoredEntry<Object>> shopOrderQuantityEntries = shopOrderQuantitySet.entryRange(0, -1);
        Map<Long,Double> shopOrderQuantityMap = new HashMap<>();
        shopOrderQuantityEntries.forEach(a -> {
            Object value = a.getValue();
            Double score = a.getScore();
            shopOrderQuantityMap.put((Long)value,score);
        });

        MapCharWrapper mapCharWrapper = new MapCharWrapper();
        List<MapChart> dataList = mapCharWrapper.getDataList();
        shopNameMap.forEach((k,v) -> {
            MapChart mapChart = new MapChart();
            Long id = v.getId();
            String shopName = v.getShopName();
            BigDecimal latitude = v.getLatitude();
            BigDecimal longitude = v.getLongitude();
            if(latitude != null && longitude != null && v.getShopStatus().equals(1)){
                mapChart.setName(shopName);
                mapChart.setId(id.toString());
                List<BigDecimal> list = new ArrayList<>(3);
                list.add(longitude);
                list.add(latitude);
                mapChart.setValue(list);
                Double scoreSnapshot = shopOrderQuantitySnapshotMap.get(id);
                Double score = shopOrderQuantityMap.get(id);
                if(scoreSnapshot == null){
                    scoreSnapshot = 0.0;
                }
                if(score == null){
                    score = 0.0;
                }
                String orders = String.valueOf((int)(score - scoreSnapshot));
                list.add(new BigDecimal(orders));
                mapChart.setOrders(orders);
                dataList.add(mapChart);
            }
        });
        mapCharWrapper.setSum(String.valueOf(dataList.size()));
        realTimeData.setData9(mapCharWrapper);
        return realTimeData;
    }

    public RealTimeData getAllData(){
        RealTimeData realTimeData = new RealTimeData();
        getChartData(realTimeData);
        getData(realTimeData);
        getMapData(realTimeData);
        return realTimeData;
    }

    /**
     * 大类销售占比 top n + 1
     */
    public List<CategorySales> getCategorySalesTopN(int n){
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        //获取月总销售金额
        String totalTurnoverKey = RedisDataConstants.Monthly.TOTAL_TURNOVER + current$yyMM;
        RAtomicDouble totalTurnoverRa = redisson.getAtomicDouble(totalTurnoverKey);
        final double totalTurnover = totalTurnoverRa.get();
        List<CategorySales> categorySalesList = new ArrayList<>(n + 1);
        String categorySalesKey = RedisDataConstants.Monthly.CATEGORY_SALES + current$yyMM;
        RScoredSortedSet<String> scoredSortedSet = redisson.getScoredSortedSet(categorySalesKey);
        Collection<ScoredEntry<String>> scoredEntries = scoredSortedSet.entryRangeReversed(0, n);
        int percentagSum = 0;
        for (ScoredEntry<String> c : scoredEntries) {
            String value = c.getValue();
            Double score = c.getScore();
            CategorySales categorySales = new CategorySales();
            categorySales.setCategoryName(categoryNameMap.get(Long.parseLong(value)));
            int percentag = (int)((score / totalTurnover) * 100);
            percentag = percentag > 100 ? 100 : percentag;
            percentagSum += percentag;
            categorySales.setPercentag(String.valueOf(percentag));
            categorySalesList.add(categorySales);
        }
        CategorySales categorySales = new CategorySales();
        categorySales.setCategoryName("其他");
        categorySales.setPercentag(String.valueOf(100 - percentagSum));
        if(categorySalesList.size() >= 5){
            categorySalesList.add(categorySales);
        }
        return categorySalesList;
    }

    public SeriesAndxaxisList getOrderQuantityDayByDay(){
        SeriesAndxaxisList seriesAndxAxisList = new SeriesAndxaxisList();
        List<BigDecimal> series = seriesAndxAxisList.getSeries();
        List<String> xAxisList = seriesAndxAxisList.getXAxis();
        IntStream.range(1,31).forEach(i -> {
            LocalDate localDate = LocalDate.now().plusDays(-i);
            String xAxis = DateUtils.get$yyMMdd(localDate);
            final String current$yyMMdd =  xAxis;
            String orderQuantityKey = RedisDataConstants.Daile.ORDER_QUANTITY + current$yyMMdd;
            RAtomicLong OrderQuantityRa = redisson.getAtomicLong(orderQuantityKey);
            OrderQuantityRa.expire(RedisDataConstants.Monthly.EXPIRE_TIME,RedisDataConstants.Monthly.TIME_UNIT);
            long v = OrderQuantityRa.get();
            xAxisList.add(xAxis.substring(6));
            series.add(new BigDecimal(v).divide(new BigDecimal(10000),2,RoundingMode.HALF_UP));
        });
        Collections.reverse(xAxisList);
        Collections.reverse(series);
        return seriesAndxAxisList;
    }

    /**
     * 销售额top n
     * @param n
     * @return
     */
    public List<TurnoverData> getTurnoverTopN(int n){
        List<TurnoverData> turnoverDataList = new ArrayList<>(n);
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String turnoverKey = RedisDataConstants.Monthly.TURNOVER + current$yyMM;
        RScoredSortedSet<Long> scoredSortedSet = redisson.getScoredSortedSet(turnoverKey);
        Collection<ScoredEntry<Long>> scoredEntries = scoredSortedSet.entryRangeReversed(0, n);
        scoredEntries.forEach(entity -> {
            TurnoverData turnoverData = new TurnoverData();
            Long value = entity.getValue();
            Double score = entity.getScore();
            turnoverData.setShopSale(new BigDecimal(score).setScale(0, RoundingMode.HALF_UP).toPlainString());
            ShopODTO shopODTO = shopNameMap.get(value);
            if(shopODTO != null){
                turnoverData.setShopName(shopODTO.getShopName());
                turnoverData.setShopId(value.toString());
                int percentag = (int) ((score / turnoverBaseNumber) * 100);
                percentag = percentag > 100 ? 100 : percentag;
                turnoverData.setShopPercentag(String.valueOf(percentag));
                turnoverDataList.add(turnoverData);
            }
        });
        return turnoverDataList;
    }

    public RealTimeData getShopData8And7(Long shopId){
        RealTimeData realTimeData = new RealTimeData();
        //获取门店年总
        final String current$yy =  DateUtils.get$yy(LocalDate.now());
        //年销售
        String shopTotalTurnoverRedisKey = RedisDataConstants.Year.TOTAL_TURNOVER + current$yy + SHOP_ID + shopId;
        RAtomicDouble shopTotalTurnoverRa = redisson.getAtomicDouble(shopTotalTurnoverRedisKey);
        double shopYearTotalTurnover = shopTotalTurnoverRa.get();
        Data7 data7 = new Data7();
        data7.setYearTotalTurnover(new BigDecimal(shopYearTotalTurnover).setScale(0,RoundingMode.HALF_UP).toPlainString());

        //门店月销售
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String turnoverKey = RedisDataConstants.Monthly.TURNOVER + current$yyMM;
        RScoredSortedSet<Long> turnoverSet = redisson.getScoredSortedSet(turnoverKey);
        Double turnover = turnoverSet.getScore(shopId);

        //门店订单量
        String shopOrderQuantityKey = RedisDataConstants.Monthly.SHOP_ORDER_QUANTITY + current$yyMM;
        RScoredSortedSet<Object> shopOrderQuantitySet = redisson.getScoredSortedSet(shopOrderQuantityKey);
        Double shopOrderQuantity = shopOrderQuantitySet.getScore(shopId);

        Data8 data8 = new Data8();
        BigDecimal turnoverBg = new BigDecimal(turnover).setScale(0, RoundingMode.HALF_UP);
        BigDecimal shopOrderQuantityBg = new BigDecimal(shopOrderQuantity).setScale(0, RoundingMode.HALF_UP);
        data8.setMonthlyTotalTurnover(turnoverBg.toPlainString());
        data8.setMonthlyOrderQuantity(shopOrderQuantityBg.toPlainString());
        data8.setAverageCustomerPrice(turnoverBg.divide(shopOrderQuantityBg,0,RoundingMode.HALF_UP).toPlainString());

        realTimeData.setData7(data7);
        realTimeData.setData8(data8);
        return realTimeData;
    }

    /**
     * 单门店毛利额排名 月
     * @param n top N
     */
    public List<GrossProfit> getGrossProfitTopN(int n){
        List<GrossProfit> grossProfits = new ArrayList<>(n);
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String grossProfitKey = RedisDataConstants.Monthly.GROSS_PROFIT + current$yyMM;
        RScoredSortedSet<Long> scoredSortedSet = redisson.getScoredSortedSet(grossProfitKey);
        Collection<ScoredEntry<Long>> scoredEntries = scoredSortedSet.entryRangeReversed(0, n);
        scoredEntries.forEach(entity -> {
            Long value = entity.getValue();
            Double score = entity.getScore();
            GrossProfit grossProfit = new GrossProfit();
            ShopODTO shopODTO = shopNameMap.get(value);
            if(shopODTO != null){
                grossProfit.setShopName(shopODTO.getShopName());
                grossProfit.setShopSale(new BigDecimal(score).setScale(0, RoundingMode.HALF_UP).toPlainString());
                grossProfit.setShopId(value.toString());
                int percentag = (int) ((score / grossProfitBaseNumber) * 100);
                percentag = percentag > 100 ? 100 : percentag;
                grossProfit.setShopPercentag(String.valueOf(percentag));
                grossProfits.add(grossProfit);
            }
        });
        return grossProfits;
    }

    /**
     * 今年销售额
     * @return
     */
    public Double getYearTotalTurnover(){
        final String current$yy =  DateUtils.get$yy(LocalDate.now());
        String totalTurnoverKey = RedisDataConstants.Year.TOTAL_TURNOVER + current$yy;
        RAtomicDouble totalTurnoverRa = redisson.getAtomicDouble(totalTurnoverKey);
        return totalTurnoverRa.get();
    }

    /**
     * 返回月总销售
     * @return
     */
    public Double getMonthlyTotalTurnover(){
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String totalTurnoverKey = RedisDataConstants.Monthly.TOTAL_TURNOVER + current$yyMM;
        RAtomicDouble totalTurnoverRa = redisson.getAtomicDouble(totalTurnoverKey);
        return totalTurnoverRa.get();
    }

    public Long getMonthlyOrderQuantity(){
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String orderQuantityKey = RedisDataConstants.Monthly.ORDER_QUANTITY + current$yyMM;
        RAtomicLong orderQuantityRa = redisson.getAtomicLong(orderQuantityKey);
        return orderQuantityRa.get();
    }

    //月销售额趋势
    public SeriesAndxaxisList getMonthTurnoverDayByDay(){
        SeriesAndxaxisList seriesAndxAxisList = new SeriesAndxaxisList();
        List<BigDecimal> series = seriesAndxAxisList.getSeries();
        List<String> xAxisList = seriesAndxAxisList.getXAxis();
        IntStream.range(1,31).forEach(i -> {
            LocalDate localDate = LocalDate.now().plusDays(-i);
            String xAxis = DateUtils.get$yyMMdd(localDate);
            final String current$yyMMdd =  xAxis;
            String totalTurnoverKey = RedisDataConstants.Daile.TOTAL_TURNOVER + current$yyMMdd;
            RAtomicDouble totalTurnoverRa = redisson.getAtomicDouble(totalTurnoverKey);
            totalTurnoverRa.expire(RedisDataConstants.Monthly.EXPIRE_TIME,RedisDataConstants.Monthly.TIME_UNIT);
            double v = totalTurnoverRa.get();
            xAxisList.add(xAxis.substring(6));
            series.add(new BigDecimal(v).divide(unit,2,RoundingMode.HALF_UP));
        });
        Collections.reverse(xAxisList);
        Collections.reverse(series);
        return seriesAndxAxisList;
    }

    //月毛利额趋势
    public SeriesAndxaxisList getMonthGrossProfitDayByDay(){
        SeriesAndxaxisList seriesAndxAxisList = new SeriesAndxaxisList();
        List<BigDecimal> series = seriesAndxAxisList.getSeries();
        List<String> xAxisList = seriesAndxAxisList.getXAxis();
        IntStream.range(1,31).forEach(i -> {
            LocalDate localDate = LocalDate.now().plusDays(-i);
            String xAxis = DateUtils.get$yyMMdd(localDate);
            final String current$yyMMdd =  xAxis;
            String totalGrossProfit = RedisDataConstants.Daile.TOTAL_GROSS_PROFIT + current$yyMMdd;
            RAtomicDouble totalTurnoverRa = redisson.getAtomicDouble(totalGrossProfit);
            totalTurnoverRa.expire(RedisDataConstants.Monthly.EXPIRE_TIME,RedisDataConstants.Monthly.TIME_UNIT);
            double v = totalTurnoverRa.get();
            xAxisList.add(xAxis.substring(6));
            series.add(new BigDecimal(v).divide(unit,2,RoundingMode.HALF_UP));
        });
        Collections.reverse(xAxisList);
        Collections.reverse(series);
        return seriesAndxAxisList;
    }

    //计算坪效
    public SeriesAndxaxisList getGroundEffects(){
        SeriesAndxaxisList seriesAndxaxisList = new SeriesAndxaxisList();
        List<GroundEffect> groundEffects = new ArrayList<>();
        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String turnoverKey = RedisDataConstants.Monthly.TURNOVER + current$yyMM;
        RScoredSortedSet<Long> scoredSortedSet = redisson.getScoredSortedSet(turnoverKey);
        Collection<ScoredEntry<Long>> scoredEntries = scoredSortedSet.entryRangeReversed(0, 9);
        scoredEntries.forEach(entity -> {
            GroundEffect groundEffect = new GroundEffect();
            Long value = entity.getValue();
            Double score = entity.getScore();
            ShopODTO shopODTO = shopNameMap.get(value);
            BigDecimal shopArea = null;
            if(shopODTO != null){
                shopArea = shopODTO.getShopArea();
            }
            if(shopArea == null || BigDecimal.ZERO.equals(shopArea)){
                //ignore
            }else{
                String shopName = shopNameMap.get(value).getShopName();
                groundEffect.setEffect(new BigDecimal(score).divide(shopArea,4,RoundingMode.HALF_UP));
                groundEffect.setShopId(value.toString());
                groundEffect.setShopArea(shopArea);
                groundEffect.setShopName(shopName);
                groundEffects.add(groundEffect);
            }
        });
        groundEffects.sort(Comparator.comparing(GroundEffect::getShopArea).reversed());
        List<BigDecimal> series = seriesAndxaxisList.getSeries();
        List<String> xAxis = seriesAndxaxisList.getXAxis();
        groundEffects.forEach(g -> {
            series.add(g.getEffect().divide(new BigDecimal(1000),2,RoundingMode.HALF_UP));
            xAxis.add(g.getShopName());
        });
        return seriesAndxaxisList;
    }

    public boolean init(){
        Example example = new Example(CommodityShopTax.class);
        String start = DateUtil.get4yMd(DateUtil.addDay(new Date(), -31));
        String end = DateUtil.get4yMd(new Date());
        example.createCriteria()
                .andLessThanOrEqualTo("dateTime", end)
                .andGreaterThanOrEqualTo("dateTime",start);
        List<CommodityShopTax> commodityShopTaxes = commodityShopTaxMapper.selectByExample(example);

        final String current$yyMM =  DateUtils.get$yyMM(LocalDate.now());
        String grossProfitKey = OrderConstants.Monthly.GROSS_PROFIT + current$yyMM;
        RScoredSortedSet<Long> grossProfitSortedSet = redisson.getScoredSortedSet(grossProfitKey);

        String orderQuantityKey = OrderConstants.Monthly.ORDER_QUANTITY + current$yyMM;
        RAtomicLong orderQuantityRa = redisson.getAtomicLong(orderQuantityKey);

        String turnoverKey = OrderConstants.Monthly.TURNOVER + current$yyMM;
        RScoredSortedSet<Long> turnoverSortedSet = redisson.getScoredSortedSet(turnoverKey);

        BigDecimal allTotal = BigDecimal.ZERO;

        for(CommodityShopTax c : commodityShopTaxes){
            BigDecimal costTotal = c.getCostTotal(); //成本
            BigDecimal totalSales = c.getTotalSales();//销售
            BigDecimal grossProfit = totalSales.subtract(costTotal);
            Integer offlineVisitorNumber = c.getOfflineVisitorNumber();
            orderQuantityRa.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
            orderQuantityRa.addAndGet(offlineVisitorNumber);
            Long shopId = c.getShopId();
            grossProfitSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
            grossProfitSortedSet.addScore(shopId,grossProfit);
            turnoverSortedSet.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
            turnoverSortedSet.addScore(shopId,totalSales);
            allTotal = allTotal.add(totalSales);
        }

        String totalTurnoverKey = OrderConstants.Monthly.TOTAL_TURNOVER + current$yyMM;
        RAtomicDouble totalTurnoverRa = redisson.getAtomicDouble(totalTurnoverKey);
        totalTurnoverRa.expire(OrderConstants.Monthly.EXPIRE_TIME,OrderConstants.Monthly.TIME_UNIT);
        totalTurnoverRa.addAndGet(allTotal.doubleValue());

        Map<String, List<CommodityShopTax>> commodityShopTaxGroupByDateTime = commodityShopTaxes.stream().collect(Collectors.groupingBy(CommodityShopTax::getDateTime));
        for(String dateTime : commodityShopTaxGroupByDateTime.keySet()){

            String data$yyMMdd = dateTime.substring(2);
            List<CommodityShopTax> commodityShopTaxeslist = commodityShopTaxGroupByDateTime.get(dateTime);
            commodityShopTaxeslist.forEach(c -> {
                BigDecimal costTotal = c.getCostTotal(); //成本
                BigDecimal totalSales = c.getTotalSales();//销售
                Integer offlineVisitorNumber = c.getOfflineVisitorNumber();
                if(costTotal != null && totalSales != null && offlineVisitorNumber != null){
                    BigDecimal grossProfit = totalSales.subtract(costTotal); //毛利
                    String totalGrossProfit = OrderConstants.Daile.TOTAL_GROSS_PROFIT + data$yyMMdd;
                    RAtomicDouble totalGrossProfitRa = redisson.getAtomicDouble(totalGrossProfit);
                    totalGrossProfitRa.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    totalGrossProfitRa.addAndGet(grossProfit.doubleValue());

                    String orderTotalGrossProfit = RedisDataConstants.Daile.TOTAL_GROSS_PROFIT + data$yyMMdd;
                    RAtomicDouble orderTotalGrossProfitRa = redisson.getAtomicDouble(orderTotalGrossProfit);
                    orderTotalGrossProfitRa.expire(RedisDataConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    orderTotalGrossProfitRa.addAndGet(grossProfit.doubleValue());
                    //--
                    String totalTurnoverKeyDay = OrderConstants.Daile.TOTAL_TURNOVER + data$yyMMdd;
                    RAtomicDouble totalTurnoverRaDay = redisson.getAtomicDouble(totalTurnoverKeyDay);
                    totalTurnoverRaDay.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    totalTurnoverRaDay.addAndGet(totalSales.doubleValue());

                    String orderTotalTurnoverKeyDay = RedisDataConstants.Daile.TOTAL_TURNOVER + data$yyMMdd;
                    RAtomicDouble orderTotalTurnoverRaDay = redisson.getAtomicDouble(orderTotalTurnoverKeyDay);
                    orderTotalTurnoverRaDay.expire(RedisDataConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    orderTotalTurnoverRaDay.addAndGet(totalSales.doubleValue());

                    //--
                    String orderQuantityKeyDay = OrderConstants.Daile.ORDER_QUANTITY + data$yyMMdd;
                    RAtomicDouble orderQuantityRaDay = redisson.getAtomicDouble(orderQuantityKeyDay);
                    orderQuantityRaDay.expire(OrderConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    orderQuantityRaDay.addAndGet(offlineVisitorNumber);

                    String orderOrderQuantityKeyDay = RedisDataConstants.Daile.ORDER_QUANTITY + data$yyMMdd;
                    RAtomicDouble orderOrderQuantityRaDay = redisson.getAtomicDouble(orderOrderQuantityKeyDay);
                    orderOrderQuantityRaDay.expire(RedisDataConstants.Daile.EXPIRE_TIME,OrderConstants.Daile.TIME_UNIT);
                    orderOrderQuantityRaDay.addAndGet(offlineVisitorNumber);
                }
            });
        }
        List<FirstCateSumODTO> firstCateSumODTOS = reportMapper.selectFirstCateSum(start, end);
        firstCateSumODTOS.forEach(f -> {
            Long firstId = f.getFirstId();
            BigDecimal sumPrice = f.getSumPrice();
            String categorySalesKey = OrderConstants.Monthly.CATEGORY_SALES + current$yyMM;
            RScoredSortedSet<Object> categorySalesSet = redisson.getScoredSortedSet(categorySalesKey);
            categorySalesSet.expire(OrderConstants.Monthly.EXPIRE_TIME, OrderConstants.Monthly.TIME_UNIT);
            categorySalesSet.addScore(String.valueOf(firstId),sumPrice);
        });
        return true;
    }

}
