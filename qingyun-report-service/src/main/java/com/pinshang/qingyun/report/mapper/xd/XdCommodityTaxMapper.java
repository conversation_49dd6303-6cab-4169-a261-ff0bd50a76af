package com.pinshang.qingyun.report.mapper.xd;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO;
import com.pinshang.qingyun.report.model.xd.XdCommodityTax;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Mapper
@Repository
public interface XdCommodityTaxMapper extends MyMapper<XdCommodityTax>{

    List<XdCommodityTaxODTO> findXdCommodityTaxList(@Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("shopId") Long shopId, @Param("commodityId") Long commodityId);
}
