package com.pinshang.qingyun.report.mapper.pos;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.PosReportCommodityQuantityUpdate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PosReportCommodityQuantityUpdateMapper extends MyMapper<PosReportCommodityQuantityUpdate> {

    /**
     * 单品修改数量明细
     * @param dto
     * @return
     */
    List<CommodityQuantityUpdateDTO> list(@Param("dto") CommodityQuantityUpdateIDTO dto);

    /**
     * 单品修改数量明细合计
     * @param dto
     * @return
     */
    CommodityQuantityUpdateDTO sum(@Param("dto") CommodityQuantityUpdateIDTO dto);

    List<CommodityQuantityUpdateReportDTO> report(@Param("idto")CommodityDeleteIDTO idto);

    List<CommodityQuantityUpdateReportDTO> reportSum(@Param("idto")CommodityDeleteIDTO idto);

    List<AIMonitorCommodityDTO> updateCommodityInfo(@Param("orderCode") String orderCode);
}