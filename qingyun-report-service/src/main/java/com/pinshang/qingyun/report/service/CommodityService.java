package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.exception.BusinessException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.marketing.dto.XSPricePromotionCacheGetIDTO;
import com.pinshang.qingyun.marketing.dto.XSPricePromotionCacheODTO;
import com.pinshang.qingyun.marketing.service.MtPricePromotionCacheClient;
import com.pinshang.qingyun.report.dto.CommodityInfoODTO;
import com.pinshang.qingyun.report.dto.pos.CommodityPackageSpecDTO;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.model.Commodity;
import com.pinshang.qingyun.report.util.StallUtils;
import com.pinshang.qingyun.shop.dto.ShopCommodityPriceODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.CouponCodeQueryODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityIDTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoSearchIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityClient;
import com.pinshang.qingyun.xsuser.dto.member.CommodityMemberPriceIDTO;
import com.pinshang.qingyun.xsuser.dto.member.CommodityMemberPriceODTO;
import com.pinshang.qingyun.xsuser.service.XSMemberPriceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by chenqi on 2019/5/28 11:24.
 * 商品 service
 */
@Service
@Slf4j
public class CommodityService {

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private XSMemberPriceClient xSMemberPriceClient;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private MtPricePromotionCacheClient mtPricePromotionCacheClient;

    @Autowired
    private XdStockClient xdStockClient;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private StallCommodityClient stallCommodityClient;

    private static String REPORT_BARCODE = "REPORT:BAR_CODE:";
    /**
     * 获取商品条码map
     * @return
     */
    public Map<Long,String> getCommodityBarCodeMap(List<Long> commodityIdList){
        /*List<Commodity> list = commodityMapper.findCommodityBarCodeByParam(null);
        Map<Long, String> map = list.stream().collect(
                Collectors.toMap(Commodity::getCommodityId, Commodity::getBarCode));
        return map;*/
        List<Long> idList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(commodityIdList)){
            // 去重，排序
            idList = commodityIdList.stream().distinct().collect(Collectors.toList());
            Collections.sort(idList);
        }

        RBucket<Map<Long, String>> bucket = redissonClient.getBucket(REPORT_BARCODE+idList.hashCode());
        Map<Long, String> cacheMap = bucket.get();
        if(null != cacheMap){
            Map<Long, String> map = cacheMap.entrySet().stream()
                    .collect(Collectors.toMap(e -> Long.valueOf(e.getKey()+""), e -> e.getValue()));
            return map;
        }

        List<Commodity> list= commodityMapper.findCommodityBarCodeByParam(idList);
        Map<Long, String> map = list.stream().collect(
                Collectors.toMap(Commodity::getCommodityId, Commodity::getBarCode));
        bucket.set(map, 2, TimeUnit.MINUTES);
        return map;
    }

    /**
     * 价签-查询商品信息
     * 报损商品 校验是否淘汰（原逻辑）
     */
    public CommodityInfoODTO findCommodityForPriceTag(String barcode) {
        String barcodeOrWeightCode = barcode;
        QYAssert.isTrue(!StringUtils.isEmpty(barcode), "条形码不能为空");
        // barCode如果是称重码还需要解析出条形码 是否为称重码(称重码为2打头 18位)
        boolean isWeightCode = barcode.length() == 18 && barcode.startsWith("2");
        if(isWeightCode){
            // 获取条形码
            barcode = barcode.substring(1, 7);
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ShopCommodityIDTO idto = new ShopCommodityIDTO();
        idto.setBarCode(barcode);
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        log.info("tokenInfo={}, barcode={}", tokenInfo, barcode);

        CommodityInfoODTO commodityInfoForHand = commodityMapper.findCommodityInfoForHand(barcode);
        if (commodityInfoForHand == null) {
            throw new BusinessException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode(), ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "价格方案中没有该商品");
        }
        if (isWeightCode) {
            // 称重码的报损数量从条码中解析
            String weightNumStr = String.format("%s.%s", barcodeOrWeightCode.substring(12, 14), barcodeOrWeightCode.substring(14, 17));
            commodityInfoForHand.setBreakageNum(new BigDecimal(weightNumStr));
        }

        //获取商品零售价
        CouponCodeQueryODTO couponCodeQueryODTO = shopCommodityClient.couponQuery(barcode, tokenInfo.getStoreId());
        if(couponCodeQueryODTO == null){
            throw new BusinessException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode(), "没有找到该商品的库存信息");
        }else {
            commodityInfoForHand.setRetailPrice(couponCodeQueryODTO.getRetailPrice());
        }

        //获取商品特价
        BigDecimal promotionPrice = getPromotionPrice(tokenInfo.getShopId(),Long.valueOf(commodityInfoForHand.getCommodityId()),commodityInfoForHand.getRetailPrice());
        commodityInfoForHand.setPromotionPrice(promotionPrice);
        //商品价格
        commodityInfoForHand.setCommodityPrice(commodityInfoForHand.getRetailPrice());
        //会员价
        CommodityMemberPriceIDTO commodityMemberPriceIDTO = new CommodityMemberPriceIDTO();
        commodityMemberPriceIDTO.setShopId(tokenInfo.getShopId());
        commodityMemberPriceIDTO.setCommodityId(Long.valueOf(commodityInfoForHand.getCommodityId()));
        CommodityMemberPriceODTO commodityMemberPriceODTO = xSMemberPriceClient.selectCommodityMemberPriceByCommodityId(commodityMemberPriceIDTO);
        commodityInfoForHand.setMemberPrice(null != commodityMemberPriceODTO ? commodityMemberPriceODTO.getMemberPrice() : null);

        // 正常库和临时库
        List<ShopCommodityStockDTO> shopCommodityStockDTOS = xdStockClient.queryShopCommodityStock(tokenInfo.getShopId(), Arrays.asList(commodityInfoForHand.getCommodityId()));
        if (!CollectionUtils.isEmpty(shopCommodityStockDTOS)) {
            Map<Long, ShopCommodityStockDTO> idAndObjMap = shopCommodityStockDTOS.stream().collect(Collectors.toMap(ShopCommodityStockDTO::getCommodityId, Function.identity()));
            ShopCommodityStockDTO shopCommodityStockDTO = idAndObjMap.get(commodityInfoForHand.getCommodityId());
            if (shopCommodityStockDTO != null) {
                commodityInfoForHand.setStockNumber(shopCommodityStockDTO.getStockNumber());
                commodityInfoForHand.setStockQuantity(shopCommodityStockDTO.getStockQuantity());
                commodityInfoForHand.setQualityNumber(shopCommodityStockDTO.getQualityNumber());
                commodityInfoForHand.setQualityQuantity(shopCommodityStockDTO.getQualityQuantity());
            }
        }

        return commodityInfoForHand;
    }

    /**
     * 手持PDA 报损-查询商品信息
     * 报损商品 不校验是否淘汰
     */
    public CommodityInfoODTO findCommodity(String barcode, Long shopId, Long stallId) {
        String barcodeOrWeightCode = barcode;
        QYAssert.isTrue(!StringUtils.isEmpty(barcode), "条形码不能为空");
        // barCode如果是称重码还需要解析出条形码 是否为称重码(称重码为2打头 18位)
        boolean isWeightCode = barcode.length() == 18 && barcode.startsWith("2");
        if(isWeightCode){
            // 获取条形码
            barcode = barcode.substring(1, 7);
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Boolean isBigShop = StallUtils.isBigShop(tokenInfo.getManagementMode());
        CommodityInfoODTO commodityInfoForHand;
        if (BooleanUtils.isTrue(isBigShop)) {
            QYAssert.notNull(stallId, "档口id不能为空");
            CommodityBaseInfoSearchIDTO req = new CommodityBaseInfoSearchIDTO();
            req.setShopId(shopId);
            req.setStallId(stallId);
            req.setCommodityCode(barcode);
            List<CommodityBaseInfoODTO> list = stallCommodityClient.fuzzySearchByCommodityCode(req);
            if (CollectionUtils.isNotEmpty(list)) {
                CommodityBaseInfoODTO commodityBaseInfo = list.get(0);
                commodityInfoForHand = BeanCloneUtils.copyTo(commodityBaseInfo, CommodityInfoODTO.class);
                commodityInfoForHand.setCommodityUnit(commodityBaseInfo.getCommodityUnitName());
            } else {
                commodityInfoForHand = null;
            }
        } else {
            ShopCommodityIDTO idto = new ShopCommodityIDTO();
            idto.setBarCode(barcode);
            idto.setPageNo(1);
            idto.setPageSize(Integer.MAX_VALUE);
            log.info("tokenInfo={}, barcode={}", tokenInfo, barcode);
            commodityInfoForHand = commodityMapper.findCommodityInfoForHand(barcode);
        }
        if (commodityInfoForHand == null) {
            throw new BusinessException(ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode(), ApiErrorCodeEnum.PRICE_MODEL_NO_COMMODITY.getCode() + "价格方案中没有该商品");
        }
        if (isWeightCode) {
            // 称重码的报损数量从条码中解析
            String weightNumStr = String.format("%s.%s", barcodeOrWeightCode.substring(12, 14), barcodeOrWeightCode.substring(14, 17));
            commodityInfoForHand.setBreakageNum(new BigDecimal(weightNumStr));
        }

        //获取商品零售价
        Map<Long, ShopCommodityPriceODTO> shopCommodityPrice = shopCommodityClient.queryShopCommodityPrice(
                tokenInfo.getShopId(),
                Collections.singletonList(commodityInfoForHand.getCommodityId())
        );

        Optional.ofNullable(shopCommodityPrice)
                .map(prices -> prices.get(commodityInfoForHand.getCommodityId()))
                .ifPresent(odto -> commodityInfoForHand.setRetailPrice(odto.getCommodityPrice()));

        //获取商品特价
        BigDecimal promotionPrice = getPromotionPrice(tokenInfo.getShopId(),Long.valueOf(commodityInfoForHand.getCommodityId()),commodityInfoForHand.getRetailPrice());
        commodityInfoForHand.setPromotionPrice(promotionPrice);
        //商品价格
        commodityInfoForHand.setCommodityPrice(commodityInfoForHand.getRetailPrice());
        //会员价
        CommodityMemberPriceIDTO commodityMemberPriceIDTO = new CommodityMemberPriceIDTO();
        commodityMemberPriceIDTO.setShopId(tokenInfo.getShopId());
        commodityMemberPriceIDTO.setCommodityId(Long.valueOf(commodityInfoForHand.getCommodityId()));
        CommodityMemberPriceODTO commodityMemberPriceODTO = xSMemberPriceClient.selectCommodityMemberPriceByCommodityId(commodityMemberPriceIDTO);
        commodityInfoForHand.setMemberPrice(null != commodityMemberPriceODTO ? commodityMemberPriceODTO.getMemberPrice() : null);

        if (!BooleanUtils.isTrue(isBigShop)) {
            // 正常库和临时库
            List<ShopCommodityStockDTO> shopCommodityStockDTOS = xdStockClient.queryShopCommodityStock(tokenInfo.getShopId(), Arrays.asList(commodityInfoForHand.getCommodityId()));
            if (!CollectionUtils.isEmpty(shopCommodityStockDTOS)) {
                Map<Long, ShopCommodityStockDTO> idAndObjMap = shopCommodityStockDTOS.stream().collect(Collectors.toMap(ShopCommodityStockDTO::getCommodityId, Function.identity()));
                ShopCommodityStockDTO shopCommodityStockDTO = idAndObjMap.get(commodityInfoForHand.getCommodityId());
                if (shopCommodityStockDTO != null) {
                    commodityInfoForHand.setStockNumber(shopCommodityStockDTO.getStockNumber());
                    commodityInfoForHand.setStockQuantity(shopCommodityStockDTO.getStockQuantity());
                    commodityInfoForHand.setQualityNumber(shopCommodityStockDTO.getQualityNumber());
                    commodityInfoForHand.setQualityQuantity(shopCommodityStockDTO.getQualityQuantity());
                }
            }
        }

        return commodityInfoForHand;
    }

    /**
     * 获取商品特价（to C 的特价）
     */
    public  BigDecimal getPromotionPrice(Long shopId,Long commodityId,BigDecimal retailPrice){
        // 获取商品特价
        XSPricePromotionCacheGetIDTO pricePromotionIdto = new XSPricePromotionCacheGetIDTO();
        pricePromotionIdto.setChannelType(OrderSourceTypeEnum.POS.getCode());
        pricePromotionIdto.setShopId(shopId);
        List<XSPricePromotionCacheGetIDTO.XSPricePromotionCommodityIDTO> pricePromotionCommodityVos= new ArrayList<>();
        XSPricePromotionCacheGetIDTO.XSPricePromotionCommodityIDTO xsPricePromotionCommodityVo = new XSPricePromotionCacheGetIDTO.XSPricePromotionCommodityIDTO();
        xsPricePromotionCommodityVo.setCommodityId(commodityId);
        xsPricePromotionCommodityVo.setCommodityPrice(retailPrice);
        xsPricePromotionCommodityVo.setQuantity(BigDecimal.ONE);
        pricePromotionCommodityVos.add(xsPricePromotionCommodityVo);
        pricePromotionIdto.setCommodityList(pricePromotionCommodityVos);
        BigDecimal promotionPrice = null;
        try{
            Map<Long, XSPricePromotionCacheODTO> specialOfferMap = mtPricePromotionCacheClient.getMap(pricePromotionIdto);
            XSPricePromotionCacheODTO xsPricePromotionCacheODTO = specialOfferMap.get(commodityId);
            if(xsPricePromotionCacheODTO != null){
                promotionPrice = xsPricePromotionCacheODTO.getPrice();
            }
        }catch (Exception e){
            log.error("get promotion price error, exception is:", e);
        }
        return promotionPrice;
    }
    public Commodity findCommodityById(Long commodityId) {
        return commodityMapper.findCommodityById(commodityId);
    }

    /**
     * 获取商品包装规格
     * @param commodityCodeSet
     * @return
     */
    public List<CommodityPackageSpecDTO> getCommodityListByCodes(Set<String> commodityCodeSet) {
        return commodityMapper.getCommodityListByCodes(commodityCodeSet);
    }
}
