package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.GuestListSummaryReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface GuestListSummaryReportMapper extends MyMapper<GuestListSummaryReport> {

    List<GuestListSummaryReportOrderODTO> guestSummaryReport(@Param("guestListSummaryReportOrderIDTO") GuestListSummaryReportOrderIDTO guestListSummaryReportOrderIDTO);

    int deleteGuestListSummaryReportByOrderCodes(@Param("orderCode") Long orderCodes);

    int deleteGuestListSummaryReportByTimeRange(@Param("saleTime") String saleTime,@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<SalesSummaryReportOrderODTO> getJointRate(@Param("jointIDTO") GuestListSummaryReportOrderIDTO idto);

    /**
     *以不同的维度获取净销售值
     * @param dto
     * @return
     */
    List<NetSalesListDTO> netSalesList(@Param("dto") CommodityDeleteIDTO dto);

    Double netSalesCount(@Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("shopIds") List<Long> shopIds,
                             @Param("saleTimes") List<String> saleTimes, @Param("createIds") List<Long> createIds);
}
