package com.pinshang.qingyun.report.dto.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/1/11
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class TableColumnODTO {
    private String id;
    private String prop;
    private String label;
    private Object sum;

    public TableColumnODTO(String id, String prop, String label) {
        this.id = id;
        this.prop = prop;
        this.label = label;
    }
}
