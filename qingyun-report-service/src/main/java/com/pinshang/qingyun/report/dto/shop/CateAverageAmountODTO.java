package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @see ExcelSheetTitleEnum#CATE_AVERAGE_AMOUNT
 */
@Data
public class CateAverageAmountODTO {

    @ExcelProperty("门店")
    String shopName;
    @ExcelProperty("分类")
    String cateName;
    @ApiModelProperty("客单量")
    @ExcelProperty("今日客单量")
    Long offlineVisitorNumber = 0L;
    @ApiModelProperty("上周客单量")
    @ExcelProperty("上周客单量")
    Long lastweekOfflineVisitorNumber = 0L;
    @ApiModelProperty("客单量同比")
    @ExcelProperty("客单量同比")
    String visitorNumberRatio;
    @ApiModelProperty("客单价")
    @ExcelProperty("今日客单价")
    BigDecimal offlineAverageAmount = BigDecimal.ZERO;
    @ApiModelProperty("上周客单价")
    @ExcelProperty("上周客单价")
    BigDecimal lastweekOfflineAverageAmount = BigDecimal.ZERO;
    @ApiModelProperty("客单价同比")
    @ExcelProperty("客单价同比")
    String averageAmountRatio;

    public void initNullValues() {
        this.offlineVisitorNumber = null;
        this.lastweekOfflineVisitorNumber = null;
        this.visitorNumberRatio = null;
        this.offlineAverageAmount = null;
        this.lastweekOfflineAverageAmount = null;
        this.averageAmountRatio = null;
    }


    public BigDecimal getOfflineAverageAmount() {
        //合计没有shopName 只有合计要除
        if(offlineVisitorNumber != null && offlineVisitorNumber != 0L && StringUtils.isBlank(shopName)){
            return offlineAverageAmount.divide(new BigDecimal(offlineVisitorNumber),2,RoundingMode.HALF_UP);
        }
        return offlineAverageAmount;
    }

    public BigDecimal getLastweekOfflineAverageAmount() {
        //合计没有shopName 只有合计要除
        if(lastweekOfflineVisitorNumber != null && lastweekOfflineVisitorNumber != 0L && StringUtils.isBlank(shopName)){
            return lastweekOfflineAverageAmount.divide(new BigDecimal(lastweekOfflineVisitorNumber),2,RoundingMode.HALF_UP);
        }
        return lastweekOfflineAverageAmount;
    }

    public String getVisitorNumberRatio() {
        Long lastweekOfflineVisitorNumber = this.lastweekOfflineVisitorNumber;
        Long offlineVisitorNumber = this.offlineVisitorNumber;
        if(lastweekOfflineVisitorNumber == null){
            return null;
        }
        if(lastweekOfflineVisitorNumber <= 0){
            this.visitorNumberRatio = "100%";
            return visitorNumberRatio;
        }else{
            BigDecimal visitorNumberRatioBg = BigDecimal.valueOf(offlineVisitorNumber - lastweekOfflineVisitorNumber).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(lastweekOfflineVisitorNumber), 2, RoundingMode.HALF_UP);
            return visitorNumberRatioBg.toString() + "%";
        }
    }

    public String getAverageAmountRatio() {
        BigDecimal lastweekOfflineAverageAmount =  getLastweekOfflineAverageAmount();//this.lastweekOfflineAverageAmount;
        BigDecimal offlineAverageAmount = getOfflineAverageAmount();//this.offlineAverageAmount;
        if(lastweekOfflineAverageAmount == null){
            return null;
        }
        if(BigDecimal.ZERO.compareTo(lastweekOfflineAverageAmount) >= 0 ){
            this.averageAmountRatio = "100%";
            return averageAmountRatio;
        }else{
            BigDecimal averageAmountRationBg = offlineAverageAmount.subtract(lastweekOfflineAverageAmount).multiply(BigDecimal.valueOf(100)).divide(lastweekOfflineAverageAmount, 2, RoundingMode.HALF_UP);
            return averageAmountRationBg.toString() + "%";
        }
    }
}
