package com.pinshang.qingyun.report.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class UnsalableODTO {
    @ExcelIgnore
    private Long shopId;

    @ExcelProperty("门店编码")
    @ApiModelProperty("门店编码")
    private String shopCode;

    @ExcelProperty("门店名称")
    @ApiModelProperty("门店名称")
    private String shopName;

    @ExcelIgnore
    private Long commodityId;

    @ExcelProperty("商品编码")
    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    @ApiModelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("条码")
    @ApiModelProperty("条形码")
    private String barCode;

    @ExcelIgnore
    @ApiModelProperty("主副条码集合")
    private List<String> barCodeList;

    @ExcelProperty("规格")
    @ApiModelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("计量单位")
    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ExcelIgnore
    @ApiModelProperty("是否称重 0 否  1 是")
    private Integer isWeight;

    @ExcelProperty("是否称重")
    @ApiModelProperty("是否称重 0 否  1 是")
    private String isWeightStr;

    @ExcelProperty("库存数量")
    @ApiModelProperty("库存数量")
    private BigDecimal stockQuantity;

    @ExcelProperty("库存份数")
    @ApiModelProperty("库存份数")
    private BigDecimal stockNum;

    @ExcelProperty("一级类目")
    @ApiModelProperty("大类名称")
    private String commodityFirstName;

    @ExcelProperty("二级类目")
    @ApiModelProperty("中类名称")
    private String commoditySecondName;

    @ExcelProperty("三级类目")
    @ApiModelProperty("小类名称")
    private String commodityThirdName;

    public String getIsWeightStr() {
        return null != this.isWeight && 1 == this.isWeight ? "是" : "否";
    }
}
