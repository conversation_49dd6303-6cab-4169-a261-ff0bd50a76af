package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName HelpCardCheckDetail4InvoiceIDTO
 * <AUTHOR>
 * @Date 2023/3/9 16:35
 * @Description HelpCardCheckDetail4InvoiceIDTO
 * @Version 1.0
 */
@Data
public class HelpCardCheckDetail4InvoiceIDTO extends Pagination {

    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("t_pos_help_card_account_check.id")
    private List<Long> idList;
    @ApiModelProperty("排除 t_pos_help_card_account_check.id")
    private List<Long> notIdList;

    @ApiModelProperty("区域id")
    private List<Long> areaIdList;

    private Long companyId;


}
