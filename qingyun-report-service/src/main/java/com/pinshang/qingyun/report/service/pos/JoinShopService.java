package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.report.client.report.service.PosAdminClient;
import com.pinshang.qingyun.report.dto.pos.GuestListSummaryReportOrderODTO;
import com.pinshang.qingyun.report.dto.pos.SummaryAmountDTO;
import com.pinshang.qingyun.report.dto.pos.SummaryAmountIDTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.joinShop.*;
import com.pinshang.qingyun.report.mapper.pos.CashierWaterReportMapper;
import com.pinshang.qingyun.report.mapper.pos.SalesSummaryReportMapper;
import com.pinshang.qingyun.report.mapper.pos.SalesWaterReportMapper;
import com.pinshang.qingyun.report.model.pos.ReportSalesWater;
import com.pinshang.qingyun.report.model.pos.SalesSummaryReport;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.shop.dto.report.CommodityTaxReportODto;
import com.pinshang.qingyun.shop.dto.shop.CommodityTaxInfoByShopIDTO;
import com.pinshang.qingyun.shop.service.ShopReportClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class JoinShopService {

    @Autowired
    private CashierWaterReportMapper cashierWaterReportMapper;

    @Autowired
    private SalesSummaryReportMapper salesSummaryReportMapper;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private SalesWaterReportMapper salesWaterReportMapper;

    @Autowired
    private PosAdminClient posAdminClient;

    @Autowired
    private ShopReportClient shopReportClient;

    public List<SummaryAmountDTO> withDrawAmountList(SummaryAmountIDTO dto) {
        QYAssert.isTrue(null != dto.getShopIds(), "店铺id不能为空");
        QYAssert.isTrue(null != dto.getDate(), "日期不能为空");
        List<PayTypeListDTO> payTypeListDTOS = posAdminClient.withDrawRateList().stream().filter(e -> YesOrNoEnums.YES.getCode().equals(e.getWithdrawStatus())).collect(Collectors.toList());

        List<SummaryAmountDTO> res = new ArrayList<>();

        if (SpringUtil.isNotEmpty(payTypeListDTOS)) {
            //判断日期是不是当天
            Boolean currentDay = DateUtil.isToday(DateUtil.parseDate(dto.getDate(), "yyyy-MM-dd"));
            dto.setCurrentDay(currentDay);

            dto.setStartTime(dto.getDate() + " 00:00:00");
            dto.setEndTime(dto.getDate() + " 23:59:59");
            dto.setPayTypes(payTypeListDTOS.stream().map(PayTypeListDTO::getPayCode).collect(Collectors.toList()));

            List<SummaryAmountDTO> list = cashierWaterReportMapper.getSummaryAmount(dto);
            if (SpringUtil.isNotEmpty(list)) {
                Map<String, BigDecimal> payTypeMap = payTypeListDTOS.stream().collect(Collectors.toMap(PayTypeListDTO::getPayCode, PayTypeListDTO::getWithdrawRate));
                Map<Long, List<SummaryAmountDTO>> summaryAmountByShopId = list.stream().collect(Collectors.groupingBy(SummaryAmountDTO::getShopId));
                for (Long shopId : summaryAmountByShopId.keySet()) {
                    SummaryAmountDTO summaryAmountDTO = new SummaryAmountDTO();
                    summaryAmountDTO.setShopId(shopId);
                    BigDecimal totalAmount = BigDecimal.ZERO;
                    List<SummaryAmountDTO> summaryAmountDTOS = summaryAmountByShopId.get(shopId);
                    for (SummaryAmountDTO summaryAmountDTO1 : summaryAmountDTOS) {
                        if (payTypeMap.containsKey(summaryAmountDTO1.getPayType())) {
                            BigDecimal amount = summaryAmountDTO1.getAmount().subtract( summaryAmountDTO1.getAmount().multiply( payTypeMap.get(summaryAmountDTO1.getPayType()) ));
                            totalAmount = totalAmount.add(amount);
                        }
                    }
                    summaryAmountDTO.setAmount(totalAmount);
                    res.add(summaryAmountDTO);
                }
            }
        }

        return res;
    }

    /**
     * 钱大妈  单品实时销售
     * @param dto
     * @return
     */
    public TablePageInfo<SingleProductSalesDTO> singleProductSalesPage(SingleProductSalesIDTO dto) {
        QYAssert.isTrue(null != dto.getShopId(), "门店不能为空");
        QYAssert.isTrue(null != dto.getDate(), "日期不能为空");
        QYAssert.isTrue(null != dto.getEndTime() && null != dto.getStartTime(), "时间段不能为空");

        //判断日期是不是当天
//        Boolean currentDay = DateUtil.isToday(DateUtil.parseDate(dto.getDate(), "yyyy-MM-dd"));
//        dto.setCurrentDay(currentDay);

        dto.setStartTime(dto.getDate() + " " + dto.getStartTime());
        dto.setEndTime(dto.getDate() + " " + dto.getEndTime());

        PageInfo<SingleProductSalesDTO> pageDate = PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            salesSummaryReportMapper.singleProductSalesList(dto);
        });
        SingleProductSalesDTO singleProductSalesDTO = null;
        if (pageDate.getSize() > 0) {
            List<Long> commodityIds = pageDate.getList().stream().map(SingleProductSalesDTO::getCommodityId).collect(Collectors.toList());
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIds);
            for (SingleProductSalesDTO sales : pageDate.getList()) {
                //销售数量 = 销售数量合计 + 赠品数量合计 - 退货数量合计
                sales.setSaleQuantity(sales.getSumSaleQuantity().add(sales.getSumGiveQuantity()).subtract(sales.getSumReturnQuantity()).setScale(2, RoundingMode.HALF_UP));
                //销售额 = 销售成交金额合计 - 退货金额合计
                sales.setSaleAmount(sales.getSumSaleAmount().subtract(sales.getSumReturnAmount()).setScale(2, RoundingMode.HALF_UP));
                //销售原价金额 = 销售的原价金额合计-退货的原价金额合计
                sales.setCommodityPriceAmount(sales.getSumCommodityPrice().add(sales.getSumGiveCommodityPrice()).subtract(sales.getSumReturnCommodityPrice()).setScale(2, RoundingMode.HALF_UP));
                //折扣额 = 原价金额合计-成交金额合计
                sales.setDiscountAmount(sales.getCommodityPriceAmount().subtract(sales.getSaleAmount()));
                //折扣额/原价金额
                if (sales.getCommodityPriceAmount().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal discountRate = sales.getDiscountAmount().divide(sales.getCommodityPriceAmount(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                    sales.setDiscountRate(discountRate+"%");
                } else {
                    sales.setDiscountRate("0");
                }


                String barCodes = barCodeMap.get(sales.getCommodityId());
                sales.setBarCodeList(barCodes);
                sales.setBarCode(barCodes != null ? barCodes.split(",")[0]:"");

            }


            singleProductSalesDTO = salesSummaryReportMapper.singleProductSalesSum(dto);
            if (null != singleProductSalesDTO) {
                singleProductSalesDTO.setCommodityPriceAmount(singleProductSalesDTO.getCommodityPriceAmount().setScale(2, RoundingMode.HALF_UP));
            }
        }

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(singleProductSalesDTO);
        return tablePageInfo;
    }

    /**
     * 综合分析，支持多个门店，只能是一天
     */
//    public List<AnalysisBySynthesisDTO> analysisBySynthesis(AnalysisBySynthesisIDTO dto) {
//
//        QYAssert.isTrue(null != dto.getShopIds(), "门店不能为空");
//        QYAssert.isTrue(null != dto.getDate(), "日期不能为空");
//
//
//        dto.setStartTime(dto.getDate() + " 00:00:00");
//        dto.setEndTime(dto.getDate() + " 23:59:59");
//
//        //查询订单那边的信息
//        CommodityTaxInfoByShopIDTO idto = new CommodityTaxInfoByShopIDTO();
//        idto.setBeginTime(dto.getStartTime());
//        idto.setEndTime(dto.getEndTime());
//        idto.setShopIdList(dto.getShopIds());
//        List<CommodityTaxReportODto> orderList  = shopReportClient.getCommodityTaxByDayForJm(idto);
//        Map<Long, CommodityTaxReportODto> orderMap = orderList.stream().collect(Collectors.toMap(CommodityTaxReportODto::getShopId, e -> e));
//
//        List<AnalysisBySynthesisDTO> res = new ArrayList<>();
//
//        //销售流水列表
//        List<ReportSalesWater> salesWaterList = salesWaterReportMapper.salesWaterList(dto);
//        if (SpringUtil.isNotEmpty(salesWaterList)) {
//            Map<Long, List<ReportSalesWater>> mapByShopId = salesWaterList.stream().collect(Collectors.groupingBy(ReportSalesWater::getShopId));
//            Date date19 = DateUtil.parseDate(dto.getDate()+" 19:00:00", "yyyy-MM-dd HH:mm:ss");
//            for (Long shopId : mapByShopId.keySet()) {
//
//                CommodityTaxReportODto commodityTaxReportODto = orderMap.get(shopId);
//
//                AnalysisBySynthesisDTO analysisBySynthesisDTO = new AnalysisBySynthesisDTO();
//                analysisBySynthesisDTO.setShopId(shopId);
//                List<ReportSalesWater> salesWaterListByShopId = mapByShopId.get(shopId);
//                //营业额 = 销售成交金额合计-退货金额合计
//                BigDecimal totalAmount = salesWaterListByShopId.stream().map(ReportSalesWater::getTransactionAmount).reduce((sum, o) -> sum.add(o)).get();
//                analysisBySynthesisDTO.setTurnover(totalAmount);
//                //客单量
//                Long totalCustomers =  salesWaterListByShopId.stream().map(ReportSalesWater::getOrderCode).distinct().count();
//                analysisBySynthesisDTO.setTotalCustomers(totalCustomers);
//
//                List<ReportSalesWater> salesWaterListBefore19 = salesWaterList.stream().filter(salesWater -> salesWater.getSaleTime().compareTo(date19) < 0).collect(Collectors.toList());
//                BigDecimal salesBefore19 = salesWaterListBefore19.stream().map(ReportSalesWater::getTransactionAmount).reduce((sum, o) -> sum.add(o)).get();
//                //19点前的销售额 = 销售额合计-退货金额合计
//                analysisBySynthesisDTO.setSalesBefore19(salesBefore19);
//
//                //19点前的来客数
//                Long customersBefore19 = salesWaterListBefore19.stream().map(ReportSalesWater::getOrderCode).distinct().count();
//                analysisBySynthesisDTO.setCustomersBefore19(customersBefore19);
//
//
//
//                //时段折扣率 = 折扣金额合计/原价金额合计  折扣金额合计=原价金额合计-销售额合计
//                BigDecimal discountRate = new BigDecimal("0.00");
//                //原价金额合计 = （销售数量合计+赠品数量合计）*零售价 - 退货的原价金额合计
//                BigDecimal retailAmount = salesWaterListByShopId.stream().map(ReportSalesWater::getRetailAmount).reduce((sum, o) -> sum.add(o)).get();
//                if (retailAmount.compareTo(BigDecimal.ZERO) > 0) {
//                    Integer timeFrame = getTimeFrame(salesWaterListByShopId.get(0).getSaleTime(), salesWaterListByShopId.get(salesWaterListByShopId.size() - 1).getSaleTime());
//
//                    discountRate = retailAmount.subtract(analysisBySynthesisDTO.getTurnover()).divide(retailAmount, 4, RoundingMode.HALF_UP).divide(new BigDecimal(timeFrame), 4, RoundingMode.HALF_UP);
//                }
//                analysisBySynthesisDTO.setDiscountRate(discountRate);
//
//                if (null != commodityTaxReportODto) {
//                    //货款  saleReturnOrderTotal退货和少货在一起
//                    BigDecimal goodsPayment = commodityTaxReportODto.getOrderTotal().subtract(commodityTaxReportODto.getSaleReturnOrderTotal());
//                    analysisBySynthesisDTO.setGoodsPayment(goodsPayment);
//
//                    //损耗 （实收数量-退货数量-少货数量-销售数量）/（实收数量-退货数量-少货数量）
//                    BigDecimal lossRate = BigDecimal.ZERO;
//                    BigDecimal saleQuantity = salesWaterListByShopId.stream().map(ReportSalesWater::getQuantity).reduce((sum, o) -> sum.add(o)).get();
//                    BigDecimal orderTotal = commodityTaxReportODto.getOrderTotal().subtract(commodityTaxReportODto.getSaleReturnOrderTotal());
//                    if (orderTotal.compareTo(BigDecimal.ZERO) != 0) {
//                        lossRate = orderTotal.subtract(saleQuantity).divide(orderTotal, 4, RoundingMode.HALF_UP);
//                    }
//                    analysisBySynthesisDTO.setLossRate(lossRate);
//
//                    //加盟商净毛利（总销售额-总进货-手续费）
//                    BigDecimal serviceChange = getServiceChange(shopId, dto.getDate());
//                    BigDecimal franchiseeNetProfit = totalAmount.subtract(goodsPayment).subtract(serviceChange);
//                    analysisBySynthesisDTO.setFranchiseeNetProfit(franchiseeNetProfit);
//                } else {
//                    log.warn("{}=没有获取到订货信息", shopId);
//                }
//
//                res.add(analysisBySynthesisDTO);
//            }
//        }
//        return res;
//    }

    public List<AnalysisBySynthesisDTO> analysisBySynthesis(AnalysisBySynthesisIDTO dto) {
        QYAssert.isTrue(null != dto.getShopIds(), "门店不能为空");
        QYAssert.isTrue(null != dto.getDate(), "日期不能为空");


        dto.setStartTime(dto.getDate() + " 00:00:00");
        dto.setEndTime(dto.getDate() + " 23:59:59");

        //判断日期是不是当天
//        Boolean currentDay = DateUtil.isToday(DateUtil.parseDate(dto.getDate(), "yyyy-MM-dd"));
//        dto.setCurrentDay(currentDay);

        //查询订单那边的信息
        CommodityTaxInfoByShopIDTO idto = new CommodityTaxInfoByShopIDTO();
        idto.setBeginTime(dto.getStartTime());
        idto.setEndTime(dto.getEndTime());
        idto.setShopIdList(dto.getShopIds());
        List<CommodityTaxReportODto> orderList  = shopReportClient.getCommodityTaxByDayForJm(idto);
        log.warn("日结的时候跑综合报表数据={}", orderList);
        Map<Long, CommodityTaxReportODto> orderMap = orderList.stream().collect(Collectors.toMap(CommodityTaxReportODto::getShopId, e -> e));

        List<AnalysisBySynthesisDTO> res = new ArrayList<>();

        List<SalesSummaryReport> salesSummaryReportList = salesSummaryReportMapper.salesSummaryList(dto);

        if (SpringUtil.isNotEmpty(salesSummaryReportList)) {
            Map<Long, List<SalesSummaryReport>> salesSummaryReportMap = salesSummaryReportList.stream().collect(Collectors.groupingBy(SalesSummaryReport::getShopId));
            Date date19 = DateUtil.parseDate(dto.getDate()+" 19:00:00", "yyyy-MM-dd HH:mm:ss");

            for (Long shopId : salesSummaryReportMap.keySet()) {
                CommodityTaxReportODto commodityTaxReportODto = orderMap.get(shopId);

                AnalysisBySynthesisDTO analysisBySynthesisDTO = new AnalysisBySynthesisDTO();
                analysisBySynthesisDTO.setShopId(shopId);

                //营业额 = 销售成交金额合计-退货金额合计
                List<SalesSummaryReport> salesSummaryList = salesSummaryReportMap.get(shopId);
                BigDecimal salesAmount = salesSummaryList.stream().filter(e -> null != e.getTatalAmount()).map(SalesSummaryReport::getTatalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                analysisBySynthesisDTO.setTurnover(salesAmount);

                //客单量
                Long totalCustomers = salesSummaryList.stream().map(SalesSummaryReport::getOrderCode).distinct().count();
                analysisBySynthesisDTO.setTotalCustomers(totalCustomers);

                //
                List<SalesSummaryReport> salesSummaryListBefore19 = salesSummaryList.stream().filter(e -> e.getCreateTime().compareTo(date19) < 0).collect(Collectors.toList());
                //截止到19点前的销售额 = 销售额合计-退货金额合计
                BigDecimal salesBefore19 = salesSummaryListBefore19.stream().filter(e -> null != e.getTatalAmount()).map(SalesSummaryReport::getTatalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                analysisBySynthesisDTO.setSalesBefore19(salesBefore19);

                //截止到19点前的来客数
                Long customersBefore19 = salesSummaryListBefore19.stream().map(SalesSummaryReport::getOrderCode).distinct().count();
                analysisBySynthesisDTO.setCustomersBefore19(customersBefore19);

                //时段折扣率 = 折扣金额合计/原价金额合计  折扣金额合计=原价金额合计-销售额合计
                BigDecimal discountRate = new BigDecimal("0.00");
                //销售的原价金额
                BigDecimal saleCommodityPrice = salesSummaryList.stream().map(e -> e.getCommodityPrice().multiply(e.getSaleQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal giveCommodityPrice = salesSummaryList.stream().filter(e -> null != e.getGiveQuantity()).map(e -> e.getGiveQuantity().multiply(e.getCommodityPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal returnCommodityPrice = salesSummaryList.stream().filter(e -> null != e.getReturnQuantity()).map(e -> e.getReturnQuantity().multiply(e.getCommodityPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
                //原价金额合计 = 【（销售数量+赠品数量）*零售价】  - 退货金额合计
                BigDecimal commodityPrice = saleCommodityPrice.add(giveCommodityPrice).subtract(returnCommodityPrice);
                if (commodityPrice.compareTo(BigDecimal.ZERO) != 0) {
                    Integer timeFrame = getTimeFrame(salesSummaryList.get(0).getCreateTime(), salesSummaryList.get(salesSummaryList.size() - 1).getCreateTime());
                    discountRate = commodityPrice.subtract(salesAmount).divide(commodityPrice, 4, RoundingMode.HALF_UP).divide(new BigDecimal(timeFrame) , 4, RoundingMode.HALF_UP);
                }
                analysisBySynthesisDTO.setDiscountRate(discountRate);

                if (null != commodityTaxReportODto) {
                    //货款  saleReturnOrderTotal退货和少货在一起
                    BigDecimal goodsPayment = commodityTaxReportODto.getOrderTotal().subtract(commodityTaxReportODto.getSaleReturnOrderTotal());
                    analysisBySynthesisDTO.setGoodsPayment(goodsPayment);

                    //损耗 （实收数量-退货数量-少货数量-销售数量）/（实收数量-退货数量-少货数量）
                    BigDecimal lossRate = BigDecimal.ZERO;
                    BigDecimal saleQuantity = salesSummaryList.stream().map(SalesSummaryReport::getTatalQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderTotal = commodityTaxReportODto.getOrderQuanty().subtract(commodityTaxReportODto.getSaleReturnOrderQuantity());
                    if (orderTotal.compareTo(BigDecimal.ZERO) != 0) {
                        lossRate = orderTotal.subtract(saleQuantity).divide(orderTotal, 4, RoundingMode.HALF_UP);
                    }
                    analysisBySynthesisDTO.setLossRate(lossRate);

                    //加盟商净毛利（总销售额-总进货-手续费）
                    BigDecimal serviceChange = getServiceChange(shopId, dto.getDate());
                    BigDecimal franchiseeNetProfit = salesAmount.subtract(goodsPayment).subtract(serviceChange);
                    analysisBySynthesisDTO.setFranchiseeNetProfit(franchiseeNetProfit);

                }
                res.add(analysisBySynthesisDTO);
            }
        }
        return res;
    }

    public BigDecimal getServiceChange(Long shopId, String date) {
        SummaryAmountIDTO dto = new SummaryAmountIDTO();
        dto.setShopIds(Arrays.asList(shopId));
        dto.setStartTime(date + " 00:00:00");
        dto.setEndTime(date + " 23:59:59");

        List<PayTypeListDTO> payTypeListDTOS = posAdminClient.withDrawRateList();
        payTypeListDTOS = payTypeListDTOS.stream().filter(e -> YesOrNoEnums.YES.getCode().equals(e.getWithdrawStatus())).collect(Collectors.toList());
        List<String> payCodes = payTypeListDTOS.stream().map(PayTypeListDTO::getPayCode).collect(Collectors.toList());
        dto.setPayTypes(payCodes);

        Boolean currentDay = DateUtil.isToday(DateUtil.parseDate(date, "yyyy-MM-dd"));
        dto.setCurrentDay(currentDay);

        List<SummaryAmountDTO> summaryAmountDTOS = cashierWaterReportMapper.getSummaryAmount(dto);
        //手续费
        BigDecimal serviceChange = BigDecimal.ZERO;
        if (SpringUtil.isNotEmpty(summaryAmountDTOS)) {
            Map<String, BigDecimal> payTypeMap = payTypeListDTOS.stream().collect(Collectors.toMap(PayTypeListDTO::getPayCode, PayTypeListDTO::getWithdrawRate));
            for (SummaryAmountDTO summaryAmountDTO : summaryAmountDTOS) {
                if (payTypeMap.containsKey(summaryAmountDTO.getPayType())) {
                    serviceChange = serviceChange.add(summaryAmountDTO.getAmount().multiply(payTypeMap.get(summaryAmountDTO.getPayType())));
                }
            }
        }

        return serviceChange.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 时段
     * 17:35 - 20:00 时段是2   19点后不计算
     * 8:10 -  12:20 时段是5
     * @param startTime
     * @param endTime
     * @return
     */
    public Integer getTimeFrame(Date startTime, Date endTime) {
        Integer startHour = DateUtil.getPartOfTime(startTime,"hour");
        Integer endHour = DateUtil.getPartOfTime(endTime,"hour");
        Integer endMinute = DateUtil.getPartOfTime(endTime,"minute");
        if (endMinute > 0 && endHour < 19) {
            endHour = endHour + 1;
        }
        if (endHour >= 19) {
            endHour = 19;
        }
        return endHour - startHour;
    }
}
