package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.model.ClearingChargesItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Repository
public interface ClearingChargesItemMapper extends MyMapper<ClearingChargesItem> {
    ClearingChargesItem getHeaderSum(Long id);

    List<ReconciliationOfRurnoverODTO> reconciliationOfRurnover(@Param("idto")ClearingChargesItemIDTO idto);

    List<ReconciliationStatementODTO> reconciliationStatement(@Param("idto")ClearingChargesItemIDTO idto);

    ReconciliationOfRurnoverODTO  reconciliationOfRurnoverHearder(@Param("idto")ClearingChargesItemIDTO idto);

    BigDecimal selectPaymentSumByType(@Param("types") List<String> types,@Param("payDate") Date payDate,@Param("shopId")Long shopId);

    List<ShopActualSummaryODTO> shopActualSummary(@Param("idto") ShopActualSummaryIDTO idto);

    ShopActualSummaryODTO shopActualSummaryHeader(@Param("idto") ShopActualSummaryIDTO idto);
}
