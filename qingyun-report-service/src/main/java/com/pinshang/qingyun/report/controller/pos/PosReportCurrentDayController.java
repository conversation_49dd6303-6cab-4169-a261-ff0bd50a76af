package com.pinshang.qingyun.report.controller.pos;

import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.enums.PosReportSummaryTypeEnum;
import com.pinshang.qingyun.report.enums.PosReportWithShopEnum;
import com.pinshang.qingyun.report.service.pos.PosReportCurrentDayService;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: sk
 * @Date: 2021/4/27
 */
@Slf4j
@RestController
@RequestMapping("/posReportsCurrent")
public class PosReportCurrentDayController {

    @Autowired
    private PosReportCurrentDayService posReportCurrentDayService;

    @Autowired
    private IRenderService renderService;

    @ApiOperation(value = "商品销售汇总报表、类别销售汇总报表", notes = "商品销售汇总报表、类别销售汇总报表")
    @RequestMapping(value = "/commoditySalesSummaryReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @MethodRender
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return posReportCurrentDayService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,0);
    }

    /**
     * 注意：报表查询今日数据和导出，走这个接口
     * @param salesSummaryReportOrderIDTO
     * @return
     */
    @ApiOperation(value = "门店销售汇总分析报表，类别销售汇总分析报表，商品销售汇总分析报表", notes = "门店销售汇总分析报表，类别销售汇总分析报表，商品销售汇总分析报表")
    @RequestMapping(value = "/commoditySalesSummary2Report", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @MethodRender
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummary2Report(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        return posReportCurrentDayService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,1);
    }

    /**
     * 档口商品销售分析及档口销售分析
     * @param salesSummaryReportOrderIDTO
     * @return
     */
    @ApiOperation(value = "门店销售汇总分析报表，类别销售汇总分析报表，商品销售汇总分析报表，档口销售分析报表", notes = "门店销售汇总分析报表，类别销售汇总分析报表，商品销售汇总分析报表，档口销售分析报表")
    @RequestMapping(value = "/stallSalesSummary2Report", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @MethodRender
    public TablePageInfo<SalesSummaryReportOrderODTO> stallSalesSummary2Report(SalesSummaryReportOrderIDTO salesSummaryReportOrderIDTO){
        salesSummaryReportOrderIDTO.setQueryType(1);
        return posReportCurrentDayService.commoditySalesSummaryReport(salesSummaryReportOrderIDTO,1);
    }

    @ApiOperation(value = "商品销售汇总，类别销售汇总报表导出", notes = "商品销售汇总，类别销售汇总报表导出")
    @RequestMapping(value = "/exportInfo/commoditySalesSummaryReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_T0SR_COMMODITY")
    public void commoditySalesSummaryReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        Integer summaryType = idto.getSummaryType();
        Integer isWithShop = idto.getIsWithShop();

        TablePageInfo<SalesSummaryReportOrderODTO> result = posReportCurrentDayService.commoditySalesSummaryReport(idto,0);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            renderService.render(list, "/exportInfo/commoditySalesSummaryReport");
            //设置总行
            setCommodityTotalRow(summaryType, isWithShop, result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setDataLst(summaryType, isWithShop, dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode() || summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            fileName="商品销售汇总报表";
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            fileName="类别销售汇总报表";
        }
        ExcelSheetTitleEnum sheetTitle = getExcelSheetTitle(summaryType, isWithShop);

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        fileName = String.format("%s_%s", fileName, new DateTime().toString("yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

    }

    @ApiOperation(value = "档口商品销售分析及档口销售分析报表，档口销售分析报表导出", notes = "商品销售汇总，类别销售汇总报表，档口销售分析报表导出")
    @RequestMapping(value = "/exportInfo/stallSalesSummary2Report", method = RequestMethod.GET)
    public ModelAndView stallSalesSummary2ReportExport(@RequestParam(value="shopId", required=false) Long shopId, @RequestParam(value="beginDate", required=false) String beginDate, @RequestParam(value="endDate", required=false) String endDate,
                                                       @RequestParam(value="cateId1", required=false) Long cateId1, @RequestParam(value="cateId2", required=false) Long cateId2, @RequestParam(value="cateId3", required=false) Long cateId3,
                                                       @RequestParam(value="commodityKey", required=false) String commodityKey, @RequestParam(value="barCode", required=false) String barCode,
                                                       @RequestParam(value="summaryType", required=false) Integer summaryType, @RequestParam(value="stallId", required=false) Long stallId, @RequestParam(value="commodityId", required=false) Long commodityId,
                                                       @RequestParam(value="orgCode", required=false) String orgCode, @RequestParam(value = "consignmentId", required = false) Long consignmentId,
                                                       @RequestParam(value="shopType", required=false) Integer shopType, @RequestParam(value="provinceId", required=false) Long provinceId,
                                                       @RequestParam(value="shopTypeList", required=false) List<Integer> shopTypeList,
                                                       @RequestParam(value="managementModeList", required=false) List<Integer> managementModeList){
        SalesSummaryReportOrderIDTO idto=new SalesSummaryReportOrderIDTO();
        //设置参数
        setSalesSummaryParam(shopId, beginDate, endDate, cateId1, cateId2, cateId3, commodityKey, barCode, summaryType, null,null, commodityId, orgCode,shopType, null, provinceId,shopTypeList, managementModeList,idto);
        if (null != consignmentId) {
            idto.setConsignmentId(consignmentId);
        }
        if (null != stallId) {
            idto.setStallId(stallId);
        }
        idto.setQueryType(1);
        TablePageInfo<SalesSummaryReportOrderODTO> result = posReportCurrentDayService.commoditySalesSummaryReport(idto,1);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst;
        int i = 1;
        if(null !=list && !list.isEmpty()){
            renderService.render(list, "/exportInfo/stallSalesSummary2Report");
            idto.setSummaryType(summaryType);
            //设置总行
            setStallTotalRow(idto.getSummaryType(), result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setStallDataList(idto.getSummaryType(), dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        ExcelSheetTitleEnum sheetTitle = null;
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode()){
            fileName="档口商品销售分析报表";
            sheetTitle = ExcelSheetTitleEnum.STALL_COMMODITY_SALES_SUMMARY;
        }
        if(idto.getSummaryType() ==PosReportSummaryTypeEnum.SIX.getCode()){
            fileName="档口销售分析报表";
            sheetTitle = ExcelSheetTitleEnum.STALL_SHOP_SALES_SUMMARY;
        }
        if(idto.getSummaryType() ==PosReportSummaryTypeEnum.ELEVEN.getCode()){
            fileName="档口销售分析报表";
            sheetTitle = ExcelSheetTitleEnum.STALL_SHOP_SALES_SUMMARY;
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }


    public void commoditySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        Integer summaryType = idto.getSummaryType();
        Integer isWithShop = idto.getIsWithShop();

        TablePageInfo<SalesSummaryReportOrderODTO> result = posReportCurrentDayService.commoditySalesSummaryReport(idto,1);
        List<SalesSummaryReportOrderODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;
        if(null !=list && !list.isEmpty()){
            renderService.render(list, "/exportInfo/commoditySalesSummaryAnalysisReport");
            //设置总行
            setCommodityAnalysisTotalRow(summaryType, isWithShop, result, data);
            for(SalesSummaryReportOrderODTO dto:list){
                dataLst = new ArrayList<>();
                setDataLstAnalysis(summaryType, isWithShop, dataLst, dto);
                data.put("key_"+ i++, dataLst);
            }
        }
        String fileName="";
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            fileName="商品销售汇总分析报表";
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            fileName="类别销售汇总分析报表";
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode() || summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            fileName="门店销售汇总分析报表";
        }
        ExcelSheetTitleEnum sheetTitle = getAnalysisExcelSheetTitle(summaryType, isWithShop);

        //兼容旧逻辑
        List<List<String>> excelData = ViewExcel.easyExcelDataAdapter(data);
        List<List<String>> excelHead = ExcelSheetTitleEnum.getExcelHead(sheetTitle);

        fileName = String.format("%s_%s", fileName, new DateTime().toString("yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        String sheetName = sheetTitle.getName();
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet(sheetName).doWrite(excelData);

    }

    @ApiOperation(value = "商品销售汇总分析  今日报表导出", notes = "门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  报表导出")
    @RequestMapping(value = "/exportInfo/commoditySalesSummaryAnalysisReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_T0SR_COMMODITY_SUMMARY")
    public void exportExcelCommoditySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {
        /* 暂不加限制, 为兼容前端旧版本
        QYAssert.isTrue(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType ==PosReportSummaryTypeEnum.TWO.getCode(),
                "商品销售汇总, 汇总类型不存在");
        */
        commoditySalesSummaryAnalysisReport(idto, response);
    }

    @ApiOperation(value = "类别销售汇总分析  今日报表导出", notes = "门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  报表导出")
    @RequestMapping(value = "/exportInfo/category/commoditySalesSummaryAnalysisReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_T0SR_CATEGORY_SUMMARY")
    public void exportExcelCategorySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(idto.getSummaryType() ==PosReportSummaryTypeEnum.THREE.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FOUR.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.FIVE.getCode(),
                "类别销售汇总, 汇总类型不存在");
        commoditySalesSummaryAnalysisReport(idto, response);
    }

    @ApiOperation(value = "门店销售汇总分析 今日报表导出", notes = "")
    @RequestMapping(value = "/exportInfo/shop/commoditySalesSummaryAnalysisReport", method = RequestMethod.POST)
    @FileCacheQuery(bizCode = "POS_T0SR_SHOP_SUMMARY")
    public void shopCommoditySalesSummaryAnalysisReport(@RequestBody SalesSummaryReportOrderIDTO idto, HttpServletResponse response) throws IOException {

        QYAssert.isTrue(idto.getSummaryType() ==PosReportSummaryTypeEnum.SIX.getCode() || idto.getSummaryType() ==PosReportSummaryTypeEnum.SEVEN.getCode(),
                "门店销售汇总, 汇总类型不存在");
        commoditySalesSummaryAnalysisReport(idto, response);
    }

    private void setDataLstAnalysis(Integer summaryType,Integer isWithShop, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            dataLst.add(dto.getSaleTime());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()  || summaryType==PosReportSummaryTypeEnum.SIX.getCode() || summaryType==PosReportSummaryTypeEnum.SEVEN.getCode() || ( null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
        }

        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
        }else{
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
        }
        dataLst.add(dto.getWeightAmount()+"");
        dataLst.add(dto.getGrossprofitmarginAmount()+"");
        dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
        dataLst.add(dto.getNoTaxSaleAmount()+"");
        dataLst.add(dto.getNoTaxWeightAmount()+"");
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getSalesAmountPercent());
        }
    }

    //门店销售汇总分析，类别销售汇总分析，商品销售汇总分析  导出合计行设置
    private void setCommodityAnalysisTotalRow(Integer summaryType,Integer isWithShop, TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(12,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,15);
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(9,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,13);
        }

        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()  && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(2,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(3,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(6,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()   && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(3,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,9);
        }
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,10);
        }
    }

    private void setDataLst(Integer summaryType,Integer isWithShop, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode() || summaryType==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
            dataLst.add(dto.getCommodityPrice()+"");
            dataLst.add(dto.getSaleQuantity()+"");
            dataLst.add(dto.getReturnQuantity()+"");
            dataLst.add(dto.getGiveQuantity()+"");
            dataLst.add(dto.getSaleAmount()+"");
            dataLst.add(dto.getReturnAmount()+"");
            dataLst.add(dto.getGiveAmount()+"");
            dataLst.add(dto.getDiscountAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");

        }
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            dataLst.add(dto.getTaxRate()+"");
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() || summaryType==PosReportSummaryTypeEnum.FOUR.getCode() || summaryType==PosReportSummaryTypeEnum.FIVE.getCode()){
            dataLst.add(dto.getSaleQuantity()+"");
            dataLst.add(dto.getSaleAmount()+"");
            dataLst.add(dto.getReturnQuantity()+"");
            dataLst.add(dto.getReturnAmount()+"");
            dataLst.add(dto.getGiveQuantity()+"");
            dataLst.add(dto.getDiscountAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getNoTaxRateAmount()+"");
            dataLst.add(dto.getTaxRateAmount()+"");
            dataLst.add(dto.getSalesAmountPercent());
        }
    }

    private ExcelSheetTitleEnum getAnalysisExcelSheetTitle(Integer summaryType,Integer isWithShop) {
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()  && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null != isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_ANALYSIS_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode()){
            sheetTitle=ExcelSheetTitleEnum.SHOP_SALES_SUMMARY_ANALYSIS_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.SEVEN.getCode()){
            sheetTitle=ExcelSheetTitleEnum.SHOPDAY_SUMMARY_ANALYSIS_NOSHOP;
        }
        return sheetTitle;
    }

    private ExcelSheetTitleEnum getExcelSheetTitle(Integer summaryType,Integer isWithShop) {
        ExcelSheetTitleEnum sheetTitle=null;
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            sheetTitle=ExcelSheetTitleEnum.COMMODITY_SALES_SUMMARY_NOSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_FIRST_SALES_SUMMARY_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_SECOND_SALES_SUMMARY_NOSHOP;
        }

        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_WITHSHOP;
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            sheetTitle=ExcelSheetTitleEnum.CATE_THIRD_SALES_SUMMARY_NOSHOP;
        }
        return sheetTitle;
    }

    private void setSalesSummaryParam(Long shopId,String beginDate,String endDate,Long cateId1, Long cateId2,Long cateId3,String commodityKey,String barCode,Integer summaryType,Integer isWithShop, Integer isSortByAmount,Long commodityId,String orgCode,Integer shopType,Integer managementMode,Long provinceId, List<Integer> shopTypeList, List<Integer> managementModeList,SalesSummaryReportOrderIDTO idto) {
        if(null!=shopId) {
            idto.setShopId(shopId);
        }
        if (!StringUtils.isEmpty(orgCode)) {
            idto.setOrgCode(orgCode);
        }
        if(!StringUtils.isEmpty(beginDate)) {
            idto.setBeginDate(beginDate);
        }
        if(!StringUtils.isEmpty(endDate)) {
            idto.setEndDate(endDate);
        }
        if(null !=cateId1) {
            idto.setCateId1(cateId1);
        }
        if(null !=cateId2) {
            idto.setCateId2(cateId2);
        }
        if(null !=cateId3) {
            idto.setCateId3(cateId3);
        }
        if(!StringUtils.isEmpty(commodityKey)) {
            idto.setCommodityKey(commodityKey);
        }
        if(!StringUtils.isEmpty(barCode)) {
            idto.setBarCode(barCode);
        }
        if(null !=summaryType) {
            idto.setSummaryType(summaryType);
        }
        if(null !=isWithShop) {
            idto.setIsWithShop(isWithShop);
        }
        if(null !=isSortByAmount) {
            idto.setIsSortByAmount(isSortByAmount);
        }
        if(null !=commodityId) {
            idto.setCommodityId(commodityId);
        }
        if(null != shopType){
            idto.setShopType(shopType);
        }
        if(null != managementMode){
            idto.setManagementMode(managementMode);
        }
        if(null != provinceId){
            idto.setProvinceId(provinceId);
        }

        if(CollectionUtils.isNotEmpty(shopTypeList)) {
            idto.setShopTypeList(shopTypeList);
        }

        if(CollectionUtils.isNotEmpty(managementModeList)) {
            idto.setManagementModeList(managementModeList);
        }

        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
    }

    private void setCommodityTotalRow(Integer summaryType, Integer isWithShop, TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(13,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(17,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(18,getFieldValueByName("totalGiveAmount",result.getHeader()));
            totalMap.put(19,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(20,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(21,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,21);
        }
        if(summaryType ==PosReportSummaryTypeEnum.TWO.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(10,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalGiveAmount",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(17,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(18,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,18);
        }
        if( summaryType ==PosReportSummaryTypeEnum.THREE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if(summaryType ==PosReportSummaryTypeEnum.THREE.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(2,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(3,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }

        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(6,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FOUR.getCode()  && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(3,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(4,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if( summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop== PosReportWithShopEnum.ONE.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(7,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(12,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(13,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,14);
        }
        if(summaryType ==PosReportSummaryTypeEnum.FIVE.getCode() && (null !=isWithShop && isWithShop==PosReportWithShopEnum.ZERO.getCode())){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(4,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalReturnQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalReturnAmount",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGiveQuantity",result.getHeader()));
            totalMap.put(9,getFieldValueByName("totalDiscountAmount",result.getHeader()));
            totalMap.put(10,getFieldValueByName("totalQuantity",result.getHeader()));
            totalMap.put(11,getFieldValueByName("totalAmount",result.getHeader()));
            setTotalRow(data, totalMap,12);
        }
        if(summaryType ==PosReportSummaryTypeEnum.ELEVEN.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(5,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(6,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(7,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(8,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,11);
        }
    }
    private void setStallTotalRow(Integer summaryType,TablePageInfo<SalesSummaryReportOrderODTO> result, Map<String, List<String>> data) {
        if(summaryType == PosReportSummaryTypeEnum.ONE.getCode()){
            Map<Integer,String> totalMap=new HashMap<>();
            totalMap.put(13,getFieldValueByName("totalSaleQuantity",result.getHeader()));
            totalMap.put(14,getFieldValueByName("totalSaleAmount",result.getHeader()));
            totalMap.put(15,getFieldValueByName("totalSaleWeight",result.getHeader()));
            totalMap.put(16,getFieldValueByName("totalGrossprofitmarginAmount",result.getHeader()));
            setTotalRow(data, totalMap,16);
        }
    }

    private String getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value==null?"":value.toString();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return null;
        }
    }
    private void setTotalRow(Map<String, List<String>> data, Map<Integer, String> totalMap,Integer length) {
        List<String> dataLst = new ArrayList<>();
        totalMap.put(1,"合计");
        for(int j=1;j<=length;j++){
            dataLst.add(totalMap.get(j));
        }
        data.put("key_0", dataLst);
    }

    private void setStallDataList(Integer summaryType, List<String> dataLst, SalesSummaryReportOrderODTO dto) {
        if(summaryType ==PosReportSummaryTypeEnum.ONE.getCode()){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
            dataLst.add(dto.getStallName());
            dataLst.add(dto.getCommodityFirstName());
            dataLst.add(dto.getCommoditySecondName());
            dataLst.add(dto.getCommodityThirdName());
            dataLst.add(dto.getBarCode());
            dataLst.add(dto.getCommodityCode());
            dataLst.add(dto.getCommodityName());
            dataLst.add(dto.getCommoditySpec());
            dataLst.add(dto.getCommodityUnit());
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getWeightAmount()+"");
            dataLst.add(dto.getGrossprofitmarginAmount()+"");
            dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
            dataLst.add(dto.getNoTaxSaleAmount()+"");
            dataLst.add(dto.getNoTaxWeightAmount()+"");
        }
        if(summaryType ==PosReportSummaryTypeEnum.SIX.getCode() || summaryType ==PosReportSummaryTypeEnum.ELEVEN.getCode()){
            dataLst.add(dto.getOrgName());
            dataLst.add(dto.getShopCode());
            dataLst.add(dto.getShopName());
            dataLst.add(dto.getStallName());
            dataLst.add(dto.getTatalAmount()+"");
            dataLst.add(dto.getTatalQuantity()+"");
            dataLst.add(dto.getWeightAmount()+"");
            dataLst.add(dto.getGrossprofitmarginAmount()+"");
            dataLst.add(dto.getGrossprofitrate() == null ? "" : (dto.getGrossprofitrate()+""));
            dataLst.add(dto.getNoTaxSaleAmount()+"");
            dataLst.add(dto.getNoTaxWeightAmount()+"");
        }
    }


    @ApiOperation(value = "查询香烟大类一周销售额", notes = "查询香烟大类一周销售额")
    @RequestMapping(value = "/queryFirstCateWeekSaleAmount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BigDecimal queryFirstCateWeekSaleAmount(@RequestParam(value="shopId", required=false) Long shopId,@RequestParam(value="orderTime", required=false) Date orderTime){
        return posReportCurrentDayService.queryFirstCateWeekSaleAmount(shopId,orderTime);
    }
}
