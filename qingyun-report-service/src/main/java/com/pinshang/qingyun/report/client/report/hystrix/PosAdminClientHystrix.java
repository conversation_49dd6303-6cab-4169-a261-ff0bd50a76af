package com.pinshang.qingyun.report.client.report.hystrix;

import com.pinshang.qingyun.report.client.report.service.PosAdminClient;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.dto.pos.joinShop.PayTypeListDTO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PosAdminClientHystrix implements FallbackFactory<PosAdminClient> {

	@Override
	public PosAdminClient create(Throwable throwable) {
		return new PosAdminClient() {

			@Override
			public RetailSettingODTO getRetailSettingODTO() {
				return null;
			}

			@Override
			public MacODTO getMacById(Long macId) {
				return null;
			}

			@Override
			public List<PayTypeListDTO> withDrawRateList() {
				return null;
			}

			@Override
			public TradeODTO queryPrint(Long orderCode) {
				return null;
			}
		};
	}
}
