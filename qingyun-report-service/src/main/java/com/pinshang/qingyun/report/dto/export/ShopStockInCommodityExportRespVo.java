package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/1
 * 门店实收结算商品表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopStockInCommodityExportRespVo {
    @ExcelProperty("部门")
    private String orgName;
    @ExcelProperty("客户编码")
    private String storeCode;
    @ExcelProperty("门店名称")
    private String shopName;

    @ExcelProperty("商品编码")
    private String commodityCode;
    @ExcelProperty("商品名称")
    private String commodityName;
    @ExcelProperty("条码")
    private String barCode;
    @ExcelProperty("规格")
    private String commoditySpec;
    @ExcelProperty("计量单位")
    private String commodityUnitName;

    @ExcelProperty("订货数量")
    private BigDecimal orderQuantity;
    @ExcelProperty("订货金额")
    private BigDecimal orderAmount;
    @ExcelProperty("实发数量")
    private BigDecimal deliveryQuantity;
    @ExcelProperty("实发金额")
    private BigDecimal deliveryAmount;
    @ExcelProperty("调拨入库数量")
    private BigDecimal allotInQuantity;
    @ExcelProperty("调拨入库金额")
    private BigDecimal allotInAmount;
    @ExcelProperty("云超转入数量")
    private BigDecimal cloudInQuantity;
    @ExcelProperty("云超转入金额")
    private BigDecimal cloudInAmount;
    @ExcelProperty("实退数量")
    private BigDecimal returnQuantity;
    @ExcelProperty("实退金额")
    private BigDecimal returnAmount;
    @ExcelProperty("调拨出库数量")
    private BigDecimal allotOutQuantity;
    @ExcelProperty("调拨出库金额")
    private BigDecimal allotOutAmount;

    @ExcelProperty("实收数量总计")
    private BigDecimal totalQuantity;
    @ExcelProperty("实收金额合计")
    private BigDecimal totalAmount;

    @ExcelProperty("后台大类")
    private String commodityFirstCateName;
    @ExcelProperty("后台中类")
    private String commoditySecondCateName;
    @ExcelProperty("后台小类")
    private String commodityThirdCateName;
}
