package com.pinshang.qingyun.report.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

/**
 * 品类
 *
 * <AUTHOR>
 *
 * @date 2019.5.28
 */
@Data
@NoArgsConstructor
@Table(name="t_category")
public class Category {

    // ID
    private Long id;

    // 父节点ID：无父节点为0
    private Long parentId;

	// 编码
	private String cateCode;

    // 名称
    private String cateName;

    // 级别：支持1、2、3级
    private Integer cateLevel;

	// 状态：1-正常、0-关闭
	private Integer status;
	
}