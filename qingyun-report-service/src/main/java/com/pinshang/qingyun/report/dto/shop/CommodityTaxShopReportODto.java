package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommodityTaxShopReportODto {
	@ExcelIgnore
	private Long shopId;
	@ExcelProperty("门店名称")
	private String shopName;
	@ExcelIgnore
	private String barCode;
	@ExcelIgnore
	private Long commodityId;
	@ExcelIgnore
	private String commodityCode;
	@ExcelIgnore
	private String commodityName;
	@ExcelIgnore
	private String commoditySpec;
	@ExcelIgnore
	private Long cateId;
	@ExcelIgnore
	private String cateName;
	//订单
	@ExcelProperty("总销售额")
	private BigDecimal totalSales;//总销售额
	@ExcelIgnore
	private BigDecimal totalSalesQuantity; //总销售数量
	@ExcelProperty("线上销售额")
	private BigDecimal onlineSales;//线上销售额
	@ExcelProperty("线下销售额")
	private BigDecimal offlineSales;//线下销售额
	@ExcelProperty("线下销售额占比%")
	private BigDecimal offlineRatio;//线下销售额占比
	@ExcelProperty("毛利率%")
	private BigDecimal grossProfitRate;//毛利率

	//收货
	@ExcelIgnore
	private BigDecimal orderTotal;//进货金额
	//退货
	@ExcelIgnore
	private BigDecimal saleReturnOrderTotal;//退货金额
	//库存调整
	@ExcelIgnore
	private BigDecimal stockAdjustAmountTotal;//库存更正金额
	@ExcelIgnore
	private BigDecimal weightPrice; //移动成本价
	@ExcelIgnore
	private BigDecimal costTotal; //商品的成本合计

	//库存
	/*private BigDecimal stockQuantityPrice;//库存金额
	private BigDecimal stockQuantityDay;//库存天数
	private BigDecimal stockQuantity;//库存
	private BigDecimal negativeStockQuantity;//负库存*/

	//线下
	@ExcelProperty("线下来客数")
	private BigDecimal offlineVisitorNumber;//线下来客数
	@ExcelProperty("线下客单价")
	private BigDecimal offlineAverageAmount;//线下客单价
	@ExcelIgnore
	private String barCodes;	// 子码列表
}
