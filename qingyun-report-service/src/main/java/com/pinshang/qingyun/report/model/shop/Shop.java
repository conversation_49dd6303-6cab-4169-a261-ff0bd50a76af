package com.pinshang.qingyun.report.model.shop;

import com.pinshang.qingyun.report.model.BaseModel;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_md_shop")
public class Shop extends BaseModel<Shop>{

    private String shopName;

    private String shopShortName;

    private String shopCode;

    private String shopAid;

    private String mobile;

    private String phone;

    private String remark;

    private Byte status;

    private Byte shopStatus;

    private Long countryId;

    private Long provinceId;

    private Long cityId;

    private Long areaId;

    private String detailAddress;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private Long enterpriseId;

    /**门店类型:1-门店，2-鲜食店**/
    private Integer shopType;
    /**联系人**/
    private String contacts;
    private String shopImg;

    /**门店面积**/
    private BigDecimal shopArea;

    /**营业执照名称**/
    private String businessLicenseName;

    //客户id
    private Long storeId;
    @Transient
    private String storeName;
    @Transient
    private String visitPicUrl;

    /** 配送范围:多边形 */
    private String polygon;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName == null ? null : shopName.trim();
    }

    public String getShopShortName() {
        return shopShortName;
    }

    public void setShopShortName(String shopShortName) {
        this.shopShortName = shopShortName;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode == null ? null : shopCode.trim();
    }

    /*
     * 获取门店pos仓库编码
     */
    public String getShopPosCode(){
        return StringUtils.isBlank(shopCode)?"":shopCode+"01";
    }

    public String getShopAid() {
        return shopAid;
    }

    public void setShopAid(String shopAid) {
        this.shopAid = shopAid == null ? null : shopAid.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(Byte shopStatus) {
        this.shopStatus = shopStatus;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress == null ? null : detailAddress.trim();
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getShopImg() {
        return shopImg;
    }

    public void setShopImg(String shopImg) {
        this.shopImg = shopImg;
    }

    public String getPolygon() {
        return polygon;
    }

    public void setPolygon(String polygon) {
        this.polygon = polygon;
    }

    public String getVisitPicUrl() {
        return visitPicUrl;
    }

    public void setVisitPicUrl(String visitPicUrl) {
        this.visitPicUrl = visitPicUrl;
    }

    public BigDecimal getShopArea() {
        return shopArea;
    }

    public void setShopArea(BigDecimal shopArea) {
        this.shopArea = shopArea;
    }

    public String getBusinessLicenseName() {
        return businessLicenseName;
    }

    public void setBusinessLicenseName(String businessLicenseName) {
        this.businessLicenseName = businessLicenseName;
    }
}