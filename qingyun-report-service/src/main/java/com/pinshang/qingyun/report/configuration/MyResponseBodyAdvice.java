package com.pinshang.qingyun.report.configuration;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.report.annotation.NotAdvice;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.annotation.Annotation;

/**
 * Created by weican on 2017-04-20.
 * 统一改造了gateway 不再需要在项目中包装
 */
//@Order(2)
//@ControllerAdvice(basePackages = "com.pinshang")
//@ConditionalOnProperty(
//        prefix = "pinshang.qingyun", name = "project",
//        matchIfMissing = false
//)
@Deprecated
public class MyResponseBodyAdvice implements ResponseBodyAdvice<Object> {
    private final static ApiResult API_RESULT = new ApiResult().initSuccess();
    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        Annotation[] methodAnnotations = methodParameter.getMethodAnnotations();
        for (Annotation methodAnnotation : methodAnnotations) {
            Class<? extends Annotation> annotationType = methodAnnotation.annotationType();
            if(NotAdvice.class.equals(annotationType)){
                return false;
            }
        }
        return true;
    }
    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType,
                                  Class<? extends HttpMessageConverter<?>> aClass,
                                  ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        String uri = serverHttpRequest.getURI().getPath();
        if(  o instanceof ApiResponse || uri.startsWith("/sync")  ){
            return o;
        }
        ApiResponse response = new ApiResponse();

        response.setData(o);
        response.setResult(API_RESULT);
        if( aClass.getName().equals(StringHttpMessageConverter.class.getName())){
            String  json = JsonUtil.java2json(response).replace("\\","");
            return json;
        }
        return response;
    }

}
