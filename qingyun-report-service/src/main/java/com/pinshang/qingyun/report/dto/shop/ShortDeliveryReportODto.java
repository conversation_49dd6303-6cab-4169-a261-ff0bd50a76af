package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ShortDeliveryReportODto {
	private Long storeId;
	private String shopCode;
	private String shopName;
	private Date deliveryDate;
	private String orderCode;
	private String categoryName;
	private String secondCategoryName;
	private String thirdCategoryName;
	private String barCode;
	private String barCodes;	// 子码列表
	private String commodityId;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	private String commodityUnitName;
	private BigDecimal orderNum;
    private BigDecimal deliveryNum;
	private BigDecimal differNum;
	private String rate;
	private BigDecimal price;
	private String factoryName;
	private String workshopName;
	private String storeCode;
	private String createName;
	private Integer shopType;

	public String getShopTypeName() {
		return ShopTypeEnums.getName(this.shopType);
	}

	@ApiModelProperty("线路组")
	private String storeLineGroupName;

	@ApiModelProperty("仓库名称")
	private String warehouseName;

	@ApiModelProperty("经营模式")
	private String managementModeName;
}
