package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class NetSalesListDTO {

    @ApiModelProperty(value = "门店id")
    private Long shopId;

    @ApiModelProperty(value = "收银员")
    private Long createId;

    @ApiModelProperty(value = "销售日期")
    private String saleTime;

    @ApiModelProperty(value = "净销售额")
    private BigDecimal netSales;
}
