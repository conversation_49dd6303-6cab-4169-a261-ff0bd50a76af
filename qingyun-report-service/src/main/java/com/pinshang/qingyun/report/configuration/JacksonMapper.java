package com.pinshang.qingyun.report.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * @Author: sk
 * @Date: 2022/10/24
 */
public class JacksonM<PERSON>per extends ObjectMapper {

    public JacksonMapper() {
        super();
        //this.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
        //this.configure(JsonGenerator.Feature.IGNORE_UNKNOWN, true);
        this.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //this.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //this.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        //this.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);

        //simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        //simpleModule.addSerializer(long.class, ToStringSerializer.instance);

        registerModule(simpleModule);

    }

}
