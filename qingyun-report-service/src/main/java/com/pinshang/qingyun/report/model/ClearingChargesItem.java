package com.pinshang.qingyun.report.model;

import lombok.Data;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
@Table(name = "t_clearing_charges_item")
//收银月报明细
@Data
public class ClearingChargesItem {

    private Long id;
    //收银月报id
    private Long clearingChargesId;
    //导入日期
    private Date importDate;
    //导入月份
    private String importDateMonth;
    //门店编码
    private String shopCode;
    //门店名称
    private String shopName;
    //门店id
    private Long shopId;
    //现金
    private BigDecimal cash;
    //pos收现金
    private BigDecimal posCash;
    //pos抹零
    private BigDecimal moLing;
    @Transient
    private BigDecimal cashDiff;

    //银行
    private BigDecimal bankCard;
    //pos银行卡
    private BigDecimal posBankCard;
    //银行卡手续费
    private BigDecimal bankCardPoundage;
    @Transient
    private BigDecimal bankDiff;
    //银联聚合
    private BigDecimal unionPay;
    //pos银联聚合
    private BigDecimal posUnionPay;
    //银联聚合手续费
    private BigDecimal unionPoundage;
    @Transient
    private BigDecimal unionPayDiff;
    //微信记账
    private BigDecimal tallyWechat;
    //pos微信记账
    private BigDecimal posTallyWechat;
    //微信记账手续费
    private BigDecimal tallyWechatPoundage;
    @Transient
    private BigDecimal tallyWechatDiff;
    //支付宝记账
    private BigDecimal tallyAli;
    //pos支付宝记账
    private BigDecimal posTallyAli;
    //支付宝记账手续费
    private BigDecimal tallyAliPoundage;
    @Transient
    private BigDecimal tallAliDiff;
    //ok卡收
    private BigDecimal okCard;
    //pos ok
    private BigDecimal posOkCard;
    //ok卡手续费
    private BigDecimal okCardPoundage;
    @Transient
    private BigDecimal okCardDiff;
    //总收
    private BigDecimal totalAmount;
    //pos//总收
    private BigDecimal posTotalAmount;
    //总手续费
    private BigDecimal totalPoundage;
    @Transient
    private BigDecimal totalDiff;
    private Integer isDelete;


    public BigDecimal getCashDiff() {
        return cash.subtract(posCash);
    }

    public BigDecimal getBankDiff() {
        return bankCard.add(bankCardPoundage).subtract(posBankCard);
    }

    public BigDecimal getUnionPayDiff() {
        return unionPay.add(unionPoundage).subtract(posUnionPay);
    }

    public BigDecimal getTallyWechatDiff() {
        return tallyWechat.add(tallyWechatPoundage).subtract(posTallyWechat);
    }

    public BigDecimal getTallAliDiff() {
        return tallyAli.add(tallyAliPoundage).subtract(posTallyAli);
    }

    public BigDecimal getOkCardDiff() {
        return okCard.add(okCardPoundage).subtract(posOkCard);
    }

    public BigDecimal getTotalDiff() {
        return totalAmount.add(totalPoundage).subtract(posTotalAmount);
    }
}
