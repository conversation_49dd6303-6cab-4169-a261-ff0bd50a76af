package com.pinshang.qingyun.report.dto.shop;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class AppOperateReportODto {
	private Long id;  //shopId
	private String shopName;
	private int totalUserCount = 0;
	private int newUserCount = 0;
	private int oldUserCount = 0;
	private int totalOrderCount = 0;
	private BigDecimal realTotalAmount = BigDecimal.ZERO;
	private int outOrderCount = 0;
	private BigDecimal outRealAmount = BigDecimal.ZERO;
	private BigDecimal averageAmount = BigDecimal.ZERO;
	private int returnNum = 0;
	private BigDecimal returnAmount = BigDecimal.ZERO;
}
