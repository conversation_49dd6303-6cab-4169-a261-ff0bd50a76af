package com.pinshang.qingyun.report.model.shop;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @program: qingyun-shop
 * @description: 档口
 * @author: XX
 * @create: 2024/09/09/0009 9:31
 **/
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name="t_stall")
public class Stall extends BaseModel<Stall> {
    @Id
    private Long id;

    private String stallCode;

    private String stallName;

    private Long shopId;

    private Long stallSpId;

    private Integer status;
    
}
