package com.pinshang.qingyun.report.dto.pos.joinShop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisBySynthesisDTO {

    private Long shopId;

    /**
     * 营业额 = 销售成交金额合计-退货金额合计
     */
    private BigDecimal turnover;

    /**
     * 总的客单量
     */
    private Long totalCustomers;

    /**
     * 截止到19点前的来客数
     */
    private Long customersBefore19;

    /**
     * 截止到19点前的销售额 = 销售额合计-退货金额合计
     */
    private BigDecimal salesBefore19;

    /**
     * 货款 = 进货金额合计-退货金额合计-少货金额合计
     * 退货金额：根据退货审核的时间统计
     * 少货金额：根据少货审核的时间统计
     */
    private BigDecimal goodsPayment;

    /**
     * 时段折扣率 = 【折扣金额合计/原价金额合计】/时段数*100%
     * 折扣金额合计=原价金额合计-销售额合计
     * 其中原价金额合计=【（销售数量+赠品数量）*零售价】，销售额合计=销售成交金额合计-退货金额合计
     */
    private BigDecimal discountRate;

    /**
     * 损耗率=（实收数量-退货数量-少货数量-销售数量）/（实收数量-退货数量-少货数量）
     */
    private BigDecimal lossRate;

    /**
     * 加盟商净毛利（总销售额-总进货-手续费）
     */
    private BigDecimal franchiseeNetProfit;
}
