package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/07/03
 * @Version 1.0
 */
@Data
public class ShopTaxForJmByCateIDTO extends Pagination {

    @ApiModelProperty("开始日期 yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("结束日期 yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("一级分类")
    private Long cate1;

    @ApiModelProperty("二级分类")
    private Long cate2;

    @ApiModelProperty("三级分类")
    private Long cate3;

    @ApiModelProperty("毛利率-最小")
    private String grossMarginMin;

    @ApiModelProperty("毛利率-最大")
    private String grossMarginMax;

    @ApiModelProperty("1-按一类分组, 2-按二类分组, 3-按三类分组 4=商品")
    private Integer type;

    private Integer shopType;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("条形码")
    private String barCode;
}
