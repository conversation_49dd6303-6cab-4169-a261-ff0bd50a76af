package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "t_pos_report_guest_list_summary_day")
@Data
public class GuestListSummaryDay extends BaseModel<GuestListSummaryDay> {

    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    private Long macId;

    /** 机器号*/
    private String macCode;

    /** 机器名称*/
    private String macName;

    /** 客单量*/
    private BigDecimal guestQuantity = BigDecimal.ZERO;

    /** 客单金额*/
    private BigDecimal guestAmount = BigDecimal.ZERO;

    /** 销售日期*/
    private String saleTime;

}
