package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.base.enums.XSOrderPayStatusEnums;
import com.pinshang.qingyun.base.enums.XSOrderStatusEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportIDTO;
import com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportODTO;
import com.pinshang.qingyun.report.mapper.XsOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class XsOrderService {

    @Autowired
    private XsOrderMapper xsOrderMapper;
    /**
     * 查询订单报表信息
     * @param vo
     * @return
     * @throws ParseException
     */
    public List<XsOrderReportODTO> selectOrderReportInfo(XsOrderReportIDTO vo) throws ParseException {
        List<String> dayList = DateUtil.getDayList(vo.getStartTime(), vo.getEndTime());
        List<XsOrderReportODTO> resultList = new ArrayList<>();

        XsOrderReportIDTO pa = new XsOrderReportIDTO();
        pa.setStartTime(vo.getStartTime());
        pa.setEndTime(vo.getEndTime());
        pa.setEnterpriseId(vo.getEnterpriseId());
        pa.setShopId(vo.getShopId());
        pa.setStatus(vo.getStatus());
        // 查询每日下单用户数
        List<XsOrderReportODTO> userList = xsOrderMapper.selectOrderUserCountWithDay(pa);
        Map<String, Integer> userMap = userList.stream().collect(Collectors.toMap(XsOrderReportODTO::getCreateTime, XsOrderReportODTO::getCount));

        // 查询成功支付的订单信息
        // 已支付 pay_status = 2 and order_status != 1;
        pa.setPayStatus(XSOrderPayStatusEnums.PAYED.getCode());
        pa.setStatusList(Arrays.asList(
                XSOrderStatusEnums.CANCEL.getCode(),
                XSOrderStatusEnums.WAITING_DELIVERY.getCode(),
                XSOrderStatusEnums.DELIVERING.getCode(),
                XSOrderStatusEnums.DELIVERED.getCode(),
                XSOrderStatusEnums.DELIVERY_FAIL.getCode()
        ));
        vo.setPayStatus(XSOrderPayStatusEnums.PAYED.getCode());
        List<XsOrderReportODTO> orderList = xsOrderMapper.selectOrderReportList(vo);
        Map<String, XsOrderReportODTO> orderMap = orderList.stream().collect(Collectors.toMap(XsOrderReportODTO::getCreateTime, Function.identity()));

        // 查询已出库的订单信息
        // 置空支付状态
        pa.setPayStatus(null);
        pa.setStatusList(Arrays.asList(
                XSOrderStatusEnums.DELIVERED.getCode()
        ));
        List<XsOrderReportODTO> outList = xsOrderMapper.selectOutOrderReportList(pa);
        Map<String, XsOrderReportODTO> map = outList.stream().collect(Collectors.toMap(XsOrderReportODTO::getCreateTime, Function.identity()));
        // 组装数据(以CreateTime为key，)
        for (String str : dayList) {
            XsOrderReportODTO entry = new XsOrderReportODTO();
            // 设置时间
            entry.setCreateTime(str);
            entry.setUserOrderCount(userMap.get(entry.getCreateTime()) != null ? userMap.get(entry.getCreateTime()) : 0);

            XsOrderReportODTO orderInfo = orderMap.get(entry.getCreateTime());
            if (orderInfo != null) {
                // 填充订单信息
                entry.setCount(orderInfo.getCount());
                entry.setTotalAmount(orderInfo.getTotalAmount());
            } else {
                // 当天下单，则设置0
                entry.setCount(0);
                entry.setTotalAmount(BigDecimal.ZERO);
            }

            XsOrderReportODTO stockOutInfo = map.get(entry.getCreateTime());
            if (stockOutInfo != null) {
                // 填充出库信息
                entry.setStockOutCount(stockOutInfo.getCount());
                entry.setStockOutTotalAmount(stockOutInfo.getTotalAmount());
            } else {
                // 当天没有出库，则设置0
                entry.setStockOutCount(0);
                entry.setStockOutTotalAmount(BigDecimal.ZERO);
            }
            resultList.add(entry);
        }
        return resultList;
    }
}
