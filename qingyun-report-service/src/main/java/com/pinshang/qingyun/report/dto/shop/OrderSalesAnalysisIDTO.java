package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class OrderSalesAnalysisIDTO extends Pagination {
    private String beginDate;
    @Change(value = DataQueryConstant.NOW)
    private String endDate;
    private Integer summaryType;
    private Long shopId;
    private List<Long> shopIds = new ArrayList<>();
    private Integer shopType;

    private Long provinceId;
}
