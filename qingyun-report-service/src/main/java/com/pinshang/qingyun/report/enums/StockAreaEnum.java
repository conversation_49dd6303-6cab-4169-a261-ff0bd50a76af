package com.pinshang.qingyun.report.enums;


public enum StockAreaEnum {
    NORMAL(1, "正常库"),
    TEMP(2,"临时库")
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;


    StockAreaEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
