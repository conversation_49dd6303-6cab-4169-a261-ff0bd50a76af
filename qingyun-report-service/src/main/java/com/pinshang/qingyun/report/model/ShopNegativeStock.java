package com.pinshang.qingyun.report.model;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2022/6/28
 */

@Table(name = "t_shop_negative_stock_report")
@Data
public class ShopNegativeStock extends BaseSimplePO {

    /** 库存提取日期 */
    private Date extractTime;

    private Long shopId;
    private String shopCode;
    private String shopName;
    private String shopShortName;
    private String storeCode;
    private String storeName;

    private Long commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    private String commodityUnit;
    /** 是否称重0-不称量,1-称重 */
    private Integer isWeight;


    /** 库存数量 */
    private BigDecimal stockQuantity;

    /** 及时达必售 1 是  0 否 */
    private Integer sellStatus;

}
