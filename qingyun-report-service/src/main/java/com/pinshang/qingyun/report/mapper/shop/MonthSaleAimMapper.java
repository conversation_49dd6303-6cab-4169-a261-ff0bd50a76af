package com.pinshang.qingyun.report.mapper.shop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.shop.MonthSaleAimODTO;
import com.pinshang.qingyun.report.dto.shop.MonthSaleAimPageIDTO;
import com.pinshang.qingyun.report.model.shop.MonthSaleAim;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface MonthSaleAimMapper extends MyMapper<MonthSaleAim> {

    /**
     * 判断门店日期唯一性
     * @param shopId
     * @param saleTime
     * @return
     */
    Integer countMonthSaleAimByShopSaleTime(@Param("shopId")Long shopId, @Param("saleTime") String saleTime);

    /**
     * 日销售目标管理(分页)
     * @param idto
     * @return
     */
    List<MonthSaleAimODTO> listMonthSaleAimPage(MonthSaleAimPageIDTO idto);

    List<String> queryMonthKeyListRepeat(@Param("keyList") List<String> keyList);
}
