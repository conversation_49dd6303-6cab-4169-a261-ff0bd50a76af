package com.pinshang.qingyun.report.service.settle;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.ShopODTO;
import com.pinshang.qingyun.report.dto.settle.ShopStockInReportODTO;
import com.pinshang.qingyun.report.dto.settle.ShopStockInReportQueryIDTO;
import com.pinshang.qingyun.report.mapper.CategoryMapper;
import com.pinshang.qingyun.report.mapper.CloudCommodityReportMapper;
import com.pinshang.qingyun.report.mapper.settle.*;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.Category;
import com.pinshang.qingyun.report.model.settle.*;
import com.pinshang.qingyun.report.model.shop.CommodityTax;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxService;
import com.pinshang.qingyun.report.service.shop.ShopService;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.report.StockInCommodityODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopReportClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.report.dto.StockInCommodityReportODTO;
import com.pinshang.qingyun.xd.report.service.XdSalesReportClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/5/24
 */
@Slf4j
@Service
public class ShopStockInService {

    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;
    @Autowired
    private StockInCommodityMapper stockInCommodityMapper;
    @Autowired
    private StockInThirdMapper stockInThirdMapper;
    @Autowired
    private StockInSecondMapper stockInSecondMapper;
    @Autowired
    private StockInFirstMapper stockInFirstMapper;
    @Autowired
    private StockInShopMapper stockInShopMapper;
    @Autowired
    private ShopReportClient shopReportClient;
    @Autowired
    private SMMUserClient sMMUserClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CloudCommodityReportMapper cloudCommodityReportMapper;
    @Autowired
    private XdSalesReportClient xdSalesReportClient;

    @Autowired
    private IRenderService renderService;
    /**
     * 门店实际入库汇总
     * @param day
     */
    @Transactional(rollbackFor = Exception.class)
    public void shopStockInTax(String day) {
        long listBegin = System.currentTimeMillis();

        // 获取数据
        List<StockInCommodity> stockInCommodityList = getStockInCommodity(day);
        if(CollectionUtils.isEmpty(stockInCommodityList)){
            return;
        }

        long listEnd = System.currentTimeMillis();
        log.info("查询门店实际入库数据耗时:" + (listEnd - listBegin) + "ms");

        //清除数据
        deleteShopStockInTax(day);
        long cleanEnd = System.currentTimeMillis();
        log.info("删除数据耗时:" + (cleanEnd - listEnd) + "ms");

        if(CollectionUtils.isNotEmpty(stockInCommodityList)){
            if(stockInCommodityList.size() > 2000 ){
                batchInsertStockInCommodity(stockInCommodityList);
            }else{
                insertStockInCommodity(stockInCommodityList);
            }
        }

        long insertEnd = System.currentTimeMillis();
        log.info("插入商品级别数据耗时:" + (insertEnd - cleanEnd) + "ms");

        //小类级别汇总
        List<StockInThird> thirdCateList = commodityThirdCate(stockInCommodityList);
        long thirdEnd = System.currentTimeMillis();
        log.info("小类级别汇总耗时:" + (thirdEnd - insertEnd) + "ms");

        //中类级别汇总
        List<StockInSecond> secondCateList = commoditySecondCate(thirdCateList);
        long secondEnd = System.currentTimeMillis();
        log.info("中类级别汇总耗时:" + (secondEnd - thirdEnd) + "ms");

        //大类级别汇总
        List<StockInFirst> firstCateList = commodityFirstCate(secondCateList);
        long firstEnd = System.currentTimeMillis();
        log.info("大类级别汇总耗时:" + (firstEnd - secondEnd) + "ms");

        //门店级别汇总
        commodityShopCate(firstCateList);
        long shopEnd = System.currentTimeMillis();
        log.info("门店级别汇总耗时:" + (shopEnd - firstEnd) + "ms");

    }

    /**
     * 获取商品级别数据
     * @param day
     * @return
     */
    public List<StockInCommodity> getStockInCommodity(String day){
        List<StockInCommodity> returnList = new ArrayList<>();

        //分任务获取订货发货数量金额、调拨入库出库数量金额、退货数量金额
        Map<String, List<StockInCommodity>> dataMap = getTaskData(day);

        //获取数据
        List<StockInCommodity> orderList = dataMap.get("orderList");
        List<StockInCommodity> allotList = dataMap.get("allotList");
        List<StockInCommodity> returnOrderList = dataMap.get("returnOrderList");
        List<StockInCommodity> cloudCommodityList = dataMap.get("cloudCommodityList");

        //定义放各种数据map
        Map<String, StockInCommodity> orderListMap = new HashMap<>(128*1024);
        Map<String, StockInCommodity> allotListMap = new HashMap<>(64*1024);
        Map<String, StockInCommodity> returnOrderListMap = new HashMap<>(256);
        Map<String, StockInCommodity> cloudCommodityMap = new HashMap<>(256);

        //list数据放map
        setListMap(returnList, orderList, allotList, returnOrderList, cloudCommodityList,
                 orderListMap, allotListMap, returnOrderListMap, cloudCommodityMap);

        List<Long> commodityIdList = returnList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
        returnList = new ArrayList<>();

        // 查询当天的产生交易的商品信息
        List<StockInCommodity> stockInCommodityList = getCommodityTaxes(commodityIdList);


        // 组装数据
        for (StockInCommodity stockInCommodity : stockInCommodityList) {
            // 订货发货数量金额
            StockInCommodity orderEntry = orderListMap.get(stockInCommodity.getShopId() + "" + stockInCommodity.getCommodityId());
            if(null != orderEntry ) {
                stockInCommodity.setOrderQuantity((stockInCommodity.getOrderQuantity() != null ? stockInCommodity.getOrderQuantity() : BigDecimal.ZERO).add(orderEntry.getOrderQuantity()));
                stockInCommodity.setOrderAmount((stockInCommodity.getOrderAmount() != null ? stockInCommodity.getOrderAmount() : BigDecimal.ZERO).add(orderEntry.getOrderAmount()));
                stockInCommodity.setDeliveryQuantity((stockInCommodity.getDeliveryQuantity() != null ? stockInCommodity.getDeliveryQuantity() : BigDecimal.ZERO).add(orderEntry.getDeliveryQuantity()));
                stockInCommodity.setDeliveryAmount((stockInCommodity.getDeliveryAmount() != null ? stockInCommodity.getDeliveryAmount() : BigDecimal.ZERO).add(orderEntry.getDeliveryAmount()));
            }

            //调拨入库出库数量金额
            StockInCommodity allotEntry = allotListMap.get(stockInCommodity.getShopId() + "" + stockInCommodity.getCommodityId());
            if(null != allotEntry ) {
                stockInCommodity.setAllotInQuantity((stockInCommodity.getAllotInQuantity() != null ? stockInCommodity.getAllotInQuantity() : BigDecimal.ZERO).add(allotEntry.getAllotInQuantity()));
                stockInCommodity.setAllotInAmount((stockInCommodity.getAllotInAmount() != null ? stockInCommodity.getAllotInAmount() : BigDecimal.ZERO).add(allotEntry.getAllotInAmount()));
                stockInCommodity.setAllotOutQuantity((stockInCommodity.getAllotOutQuantity() != null ? stockInCommodity.getAllotOutQuantity() : BigDecimal.ZERO).add(allotEntry.getAllotOutQuantity()));
                stockInCommodity.setAllotOutAmount((stockInCommodity.getAllotOutAmount() != null ? stockInCommodity.getAllotOutAmount() : BigDecimal.ZERO).add(allotEntry.getAllotOutAmount()));
            }

            // 退货数量金额
            StockInCommodity returnOrderEntry = returnOrderListMap.get(stockInCommodity.getShopId() + "" + stockInCommodity.getCommodityId());
            if(null != returnOrderEntry ) {
                stockInCommodity.setReturnQuantity((stockInCommodity.getReturnQuantity() != null ? stockInCommodity.getReturnQuantity() : BigDecimal.ZERO).add(returnOrderEntry.getReturnQuantity()));
                stockInCommodity.setReturnAmount((stockInCommodity.getReturnAmount() != null ? stockInCommodity.getReturnAmount() : BigDecimal.ZERO).add(returnOrderEntry.getReturnAmount()));
            }

            // 退货数量金额
            StockInCommodity cloudCommodityEntry = cloudCommodityMap.get(stockInCommodity.getShopId() + "" + stockInCommodity.getCommodityId());
            if(null != cloudCommodityEntry ) {
                stockInCommodity.setCloudInQuantity((stockInCommodity.getCloudInQuantity() != null ? stockInCommodity.getCloudInQuantity() : BigDecimal.ZERO).add(cloudCommodityEntry.getCloudInQuantity()));
                stockInCommodity.setCloudInAmount((stockInCommodity.getCloudInAmount()!= null ? stockInCommodity.getCloudInAmount() : BigDecimal.ZERO).add(cloudCommodityEntry.getCloudInAmount()));
            }
        }

        // 处理数据
        for (StockInCommodity taxEntry : stockInCommodityList) {
            if((taxEntry.getOrderQuantity() != null && taxEntry.getOrderQuantity().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getDeliveryQuantity() != null && taxEntry.getDeliveryQuantity().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getAllotInQuantity() != null && taxEntry.getAllotInQuantity().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getAllotOutQuantity() != null && taxEntry.getAllotOutQuantity().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getReturnQuantity() != null && taxEntry.getReturnQuantity().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getCloudInQuantity() != null && taxEntry.getCloudInQuantity().compareTo(BigDecimal.ZERO) != 0)){
                taxEntry.setDateTime(day);
                taxEntry.setCreateId(1L);
                taxEntry.setCreateTime(new Date());
                returnList.add(taxEntry);
            }
        }

        return returnList;
    }

    /**
     * 查询当天的产生交易的商品信息
     * @return
     */
    public List<StockInCommodity> getCommodityTaxes(List<Long> commodityIdList) {
        List<StockInCommodity> list = new ArrayList<>();
        List<CommodityTax> commodityList = shopCommodityTaxService.getCommodityTaxes(new HashSet(commodityIdList),null);
        if(CollectionUtils.isNotEmpty(commodityList)) {
            for (CommodityTax tax : commodityList) {
                StockInCommodity stockInCommodity = new StockInCommodity();
                BeanUtils.copyProperties(tax,stockInCommodity);
                list.add(stockInCommodity);
            }
        }
        return list;
    }

    /**
     * 各数据放入map
     */
    private void setListMap(List<StockInCommodity> returnList, List<StockInCommodity> orderList, List<StockInCommodity> allotList,
                            List<StockInCommodity> returnOrderList, List<StockInCommodity> cloudCommodityList,
                            Map<String, StockInCommodity> orderListMap, Map<String, StockInCommodity> allotListMap,
                            Map<String, StockInCommodity> returnOrderListMap, Map<String, StockInCommodity> cloudCommodityMap) {

        if(CollectionUtils.isNotEmpty(orderList)){
            returnList.addAll(orderList);
            for(StockInCommodity entry : orderList){
                //orderListMap.put(entry.getShopId() + "" + entry.getCommodityId(),entry);
                String key = entry.getShopId() + "" + entry.getCommodityId();
                StockInCommodity stockInCommodity = orderListMap.get(key);
                if(stockInCommodity != null){
                    stockInCommodity.setOrderQuantity(stockInCommodity.getOrderQuantity().add(entry.getOrderQuantity()));
                    stockInCommodity.setOrderAmount(stockInCommodity.getOrderAmount().add(entry.getOrderAmount()));
                    stockInCommodity.setDeliveryQuantity(stockInCommodity.getDeliveryQuantity().add(entry.getDeliveryQuantity()));
                    stockInCommodity.setDeliveryAmount(stockInCommodity.getDeliveryAmount().add(entry.getDeliveryAmount()));
                    orderListMap.put(key,stockInCommodity);
                }else {
                    orderListMap.put(key,entry);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(allotList)){
            returnList.addAll(allotList);
            for(StockInCommodity entry:allotList){
                //allotListMap.put(entry.getShopId() + "" + entry.getCommodityId(),entry);
                String key = entry.getShopId() + "" + entry.getCommodityId();
                StockInCommodity stockInCommodity = allotListMap.get(key);
                if(stockInCommodity != null){
                    stockInCommodity.setAllotInQuantity(stockInCommodity.getAllotInQuantity().add(entry.getAllotInQuantity()));
                    stockInCommodity.setAllotInAmount(stockInCommodity.getAllotInAmount().add(entry.getAllotInAmount()));
                    stockInCommodity.setAllotOutQuantity(stockInCommodity.getAllotOutQuantity().add(entry.getAllotOutQuantity()));
                    stockInCommodity.setAllotOutAmount(stockInCommodity.getAllotOutAmount().add(entry.getAllotOutAmount()));
                    allotListMap.put(key,stockInCommodity);
                }else {
                    allotListMap.put(key,entry);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(returnOrderList)){
            returnList.addAll(returnOrderList);
            for(StockInCommodity entry:returnOrderList){
                returnOrderListMap.put(entry.getShopId() + "" + entry.getCommodityId(),entry);
            }
        }

        if(CollectionUtils.isNotEmpty(cloudCommodityList)){
            returnList.addAll(cloudCommodityList);
            for(StockInCommodity entry:cloudCommodityList){
                String key = entry.getShopId() + "" + entry.getCommodityId();
                StockInCommodity stockInCommodity = cloudCommodityMap.get(key);
                 if(stockInCommodity != null){
                     stockInCommodity.setCloudInQuantity(stockInCommodity.getCloudInQuantity().add(entry.getCloudInQuantity()));
                     stockInCommodity.setCloudInAmount(stockInCommodity.getCloudInAmount().add(entry.getCloudInAmount()));
                     cloudCommodityMap.put(key,stockInCommodity);
                 }else {
                     cloudCommodityMap.put(key,entry);
                 }
            }
        }
    }


    /**
     * 分任务查询各数据信息
     */
    private Map<String, List<StockInCommodity>> getTaskData(String day) {
        Map<String,List<StockInCommodity>> dataMap = new HashMap<>(256*1024);


        // 订货发货数量金额
        CompletableFuture<List<StockInCommodity>> f2 = CompletableFuture.supplyAsync(() -> {
            List<StockInCommodity> orderList = getOrderInfo(day);
            dataMap.put("orderList",orderList);
            return orderList;
        });

        // 调拨入库出库数量金额
        CompletableFuture<List<StockInCommodity>> f3 = CompletableFuture.supplyAsync(() -> {
            List<StockInCommodity> allotList = getAllotInfo(day);
            dataMap.put("allotList",allotList);
            return allotList;
        });

        // 退货数量金额
        CompletableFuture<List<StockInCommodity>> f4 = CompletableFuture.supplyAsync(() -> {
            List<StockInCommodity> returnOrderList = getSaleReturnOrderInfo(day);
            dataMap.put("returnOrderList",returnOrderList);
            return returnOrderList;
        });

        // 云超转入数量、转入金额
        CompletableFuture<List<StockInCommodity>> f5 = CompletableFuture.supplyAsync(() -> {
            List<StockInCommodity> cloudCommodityList = getCloudInReport(day);
            dataMap.put("cloudCommodityList",cloudCommodityList);
            return cloudCommodityList;
        });

        //所有查询结束，任务才结束
        CompletableFuture<Void> all = CompletableFuture.allOf(f2,f3,f4,f5);
        all.join();
        return dataMap;
    }

    /**
     * 获取云超转入数量、转入金额
     * @param day
     * @return
     */
    public List<StockInCommodity> getCloudInReport(String day){
        List<StockInCommodity> list = new ArrayList<>();

        // 云超订单取消明细表、云超配送失败明细表
        List<StockInCommodity> stockInCommodityList = cloudCommodityReportMapper.getCloudInReport(day);
        if(CollectionUtils.isNotEmpty(stockInCommodityList)){
            list.addAll(stockInCommodityList);
        }

        // 云超客户退货明细表
        List<StockInCommodityReportODTO> reportODTOList = xdSalesReportClient.getCloudReturnOrderList(day);
        if(CollectionUtils.isNotEmpty(reportODTOList)){
            for(StockInCommodityReportODTO odto:reportODTOList){
                StockInCommodity stockInCommodity = new StockInCommodity();
                BeanUtils.copyProperties(odto,stockInCommodity);
                list.add(stockInCommodity);
            }
        }
        return list;
    }

    /**
     * 查询订货发货数量金额
     * @param day
     * @return
     */
    public List<StockInCommodity> getOrderInfo(String day){
        List<StockInCommodity> list = new ArrayList<>();
        List<StockInCommodityODTO> odtoList = shopReportClient.getStockInCommodityFromOrder(day);
        if(CollectionUtils.isNotEmpty(odtoList)){
             for(StockInCommodityODTO odto:odtoList){
                StockInCommodity stockInCommodity = new StockInCommodity();
                BeanUtils.copyProperties(odto,stockInCommodity);
                list.add(stockInCommodity);
            }
        }
        return list;
    }

    /**
     * 查询调拨入库出库数量金额
     * @param day
     * @return
     */
    public List<StockInCommodity> getAllotInfo(String day){
        List<StockInCommodity> list = new ArrayList<>();
        List<StockInCommodityReportODTO> odtoList = xdSalesReportClient.findAllotOrderByAllotInTime(day);
        if(CollectionUtils.isNotEmpty(odtoList)){
            for(StockInCommodityReportODTO odto:odtoList){
                StockInCommodity stockInCommodity = new StockInCommodity();
                BeanUtils.copyProperties(odto,stockInCommodity);
                list.add(stockInCommodity);
            }
        }
        return list;
    }

    /**
     * 查询退货数量金额
     * @param day
     * @return
     */
    public List<StockInCommodity> getSaleReturnOrderInfo(String day){
        List<StockInCommodity> list = new ArrayList<>();
        List<StockInCommodityODTO> odtoList = shopReportClient.getStockInCommodityFromReturnOrder(day);
        if(CollectionUtils.isNotEmpty(odtoList)){
            for(StockInCommodityODTO odto:odtoList){
                StockInCommodity stockInCommodity = new StockInCommodity();
                BeanUtils.copyProperties(odto,stockInCommodity);
                list.add(stockInCommodity);
            }
        }
        return list;
    }



    /**
     * 小类汇总
     * @param stockInCommodityList
     * @return
     */
    public List<StockInThird> commodityThirdCate(List<StockInCommodity> stockInCommodityList){
        List<StockInThird> thirdCateList = new ArrayList<>();
        Map<String, StockInThird> thirdCateMap = new HashMap<>(stockInCommodityList.size());

        for (StockInCommodity c : stockInCommodityList) {
            String thirdKey = c.getShopId() + "" + c.getCommodityThirdId();
            if (thirdCateMap.get(thirdKey) != null) {
                StockInThird thirdCateTax = thirdCateMap.get(thirdKey);
                thirdCateTax.setOrderQuantity(thirdCateTax.getOrderQuantity().add(c.getOrderQuantity()));
                thirdCateTax.setOrderAmount(thirdCateTax.getOrderAmount().add(c.getOrderAmount()));
                thirdCateTax.setDeliveryQuantity(thirdCateTax.getDeliveryQuantity().add(c.getDeliveryQuantity()));
                thirdCateTax.setDeliveryAmount(thirdCateTax.getDeliveryAmount().add(c.getDeliveryAmount()));
                thirdCateTax.setAllotInQuantity(thirdCateTax.getAllotInQuantity().add(c.getAllotInQuantity()));
                thirdCateTax.setAllotInAmount(thirdCateTax.getAllotInAmount().add(c.getAllotInAmount()));
                thirdCateTax.setAllotOutQuantity(thirdCateTax.getAllotOutQuantity().add(c.getAllotOutQuantity()));
                thirdCateTax.setAllotOutAmount(thirdCateTax.getAllotOutAmount().add(c.getAllotOutAmount()));
                thirdCateTax.setReturnQuantity(thirdCateTax.getReturnQuantity().add(c.getReturnQuantity()));
                thirdCateTax.setReturnAmount(thirdCateTax.getReturnAmount().add(c.getReturnAmount()));
                thirdCateTax.setCloudInQuantity(thirdCateTax.getCloudInQuantity().add(c.getCloudInQuantity()));
                thirdCateTax.setCloudInAmount(thirdCateTax.getCloudInAmount().add(c.getCloudInAmount()));
            } else {
                StockInThird thirdCateTax = new StockInThird();
                SpringUtil.copyProperties(c, thirdCateTax);
                thirdCateMap.put(thirdKey, thirdCateTax);
            }
        }
        for (StockInThird value : thirdCateMap.values()) {
            thirdCateList.add(value);
        }
        insertStockInThird(thirdCateList);
        return thirdCateList;
    }

    /**
     * 中类汇总
     * @param stockInThirdList
     * @return
     */
    public List<StockInSecond> commoditySecondCate(List<StockInThird> stockInThirdList){
        List<StockInSecond> secondCateList = new ArrayList<>();
        Map<String, StockInSecond> thirdCateMap = new HashMap<>(stockInThirdList.size());

        for (StockInThird c : stockInThirdList) {
            String secondKey = c.getShopId() + "" + c.getCommoditySecondId();
            if (thirdCateMap.get(secondKey) != null) {
                StockInSecond secondCateTax = thirdCateMap.get(secondKey);
                secondCateTax.setOrderQuantity(secondCateTax.getOrderQuantity().add(c.getOrderQuantity()));
                secondCateTax.setOrderAmount(secondCateTax.getOrderAmount().add(c.getOrderAmount()));
                secondCateTax.setDeliveryQuantity(secondCateTax.getDeliveryQuantity().add(c.getDeliveryQuantity()));
                secondCateTax.setDeliveryAmount(secondCateTax.getDeliveryAmount().add(c.getDeliveryAmount()));
                secondCateTax.setAllotInQuantity(secondCateTax.getAllotInQuantity().add(c.getAllotInQuantity()));
                secondCateTax.setAllotInAmount(secondCateTax.getAllotInAmount().add(c.getAllotInAmount()));
                secondCateTax.setAllotOutQuantity(secondCateTax.getAllotOutQuantity().add(c.getAllotOutQuantity()));
                secondCateTax.setAllotOutAmount(secondCateTax.getAllotOutAmount().add(c.getAllotOutAmount()));
                secondCateTax.setReturnQuantity(secondCateTax.getReturnQuantity().add(c.getReturnQuantity()));
                secondCateTax.setReturnAmount(secondCateTax.getReturnAmount().add(c.getReturnAmount()));
                secondCateTax.setCloudInQuantity(secondCateTax.getCloudInQuantity().add(c.getCloudInQuantity()));
                secondCateTax.setCloudInAmount(secondCateTax.getCloudInAmount().add(c.getCloudInAmount()));
            } else {
                StockInSecond thirdCateTax = new StockInSecond();
                SpringUtil.copyProperties(c, thirdCateTax);
                thirdCateMap.put(secondKey, thirdCateTax);
            }
        }
        for (StockInSecond value : thirdCateMap.values()) {
            secondCateList.add(value);
        }
        insertStockInSecond(secondCateList);
        return secondCateList;
    }

    /**
     * 大类汇总
     * @param stockInSecondList
     * @return
     */
    public List<StockInFirst> commodityFirstCate(List<StockInSecond> stockInSecondList){
        List<StockInFirst> firstCateList = new ArrayList<>();
        Map<String, StockInFirst> firstCateMap = new HashMap<>(stockInSecondList.size());

        for (StockInSecond c : stockInSecondList) {
            String firstKey = c.getShopId() + "" + c.getCommodityFirstId();
            if (firstCateMap.get(firstKey) != null) {
                StockInFirst firstCateTax = firstCateMap.get(firstKey);
                firstCateTax.setOrderQuantity(firstCateTax.getOrderQuantity().add(c.getOrderQuantity()));
                firstCateTax.setOrderAmount(firstCateTax.getOrderAmount().add(c.getOrderAmount()));
                firstCateTax.setDeliveryQuantity(firstCateTax.getDeliveryQuantity().add(c.getDeliveryQuantity()));
                firstCateTax.setDeliveryAmount(firstCateTax.getDeliveryAmount().add(c.getDeliveryAmount()));
                firstCateTax.setAllotInQuantity(firstCateTax.getAllotInQuantity().add(c.getAllotInQuantity()));
                firstCateTax.setAllotInAmount(firstCateTax.getAllotInAmount().add(c.getAllotInAmount()));
                firstCateTax.setAllotOutQuantity(firstCateTax.getAllotOutQuantity().add(c.getAllotOutQuantity()));
                firstCateTax.setAllotOutAmount(firstCateTax.getAllotOutAmount().add(c.getAllotOutAmount()));
                firstCateTax.setReturnQuantity(firstCateTax.getReturnQuantity().add(c.getReturnQuantity()));
                firstCateTax.setReturnAmount(firstCateTax.getReturnAmount().add(c.getReturnAmount()));
                firstCateTax.setCloudInQuantity(firstCateTax.getCloudInQuantity().add(c.getCloudInQuantity()));
                firstCateTax.setCloudInAmount(firstCateTax.getCloudInAmount().add(c.getCloudInAmount()));
            } else {
                StockInFirst thirdCateTax = new StockInFirst();
                SpringUtil.copyProperties(c, thirdCateTax);
                firstCateMap.put(firstKey, thirdCateTax);
            }
        }
        for (StockInFirst value : firstCateMap.values()) {
            firstCateList.add(value);
        }
        insertStockInFirst(firstCateList);
        return firstCateList;
    }

    /**
     * 门店汇总
     * @param stockInFirstList
     * @return
     */
    public List<StockInShop> commodityShopCate(List<StockInFirst> stockInFirstList){
        List<StockInShop> shopCateList = new ArrayList<>();
        Map<String, StockInShop> shopCateMap = new HashMap<>(stockInFirstList.size());

        for (StockInFirst c : stockInFirstList) {
            String shopKey = c.getShopId() + "";
            if (shopCateMap.get(shopKey) != null) {
                StockInShop shopCateTax = shopCateMap.get(shopKey);
                shopCateTax.setOrderQuantity(shopCateTax.getOrderQuantity().add(c.getOrderQuantity()));
                shopCateTax.setOrderAmount(shopCateTax.getOrderAmount().add(c.getOrderAmount()));
                shopCateTax.setDeliveryQuantity(shopCateTax.getDeliveryQuantity().add(c.getDeliveryQuantity()));
                shopCateTax.setDeliveryAmount(shopCateTax.getDeliveryAmount().add(c.getDeliveryAmount()));
                shopCateTax.setAllotInQuantity(shopCateTax.getAllotInQuantity().add(c.getAllotInQuantity()));
                shopCateTax.setAllotInAmount(shopCateTax.getAllotInAmount().add(c.getAllotInAmount()));
                shopCateTax.setAllotOutQuantity(shopCateTax.getAllotOutQuantity().add(c.getAllotOutQuantity()));
                shopCateTax.setAllotOutAmount(shopCateTax.getAllotOutAmount().add(c.getAllotOutAmount()));
                shopCateTax.setReturnQuantity(shopCateTax.getReturnQuantity().add(c.getReturnQuantity()));
                shopCateTax.setReturnAmount(shopCateTax.getReturnAmount().add(c.getReturnAmount()));
                shopCateTax.setCloudInQuantity(shopCateTax.getCloudInQuantity().add(c.getCloudInQuantity()));
                shopCateTax.setCloudInAmount(shopCateTax.getCloudInAmount().add(c.getCloudInAmount()));
            } else {
                StockInShop shopCateTax = new StockInShop();
                SpringUtil.copyProperties(c, shopCateTax);
                shopCateMap.put(shopKey, shopCateTax);
            }
        }
        for (StockInShop value : shopCateMap.values()) {
            shopCateList.add(value);
        }
        insertStockInShop(shopCateList);
        return shopCateList;
    }

    /**
     * 批量插入商品级别数据
     * @param cs
     */
    public void batchInsertStockInCommodity(List<StockInCommodity> cs) {
        int index = 0;
        int count = 2000;
        while (true) {
            List<StockInCommodity> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                insertStockInCommodity(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    public void insertStockInCommodity(List<StockInCommodity> list) {
        stockInCommodityMapper.insertList(list);
    }
    public void insertStockInThird(List<StockInThird> list) {
        stockInThirdMapper.insertList(list);
    }
    public void insertStockInSecond(List<StockInSecond> list) {
        stockInSecondMapper.insertList(list);
    }
    public void insertStockInFirst(List<StockInFirst> list) {
        stockInFirstMapper.insertList(list);
    }
    public void insertStockInShop(List<StockInShop> list) {
        stockInShopMapper.insertList(list);
    }

    /**
     * 删除 门店实际入库汇总
     * @param day
     */
    public void deleteShopStockInTax(String day) {
        Example commodityExample = new Example(StockInCommodity.class);
        commodityExample.createCriteria().andEqualTo("dateTime", day);
        stockInCommodityMapper.deleteByExample(commodityExample);

        Example thirdExample = new Example(StockInThird.class);
        thirdExample.createCriteria().andEqualTo("dateTime", day);
        stockInThirdMapper.deleteByExample(thirdExample);

        Example secondExample = new Example(StockInSecond.class);
        secondExample.createCriteria().andEqualTo("dateTime", day);
        stockInSecondMapper.deleteByExample(secondExample);

        Example firstExample = new Example(StockInFirst.class);
        firstExample.createCriteria().andEqualTo("dateTime", day);
        stockInFirstMapper.deleteByExample(firstExample);


        Example shopExample = new Example(StockInShop.class);
        shopExample.createCriteria().andEqualTo("dateTime", day);
        stockInShopMapper.deleteByExample(shopExample);
    }


    /**
     * 门店商品结算报表
     * @param vo
     * @return
     */
    public TablePageInfo<ShopStockInReportODTO> shopStockInReport(ShopStockInReportQueryIDTO vo) {
        QYAssert.notNull(vo.getReportType(),"参数错误");

        if(StringUtils.isNotBlank(vo.getSettleTimeBegin()) && StringUtils.isNotBlank(vo.getSettleTimeEnd())){
            DateTime beginDate = DateTime.parse(vo.getSettleTimeBegin(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            DateTime endDate = DateTime.parse(vo.getSettleTimeEnd(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "查询时间段不能大于31天");

            vo.setSettleTimeBegin(vo.getSettleTimeBegin() + " 00:00:00");
            vo.setSettleTimeEnd(vo.getSettleTimeEnd() + " 23:59:59");
        }

        TablePageInfo info = new TablePageInfo();
        List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            info.setList(null);
            return info;
        }

        if (StringUtils.isNotBlank(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (null != vo.getShopId()) {
                    shopIdList.retainAll(Arrays.asList(vo.getShopId()));
                }
                if (CollectionUtils.isEmpty(shopIdList)) {
                    info.setList(null);
                    return info;
                }
            }else {
                info.setList(null);
                return info;
            }
        }
        vo.setShopIdList(shopIdList);

        PageInfo<ShopStockInReportODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->{
            stockInCommodityMapper.shopStockInReport(vo);
        });

        // 查询sum值
        ShopStockInReportODTO sum = new ShopStockInReportODTO();
        if(CollectionUtils.isNotEmpty(pageInfo.getList())){
            List<Long> commodityIds = pageInfo.getList().stream().map(ShopStockInReportODTO::getCommodityId).collect(Collectors.toList());
            List<Long> shopIds = pageInfo.getList().stream().map(ShopStockInReportODTO::getShopId).collect(Collectors.toList());

//            Map<Long, OrgAndParentInfoODTO> orgMap = shopService.getOrgMap(shopIds);
            // 一品多码
            Map<Long, String> commodityMap = commodityService.getCommodityBarCodeMap(commodityIds);

//            List<Category> cateList = categoryMapper.selectAll();
//            Map<Long, String> cateMap = cateList.stream().collect(Collectors.toMap(Category::getId,Category::getCateName,(key1 , key2)-> key2));

            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, ShopODTO> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, Function.identity()));

            pageInfo.getList().forEach(e -> {
                if(vo.getReportType() == ShopStockInReportQueryIDTO.ReportType.COMMODITY){
                    if(StringUtils.isNotBlank(commodityMap.get(e.getCommodityId()))){
                        String[] barCode = commodityMap.get(e.getCommodityId()).split(",");
                        e.setBarCode(barCode[0]);
                        List barCodeList = java.util.Arrays.asList(barCode);
                        e.setBarCodeList(barCodeList);

//                        e.setCommodityFirstCateName(cateMap.get(e.getCommodityFirstId()));
//                        e.setCommoditySecondCateName(cateMap.get(e.getCommoditySecondId()));
//                        e.setCommodityThirdCateName(cateMap.get(e.getCommodityThirdId()));
                    }
                }

//                OrgAndParentInfoODTO orgDto = orgMap.get(e.getShopId());
//                if(orgDto != null){
//                    e.setOrgName(orgDto.getParentOrgName());
//                }
                ShopODTO shopODTO = shopMap.get(e.getShopId());
                if(shopODTO != null){
                    e.setStoreCode(shopODTO.getStoreCode());
 //                   e.setShopName(shopODTO.getShopName());
                }
            });

            // 查询sum值
            sum = stockInCommodityMapper.shopStockInReportSum(vo);
        }

  //      renderService.render(pageInfo.getList(), "/shopStockIn/shopStockInReport");

        TablePageInfo tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }
}
