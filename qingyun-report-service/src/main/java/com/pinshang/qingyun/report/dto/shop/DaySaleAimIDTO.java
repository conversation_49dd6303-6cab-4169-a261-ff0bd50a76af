package com.pinshang.qingyun.report.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DaySaleAimIDTO{
    private Long id;

    @ApiModelProperty("门店")
    private  Long shopId;

    @ApiModelProperty("日期:(yyyy-MM-dd)")
    private String saleTime;

    @ApiModelProperty("目标销售额")
    private BigDecimal aimSaleAmount;

    @ApiModelProperty("备注")
    private  String remark;

}
