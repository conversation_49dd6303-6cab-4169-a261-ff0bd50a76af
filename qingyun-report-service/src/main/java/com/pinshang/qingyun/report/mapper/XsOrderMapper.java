package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportIDTO;
import com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportODTO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XsOrderMapper {

    List<XsOrderReportODTO> selectOrderUserCountWithDay(XsOrderReportIDTO vo);

    List<XsOrderReportODTO> selectOrderReportList(XsOrderReportIDTO vo);

    List<XsOrderReportODTO> selectOutOrderReportList(XsOrderReportIDTO vo);
}
