package com.pinshang.qingyun.report.dto.pos;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class OrderItem {

	private Long orderId;

	/** 商品ID */
	private Long commodityId;

	/** 商品数量 */
	private BigDecimal quantity;

	/** 实际商品数量 */
	private BigDecimal realQuantity;

	/** 商品单价 */
	private BigDecimal price;

	/** 成本价 */
	private BigDecimal weightPrice;

	/** 特价 */
	private BigDecimal promotionPrice;

	/** 会员价 */
	private BigDecimal memberPrice;

	/** 促销计算前价格 单价*/
	private BigDecimal bySalesPrice;

	/** 售价(最终分摊价格) */
	private BigDecimal salePrice;

	/** 0 普通商品 1:满x元减y元;2:每满x元减y元;3:第x件y折;4:满x件y折;5:买x件赠y件(非酒类);6:买x付y,7-特价,8 优惠券 ( 促销标志，0=普通商品，1＝赠品，2＝特价 V1版本标识) */
	private Integer promotionStatus;

	/** 商品总金额(优惠计算后保存的金额),原金额=quantity*价格 */
	private BigDecimal totalAmount;

	/** 实发金额 */
	private String realAmount;

	/** 成本总金额 */
	private BigDecimal totalWeight;

	/** 税率 */
	private BigDecimal taxRate;

	/** 实际分摊的优惠券金额 */
	private BigDecimal couponAmount;

	/** 是否有退货: 1=有退货，0＝未退货 */
	private Integer returnStatus;

	/** 1=称重，0=非称重 */
	private Integer isWeight;

	/** 是否已经打折过  0 没有 1 有*/
	private Integer isDiscounted;
	/** pos显示标签(1:惠 2:折 3:赠 4:促)(线下) */
	private Integer posItemTag;

	/** 线下pos上一步需要删除的行 **/
	private Integer goBackDel;

	private String weightCode;

	private String barCode;

	/** 优惠码id **/
	private Long posCouponId;
}
