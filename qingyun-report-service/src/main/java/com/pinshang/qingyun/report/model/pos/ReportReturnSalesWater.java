package com.pinshang.qingyun.report.model.pos;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2021/7/7 18:02
 */
@Data
@ToString
@Entity
@Table(name = "t_pos_report_return_sales_water")
@NoArgsConstructor
public class ReportReturnSalesWater {
    private Long id;
    /** 订单号*/
    private String orderCode;
    /** 交易时间*/
    private Date saleTime;
    /** 货号*/
    private String commodityCode;
    /** 品名*/
    private String commodityName;
    /** 单位*/
    private String unit;
    /** 规格*/
    private String commoditySpec;
    /** 成交价格*/
    private BigDecimal transactionPrice;
    /** 数量*/
    private BigDecimal quantity;
    /** 成交金额*/
    private BigDecimal transactionAmount;
    /** 零售价*/
    private BigDecimal retailPrice;
    /** 零售金额*/
    private BigDecimal retailAmount;
    /** 零售金额/零售价*/
    private BigDecimal transactionRetailRate;
    /** 后台特价*/
    private BigDecimal backgroudBargainPrice;
    /** 会员价*/
    private BigDecimal memberPrice;
    /** 手动议价后*/
    private BigDecimal handBargainEnd;
    /** 手动折扣后*/
    private BigDecimal handDiscountEnd;
    /** 后台促销分摊后*/
    private BigDecimal backgroundPromotionShareEnd;
    /** 整单议价分摊后*/
    private BigDecimal orderBargainShareEnd;
    /** 整单折扣分摊后*/
    private BigDecimal orderDiscountShareEnd;
    /** 条形码*/
    private String barCode;
    /** 会员卡号*/
    private String memberCardNo;
    /** 会员姓名*/
    private String memberName;
    /** 订单支付金额*/
    private BigDecimal orderTotalAmount;
    /** 大类ID*/
    private Long commodityFirstId;
    /** 大类*/
    private String commodityFirstKind;
    /** 中类ID*/
    private Long commoditySecondId;
    /** 中类*/
    private String commoditySecondKind;
    /** 小类ID*/
    private Long commodityThirdId;
    /** 小类*/
    private String commodityThirdKind;
    /** 品牌名称*/
    private String brandName;
    /** 收银员编号*/
    private String employeeNumber;
    /** 收银员姓名*/
    private String createName;
    /** 授权人编号*/
    private String opUserCode;
    /** 授权人名称*/
    private String opUserName;
    /** 退货原因*/
    private String returnReason;
    /** 供应商名称*/
    private String supplierName;
    /** 原单号*/
    private String originOrderCode;
    /** 促销编号*/
    private String promotionCode;
    /** 门店编码*/
    private Long shopId;
    /** 门店名称*/
    private String shopName;
    /** pos机*/
    private Long posMacId;
    /** POS机号*/
    private String macCode;
    /** 称重码*/
    private String weightCode;
    /** 折扣码*/
    private String couponCode;
    /** 原单交易时间*/
    private Date originSaleTime;
    /** 收银渠道*/
    private String transactionChannel;

}
