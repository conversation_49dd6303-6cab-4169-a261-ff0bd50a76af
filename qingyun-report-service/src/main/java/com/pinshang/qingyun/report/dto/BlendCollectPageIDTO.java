package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/12/16
 */
@Data
public class BlendCollectPageIDTO extends Pagination {

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("收款项 1: 聚合  2:现金 ")
    private Integer collectType;

    @ApiModelProperty("开始时间yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("结束时间yyyy-MM-dd")
    private String endDate;

    private List<String> payTypeList;

    private List<String> dateList;
}
