package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrderDeleteIDTO extends Pagination {

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("操作日期开始时间yyyy-MM-dd HH:mm:ss")
    private String beginDate;

    @ApiModelProperty("操作日期结束时间yyyy-MM-dd HH:mm:ss")
    private String endDate;

    @ApiModelProperty("操作类型：1挂单删除 2挂单未结算 3整单取消")
    private Integer operateType;

    @ApiModelProperty("操作人")
    private Integer operateId;

    @ApiModelProperty("门店code")
    private String shopCode;

    @ApiModelProperty("门店id列表-后端使用")
    private List<Long> shopIdList;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

    @ApiModelProperty("开始小时")
    private String hourBegin;
    @ApiModelProperty("结束小时")
    private String hourEnd;
    @ApiModelProperty("作废订单金额≥")
    private String compareOrderAmount;
}
