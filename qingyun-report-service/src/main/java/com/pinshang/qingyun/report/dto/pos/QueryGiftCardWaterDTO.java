package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QueryGiftCardWaterDTO extends Pagination {

    @ApiModelProperty("门店类型")
    private Integer shopType;

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("开始时间")
    private String dateStart;

    @ApiModelProperty("结束时间")
    private String dateEnd;

    @ApiModelProperty("员工编号")
    private String employeeNumber;

    @ApiModelProperty("销售渠道")
    private Integer cardSourceType;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("支付类型(code)")
    private String payType;

    @ApiModelProperty("模糊查询，商品名称或者code")
    private String commodityInfo;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("序列号")
    private String cardSn;

    //---代销

    @ApiModelProperty("部门编号")
    private String orgNum;

    @ApiModelProperty("1清美卡售卖 2香烟售卖")
    private Integer type;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("代销商id")
    private Long consignmentId;


}
