package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MemberExceptionUseIDTO extends Pagination {

	@ApiModelProperty(position = 1, value ="销售日期yyyy-MM-dd HH:mm:ss")
	private String beginTime;

	@ApiModelProperty(position = 2, value ="销售日期yyyy-MM-dd HH:mm:ss")
	private String endTime;

	@ApiModelProperty(position = 3, value ="会员卡号")
	private String cardNo;

	@ApiModelProperty(position = 4, value ="门店名称")
	private Long shopId;

	@ApiModelProperty(position = 4, value ="门店编码")
	private String shopCode;

	@ApiModelProperty(position = 5, value = "门店id列表-后端使用")
	private List<Long> shopIdList;

	@ApiModelProperty(position = 5, value = "是否剔除异常会员 1剔除 0不剔除")
	private Integer removeAbnormalUser;

	@ApiModelProperty(position = 6, value = "需要剔除的公共会员账号-后端使用")
	private List<Long> nonIntegralUserIdList;
}
