package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName HelpCardTradeWaterPageODTO
 * <AUTHOR>
 * @Date 2023/3/13 11:09
 * @Description HelpCardTradeWaterPageODTO
 * @Version 1.0
 */
@Data
public class HelpCardTradeWaterPageODTO {
    @ApiModelProperty("帮困卡号")
    @ExcelProperty(value = "帮困卡号")
    private String cardNo;

    @ApiModelProperty("设备号")
    @ExcelProperty(value = "设备号")
    private String payCode;

    @ApiModelProperty("交易时间")
    @ExcelProperty(value = "交易时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date transactionDate;

    @ApiModelProperty("帮困卡成交金额")
    @ExcelProperty(value = "帮困卡成交金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal amount;

}
