package com.pinshang.qingyun.report.dto.pos.joinShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单品销售报表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SingleProductSalesIDTO extends Pagination {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("开始时间段")
    private String startTime;

    @ApiModelProperty("结束时间段")
    private String EndTime;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("分类id")
    private Long cateId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("是否当天")
    private Boolean currentDay;
}
