package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CashierWaterIDTO extends Pagination {

    @ApiModelProperty(position = 1, required = false, value = "收银日期开始时间yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @ApiModelProperty(position = 2, required = false, value = "收银日期结束时间yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(position = 3, required = false, value = "收银员编号")
    private String employeeNumber;

    @ApiModelProperty(position = 4, required = false, value = "POS机号")
    private String macCode;

    @ApiModelProperty(position = 5, required = false, value = "门店id")
    private Long shopId;

    @ApiModelProperty(position = 6, required = false, value = "销售方式(1:销售, 2:退货)")
    private Integer saleType;

    @ApiModelProperty(position = 7, required = false, value = "订单号")
    private String orderCode;

    @ApiModelProperty(position = 8, required = false, value = "付款方式(编码,ZF开头)")
    private String payType;

    @ApiModelProperty(position = 9, required = false, value = "会员卡号")
    private String memberCardNo;

    @ApiModelProperty(position = 9, required = false, value = "小票金额(元)")
    private String beginAmount;

    @ApiModelProperty(position = 10, required = false, value = "小票金额(元)")
    private String endAmount;

    private List<Long> shopIdList;

    @ApiModelProperty(position = 11,  value = "收银渠道:8,联网收银 9,本地收银")
    private String cashChannel;

    @ApiModelProperty(position = 12,  value = "第三方订单号")
    private String thirdPartyOrderId;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;


}