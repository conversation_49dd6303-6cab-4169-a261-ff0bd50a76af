package com.pinshang.qingyun.report.controller.pos;

import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.report.service.pos.PosHelpCardTradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * @ClassName PosHelpCardTradeController
 * <AUTHOR>
 * @Date 2023/2/28 17:20
 * @Description PosHelpCardTradeController
 * @Version 1.0
 */
@Api(value = "posHelpCardTrade")
@RestController
@RequestMapping("posHelpCardTrade")
@Slf4j
public class PosHelpCardTradeController {
    @Autowired
    private PosHelpCardTradeService posHelpCardTradeService;

    @ApiOperation(value = "自动对账job", notes = "自动对账job", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/helpCardAutoCheckJob")
    public Boolean helpCardAutoCheckJob(@RequestParam("date") String date) throws IOException {
        return posHelpCardTradeService.helpCardAutoCheckJob(date);
    }
}
