package com.pinshang.qingyun.report.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by da<PERSON><PERSON> on 2017/4/19.
 */
public enum ExcelSheetTitleEnum {

    CATEGORY_SALES_PERCENT_REPORT("category_sales_percent", "类别销售周同比报表",new String[] {"大类", "今日销售额", "今日销售额占比", "上周销售额", "销售额同比"}),
    SHOP_SALES_PERCENT_REPORT("shop_sales_percent", "门店销售周同比报表",new String[] {"门店", "今日销售额", "上周销售额", "销售额同比", "今日来客","上周来客","来客同比","今日客单价","上周客单价","客单价同比"}),

    DAY_SAIL_AIM("day_sale_aim","日销售目标管理报表", new String[] {"日期","门店编码","门店名称","目标销售金额","备注"}),
    MONTH_SAIL_AIM("month_sale_aim","月销售目标管理报表", new String[] {"月份","门店编码","门店名称","目标销售金额","备注"}),

    DAY_SALES_COMPLETION_RATE("day_sales_completion_rate","日销售完成率报表",new String[]{"序号","日期","门店编号","门店名称","目标销售金额","已完成销售金额","销售额完成率"}),
    MONTH_SALES_COMPLETION_RATE("month_sales_completion_rate","月销售完成率报表",new String[]{"序号","月份","门店编号","门店名称","目标销售金额","已完成销售金额","销售额完成率"}),

    SHOP_SHORT_DELIVERY_HQ("shop_short_delivery","短交报表", new String[] {"门店类型","门店名称","送货日期","订单号","大类", "中类", "小类" ,"条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","发货数量","差异数量","短交比例","工厂","生产组", "客户编号","下单人","线路组","发货仓库","经营模式"}),
    SHOP_SHORT_DELIVERY("shop_short_delivery","短交报表", new String[] {"门店类型","门店名称","送货日期","订单号","大类", "中类", "小类" ,"条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","发货数量","差异数量","短交比例","工厂","生产组", "客户编号","下单人"}),
    SHOP_STOCK_ADJUST("shop_stock_adjust","库存调整报表", new String[] { "调整日期","调整单号","分类","条形码","商品编码","商品名称","规格","单位","调整类型","调整数量","调整原因"}),
    RETURN_ORDER_DETAILS_REPORT_LIST("order_details_report_list","退单明细报表",new String[] {"门店", "退货日期","退单编号","商品分类","条形码","商品编码","商品名称","规格","单位","单价","退货数量","退货金额","退货原因","备注"}),

    SHOP_COMMODITY_TAX_SHOP("shop_commodity_tax","商品总表（含税）", new String[] { "门店名称","总销售额","线上销售额","线下销售额","线下销售额占比%","毛利率%","线下来客数","线下客单价"}),
    SHOP_COMMODITY_TAX("shop_commodity_tax","商品总表（含税）", new String[] { "门店名称","条形码","商品编码","商品名称","规格","总销售额","线上销售额","线下销售额","线下销售额占比%","毛利率%"}),
    SHOP_COMMODITY_TAX_CATE("shop_commodity_tax","商品总表（含税）", new String[] { "门店名称","分类","总销售额","线上销售额","线下销售额","线下销售额占比%","毛利率%"}),

    ACTUAL_RECEIPT_ANALYSIS_REPORT("actual_receipt_analysis_report","门店实收商品分析表",new String[] {"门店名称","一级分类","条形码","商品编码","商品名称","规格","订货数量","实发数量","实收数量","实收差异数量","供货价","供货金额","供应商","采购员"}),
    ACTUAL_RECEIPT_ANALYSIS_REPORT_MONTH("actual_receipt_analysis_report_month","门店实收商品分析表(月度)",new String[] {"门店名称","一级分类","条形码","商品编码","商品名称","规格","订货数量","实发数量","实收数量","实收差异数量","供货价","供货金额","供应商","采购员"}),

    SETTLEMENT_DETAIL_REPORT("settlement_detail_report","结算明细报表",new String[]{"门店","送货日期","结算日期","订单编号","预订单编号","分类","条形码","商品编码","商品名称","规格","计量单位","订货价","订货数量","实发数量","结算金额"}),

    OPERATING_REPORT_LIST("operating_report_list","运营报表",new String[] {}),//此title是动态设置的，慎用

    APP_OPERATE_REPORT("app_operate_report","app运营报表",new String[] {"门店","下单总用户数","新用户数","老用户数","下单订单数","下单订单总额","出库订单数","出库净额","单均价","退货商品个数","退货金额"}),

    SHOP_COMMODITY_ONLINE_REPORT("shop_commodity_online_report","商品销售明细表", new String[] { "门店名称","大类","中类","小类","条形码","商品编码","商品名称","规格","品牌","出库数量","出库净额","退货数量","退货金额","销售额","毛利率%","基准价","平均售价","顾客数","抵用券金额","满减金额","满折金额"}),
    COMMODITY_STOCK_REPORT("commodity_stock_report","商品库存情况表", new String[] {"门店", "大类", "中类", "小类", "条形码", "商品编码", "商品名称", "规格", "品牌", "商品状态", "上下架状态", "移动平均价", "实时库存", "库存金额", "本月期初库存", "本月入库数量", "当前进价"}),

    SHOP_CATE_USER("shop_cate_user","分类顾客数报表", new String[] { "门店","分类","顾客数","销售数量","销售额","件单价"}),

    COMMODITY_DETAIL_REPORT("commodity_detail_report", "商品信息明细报表", new String[]{"门店", "大类", "中类", "小类", "条形码", "商品编码", "商品名称", "规格", "品牌", "副标题", "商品状态", "上下架状态", "包装类型", "是否称重", "起卖重量(g)", "计量单位", "税率", "是否即食", "存储条件", "保质期", "产地", "物流模式", "默认供应商", "销售箱规", "采购箱规", "默认主图", "主图数量", "长图", "移动平均价", "进价", "基准价", "线上特价", "前7天销售数量", "前30天销售数量", "年至今销售数量", "当前库存"}),
    ORDER_SALES_ANALYSIS_REPORT("order_sales_analysis_report","订货销售分析报表",new String[]{"门店","分类","订货金额","订货占比","销售金额","销售占比","退货金额","销售与订货差额"}),
    CATE_AVERAGE_AMOUNT("cate_average_amount","类别客单周同比",new String[]{"门店","分类","今日客单量","上周客单量","客单量同比","今日客单价","上周客单价","客单量同比"}),
    MONTH_CASHIER_REPORT("monthCashierReport","收银对账月报表",new String[]{"门店编码","门店名称","现金实收","银行卡手续费","银行卡实收","聚合手续费","聚合实收","支付宝（记账）手续费","支付宝（记账）实收","微信（记账）手续费","微信（记账）实收","ok卡手续费","ok卡实收","手续费合计","实收合计"}),
    SHOP_ACTUAL_SUMMARY("shopActualSummary","门店实收汇总表",new String[]{"门店", "现金", "银行卡", "聚合", "OK卡", "支付宝（记账）", "微信（记账）", "合计"}),
    RECONCILIATION_OF_RURNOVER("reconciliationOfRurnover","门店营业款对账明细",new String[]{"日期","现金","","","","银行卡","","","","聚合","","","","OK卡","","","","支付宝（记账）","","","","微信（记账）","","","","合计"}),
    EXPORT_RECONCILIATION_STATEMENT("exportReconciliationStatement","门店营业款对账总表",new String[]{"门店","现金","","","","银行卡","","","","聚合","","","","OK卡","","","","支付宝（记账）","","","","微信（记账）","","","","合计"}),

    SHOP_REAL_DELIVERY("shop_real_delivery","商品实发汇总报表", new String[] {"商品编码","商品名称","规格","条码","一级品类","计量单位","订货数量","实发数量","实发金额","差异数量","工厂","生产组","车间"}),

    SHOP_REAL_DELIVERY_STORE_TYPE("shop_real_delivery_store_type","商品实发汇总报表(客户类型)", new String[] {"客户类型","商品编码","商品名称","规格","条码","一级品类","计量单位","订货数","实发数量","实发金额","默认仓库","工厂","生产组","车间"}),

    // POS----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    CATE_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","类别","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"类别","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","类别编码","类别","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"类别编码","类别","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),


    COMMODITY_SALES_SUMMARY_WITHSHOP("commodity_sales_summary_withshop","商品销售汇总",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","零售价","销售数量","退货数量","赠送数量","销售金额","退货金额","赠送金额","让利金额","数量小计","金额小计","不含税金额","税额"}),
    COMMODITY_SALES_SUMMARY_NOSHOP("commodity_sales_summary_noshop","商品销售汇总",new String[] {"大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","零售价","销售数量","退货数量","赠送数量","销售金额","退货金额","赠送金额","让利金额","数量小计","金额小计","税率","不含税金额","税额"}),

    COMMODITY_SALES_SUMMARY_ANALYSIS_WITHSHOP("commodity_sales_summary_analysis_withshop","商品销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),
    COMMODITY_SALES_SUMMARY_ANALYSIS_NOSHOP("commodity_sales_summary_analysis_noshop","商品销售汇总分析",new String[] {"大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),

    CATE_FIRST_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","大类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_FIRST_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"大类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","大类名称","中类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"大类名称","中类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_WITHSHOP("cate_sales_summary_withshop","类别销售汇总",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_NOSHOP("cate_sales_summary_noshop","类别销售汇总",new String[] {"大类名称","中类名称","小类名称","销售数量","销售金额","退货数量","退货金额","赠送数量","让利金额","数量小计","金额小计","不含税金额","税额","销售额占比"}),

    CATE_FIRST_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_FIRST_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"大类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","中类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_SECOND_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"大类名称","中类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_ANALYSIS_WITHSHOP("cate_sales_summary_analysis_withshop","类别销售汇总分析",new String[] {"部门","门店编码","门店","大类名称","中类名称","小类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),
    CATE_THIRD_SALES_SUMMARY_ANALYSIS_NOSHOP("cate_sales_summary_analysis_noshop","类别销售汇总分析",new String[] {"大类名称","中类名称","小类名称","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本","销售额占比"}),


    SHOP_SALES_SUMMARY_ANALYSIS_WITHSHOP("shop_sales_summary_analysis_withshop","门店销售汇总分析",new String[] {"部门","门店编码","门店","销售金额","销售数量","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),
    SHOPDAY_SUMMARY_ANALYSIS_NOSHOP("shopday_sales_summary_analysis_noshop","门店销售汇总分析",new String[] {"销售日期","部门","门店编码","门店","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),

    BEST_SELLS_REPORT("best_sells_report","畅销品排行",new String[] {"一级分类","商品编码","条形码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","平均售价"}),
    SALES_PROMOTION_REPORT("sales_promotion_report","促销销售报表",new String[] {"门店","分类","条形码","商品编码","商品名称","规格","单位","零售价","数量","销售金额","让利金额","赠送金额","特价类型"}),

    CASHIER_DISCOUNT_REPORT("cashier_discount_report","收银员让利报表",new String[] {"销售日期","门店编码","门店","收银员编号","收银员名称","原价金额","实收金额","让利金额"}),

    GUEST_SUMMARY_BY_POS_REPORT("guest_summary_by_pos_report","客单分析报表按pos机",new String[] {"销售日期","门店编码","门店","pos机号","pos机名称","客单量","客单金额","客单总量","客单总金额","金额占比","客单占比","平均客单价"}),
    GUEST_SUMMARY_BY_SHOP_REPORT("guest_summary_by_shop_report","客单分析报表按门店",new String[] {"销售日期","门店","客单量","客单金额","平均客单价","连带率"}),

    GUEST_SUMMARY_HOUR_REPORT("guest_summary_hour_report","客单时段分析报表",new String[] {"时段","客单量","客单占比","客单金额","金额占比","平均客单价"}),
    //CashierDailyList

    CASHIER_DAILY_LIST_BY_POS_DY_REPORT("cashier_daily_list_by_pos_dy_report","收银日报按POS机汇总",new String[] {"门店编码","门店名称","收银员编号","收银员名称", "POS机号", "销售方式","合计"}, new String[] {"shopCode", "shopName", "saleNumber", "saleName", "macCode", "saleType", "total"}),
    CASHIER_DAILY_LIST_BY_POS_DY_QUERY("cashier_daily_list_by_pos_dy_copy_query","收银日报按POS机汇总",new String[] {"门店编码","门店名称","收银员编号","收银员名称","POS类型", "POS机号", "销售方式","合计"}, new String[] {"shopCode", "shopName", "saleNumber", "saleName", "posTypeName", "macCode", "saleType", "total"}),

    CASHIER_DAILY_LIST_DY_REPORT("cashier_daily_list_dy_report","收银日报按收银员汇总",new String[] {"门店编码","门店名称","收银员编号","收银员名称","合计"}, new String[] {"shopCode", "shopName", "saleNumber", "saleName", "total"}),
    CASHIER_DAILY_LIST_DY_QUERY("cashier_daily_list_dy_query","收银日报按收银员汇总",new String[] {"门店编码","门店名称","POS类型","收银员编号","收银员名称","合计"}, new String[] {"shopCode", "shopName", "posTypeName", "saleNumber", "saleName", "total"}),

    CASHIER_DAILY_LIST_BY_SHOP_DY_REPORT("cashier_daily_list_by_shop_dy_report","收银日报按门店汇总",new String[] {"门店编码","门店名称","合计"}, new String[]{"shopCode", "shopName", "total"}),
    CASHIER_DAILY_LIST_BY_SHOP_DY_QUERY("cashier_daily_list_by_shop_dy_query","收银日报按门店汇总",new String[] {"门店编码","门店名称","POS类型","合计"}, new String[]{"shopCode", "shopName", "posTypeName", "total"}),

    CASHIER_DAILY_LIST_BY_DATE_DY_REPORT("cashier_daily_list_by_date_dy_report","收银日报按日期",new String[] {"门店编码","门店名称","收银日期","合计"}, new String[] {"shopCode", "shopName", "createTime", "total"}),
    CASHIER_DAILY_LIST_BY_DATE_DY_QUERY("cashier_daily_list_by_date_dy_query","收银日报按日期",new String[] {"门店编码","门店名称","收银日期","POS类型","合计"}, new String[] {"shopCode", "shopName", "createTime", "posTypeName", "total"}),

    CASHIER_DAILY_LIST_BY_PAYMENT("cashier_daily_list_by_payment","收银日报按付款方式",new String[] {"门店编码","门店名称","POS类型","收银员编号","收银员名称","销售方式","付款方式","金额","销售额","退款额"}),

    CASHIER_RECONCILED_LIST("cashier_reconciled_list","收银员对账",new String[] {"门店编码","门店名称","收银员账号","收银员名称","POS类型","POS机号","收款方式","净销售额","销售金额","销售笔数","退货金额","退货笔数"}),
    ACCOUNT_RECORD_LIST("account_record_list","前台收银对账记录",new String[] {"门店编码","门店名称","POS类型","POS机号","收银员编号","收银员名称","对账时间", "对账渠道","首笔交易时间","末笔交易时间","合计总金额","现金小计","银行卡小计","OK卡小计","记账(聚合)小计","记账(索帮)小计","聚合支付小计","数字货币小计","索迪斯小计","悦享APP小计","OK卡APP小计","积分支付小计","抹零小计","清美卡","清美App支付","帮困卡","清美换货","换货(票)","售卡收入合计总金额","售卡聚合支付金额","售卡现金金额","代销收入总金额","代销聚合支付金额","代销现金金额","营收/售卡/代销总现金合计"}),
    DIURNAL_RECONCILIATION_LIST("diurnal_reconciliation_list","收银员日对账",new String[] {"门店编码","门店名称","收银日期","收银员编号","收银员名称","销售方式","收款方式","销售金额"}),
    CASHIER_WATER_REPORT("cashier_water_report","收银流水报表",new String[] {"门店编码", "门店名称", "订单号", "收银渠道", "收银时间", "销售金额", "销售方式", "付款金额", "付款方式", "第三方支付单号","会员卡号", "会员姓名", "收银员编号", "收银员名称","POS类型", "POS机号", "退货原单号"}),
    SALES_WATER_REPORT("sales_water_report","销售流水报表",new String[] {"销售方式", "订单号", "收银渠道", "销售时间", "货号", "品名", "单位", "规格", "成交价格", "数量", "份数","成交金额", "零售价", "零售价金额", "成交/零售价", "后台特价", "会员价", "手动单品改价后", "手动单品折扣后", "后台促销分摊后", "手动整单改价分摊后", "手动整单折扣分摊后", "优惠券分摊后", "券序列号","销售条码", "会员卡号", "会员姓名", "大类名称", "中类名称", "小类名称", "品牌名称", "收银员编号", "收银员名称", "授权人编号", "授权人名称", "折扣/特价码", "退货原因", "供应商名称", "原单号", "促销编号", "优惠券编码","门店名称", "POS类型", "POS机号", "档口"}),
    UNION_CONFIG_REPORT("union_config_report","银联扫码配置信息导出--清美鲜食门店清单",new String[] {"pos通编号", "pos通终端号", "商户名称", "分店名称", "分店地址"}),
    MEMBER_ACCOUNT_EXECEPTION_USER_REPORT("member_account_exception_use_report","会员号异常使用报表",new String[] {"会员卡号", "会员姓名", "会员类型", "收银日期", "销售笔数", "销售总金额", "门店编码","门店简称","客户编码"}),
    BREAKAGE_ENTERING("breakage_entering","查看报损记录",new String[] {"门店名称", "报损渠道", "商品编码", "条形码", "品名和规格", "单位", "报损数量", "零售价", "报损售价金额", "成本价", "报损成本金额", "报损人", "报损时间", "监控位置", "报损原因", "生产日期", "到期日期", "退货单号"}),
    DD_BREAKAGE_ENTERING("breakage_entering","查看报损记录",new String[] {"门店名称", "报损渠道", "商品编码", "条形码", "品名和规格", "单位", "报损数量", "零售价", "报损售价金额", "成本价", "报损成本金额", "报损人", "报损时间", "监控位置", "报损原因", "生产日期", "到期日期", "退货单号", "货位", "档口", "库区"}),
    BREAKAGE_ENTERING_DAY("breakage_entering"," 门店报损汇总表",new String[] {"门店编码", "门店名称", "报损成本金额"}),


    ORDER_DELETE_REPORT("order_delete_report","订单作废报表", new String[] {"门店名称","POS机号","订单号","商品数量","订单金额","操作类型","挂单时间","挂单人账号","挂单人姓名","作废时间","作废人账号","作废姓名"}),
    RETURN_SALES_WATER_REPORT("return_sales_water_report","退货明细",new String[] {"门店编码","门店名称","POS机号","收银员账号","收银员姓名","退货日期","退货原因","有无原单","退货成交金额","原单中金额","后台特价后金额","手动优惠后金额","退货订单号","退货时间","货号","条形码","品名","规格","退货成交单价","数量","单位","份数","无原单授权人账号","授权人名称","收银渠道","原单销售日期","原单销售时间","原单号","会员卡号","会员姓名","大类名称","中类名称","小类名称","品牌名称","供应商名称"}),

    HANDLE_DISCOUNT_DETAIL_REPORT("handle_discount_detail_report","手动优惠明细",new String[] {"门店编码", "门店名称", "客户编码","收银员账号", "收银员姓名", "收银时间", "订单号", "后台大类", "条形码", "商品编码", "商品名称", "规格","单位", "零售价", "数量", "零售价金额", "后台自动优惠金额", "手动优惠金额", "成交金额", "手动/零售价", "手动优惠类型", "授权人账号", "授权人姓名"}),


    COMMODITY_REAL_DELIVERY_WITH_SHOP("commodity_real_delivery"," 云超实发汇总表", new String[] {"部门","客户编码","门店","工厂","生产组","商品编码","商品","规格","计量单位","商品数量","商品金额","实收金额","实发数量","C端实发金额","销售成本","条码","税率","大类","中类","小类","车间"}),
    COMMODITY_REAL_DELIVERY("commodity_real_delivery"," 云超实发汇总表", new String[] {"工厂","生产组","商品编码","商品","规格","计量单位","商品数量","商品金额","实收金额","实发数量","C端实发金额","销售成本","条码","税率","大类","中类","小类","车间"}),

    CLOUD_COMMODITY_CANCEL("cloud_commodity_cancel"," 云超订单取消明细表", new String[] {"部门","客户编码","门店名称","订单编号","订单来源","商品编码","商品名称","规格","单位","订单数量","商品金额","实收金额","销售成本","实发数量","实发金额","条码","后台大类","后台中类","后台小类","税率","退货完成时间"}),
    CLOUD_COMMODITY_DIFF_RETURN("cloud_commodity_diff_return"," 云超补差退款明细表", new String[] {"部门","客户编码","门店名称","订单编号","订单来源","预约日期","商品编码","商品名称","规格","单位","订单数量","商品金额","实收金额","实发数量","实发金额","差异退款金额","条码","后台大类","后台中类","后台小类","税率","补差退款时间"}),
    CLOUD_COMMODITY_FAIL("cloud_commodity_fail"," 云超配送失败明细表", new String[] {"部门","客户编码","门店名称","订单编号","订单来源","预约日期","配送失败时间","商品编码","商品名称","规格","单位","订单数量","商品金额","实收金额","销售成本","实发数量","实发金额","条码","后台大类","后台中类","后台小类","税率"}),


    STOCK_IN_REPORT_COMMODITY("stock_in_report"," 门店商品实发表", new String[] {"部门","客户编码","门店名称","商品编码","商品名称","条码","规格","计量单位","订货数量","订货金额","实发数量","实发金额","调拨入库数量","调拨入库金额","云超转入数量","云超转入金额","实退数量","实退金额","调拨出库数量","调拨出库金额","实收数量总计","实收金额总计","大类","中类","小类"}),
    STOCK_IN_REPORT_CATE("stock_in_report"," 门店商品实发表", new String[] {"部门","客户编码","门店名称","订货金额","实发金额","调拨入库金额","云超调入金额","实退金额","调拨出库金额","实收金额合计","后台大类","后台中类","后台小类"}),

    ON_TO_OFF_ANALYZE("on_to_off_analyze", " 线上线下客单分析", new String[] {"销售渠道", "日期", "销售额", "客单量", "平均客单价", "门店数", "单店日均销售额", "单店日均单量"}),
    CATEGORY_ALL_SALES_WEEK_PERCENT("category_all_sales_week_percent","品类销售周同比",new String[] {"大类", "今日线下销售额", "今日线上销售额", "上周线下销售额", "上周线上销售额", "销售差额", "品类销售周同比", "今日销售额占比"}),
    CATEGORY_ALL_SALES_MONTH_PERCENT("category_all_sales_month_percent","品类销售月同比",new String[] {"大类", "本月线下销售额", "本月线上销售额", "上月线下销售额", "上月线上销售额", "销售差额", "月销售额同比", "本月销售额占比"}),

    COMMODITY_QUANTITY_UPDATE_SHOP("commodity_quantity_update_shop","单品修改数量报表",new String[] {"门店编码", "门店名称", "客户编码", "POS类型", "净销售额", "增加单品次数", "增加单品总金额","减少单品次数", "减少单品总金额","增加单品与净销售额比", "减少单品与净销售额比"}),
    COMMODITY_QUANTITY_UPDATE_TIME("commodity_quantity_update_time","单品修改数量报表",new String[] {"门店编码", "门店名称", "客户编码", "操作日期", "POS类型", "净销售额", "增加单品次数", "增加单品总金额","减少单品次数", "减少单品总金额","增加单品与净销售额比", "减少单品与净销售额比"}),
    COMMODITY_QUANTITY_UPDATE_CASHER("commodity_quantity_update_casher","单品修改数量报表",new String[] {"门店编码", "门店名称", "客户编码", "收银员编码", "收银员名称","操作日期", "POS类型", "净销售额", "增加单品次数", "增加单品总金额","减少单品次数", "减少单品总金额","增加单品与净销售额比", "减少单品与净销售额比"}),
    STALL_COMMODITY_SALES_SUMMARY("stall_commodity_sales_summary","档口商品销售分析",new String[] {"部门","门店编码","门店","档口","大类名称","中类名称","小类名称","条形码","商品编码","商品名称","规格","单位","销售数量","销售金额","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),

    STALL_SHOP_SALES_SUMMARY("stall_shop_sales_summary","档口销售分析",new String[] {"部门","门店编码","门店","档口","销售金额","销售数量","销售成本","毛利额","毛利率","不含税销售额","不含税销售成本"}),

    ;
    private String code;
    private String name;
    private String[] titles;
    /** 属性名 */
    private String[] props;

    private ExcelSheetTitleEnum(String code, String name, String[] titles) {
        this.code = code;
        this.name = name;
        this.titles = titles;
    }

    ExcelSheetTitleEnum(String code, String name, String[] titles, String[] props) {
        this.code = code;
        this.name = name;
        this.titles = titles;
        this.props = props;
    }


    /**
     * 动态设置title
     * @param code
     * @param titles
     */
    public static void setTitles(String code, String[] titles) {
        for (ExcelSheetTitleEnum est : ExcelSheetTitleEnum.values()) {
            if (code.equals(est.getCode())) {
                est.setTitles(titles);
                break;
            }
        }
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String[] getTitles() {
        return titles;
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
    }

    public String[] getProps() {
        return props;
    }

    public void setProps(String[] props) {
        this.props = props;
    }

    public static List<List<String>> getExcelHead(ExcelSheetTitleEnum sheetTitle){
        String[] head = sheetTitle.getTitles();
        return Arrays.stream(head).map(title -> {
            List<String> head0 = new ArrayList<String>();
            head0.add(title);
            return head0;
        }).collect(Collectors.toList());
    }

}
