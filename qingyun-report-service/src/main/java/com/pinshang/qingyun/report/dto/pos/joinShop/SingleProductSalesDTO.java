package com.pinshang.qingyun.report.dto.pos.joinShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SingleProductSalesDTO {

    private Long commodityId;

    private String commodityFirstName;

    private String commoditySecondName;

    private String commodityThirdName;

    private String commodityCode;

    private String commodityName;

    private String barCode;

    @ApiModelProperty("子码列表")
    private String barCodeList;

    @ApiModelProperty("计量单位")
    private String commodityUnit;

    @ApiModelProperty("销售数量合计")
    private BigDecimal sumSaleQuantity;

    @ApiModelProperty("销售成交金额合计")
    private BigDecimal sumSaleAmount;

    @ApiModelProperty("退货数量合计")
    private BigDecimal sumReturnQuantity;

    @ApiModelProperty("退货金额合计")
    private BigDecimal sumReturnAmount;

    @ApiModelProperty("赠品数量合计")
    private BigDecimal sumGiveQuantity;

    @ApiModelProperty("赠品的原价金额合计")
    private BigDecimal sumGiveCommodityPrice;

    /**
     * 销售数量*销售原价金额
     */
    @ApiModelProperty("销售的原价金额合计")
    private BigDecimal sumCommodityPrice;

    /**
     * 退货数量*退货原价金额
     */
    @ApiModelProperty("退货的原价金额合计")
    private BigDecimal sumReturnCommodityPrice;


    @ApiModelProperty("销售数量 = 销售数量合计 + 赠品数量合计 - 退货数量合计")
    private BigDecimal saleQuantity;

    @ApiModelProperty("销售额 = 销售成交金额合计-退货金额合计")
    private BigDecimal saleAmount;

    /**
     * （销售数量+赠品数量）*销售原价金额 - 退货数量*退货原价金额
     */
    @ApiModelProperty("原价金额 = 销售的原价金额合计-退货的原价金额合计")
    private BigDecimal commodityPriceAmount;

    /**
     * 折扣额 = (销售数量+赠品数量)*销售原价金额 - 成交金额合计
     *                              成交金额合计=销售的成交金额-退货的成绩金额
     */
    @ApiModelProperty("折扣额 = 原价金额合计-成交金额合计")
    private BigDecimal discountAmount;

    @ApiModelProperty("折扣率 = 折扣额/原价金额，%格式，%号前面四舍五入进位 保留两个小数")
    private String discountRate;

}
