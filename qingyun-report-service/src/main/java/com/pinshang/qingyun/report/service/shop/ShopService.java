package com.pinshang.qingyun.report.service.shop;


import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.shop.Shop;
import com.pinshang.qingyun.report.service.BaseService;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShopService extends BaseService<ShopMapper,Shop> {

      @Autowired
      private OrgClient orgClient;

      /**
       * 根据门店idList获取上级组织
       * @param shopIdList
       * @return
       */
      public Map<Long, OrgAndParentInfoODTO> getOrgMap(List<Long> shopIdList){
            Map<Long, OrgAndParentInfoODTO> orgMap = new HashMap<>();
            SelectShopOrgInfoListIDTO infoListIDTO = new SelectShopOrgInfoListIDTO();
            infoListIDTO.setShopIdList(shopIdList);
            List<OrgAndParentInfoODTO> list = orgClient.selectShopOrgInfoList(infoListIDTO);
            if(CollectionUtils.isNotEmpty(list)){
                  orgMap = list.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId, e -> e));
            }
            return orgMap;
      }

}
