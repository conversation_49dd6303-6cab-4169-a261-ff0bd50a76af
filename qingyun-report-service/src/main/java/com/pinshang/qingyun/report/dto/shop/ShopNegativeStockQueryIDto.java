package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/6/29
 */
@Data
public class ShopNegativeStockQueryIDto extends Pagination {

    @ApiModelProperty("部门code")
    private String orgCode;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("销售日期开始时间yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("销售日期结束时间yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("及时达必售 1 是  0 否")
    private Integer sellStatus;

    @ApiModelProperty("经营模式：1-直营、2-外包")
    private Integer managementMode;

    private List<Long> shopIdList;
}
