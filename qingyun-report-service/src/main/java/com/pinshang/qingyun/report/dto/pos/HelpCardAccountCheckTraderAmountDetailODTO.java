package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName PosHelpCardAccountCheckingTraderAmountDetailODTO
 * <AUTHOR>
 * @Date 2023/2/24 13:38
 * @Description PosHelpCardAccountCheckingTraderAmountDetailODTO
 * @Version 1.0
 */
@Data
public class HelpCardAccountCheckTraderAmountDetailODTO {

    @ApiModelProperty("客户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long storeId;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    @ApiModelProperty("客户编码")
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode , keyName = "storeId")
    private String storeCode;

    @ApiModelProperty("门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ApiModelProperty("门店编码")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopCode,keyName = "shopId")
    private String shopCode;

    @ApiModelProperty("设备号")
    private String payCode;

    @ApiModelProperty("品鲜交易金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal pxTraderAmount;
}
