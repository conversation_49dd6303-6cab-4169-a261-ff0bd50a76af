package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.mapper.pos.ReturnSalesWaterReportMapper;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: liuZhen
 * @DateTime: 2021/7/7 20:12
 */
@Slf4j
@Service
public class PosReportReturnSalesService {
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private ReturnSalesWaterReportMapper returnSalesWaterReportMapper;
    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private CommodityService commodityService;
    
    @Autowired
    protected SMMUserClient smmUserClient;

    public Boolean replaceByTime(String oneDay) {
        String start = oneDay + " 00:00:00";
        String end = oneDay + " 23:59:59";
        return returnSalesWaterReportMapper.replaceReturnFromSalesBySaleTime(start, end) > 0;
    }

    public TablePageInfo<ReturnSalesWaterODTO> queryList(ReturnSalesWaterIDTO idto) {
        TablePageInfo<ReturnSalesWaterODTO> info = queryListExport(idto);
        if (info.getList() != null && info.getList().size() > 0) {
            List<String> CommodityCodes = info.getList().stream().map(ReturnSalesWaterODTO::getCommodityCode).collect(Collectors.toList());
            //查询
            Map<String, List<String>> map = queryCommodityBarCodeList(CommodityCodes);
            info.getList().forEach(item -> {
                item.setBarCodeList(map.get(item.getCommodityCode()));
            });
        }
        return info;
    }

    public TablePageInfo<ReturnSalesWaterODTO> queryListExport(ReturnSalesWaterIDTO idto) {
        List<Long> list = new ArrayList<>();
        if (idto.getShopId() != null) {
            list.add(idto.getShopId());
        } else if (!StringUtil.isNullOrEmpty(idto.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(idto.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                list = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
            }
        }
        idto.setShopIdList(list);
        TablePageInfo info = new TablePageInfo();
        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if (CollectionUtils.isEmpty(idto.getShopIdList())) {
            idto.setShopIdList(shopIdList);
        } else {
            List<Long> longList = idto.getShopIdList().stream().filter(
                    item -> shopIdList.contains(item)).collect(Collectors.toList());
            if (longList.isEmpty()) {
                info.setList(new ArrayList());
                return info;
            }
            idto.setShopIdList(longList);
        }
        if (!StringUtil.isNullOrEmpty(idto.getSaleTimeBegin())) {
            idto.setSaleTimeBegin(idto.getSaleTimeBegin() + " 00:00:00");
        }
        if (!StringUtil.isNullOrEmpty(idto.getSaleTimeEnd())) {
            idto.setSaleTimeEnd(idto.getSaleTimeEnd() + " 23:59:59");
        }
        idto.checkData();
        PageInfo<ReturnSalesWaterODTO> page = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            returnSalesWaterReportMapper.queryList(idto);
        });
        if(CollectionUtils.isNotEmpty(page.getList())){
            List<String> commodityCodes = page.getList().stream().map(ReturnSalesWaterODTO::getCommodityCode).collect(Collectors.toList());
            Set<String> set = new HashSet<>(commodityCodes);
            List<CommodityPackageSpecDTO> taxList = commodityService.getCommodityListByCodes(set);
            Map<String, BigDecimal> map = taxList.stream().collect(Collectors.toMap(CommodityPackageSpecDTO::getCommodityCode, CommodityPackageSpecDTO::getCommodityPackageSpec));
            List<Long> commodityIdList = taxList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<String, Long> commMap = taxList.stream().collect(Collectors.toMap(CommodityPackageSpecDTO::getCommodityCode, CommodityPackageSpecDTO::getCommodityId));

            Map<Long, CommoditySupplierODto> csMap = commoditySupplierClient.queryCommodityDefaultSupplierByCommodityIdList(commodityIdList);
            for(ReturnSalesWaterODTO dto:page.getList()){
                CommoditySupplierODto csDto = csMap.get(commMap.get(dto.getCommodityCode()));
                dto.setSupplierName(null != csDto ? csDto.getSupplierName() : "");
                //反算销售份数
                BigDecimal commodityPackageSpec = map.get(dto.getCommodityCode());
                if (null != commodityPackageSpec && null != dto.getQuantity()) {
                    BigDecimal number = dto.getQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP);
                    dto.setNumber(number);
                }
            }
        }
        info = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        ReturnSalesWaterODTO header = returnSalesWaterReportMapper.sumList(idto);
        info.setHeader(header);
        return info;
    }

    public Map queryCommodityBarCodeList(List<String> CommodityCodeList) {
        List<CommodityBarCodeDTO> dtos = returnSalesWaterReportMapper.queryByCommodityCodeList(CommodityCodeList);
        Map<String, List<String>> prodMap = dtos.stream().collect(Collectors.groupingBy(CommodityBarCodeDTO::getCommodityCode,
                Collectors.mapping(CommodityBarCodeDTO::getBarCode, Collectors.toList())));
        return prodMap;
    }


}
