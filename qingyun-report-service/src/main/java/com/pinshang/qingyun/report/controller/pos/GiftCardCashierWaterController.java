package com.pinshang.qingyun.report.controller.pos;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.gift.GiftSaleTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.BeanUtils;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.ConsignmentCashierWaterDTO;
import com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterPage;
import com.pinshang.qingyun.report.dto.pos.QueryGiftCardWaterDTO;
import com.pinshang.qingyun.report.service.pos.GiftCardCashierWaterService;
import com.pinshang.qingyun.report.util.BeanUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/giftCard")
@Slf4j
public class GiftCardCashierWaterController {

    @Autowired
    private GiftCardCashierWaterService giftCardCashierWaterService;

    @Autowired
    private IRenderService renderService;

    @PostMapping("/cashierWaterPage")
    @ApiOperation("礼品卡收银流水")
    public TablePageInfo<GiftCardCashierWaterPage> cashierWaterPage(@RequestBody QueryGiftCardWaterDTO dto) {
        dto.setType(GiftSaleTypeEnum.GIFT_CARD.getCode());
        PageInfo<GiftCardCashierWaterPage> page =  giftCardCashierWaterService.cashierWaterPage(dto);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        GiftCardCashierWaterPage giftCardWaterPage = giftCardCashierWaterService.cashierWaterSum(dto);
        tablePageInfo.setHeader(giftCardWaterPage);
        return tablePageInfo;
    }

    @GetMapping("/export/cashierWaterPage")
    @ApiOperation("导出礼品卡收银流水")
    public void exportCashierWaterPage(QueryGiftCardWaterDTO dto, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(dto.getDateStart() != null && dto.getDateEnd() != null, "时间不能为空");
        dto.setType(GiftSaleTypeEnum.GIFT_CARD.getCode());
        dto.initExportPage();
        PageInfo<GiftCardCashierWaterPage> page = giftCardCashierWaterService.cashierWaterPage(dto);
        List<GiftCardCashierWaterPage> dataList = new ArrayList<>();
        GiftCardCashierWaterPage giftCardWaterPage = giftCardCashierWaterService.cashierWaterSum(dto);
        if (null != giftCardWaterPage) {
            giftCardWaterPage.setShopTypeName("合计");
        }
        dataList.add(giftCardWaterPage);
        for (GiftCardCashierWaterPage e : page.getList()) {
            e.setShopTypeName(ShopTypeEnums.get(e.getShopType()).getName());
            e.setCardSourceTypeName(OrderSourceTypeEnum.getMsg(e.getCardSourceType()));
        }
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"礼品卡收银流水");
            EasyExcel.write(response.getOutputStream(), GiftCardCashierWaterPage.class).autoCloseStream(Boolean.FALSE).sheet("礼品卡收银流水")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("礼品卡收银流水--导出异常",e);
        }
    }

    /**
     * 代销收银流水
     * @param dto
     * @return
     */
    @ApiOperation("代销收银流水")
    @PostMapping("/consignmentCashierWaterPage")
    @MethodRender
    public TablePageInfo<GiftCardCashierWaterPage> consignmentCashierWaterPage(@RequestBody QueryGiftCardWaterDTO dto) {

        PageInfo<GiftCardCashierWaterPage> page =  giftCardCashierWaterService.consignmentCashierWaterPage(dto);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        GiftCardCashierWaterPage giftCardWaterPage = giftCardCashierWaterService.consignmentCashierWaterSum(dto);
        tablePageInfo.setHeader(giftCardWaterPage);
        return tablePageInfo;
    }

    @GetMapping("/exportConsignmentCashierWater")
    @ApiOperation("导出代销收银流水")
    public void exportConsignmentCashierWater(QueryGiftCardWaterDTO dto, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(dto.getDateStart() != null && dto.getDateEnd() != null, "时间不能为空");

        dto.initExportPage();
        PageInfo<GiftCardCashierWaterPage> page = giftCardCashierWaterService.consignmentCashierWaterPage(dto);
        renderService.render(page.getList(),"/giftCard/exportConsignmentCashierWater");
        List<ConsignmentCashierWaterDTO> dataList = new ArrayList<>();
        GiftCardCashierWaterPage sum = giftCardCashierWaterService.consignmentCashierWaterSum(dto);
        if (null != sum) {
            ConsignmentCashierWaterDTO giftCardWaterPage = new ConsignmentCashierWaterDTO();
            giftCardWaterPage.setOrgName("合计");
            giftCardWaterPage.setTotalAmount(sum.getTotalAmount());
            dataList.add(giftCardWaterPage);
        }

        List<ConsignmentCashierWaterDTO> list = BeanCloneUtils.copyTo(page.getList(), ConsignmentCashierWaterDTO.class);
        dataList.addAll(list);
        try {
            ExcelUtil.setFileNameAndHead( response,"代销收银流水");
            EasyExcel.write(response.getOutputStream(), ConsignmentCashierWaterDTO.class).autoCloseStream(Boolean.FALSE).sheet("代销收银流水")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("代销收银流水--导出异常",e);
        }
    }


}
