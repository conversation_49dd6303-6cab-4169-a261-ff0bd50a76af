package com.pinshang.qingyun.report.client.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.client.report.hystrix.PosHelpCardHystrix;
import com.pinshang.qingyun.report.dto.pos.*;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName osHelpCardClient
 * <AUTHOR>
 * @Date 2023/2/22 17:10
 * @Description osHelpCardClient
 * @Version 1.0
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_POS_ADMIN_SERVICE, fallbackFactory = PosHelpCardHystrix.class, configuration = FeignClientConfiguration.class)
public interface PosHelpCardClient {

    @ApiOperation("帮困卡门店批量启用/停用")
    @RequestMapping(value = "/posHelpCard/getAvailableArea", method = RequestMethod.POST)
    List<HelpCardAvailableAreaListODTO> getAreaList(@RequestBody HelpCardAvailableAreaListIDTO idto);

    @ApiOperation(value = "区域管理详情", notes = "区域管理详情")
    @RequestMapping(value = "/posHelpCard/areaSettingDetail", method = RequestMethod.GET)
    HelpCardAreaDetailODTO areaSettingDetail(@RequestParam("id") Long id);

    @ApiModelProperty(value = "区域详情列表")
    @RequestMapping(value = "/posHelpCard/areaDetailListByIds", method = RequestMethod.POST)
    List<HelpCardAreaSettingPageODTO> areaDetailListByIds(@RequestBody HelpCardAreaSettingPageIDTO idto);
}
