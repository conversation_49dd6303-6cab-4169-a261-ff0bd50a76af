package com.pinshang.qingyun.report.dto.index;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class IndexEntry {
    //门店id
    private Long shopId;
    //门店名称
    private String shopName;
    //线上销售额
    private BigDecimal onlineSales;
    //线上成本
    private BigDecimal onlineCost;
    //线下销售额
    private BigDecimal offlineSales;
    //线下成本
    private BigDecimal offlineCost;
    //当前销售 额
    private BigDecimal totalSales;

    private BigDecimal sumTotalSales;
    //线下来客数
    private Integer offlineVisitorNumber;

    private String dateTime;
}
