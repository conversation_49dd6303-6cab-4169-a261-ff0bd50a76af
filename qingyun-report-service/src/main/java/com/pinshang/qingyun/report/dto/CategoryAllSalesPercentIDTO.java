package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName CategoryAllSalesPercentIDTO
 * <AUTHOR>
 * @Date 2022/10/27 10:13
 * @Description CategoryAllSalesPercentIDTO
 * @Version 1.0
 */
@Data
public class CategoryAllSalesPercentIDTO extends Pagination {

    @ApiModelProperty(position = 1, value ="门店id")
    private Long shopId;

    @Change(value = DataQueryConstant.NOW)
    @ApiModelProperty(position = 2, required = true, value ="销售日期(yyyy-MM-dd)")
    private String salesDate;

    @ApiModelProperty("组织code")
    private String orgCode;

    @ApiModelProperty(hidden = true)
    private List<Long> shopIdList;



    @ApiModelProperty(hidden = true)
    private String beginDate;

    @ApiModelProperty(hidden = true)
    private String endDate;

    @ApiModelProperty(hidden = true)
    private String lastMonthBeginDate;

    @ApiModelProperty(hidden = true)
    private String lastMonthEndDate;
}
