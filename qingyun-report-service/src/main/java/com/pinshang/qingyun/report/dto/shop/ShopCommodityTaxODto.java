package com.pinshang.qingyun.report.dto.shop;

import java.math.BigDecimal;

public class ShopCommodityTaxODto {
	
	private Long shopId;
	private String orderCode;
	private Long commodityId;
	private Long commodityFirstId;
	private Long commoditySecondId;
	private Long commodityThirdId;
	private Long cateId;
	//订单
	private BigDecimal totalSales =BigDecimal.ZERO;//总销售额
	private BigDecimal totalSalesQuantity =BigDecimal.ZERO; //总销售数量
	private BigDecimal onlineSales =BigDecimal.ZERO;//线上销售额
	private BigDecimal offlineSales =BigDecimal.ZERO;//线下销售额
	private BigDecimal offlineRatio;//线下销售额占比
	private BigDecimal costTotal =BigDecimal.ZERO;//成本金额合计
	private BigDecimal onlineCost =BigDecimal.ZERO;//线上成本金额合计
	private BigDecimal offlineCost =BigDecimal.ZERO;//线下成本金额合计
	private BigDecimal grossProfitRate;//毛利率

	private BigDecimal totalQuanty =BigDecimal.ZERO;//销售总量
	private BigDecimal orderQuanty =BigDecimal.ZERO;//进货数量
	//收货
	private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额
	//退货
	private BigDecimal saleReturnOrderTotal =BigDecimal.ZERO;//退货金额
	//库存调整
	private BigDecimal stockAdjustAmountTotal =BigDecimal.ZERO;//库存更正金额

	private BigDecimal weightPrice =BigDecimal.ZERO; //移动成本价
	
	private String dateTime;

	//库存
	private BigDecimal stockQuantityPrice;//库存金额
	private BigDecimal stockQuantityDay;//库存天数
	private BigDecimal stockQuantity;//库存
	private BigDecimal negativeStockQuantity;//负库存

	public String getOrderCode() {
		return orderCode;
	}

	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public Long getCommodityFirstId() {
		return commodityFirstId;
	}

	public void setCommodityFirstId(Long commodityFirstId) {
		this.commodityFirstId = commodityFirstId;
	}

	public Long getCommoditySecondId() {
		return commoditySecondId;
	}

	public void setCommoditySecondId(Long commoditySecondId) {
		this.commoditySecondId = commoditySecondId;
	}

	public Long getCommodityThirdId() {
		return commodityThirdId;
	}

	public void setCommodityThirdId(Long commodityThirdId) {
		this.commodityThirdId = commodityThirdId;
	}

	public Long getCateId() {
		return cateId;
	}

	public void setCateId(Long cateId) {
		this.cateId = cateId;
	}

	public BigDecimal getTotalSales() {
		return totalSales;
	}

	public void setTotalSales(BigDecimal totalSales) {
		this.totalSales = totalSales;
	}

	public BigDecimal getTotalSalesQuantity() {
		return totalSalesQuantity;
	}

	public void setTotalSalesQuantity(BigDecimal totalSalesQuantity) {
		this.totalSalesQuantity = totalSalesQuantity;
	}

	public BigDecimal getOnlineSales() {
		return onlineSales;
	}

	public void setOnlineSales(BigDecimal onlineSales) {
		this.onlineSales = onlineSales;
	}

	public BigDecimal getOfflineSales() {
		return offlineSales;
	}

	public void setOfflineSales(BigDecimal offlineSales) {
		this.offlineSales = offlineSales;
	}

	public BigDecimal getOfflineRatio() {
		return offlineRatio;
	}

	public void setOfflineRatio(BigDecimal offlineRatio) {
		this.offlineRatio = offlineRatio;
	}

	public BigDecimal getCostTotal() {
		return costTotal;
	}

	public void setCostTotal(BigDecimal costTotal) {
		this.costTotal = costTotal;
	}

	public BigDecimal getOnlineCost() {
		return onlineCost;
	}

	public void setOnlineCost(BigDecimal onlineCost) {
		this.onlineCost = onlineCost;
	}

	public BigDecimal getOfflineCost() {
		return offlineCost;
	}

	public void setOfflineCost(BigDecimal offlineCost) {
		this.offlineCost = offlineCost;
	}

	public BigDecimal getGrossProfitRate() {
		return grossProfitRate;
	}

	public void setGrossProfitRate(BigDecimal grossProfitRate) {
		this.grossProfitRate = grossProfitRate;
	}

	public BigDecimal getTotalQuanty() {
		return totalQuanty;
	}

	public void setTotalQuanty(BigDecimal totalQuanty) {
		this.totalQuanty = totalQuanty;
	}

	public BigDecimal getOrderQuanty() {
		return orderQuanty;
	}

	public void setOrderQuanty(BigDecimal orderQuanty) {
		this.orderQuanty = orderQuanty;
	}

	public BigDecimal getOrderTotal() {
		if(null !=orderTotal){
			return orderTotal.setScale(2,BigDecimal.ROUND_UP);
		}
		return orderTotal;
	}

	public void setOrderTotal(BigDecimal orderTotal) {
		this.orderTotal = orderTotal;
	}

	public BigDecimal getSaleReturnOrderTotal() {
		return saleReturnOrderTotal;
	}

	public void setSaleReturnOrderTotal(BigDecimal saleReturnOrderTotal) {
		this.saleReturnOrderTotal = saleReturnOrderTotal;
	}

	public BigDecimal getStockAdjustAmountTotal() {
		return stockAdjustAmountTotal;
	}

	public void setStockAdjustAmountTotal(BigDecimal stockAdjustAmountTotal) {
		this.stockAdjustAmountTotal = stockAdjustAmountTotal;
	}

	public BigDecimal getWeightPrice() {
		return weightPrice;
	}

	public void setWeightPrice(BigDecimal weightPrice) {
		this.weightPrice = weightPrice;
	}

	public String getDateTime() {
		return dateTime;
	}

	public void setDateTime(String dateTime) {
		this.dateTime = dateTime;
	}

	public BigDecimal getStockQuantityPrice() {
		return stockQuantityPrice;
	}

	public void setStockQuantityPrice(BigDecimal stockQuantityPrice) {
		this.stockQuantityPrice = stockQuantityPrice;
	}

	public BigDecimal getStockQuantityDay() {
		return stockQuantityDay;
	}

	public void setStockQuantityDay(BigDecimal stockQuantityDay) {
		this.stockQuantityDay = stockQuantityDay;
	}

	public BigDecimal getStockQuantity() {
		return stockQuantity;
	}

	public void setStockQuantity(BigDecimal stockQuantity) {
		this.stockQuantity = stockQuantity;
	}

	public BigDecimal getNegativeStockQuantity() {
		return negativeStockQuantity;
	}

	public void setNegativeStockQuantity(BigDecimal negativeStockQuantity) {
		this.negativeStockQuantity = negativeStockQuantity;
	}
}
