package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConsignmentCommoditySalesReportIDTO extends Pagination {
    @ApiModelProperty("销售时间开始 yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("销售时间结束 yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("部门Code")
    private String orgCode;

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("大类")
    private Long cateId1;
    @ApiModelProperty("中类")
    private Long cateId2;
    @ApiModelProperty("小类")
    private Long cateId3;

    @ApiModelProperty("是否区分门店(1:区分   0:不区分)")
    private Integer isWithShop;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("代销商户Id")
    private Long consignmentId;

}
