package com.pinshang.qingyun.report.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/9/27
 */
public class BigDecimalUtil {
    public static BigDecimal formate(double value) {
        DecimalFormat decimalFormat = new DecimalFormat("#0.00");
        String format = decimalFormat.format(value);

        return new BigDecimal(format);
    }


    public static void main(String[] args) {
        // 0.20
        System.out.println(BigDecimalUtil.formate(0.2));
        // 100.00
        System.out.println(BigDecimalUtil.formate(100));
        // 1.33
        System.out.println(BigDecimalUtil.formate(1.334));
        // 1.33
        System.out.println(BigDecimalUtil.formate(1.335));
        // 1.34
        System.out.println(BigDecimalUtil.formate(1.336));
    }
}
