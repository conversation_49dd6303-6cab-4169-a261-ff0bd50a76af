package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName HelpCardTransactionFlow
 * <AUTHOR>
 * @Date 2023/2/23 10:10
 * @Description HelpCardTransactionFlow
 * @Version 1.0
 */
@Data
@Table(name = "t_pos_help_card_transaction_flow")
public class HelpCardTransactionFlow extends BaseModel<HelpCardTransactionFlow> {
    @ApiModelProperty("交易日期")
    private Date transactionDate;

    @ApiModelProperty("终端标识")
    private String payCode;

    @ApiModelProperty("交易日期时间")
    private Date transactionDateTime;

    @ApiModelProperty("交易流水")
    private String serialNumber;

    @ApiModelProperty("消费卡号")
    private String cardNo;

    @ApiModelProperty("交易金额")
    private BigDecimal amount;

    @ApiModelProperty("交易金额-分")
    private BigDecimal amountCent;

    @ApiModelProperty("交易类型名称")
    private String typeName;

    @ApiModelProperty("交易返回码")
    private String returnCode;
}
