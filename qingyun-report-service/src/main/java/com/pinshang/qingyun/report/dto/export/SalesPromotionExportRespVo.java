package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SalesPromotionExportRespVo {

    @ExcelProperty("门店")
    private String shopName;

    @ExcelProperty("分类")
    private String commodityFirstName;

    @ExcelProperty("条形码")
    private String barCode;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("单位")
    private String commodityUnit;

    @ExcelProperty("零售价")
    private BigDecimal salePrice;

    @ExcelProperty("数量")
    private BigDecimal tatalQuantity;

    @ExcelProperty("销售金额")
    private BigDecimal tatalAmount;

    @ExcelProperty("让利金额")
    private BigDecimal discountAmount;

    @ExcelProperty("赠送金额")
    private BigDecimal giveAmount;

    @ExcelProperty("特价类型")
    private String promotionType;
}
