package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/2
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CloudRealDeliveryNoShopRespVo {

    @ExcelProperty(value = "工厂")
    private String commodityFactoryName;
    @ExcelProperty(value = "生产组")
    private String commodityWorkshopName;
    @ExcelProperty(value = "商品编码")
    private String commodityCode;
    @ExcelProperty(value = "商品")
    private String commodityName;
    @ExcelProperty(value = "规格")
    private String commoditySpec;
    @ExcelProperty(value = "计量单位")
    private String commodityUnitName;

    @ExcelProperty("商品数量")
    private BigDecimal quantity;
    @ExcelProperty("商品金额")
    private BigDecimal amount;
    @ExcelProperty("实收金额")
    private BigDecimal realAmount;
    @ExcelProperty("实发数量")
    private BigDecimal packageQuantity;
    @ExcelProperty("C端实发金额")
    private BigDecimal packageAmount;
    @ExcelProperty("销售成本")
    private BigDecimal weightAmount;

    @ExcelProperty("条码")
    private String barCode;
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    @ExcelProperty(value = "大类")
    private String commodityFirstKindName;
    @ExcelProperty(value = "中类")
    private String commoditySecondKindName;
    @ExcelProperty(value = "小类")
    private String commodityThirdKindName;
    @ExcelProperty(value = "车间")
    private String commodityFlowshopName;
}
