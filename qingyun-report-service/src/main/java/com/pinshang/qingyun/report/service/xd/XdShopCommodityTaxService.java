package com.pinshang.qingyun.report.service.xd;

import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO;
import com.pinshang.qingyun.report.mapper.xd.XdCommodityTaxMapper;
import com.pinshang.qingyun.report.model.shop.CommodityTax;
import com.pinshang.qingyun.report.model.xd.XdCommodityTax;
import com.pinshang.qingyun.report.service.shop.ShopCommodityTaxService;
import com.pinshang.qingyun.shop.dto.report.CommodityTaxReportODto;
import com.pinshang.qingyun.shop.service.ShopReportClient;
import com.pinshang.qingyun.xd.order.dto.XdShopCommodityTaxODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.wms.dto.ShopAbnormalReqDTO;
import com.pinshang.qingyun.xd.wms.dto.ShopAbnormalResDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class XdShopCommodityTaxService {

    @Autowired
    private XdCommodityTaxMapper xdCommodityTaxMapper;

    @Autowired
    private ShopCommodityTaxService shopCommodityTaxService;

    @Autowired
    private ShopReportClient shopReportClient;

    @Autowired
    private XdOrderClient xdOrderClient;

    @Autowired
    private XdStockClient xdStockClient;

    /**
     * 鲜道商品日汇总
     * @param day
     */
    @Transactional(rollbackFor = Exception.class)
    public void xdCommodityTax(String day){
        //清除数据
        clearXdShopTaxData(day);

        // 获取订货数据(金额、数量)、销售数据(金额、数量、成本)
        List<XdCommodityTax> xdCommodityTaxList = getXdCommodityTaxList(day);

        //新增
        if(CollectionUtils.isNotEmpty(xdCommodityTaxList)){
            if(xdCommodityTaxList.size() > 2000 ){
                batchInsertXdCommodityTax(xdCommodityTaxList);
            }else{
                xdCommodityTaxMapper.insertList(xdCommodityTaxList);
            }
        }
    }

    /**
     * 获取订货数据(金额、数量)、销售数据(金额、数量、成本)
     * @param day
     * @return
     */
    public List<XdCommodityTax> getXdCommodityTaxList(String day){
        List<XdCommodityTax> returnList = new ArrayList<>();

        // 获取订货数据
        List<XdCommodityTax> saleOrderList = getXdSaleOrderList(day);

        //获取销售数据
        List<XdCommodityTax> onlineList = getXdOnlineList(day);

        // 获取损耗
        List<XdCommodityTax> normalList = getXdAbnormalList(day);

        Map<String,XdCommodityTax> onlineListMap = new HashMap<>(64*1024);
        Map<String,XdCommodityTax> saleOrderListMap = new HashMap<>(64*1024);
        Map<String,XdCommodityTax> normalListMap = new HashMap<>(64*1024);

        //减少循环
        setListMap(returnList, onlineList, saleOrderList, normalList, onlineListMap, saleOrderListMap, normalListMap);

        Map<Long, List<XdCommodityTax>> commodityIdMap = returnList.stream().collect(Collectors.groupingBy(XdCommodityTax::getCommodityId));
        Set<Long> commodityIdSet = commodityIdMap.keySet();
        returnList = new ArrayList<>();

        // 查询当天的产生交易的商品信息
        List<XdCommodityTax> commodityTaxList = getXdCommodityTaxes(commodityIdSet);

        // 组装数据
        for(XdCommodityTax tax : commodityTaxList){
            // 线上
            XdCommodityTax onlineTax = onlineListMap.get(tax.getShopId()+""+tax.getCommodityId());
            if(null != onlineTax) {
                tax.setTotalSales((tax.getTotalSales() == null ? BigDecimal.ZERO : tax.getTotalSales()).add(onlineTax.getTotalSales()));
                tax.setTotalQuanty((tax.getTotalQuanty() == null ? BigDecimal.ZERO : tax.getTotalQuanty()).add(onlineTax.getTotalQuanty()));
                tax.setCostTotal((tax.getCostTotal() == null ? BigDecimal.ZERO : tax.getCostTotal()).add(onlineTax.getCostTotal()));
            }

            //进货
            XdCommodityTax saleOrderEntry = saleOrderListMap.get(tax.getShopId()+""+tax.getCommodityId());
            if(null != saleOrderEntry ) {
                tax.setOrderTotal((tax.getOrderTotal() == null ? BigDecimal.ZERO : tax.getOrderTotal()).add(saleOrderEntry.getOrderTotal()));
                tax.setOrderQuanty((tax.getOrderQuanty() == null ? BigDecimal.ZERO : tax.getOrderQuanty()).add(saleOrderEntry.getOrderQuanty()));
            }

            //损耗
            XdCommodityTax normalEntry = normalListMap.get(tax.getShopId()+""+tax.getCommodityId());
            if(null != normalEntry ) {
                tax.setNormalBreakageQuantity((tax.getNormalBreakageQuantity() == null ? BigDecimal.ZERO : tax.getNormalBreakageQuantity()).add(normalEntry.getNormalBreakageQuantity().multiply(tax.getCommodityPackageSpec())));
                tax.setOverNormalBreakageQuantity((tax.getOverNormalBreakageQuantity() == null ? BigDecimal.ZERO : tax.getOverNormalBreakageQuantity()).add(normalEntry.getOverNormalBreakageQuantity().multiply(tax.getCommodityPackageSpec())));
            }
        }

        // 处理数据
        for (XdCommodityTax taxEntry : commodityTaxList) {
            Boolean isOk = (taxEntry.getTotalSales() != null && taxEntry.getTotalSales().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getOrderTotal() != null && taxEntry.getOrderTotal().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getNormalBreakageQuantity() != null && taxEntry.getNormalBreakageQuantity().compareTo(BigDecimal.ZERO) != 0)
                    || (taxEntry.getOverNormalBreakageQuantity() != null && taxEntry.getOverNormalBreakageQuantity().compareTo(BigDecimal.ZERO) != 0);
            if(isOk){
                taxEntry.setDateTime(day);
                returnList.add(taxEntry);
            }
        }

        return returnList;
    }

    /**
     * 查询当天的产生交易的商品信息
     * @return
     */
    public List<XdCommodityTax> getXdCommodityTaxes(Set<Long> commodityIdSet){
        List<XdCommodityTax> xdCommodityTaxList = new ArrayList<>();
        List<CommodityTax> commodityTaxList = shopCommodityTaxService.getCommodityTaxes(commodityIdSet, ShopTypeEnums.XD.getCode());
        if(CollectionUtils.isEmpty(commodityTaxList)){return xdCommodityTaxList;}
        for(CommodityTax commodityTax : commodityTaxList){
            XdCommodityTax xdCommodityTax = new XdCommodityTax();
            BeanUtils.copyProperties(commodityTax,xdCommodityTax);
            xdCommodityTaxList.add(xdCommodityTax);
        }
        return xdCommodityTaxList;
    }

    /**
     * 数据放入map
     * @param returnList
     * @param onlineList
     * @param saleOrderList
     * @param onlineListMap
     * @param saleOrderListMap
     */
    private void setListMap(List<XdCommodityTax> returnList, List<XdCommodityTax> onlineList, List<XdCommodityTax> saleOrderList, List<XdCommodityTax> normalList, Map<String, XdCommodityTax> onlineListMap,  Map<String, XdCommodityTax> saleOrderListMap, Map<String, XdCommodityTax> normalListMap) {
        if(CollectionUtils.isNotEmpty(onlineList)){
            returnList.addAll(onlineList);
            for(XdCommodityTax entry:onlineList){
                onlineListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }

        if(CollectionUtils.isNotEmpty(saleOrderList)){
            returnList.addAll(saleOrderList);
            for(XdCommodityTax entry:saleOrderList){
                saleOrderListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }

        if(CollectionUtils.isNotEmpty(normalList)){
            returnList.addAll(normalList);
            for(XdCommodityTax entry:normalList){
                normalListMap.put(entry.getShopId()+""+entry.getCommodityId(),entry);
            }
        }
    }

    /**
     * 获取鲜道 损耗
     * @param day
     * @return
     */
    public List<XdCommodityTax> getXdAbnormalList(String day){
        List<XdCommodityTax> normalList = new ArrayList<>();
        ShopAbnormalReqDTO shopAbnormalReqDTO = new ShopAbnormalReqDTO();
        shopAbnormalReqDTO.setDate(DateUtil.parseDate(day,"yyyy-MM-dd"));
        List<ShopAbnormalResDTO> abnormalList = xdStockClient.queryShopAbnormal(shopAbnormalReqDTO);
        if(CollectionUtils.isNotEmpty(abnormalList)){
            for(ShopAbnormalResDTO oDto : abnormalList){
                XdCommodityTax xdCommodityTax = new XdCommodityTax();
                xdCommodityTax.setShopId(oDto.getShopId());
                xdCommodityTax.setCommodityId(oDto.getCommodityId());
                xdCommodityTax.setNormalBreakageQuantity(null != oDto.getAbnormalNum2() ? new BigDecimal(oDto.getAbnormalNum2()) : BigDecimal.ZERO);
                xdCommodityTax.setOverNormalBreakageQuantity(new BigDecimal(oDto.getAbnormalNum1()).subtract(xdCommodityTax.getNormalBreakageQuantity()));
                normalList.add(xdCommodityTax);
            }
        }
        return normalList;
    }
    /**
     * 获取鲜道 订货数据
     * @param day
     * @return
     */
    public List<XdCommodityTax> getXdSaleOrderList(String day){
        List<XdCommodityTax> saleOrderList = new ArrayList<>();
        List<Integer> shopTypeList = new ArrayList<>();
        shopTypeList.add(ShopTypeEnums.XD.getCode());
        List<CommodityTaxReportODto> saleOrderODTOList = shopReportClient.getCommodityTaxFromSaleOrder(day, shopTypeList);
        if(CollectionUtils.isNotEmpty(saleOrderODTOList)){
            for(CommodityTaxReportODto oDto : saleOrderODTOList){
                XdCommodityTax xdCommodityTax = new XdCommodityTax();
                BeanUtils.copyProperties(oDto,xdCommodityTax);
                saleOrderList.add(xdCommodityTax);
            }
        }
        return saleOrderList;
    }

    /**
     * 获取鲜道 销售数据
     * @param day
     * @return
     */
    public List<XdCommodityTax> getXdOnlineList(String day){
        List<XdCommodityTax> onlineList = new ArrayList<>();
        List<XdShopCommodityTaxODTO> onlineListODTOList = xdOrderClient.getXdOnlineList(day);
        if(CollectionUtils.isNotEmpty(onlineListODTOList)){
            for(XdShopCommodityTaxODTO odto : onlineListODTOList){
                XdCommodityTax xdCommodityTax = new XdCommodityTax();
                BeanUtils.copyProperties(odto,xdCommodityTax);
                onlineList.add(xdCommodityTax);
            }
        }
        return onlineList;
    }

    /**
     * 批量插入鲜道商品级别数据
     * @param cs
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertXdCommodityTax(List<XdCommodityTax> cs) {
        int index = 0;
        int count = 2000;
        while (true) {
            List<XdCommodityTax> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                xdCommodityTaxMapper.insertList(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 清除数据
     * @param day
     */
    public void clearXdShopTaxData(String day){
        Example example = new Example(XdCommodityTax.class);
        example.createCriteria().andEqualTo("dateTime", day);
        xdCommodityTaxMapper.deleteByExample(example);
    }

    /**
     * 查询鲜道商品日汇总
     * @param beginTime
     * @param endTime
     * @return
     */
    public List<XdCommodityTaxODTO> findXdCommodityTaxList(String beginTime, String endTime, Long shopId, Long commodityId){
        List<XdCommodityTaxODTO>  list =  xdCommodityTaxMapper.findXdCommodityTaxList(beginTime,endTime,shopId,commodityId);
        if(CollectionUtils.isNotEmpty(list)){
            // 数量转份数
            for(XdCommodityTaxODTO odto : list){
                BigDecimal commodityPackageSpec = odto.getCommodityPackageSpec();
                odto.setTotalQuanty(odto.getTotalQuanty().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP));
                odto.setOrderQuanty(odto.getOrderQuanty().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP));
                odto.setNormalBreakageQuantity(odto.getNormalBreakageQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP));
                odto.setOverNormalBreakageQuantity(odto.getOverNormalBreakageQuantity().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP));
            }
        }
        return list;
    }
}
