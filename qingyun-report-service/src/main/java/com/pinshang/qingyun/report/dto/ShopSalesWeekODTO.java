package com.pinshang.qingyun.report.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @see ExcelSheetTitleEnum#SHOP_SALES_PERCENT_REPORT
 * {"门店", "今日销售额", "上周销售额", "销售额同比", "今日来客","上周来客","来客同比","今日客单价","上周客单价","客单价同比"}
 */
@Data
public class ShopSalesWeekODTO {

//    private Long shopId;
    @ExcelIgnore
    private Long shopId;
    @ExcelIgnore
    private BigDecimal saleAmount;
    @ExcelIgnore
    private BigDecimal visitNum;

    @ApiModelProperty(position = 1, value ="门店名称")
    @ExcelProperty("门店")
    private String shopName;

    @ApiModelProperty(position = 2, value ="今日销售额")
    @ExcelProperty("今日销售额")
    private BigDecimal todaySalesAmount;

    @ApiModelProperty(position = 3, value ="上周销售额")
    @ExcelProperty("上周销售额")
    private BigDecimal lastWeekSalseAmount;

    @ApiModelProperty(position = 4, value ="销售额同比")
    @ExcelProperty("销售额同比")
    private String salesPercent;

    @ApiModelProperty(position = 5, value ="今日来客")
    @ExcelProperty("今日来客")
    private BigDecimal todayVisitNum;

    @ApiModelProperty(position = 6, value ="上周来客")
    @ExcelProperty("上周来客")
    private BigDecimal lastWeekVisitNum;

    @ApiModelProperty(position = 7, value ="来客同比")
    @ExcelProperty("来客同比")
    private String visitNumPercent;

    @ApiModelProperty(position = 8, value ="今日客单价")
    @ExcelProperty("今日客单价")
    private BigDecimal todayGuestPrice;

    @ApiModelProperty(position = 9, value ="上周客单价")
    @ExcelProperty("上周客单价")
    private BigDecimal lastWeekGuestPrice;

    @ApiModelProperty(position = 10, value ="客单价同比")
    @ExcelProperty("客单价同比")
    private String guestPricePercent;

}
