package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2021/7/8
 */
@Data
public class HandDiscountDetailExportODTO {

    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("收银员账号")
    private String  cashierNumber;
    @ApiModelProperty("收银员名称")
    private String  cashierName;

    @ApiModelProperty("收银时间")
    private String cashierTime;

    @ApiModelProperty("订单号")
    private String  orderCode;

    @ApiModelProperty("大类名称")
    private String commodityFirstName;

    @ApiModelProperty("条形码")
    private String barCode;

    private String commodityCode;//商品编码
    private String commodityName;//商品名称
    private String commoditySpec;//规格
    private String commodityUnit;//单位

    @ApiModelProperty("零售价")
    private BigDecimal retailPrice;
    @ApiModelProperty("数量")
    private BigDecimal quantity;
    @ApiModelProperty("零售金额")
    private BigDecimal retailAmount;


    @ApiModelProperty("后台自动优惠金额")
    private BigDecimal backgroundDiscountAmount;
    @ApiModelProperty("手动优惠金额")
    private BigDecimal handleDiscountAmount;
    @ApiModelProperty("成交金额")
    private BigDecimal transactionAmount;
    @ApiModelProperty("手动/零售价")
    private String handleRetailPercent;
    @ApiModelProperty("手动优惠类型 21:手工议价 22:手工折扣 23:赠送 24:整单议价 25:整单折扣")
    private String handleDiscountTypeName;

    @ApiModelProperty("授权人账号")
    private String opUserCode;
    @ApiModelProperty("授权人姓名")
    private String opUserName;


}
