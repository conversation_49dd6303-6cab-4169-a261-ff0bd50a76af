package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.Payment;
import com.pinshang.qingyun.report.model.pos.ReportCashierWater;
import com.pinshang.qingyun.report.service.pos.CashierWaterHeaderODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface CashierWaterReportMapper {

    void insertCashierWaterReports(@Param("cashierWaterReports") List<ReportCashierWater> cashierWaterReports);

    void insertConsumeRepeat(@Param("orderCode") Long orderCode);

    List<CashierWaterODTO> listCashierWaterReport(@Param("cashierWaterIDTO") CashierWaterIDTO cashierWaterIDTO);

    CashierWaterHeaderODTO listCashierWaterReportHeader(@Param("cashierWaterIDTO") CashierWaterIDTO cashierWaterIDTO);

    int deleteCashierWaterReportByOrderCodes(@Param("orderCode") String orderCodes);

    int deleteCashierWaterReportByTimeRange(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    int selectReportData(@Param("tableName") String tableName, @Param("orderCode") String orderCode);

    List<Payment> getLastDateOrder(@Param("lastDate") String lastDate,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    List<Payment> getLastDiscountOrder(@Param("lastDate") String lastDate,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    List<Payment> getLastSaleOrder(@Param("lastDate") String lastDate,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    List<Payment> getLastSaleWaterOrder(@Param("lastBeginDate") String lastBeginDate, @Param("lastEndDate") String lastEndDate);

    List<Payment> getLastCashierWaterOrder(@Param("lastBeginDate") String lastBeginDate, @Param("lastEndDate") String lastEndDate);

    void deleteCashierWaterDayReportBySaleTime(@Param("saleTime")String saleTime);

    void insertCashierWaterDayReport(@Param("beginTime")String beginTime,@Param("endTime") String endTime);

    List<CashierWaterDayReportODTO> queryCashierWaterDayReport(@Param("shopId")Long shopId,@Param("beginTime")String beginTime,@Param("endTime") String endTime,@Param("type") Integer type);



    Long getLastGuestOrderCount(@Param("lastDate") String lastDate,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    Long getLastDiscountOrderCount(@Param("lastDate") String lastDate,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    Long getLastSaleOrderCount(@Param("lastDate") String lastDate,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    Long getLastSaleWaterOrderCount(@Param("lastBeginDate") String lastBeginDate, @Param("lastEndDate") String lastEndDate);

    Long getLastCashierWaterOrderCount(@Param("lastBeginDate") String lastBeginDate, @Param("lastEndDate") String lastEndDate);

    BigDecimal getCashierWaterDayAmount(@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    BigDecimal getGuestListSummaryDayAmount(@Param("saleTime") String saleTime);

    BigDecimal getSalesSummaryDayAmount(@Param("saleTime") String saleTime);

    List<CommodityDeleteODTO> getNetSales(@Param("dto") CommodityDeleteIDTO dto);

    Double getNetSalesCount(@Param("dto") CommodityDeleteIDTO dto);

    BigDecimal getCashierWaterCurrentDayAmount(@Param("beginTime") String beginTime,@Param("endTime") String endTime);
    BigDecimal getGuestListSummaryCurrentDayAmount(@Param("saleTime") String saleTime);
    BigDecimal getSalesSummaryCurrentDayAmount(@Param("saleTime") String saleTime);

    int batchUpdateCashierWaterOriginOrder(List<OrderCodeODTO> list);

    /**
     * 能提现的支付方式 汇总充值金额
     * @param dto
     * @return
     */
    List<SummaryAmountDTO> getSummaryAmount(@Param("dto") SummaryAmountIDTO dto);
}
