package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.box.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WeightPriceODTO {

    @ApiModelProperty("门店id")
    private Long  shopId;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("订单号")
    private String orderCode;

    @ApiModelProperty("销售方式:  1 销售  2 退货")
    private Integer saleType;


    @ApiModelProperty("交易时间")
    private Date saleTime;

    private Long commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("包装类型")
    private String commodityPackageName;

    @ApiModelProperty("称重码")
    private String weightCode;

    @ApiModelProperty("码里单价")
    private BigDecimal  weightCodePrice;
    @ApiModelProperty("码里金额")
    private BigDecimal  weightCodeAmount;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("成交单价")
    private BigDecimal  transactionPrice;
    @ApiModelProperty("成交金额")
    private BigDecimal transactionAmount;

    @ApiModelProperty("成交金额/后台零售价 %")
    private String transactionDivRetail;

    public String getTransactionWeightPercent(){
        if(StringUtils.isNotBlank(transactionDivRetail)){
            return transactionDivRetail + "%";
        }
        return "";
    }

    @ApiModelProperty("后台零售价")
    private BigDecimal  retailPrice;
    @ApiModelProperty("后台特价")
    private BigDecimal  promotionPrice;

    @ApiModelProperty("后台零售价金额")
    private BigDecimal  retailAmount;

    @ApiModelProperty("超后台零售价金额")
    private BigDecimal  overRetailAmount;


    public String getCashChannelName(){
        if(StringUtils.isNotBlank(orderCode)){
            if(orderCode.startsWith("8")){
                return "联网收银";
            }
            if(orderCode.startsWith("9")){
                return "本地收银";
            }
        }
        return "";
    }

    public String getSaleTypeName(){
        if(saleType != null){
            if(saleType == 1){
                return "销售";
            }
            if(saleType == 2){
                return "退货";
            }
        }
        return "";
    }

    public String getSaleTimeStr(){
        return DateUtil.getDateFormate(saleTime,"yyyy-MM-dd HH:mm:ss");
    }
}
