package com.pinshang.qingyun.report.enums;

import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/08/06
 * @Version 1.0
 */
public enum ShopTypeConditionEnum {
    BASE_LIST(1, Arrays.asList(ShopTypeEnums.XS.getCode(),ShopTypeEnums.XD.getCode(), ShopTypeEnums.HYD.getCode(), ShopTypeEnums.THZT.getCode()),"鲜食店+前置仓+会员店+清美云超"),
    POS_LIST_01(2, Arrays.asList(ShopTypeEnums.XS.getCode(),ShopTypeEnums.XD.getCode(), ShopTypeEnums.HYD.getCode(), ShopTypeEnums.THZT.getCode(), ShopTypeEnums.XSJM.getCode(), ShopTypeEnums.GF.getCode()),"鲜食店+前置仓+会员店+清美云超+鲜食加盟+工坊");

    private int code;

    private List<Integer> shopTypeList;

    private String desc;

    ShopTypeConditionEnum(int code, List<Integer> shopTypeList, String desc) {
        this.code = code;
        this.shopTypeList = shopTypeList;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public List<Integer> getShopTypeList() {
        return shopTypeList;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code查询枚举
     * @param code
     * @return
     */
    public static ShopTypeConditionEnum getShopTypeListByCode(Integer code){
        if(null == code){
            return null;
        }
        return Arrays.stream(ShopTypeConditionEnum.values()).filter(it -> it.getCode() == code).findFirst().orElse(null);
    }

}
