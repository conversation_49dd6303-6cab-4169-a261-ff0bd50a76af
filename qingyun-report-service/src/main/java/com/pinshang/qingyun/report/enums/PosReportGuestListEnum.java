package com.pinshang.qingyun.report.enums;

/**
 * <AUTHOR> @Date
 */
public enum PosReportGuestListEnum {
    ONE(1, "按pos机"),
    TWO(2, "按门店"),
    THREE(3, "按时间段"),
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;

    PosReportGuestListEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
