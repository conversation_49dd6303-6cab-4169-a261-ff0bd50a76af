package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TotalCashierDiscountODTO {

     @ApiModelProperty("原价金额合计")
     private BigDecimal totalFromAmount;

     @ApiModelProperty("实收金额合计")
     private BigDecimal totalToAmount;

     @ApiModelProperty("让利金额合计")
     private BigDecimal totalDiscount;
}
