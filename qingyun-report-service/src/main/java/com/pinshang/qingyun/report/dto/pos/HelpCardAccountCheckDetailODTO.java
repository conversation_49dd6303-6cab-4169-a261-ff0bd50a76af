package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PosHelpCardAccountCheckingODTO
 * <AUTHOR>
 * @Date 2023/2/23 17:55
 * @Description PosHelpCardAccountCheckingODTO
 * @Version 1.0
 */
@Data
public class HelpCardAccountCheckDetailODTO {

    private Long id;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("对账开始时间 yyyy-MM-dd")
    private Date checkDateBegin;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("对账结束时间 yyyy-MM-dd")
    private Date checkDateEnd;

    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("对账金额")
    private BigDecimal checkAmount;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty("品鲜交易金额")
    private BigDecimal pxTraderAmount;
}
