package com.pinshang.qingyun.report.controller.pos;

import com.pinshang.qingyun.report.dto.OrderCancelODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.service.pos.OrderDeleteService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/orderDelete")
public class OrderDeleteController {

    @Autowired
    private OrderDeleteService orderDeleteService;


    /**
     * 订单作废记录(分页)
     * @param orderDeleteIDTO
     * @return
     */
    @ApiOperation(value = "订单作废列表", notes = "订单作废列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/listOrderDeletePage")
    public TablePageInfo<OrderDeleteODTO> listOrderDelete(OrderDeleteIDTO orderDeleteIDTO) {
        return orderDeleteService.listOrderDelete(orderDeleteIDTO);
    }

    @ApiOperation(value = "订单作废明细", notes = "订单作废明细", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/orderDeleteDetail")
    public OrderDeleteDetailODTO orderDeleteDetail(String orderCode) {
        return orderDeleteService.orderDeleteDetail(orderCode);
    }

    /**
     * 订单作废报表(分页)
     * @param orderCancelIDTO
     * @return
     */
    @ApiOperation(value = "订单作废报表", notes = "订单作废报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/listOrderCancelPage")
    public TablePageInfo<OrderCancelODTO> listOrderCancelPage(OrderCancelIDTO orderCancelIDTO) {
        return orderDeleteService.listOrderCancelPage(orderCancelIDTO);
    }

}
