package com.pinshang.qingyun.report.controller.xd;

import com.pinshang.qingyun.report.dto.xd.DyCommodityVerifyReportIDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/05/31
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/dyCommodityVerify")
public class DyCommodityVerifyController {


    /**
     * 新增或者覆盖抖音券核销/冲正数据
     * 触发: 线上/pda 进行核销/冲正动作
     * 覆盖: 通过dy_item_id确认是否同一张券, 如果存在 则覆盖
     * @param idto
     * @return
     */
    @RequestMapping(value = "/report", method = RequestMethod.POST)
    private Boolean report(@RequestBody DyCommodityVerifyReportIDTO idto){
        return true;
    }
}
