package com.pinshang.qingyun.report.xdrealtime;

public class ReturnOrderConstants {

    public static class App {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:app:";
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店月销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Mini {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:mini:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Elm {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:elm:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Jddj {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:jddj:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Group {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:group:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Cloud {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:cloud:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Cloud_App {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:cloud_app:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Quick {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:quick:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Warehouse {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:warehouse:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

    public static class Warehouse_App {
        private static final String DAILY_REAL_TIME_SALES_MONITORING = "{xd:real_time_sales_monitoring}:returnOrder:daily:warehouse_app:";
        //门店订单量
        public static final String SHOP_ORDER_QUANTITY = DAILY_REAL_TIME_SALES_MONITORING + "shop_order_quantity:";
        //单门店毛利 排名用
        public static final String GROSS_PROFIT = DAILY_REAL_TIME_SALES_MONITORING + "gross_profit:";
        //单门店销售 排名用
        public static final String TURNOVER = DAILY_REAL_TIME_SALES_MONITORING + "turnover:";
    }

}
