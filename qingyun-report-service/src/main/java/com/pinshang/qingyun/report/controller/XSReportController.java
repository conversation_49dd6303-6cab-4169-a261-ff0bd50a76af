package com.pinshang.qingyun.report.controller;

import com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportIDTO;
import com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportODTO;
import com.pinshang.qingyun.xsuser.dto.XSUserReportIDTO;
import com.pinshang.qingyun.xsuser.dto.XSUserReportODTO;
import com.pinshang.qingyun.xsuser.service.XSUserClient;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.XsOrderService;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/xsReport")
@Api(value = "报表相关API", tags = "xs-report", description = "报表相关")
@Slf4j
public class XSReportController {

    @Autowired
    private XsOrderService xsOrderService;
    @Autowired
    private XSUserClient xsUserClient;

    @ApiOperation(value = "查询报表总览信息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/queryOperatingReportList")
    public List<XsOrderReportODTO> queryOperatingReportList(XsOrderReportIDTO dto)throws Exception{
        //TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Map<String, XSUserReportODTO> userMap = new HashMap<>();
        // 门店id为0表示全部门店，设置为空
        dto.setShopId(dto.getShopId().equals(0L) ? null : dto.getShopId());
        if (dto.getShopId() == null) {
            // 只有全部鲜食店的时候才查询用户信息
            XSUserReportIDTO userDto = new XSUserReportIDTO();
            userDto.setStartTime(dto.getStartTime());
            userDto.setEndTime(dto.getEndTime());
            // userDto.setEnterpriseId(tokenInfo.getEnterpriseId());
            List<XSUserReportODTO> userList = xsUserClient.queryUserCountWithDay(userDto);
            userMap = userList.stream().collect(Collectors.toMap(XSUserReportODTO::getCreateTime, Function.identity()));
        }
        //dto.setEnterpriseId(tokenInfo.getEnterpriseId());
        // 查询订单报表信息(已涵盖了时间段内所有天数的信息，没有的就是0)
        List<XsOrderReportODTO> list = xsOrderService.selectOrderReportInfo(dto);
        for (XsOrderReportODTO odto : list) {
            XSUserReportODTO userDto = userMap.get(odto.getCreateTime());
            if (userDto == null) {
                odto.setUserCount(0);
                odto.setUserTotal(0);
            } else {
                odto.setUserCount(userDto.getCount());
                odto.setUserTotal(userDto.getTotal());
            }
            if (odto.getCount().compareTo(0) == 0) {
                odto.setAverageAmount(BigDecimal.ZERO);
            } else {
                // 保留两位小数
                odto.setAverageAmount(odto.getTotalAmount().divide(BigDecimal.valueOf(odto.getCount()), 2, RoundingMode.HALF_UP));
            }
        }
        return list;
    }

    @ApiOperation(value = "导出", notes = "导出运营总览报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportReportInfo", method = RequestMethod.GET)
    ModelAndView exportReportInfo(XsOrderReportIDTO dto)throws Exception{
        boolean isAllShop = dto.getShopId().equals(0L) || null == dto.getShopId();
        // 查询报表信息
        List<XsOrderReportODTO> list = this.queryOperatingReportList(dto);

        Map<String, List<String>> data = new TreeMap<>();

        List<String> dataLst0 = new ArrayList<>();
        List<String> dataLst1 = new ArrayList<>();
        if(isAllShop){
            dataLst0.add("新增用户数");
            dataLst1.add("总用户数");
        }
        List<String> dataLst2 = new ArrayList<>();
        dataLst2.add("下单用户数");
        List<String> dataLst3 = new ArrayList<>();
        dataLst3.add("下单订单数");
        List<String> dataLst4 = new ArrayList<>();
        dataLst4.add("下单订单总额");
        List<String> dataLst5 = new ArrayList<>();
        dataLst5.add("出库订单数");
        List<String> dataLst6 = new ArrayList<>();
        dataLst6.add("出库净额");
        List<String> dataLst7 = new ArrayList<>();
        dataLst7.add("单平均价");
        int i = 1;
        // Excel title
        String[] titles = new String[list.size() + 1];
        titles[0] = "日期";
        for (XsOrderReportODTO xsDto : list) {
            // 动态设置title的值
            titles[i] = xsDto.getCreateTime();
            i++;
            if(isAllShop){
                dataLst0.add(String.valueOf(xsDto.getUserCount()));
                dataLst1.add(String.valueOf(xsDto.getUserTotal()));
            }
            dataLst2.add(String.valueOf(xsDto.getUserOrderCount()));
            dataLst3.add(String.valueOf(xsDto.getCount()));
            dataLst4.add(String.valueOf(xsDto.getTotalAmount()));
            dataLst5.add(String.valueOf(xsDto.getStockOutCount()));
            dataLst6.add(String.valueOf(xsDto.getStockOutTotalAmount()));
            dataLst7.add(String.valueOf(xsDto.getAverageAmount()));
        }
        if(isAllShop){
            data.put("key_0", dataLst0);
            data.put("key_1", dataLst1);
        }
        data.put("key_2", dataLst2);
        data.put("key_3", dataLst3);
        data.put("key_4", dataLst4);
        data.put("key_5", dataLst5);
        data.put("key_6", dataLst6);
        data.put("key_7", dataLst7);
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "线上运营总览表_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        // 动态设置title的值
        ExcelSheetTitleEnum.setTitles("operating_report_list", titles);
        map.put("sheetTitle", ExcelSheetTitleEnum.OPERATING_REPORT_LIST);
        map.put("data", data);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

}
