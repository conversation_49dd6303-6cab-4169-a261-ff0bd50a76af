package com.pinshang.qingyun.report.mapper.shop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.model.shop.CommodityFirstCateTax;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CommodityTaxFirstCateMapper extends MyMapper<CommodityFirstCateTax>{
    int batchInsertOnlineSales(@Param("list") List<CommodityFirstCateTax> insertList);

    int batchUpdateOnlineSales(@Param("list") List<CommodityFirstCateTax> updateList);
}
