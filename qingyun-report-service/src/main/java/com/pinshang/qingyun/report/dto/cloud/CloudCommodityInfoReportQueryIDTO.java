package com.pinshang.qingyun.report.dto.cloud;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/2/23
 */
@Data
public class CloudCommodityInfoReportQueryIDTO extends Pagination {

    @ApiModelProperty("组织code")
    private String orgCode;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("渠道来源: 8.云超小程序 9.云超APP")
    private Integer sourceType;

    @ApiModelProperty(value = "日期开始时间 yyyy-MM-dd")
    private String orderTimeBegin;
    @ApiModelProperty(value = "日期结束时间 yyyy-MM-dd")
    private String orderTimeEnd;


    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品一级分类ID")
    private Long cateId1;
    @ApiModelProperty(value = "商品二级分类ID")
    private Long cateId2;
    @ApiModelProperty(value = "商品三级分类ID")
    private Long cateId3;

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    private List<Long> shopIdList;

    @ApiModelProperty(value = "报表类型 1:云超订单取消明细表  2:云超补差退款明细表 3:云超配送失败明细表")
    private Integer reportType;

    /** 报表类型 1:云超订单取消明细表  2:云超补差退款明细表 3:云超配送失败明细表 */
    public static final class ReportType{
        public static final int CANCEL= 1;
        public static final int DIFF_RETURN = 2;
        public static final int FAIL = 3;
    }
}
