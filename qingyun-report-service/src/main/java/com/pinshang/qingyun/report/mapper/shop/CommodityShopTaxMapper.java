package com.pinshang.qingyun.report.mapper.shop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.infrastructure.data.query.annotate.DataQuery;
import com.pinshang.qingyun.report.dto.CommodityShopTaxODTO;
import com.pinshang.qingyun.report.dto.OnToOffAnalyzeDTO;
import com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO;
import com.pinshang.qingyun.report.dto.shop.CommodityTaxODTO;
import com.pinshang.qingyun.report.dto.shop.ShopTaxForJmByCateIDTO;
import com.pinshang.qingyun.report.dto.shop.ShopTaxForJmByCateODTO;
import com.pinshang.qingyun.report.model.shop.CommodityShopTax;
import com.pinshang.qingyun.report.model.shop.CommodityTax;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CommodityShopTaxMapper extends MyMapper<CommodityShopTax> {

    List<CommodityTax> getPosCommodityTaxFromOffline(@Param("toDoDay") String toDoDay);

    List<CommodityShopTaxODTO> getPosShopTaxFromOffline(@Param("toDoDay") String toDoDay);

    List<CommodityTax> queryOffLineVisitNum(@Param("day")String day);

    List<CommodityTaxODTO> queryOffLineVisitNumByType(@Param("day") String day, @Param("type") Integer type);

    // 线上
    @DataQuery(value = "onToOnLineAnalyzeCode")
    List<OnToOffAnalyzeDTO> onToOnLineAnalyzeCode(OnToOffAnalyzeIDTO dto);

    // 线下
    @DataQuery(value = "onToOffAnalyzeCode")
    List<OnToOffAnalyzeDTO> onToOffLineDataAnalyze(OnToOffAnalyzeIDTO dto);

    List<OnToOffAnalyzeDTO> onToOffAnalyze(OnToOffAnalyzeIDTO dto);

    List<ShopTaxForJmByCateODTO> getPosCommodityTaxFromOfflineByCate(@Param("idto") ShopTaxForJmByCateIDTO idto);
}
