package com.pinshang.qingyun.report.dto.shop;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommodityOnlineReportODto {

	private Long shopId;
	private String shopName;
	private String cateName1;
	private String cateName2;
	private String cateName3;
	private String barCode;
	private Long commodityId;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	//品牌
	private String brandName;
	//出库数量
	private BigDecimal realQuantity;
	//出库净额
	private BigDecimal realAmount;
	//退货数量
	private BigDecimal returnQuantity;
	//退货金额
	private BigDecimal returnAmount;
	//销售额
	private BigDecimal salesAmount;
	//成本
	private BigDecimal costTotal;
	//前台毛利率
	private BigDecimal grossProfitRate;
	//基准价
	private BigDecimal basePrice;
	//平均售价
	private BigDecimal averagePrice;
	//顾客数
	private Integer customerCount;

	private String barCodes;	// 子码列表
	// 销售数量
	private BigDecimal salesQuantity;

}
