package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.constant.RedissKeyConst;
import com.pinshang.qingyun.report.dto.pos.ConsumeReportIDTO;
import com.pinshang.qingyun.report.mapper.pos.CashierWaterDayMapper;
import com.pinshang.qingyun.report.mapper.pos.DaySaleSummaryMapper;
import com.pinshang.qingyun.report.mapper.pos.GuestListSummaryDayMapper;
import com.pinshang.qingyun.report.model.Category;
import com.pinshang.qingyun.report.model.pos.*;
import com.pinshang.qingyun.report.search.dto.EsDaySaleSummaryODTO;
import com.pinshang.qingyun.report.search.dto.EsDayThirdSaleSummaryODTO;
import com.pinshang.qingyun.report.search.service.PosReportSearchClient;
import com.pinshang.qingyun.report.service.SaveToESService;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2021/7/7
 */
@Slf4j
@Service
public class PosNotCurrentDayService {

    @Autowired
    private SalesSummaryReportService salesSummaryReportService;
    @Autowired
    private PosReportSearchClient posReportSearchClient;
    @Autowired
    private DaySaleSummaryMapper daySaleSummaryMapper;
    @Autowired
    private GuestListSummaryDayMapper guestListSummaryDayMapper;
    @Autowired
    private CashierWaterDayMapper cashierWaterDayMapper;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Autowired
    private SaveToESService saveToESService;
    @Autowired
    private RedissonClient redissonClient;
    /**
     * pos 非当日订单直接新增
     * @return
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public Boolean consumePosNotCurrentDayReport(ConsumeReportIDTO consumeReportIDTO) {
        List<ThirdSummary> thirdSummaryList = new ArrayList<>(); // 类别汇总(三类)
        List<DaySaleSummary> daySaleSummaryList = new ArrayList<>(); // 日汇总
        List<GuestListSummaryDay> guestListSummaryDayList = new ArrayList<>(); // 客单日汇总
        List<CashierWaterDay> cashierWaterDayList = new ArrayList<>(); // 收银流水
        List<EsDaySaleSummaryODTO> daySaleSummaryESList = new ArrayList<>(); // 日汇总ES
        List<EsDayThirdSaleSummaryODTO> dDayThirdSaleSummaryESList = new ArrayList<>(); // 类别汇总ES

        List<SalesSummaryReport> salesSummaryList = consumeReportIDTO.getSalesSummaryList();
        List<GuestListSummaryReport> guestList = consumeReportIDTO.getGuestList();
        List<ReportCashierWater> cashierWaterList = consumeReportIDTO.getCashierWaterReports();
        Map<BigDecimal, Category> cateMap = salesSummaryReportService.getCategoryMap();
        for (SalesSummaryReport summaryReport : salesSummaryList) {
            // 为null 设置 0
            setDefault(summaryReport);

            String commodityFirstCode = null != cateMap.get(summaryReport.getCommodityFirstId()) ? cateMap.get(summaryReport.getCommodityFirstId()).getCateCode() : "";
            String commoditySecondCode = null != cateMap.get(summaryReport.getCommoditySecondId()) ? cateMap.get(summaryReport.getCommoditySecondId()).getCateCode() : "";
            String commodityThirdCode = null != cateMap.get(summaryReport.getCommodityThirdId()) ? cateMap.get(summaryReport.getCommodityThirdId()).getCateCode() : "";

            // 类别汇总(三类)
            ThirdSummary thirdSummary = getThirdSummary(summaryReport, commodityFirstCode, commoditySecondCode, commodityThirdCode);
            thirdSummaryList.add(thirdSummary);

            // 日汇总
            DaySaleSummary daySaleSummary = getDaySaleSummary(summaryReport, commodityFirstCode, commoditySecondCode, commodityThirdCode);
            daySaleSummaryList.add(daySaleSummary);

            // 日汇总ES
            EsDaySaleSummaryODTO esDaySaleSummary = getEsDaySaleSummaryODTO(summaryReport, commodityFirstCode, commoditySecondCode, commodityThirdCode);
            daySaleSummaryESList.add(esDaySaleSummary);

            // 类别汇总ES
            EsDayThirdSaleSummaryODTO esDayThirdSaleSummary = getEsDayThirdSaleSummaryODTO(summaryReport, commodityFirstCode, commoditySecondCode, commodityThirdCode);
            dDayThirdSaleSummaryESList.add(esDayThirdSaleSummary);
        }
        // pos商品类别汇总(三类)
        salesSummaryReportService.batchInsertThirdSummaryReport(thirdSummaryList);
        daySaleSummaryMapper.insertList(daySaleSummaryList);

        // 客单日汇总
        for (GuestListSummaryReport guestListSummary : guestList) {
            GuestListSummaryDay guestDay = new GuestListSummaryDay();
            BeanUtils.copyProperties(guestListSummary, guestDay);
            guestDay.setSaleTime(DateUtil.getDateFormate(guestListSummary.getSaleTime(), "yyyy-MM-dd"));
            guestListSummaryDayList.add(guestDay);
        }
        guestListSummaryDayMapper.insertList(guestListSummaryDayList);

        // 收银流水日汇总
        for (ReportCashierWater cashierWater : cashierWaterList) {
            CashierWaterDay cashierWaterDay = new CashierWaterDay();
            BeanUtils.copyProperties(cashierWater, cashierWaterDay);

            Date createTime = DateUtil.parseDate(DateUtil.getDateFormate(cashierWater.getCreateTime(), "yyyy-MM-dd") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            cashierWaterDay.setCreateTime(createTime);
            cashierWaterDay.setSaleType(Integer.valueOf(cashierWater.getSaleType()));
            cashierWaterDayList.add(cashierWaterDay);
        }
        cashierWaterDayMapper.insertList(cashierWaterDayList);


        // 汇总数据进ES
        saveToESService.posNotCurrentDaySaveES(daySaleSummaryESList, dDayThirdSaleSummaryESList);
        return Boolean.TRUE;
    }

    private void setDefault(SalesSummaryReport summaryReport) {
        summaryReport.setSaleQuantity(summaryReport.getSaleQuantity() != null ? summaryReport.getSaleQuantity() : BigDecimal.ZERO);
        summaryReport.setReturnQuantity(summaryReport.getReturnQuantity() != null ? summaryReport.getReturnQuantity(): BigDecimal.ZERO);
        summaryReport.setGiveQuantity(summaryReport.getGiveQuantity() != null ? summaryReport.getGiveQuantity() : BigDecimal.ZERO);
        summaryReport.setSaleAmount(summaryReport.getSaleAmount() != null ? summaryReport.getSaleAmount() : BigDecimal.ZERO);
        summaryReport.setWeightAmount(summaryReport.getWeightAmount() != null ? summaryReport.getWeightAmount() : BigDecimal.ZERO);
        summaryReport.setReturnAmount(summaryReport.getReturnAmount() != null ? summaryReport.getReturnAmount() : BigDecimal.ZERO);
        summaryReport.setGiveAmount(summaryReport.getGiveAmount() != null ? summaryReport.getGiveAmount() : BigDecimal.ZERO);
        summaryReport.setDiscountAmount(summaryReport.getDiscountAmount() != null ? summaryReport.getDiscountAmount() : BigDecimal.ZERO);
        summaryReport.setTatalQuantity(summaryReport.getTatalQuantity() != null ? summaryReport.getTatalQuantity(): BigDecimal.ZERO);
        summaryReport.setTatalAmount(summaryReport.getTatalAmount() != null ? summaryReport.getTatalAmount() : BigDecimal.ZERO);
        summaryReport.setNoTaxRateAmount(summaryReport.getNoTaxRateAmount() != null ? summaryReport.getNoTaxRateAmount() : BigDecimal.ZERO);
        summaryReport.setTaxRateAmount(summaryReport.getTaxRateAmount() != null ? summaryReport.getTaxRateAmount() : BigDecimal.ZERO);
    }

    /**
     * 日汇总ES
     */
    private EsDaySaleSummaryODTO getEsDaySaleSummaryODTO(SalesSummaryReport summaryReport, String commodityFirstCode, String commoditySecondCode, String commodityThirdCode) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        EsDaySaleSummaryODTO esDaySaleSummary = new EsDaySaleSummaryODTO();
        BeanUtils.copyProperties(summaryReport,esDaySaleSummary);
        esDaySaleSummary.setCommodityFirstId(summaryReport.getCommodityFirstId().longValue());
        esDaySaleSummary.setCommodityFirstCode(commodityFirstCode);
        esDaySaleSummary.setCommoditySecondId(summaryReport.getCommoditySecondId().longValue());
        esDaySaleSummary.setCommoditySecondCode(commoditySecondCode);
        esDaySaleSummary.setCommodityThirdId(summaryReport.getCommodityThirdId().longValue());
        esDaySaleSummary.setCommodityThirdCode(commodityThirdCode);
        esDaySaleSummary.setCommodityPrice(summaryReport.getCommodityPrice().doubleValue());
        esDaySaleSummary.setTaxRate(summaryReport.getTaxRate().doubleValue());

        esDaySaleSummary.setSaleTime(sdf.format(summaryReport.getSaleTime()));

        esDaySaleSummary.setSaleQuantity(summaryReport.getSaleQuantity().doubleValue());
        esDaySaleSummary.setReturnQuantity(summaryReport.getReturnQuantity().doubleValue());
        esDaySaleSummary.setGiveQuantity(summaryReport.getGiveQuantity().doubleValue());

        esDaySaleSummary.setSaleAmount(summaryReport.getSaleAmount().doubleValue());
        esDaySaleSummary.setWeightAmount(summaryReport.getWeightAmount().doubleValue());
        esDaySaleSummary.setReturnAmount(summaryReport.getReturnAmount().doubleValue());
        esDaySaleSummary.setGiveAmount(summaryReport.getGiveAmount().doubleValue());
        esDaySaleSummary.setDiscountAmount(summaryReport.getDiscountAmount().doubleValue());

        esDaySaleSummary.setTatalQuantity(summaryReport.getTatalQuantity().doubleValue());
        esDaySaleSummary.setTatalAmount(summaryReport.getTatalAmount().doubleValue());
        esDaySaleSummary.setNoTaxRateAmount(summaryReport.getNoTaxRateAmount().doubleValue());
        esDaySaleSummary.setTaxRateAmount(summaryReport.getTaxRateAmount().doubleValue());

        esDaySaleSummary.setId(IdWorker.getId());
        return esDaySaleSummary;
    }

    /**
     * 类别汇总ES
     * @return
     */
    private EsDayThirdSaleSummaryODTO getEsDayThirdSaleSummaryODTO(SalesSummaryReport summaryReport, String commodityFirstCode, String commoditySecondCode, String commodityThirdCode) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        EsDayThirdSaleSummaryODTO esDayThirdSaleSummary = new EsDayThirdSaleSummaryODTO();
        BeanUtils.copyProperties(summaryReport,esDayThirdSaleSummary);
        esDayThirdSaleSummary.setCommodityFirstId(summaryReport.getCommodityFirstId().longValue());
        esDayThirdSaleSummary.setCommodityFirstCode(commodityFirstCode);
        esDayThirdSaleSummary.setCommoditySecondId(summaryReport.getCommoditySecondId().longValue());
        esDayThirdSaleSummary.setCommoditySecondCode(commoditySecondCode);
        esDayThirdSaleSummary.setCommodityThirdId(summaryReport.getCommodityThirdId().longValue());
        esDayThirdSaleSummary.setCommodityThirdCode(commodityThirdCode);

        esDayThirdSaleSummary.setSaleTime(sdf.format(summaryReport.getSaleTime()));

        esDayThirdSaleSummary.setSaleQuantity(summaryReport.getSaleQuantity().doubleValue());
        esDayThirdSaleSummary.setReturnQuantity(summaryReport.getReturnQuantity().doubleValue());
        esDayThirdSaleSummary.setGiveQuantity(summaryReport.getGiveQuantity().doubleValue());

        esDayThirdSaleSummary.setSaleAmount(summaryReport.getSaleAmount().doubleValue());
        esDayThirdSaleSummary.setWeightAmount(summaryReport.getWeightAmount().doubleValue());
        esDayThirdSaleSummary.setReturnAmount(summaryReport.getReturnAmount().doubleValue());
        esDayThirdSaleSummary.setGiveAmount(summaryReport.getGiveAmount().doubleValue());
        esDayThirdSaleSummary.setDiscountAmount(summaryReport.getDiscountAmount().doubleValue());

        esDayThirdSaleSummary.setTatalQuantity(summaryReport.getTatalQuantity().doubleValue());
        esDayThirdSaleSummary.setTatalAmount(summaryReport.getTatalAmount().doubleValue());
        esDayThirdSaleSummary.setNoTaxRateAmount(summaryReport.getNoTaxRateAmount().doubleValue());
        esDayThirdSaleSummary.setTaxRateAmount(summaryReport.getTaxRateAmount().doubleValue());

        BigDecimal taxRate = new BigDecimal("1").add(summaryReport.getTaxRate());
        esDayThirdSaleSummary.setNoTaxWeightAmount(summaryReport.getWeightAmount().divide(taxRate,2,BigDecimal.ROUND_HALF_UP).doubleValue());

        esDayThirdSaleSummary.setId(IdWorker.getId());
        return esDayThirdSaleSummary;
    }

    /**
     * 日汇总
     */
    private DaySaleSummary getDaySaleSummary(SalesSummaryReport summaryReport, String commodityFirstCode, String commoditySecondCode, String commodityThirdCode) {
        DaySaleSummary daySaleSummary = new DaySaleSummary();
        BeanUtils.copyProperties(summaryReport,daySaleSummary);
        daySaleSummary.setCommodityFirstCode(commodityFirstCode);
        daySaleSummary.setCommoditySecondCode(commoditySecondCode);
        daySaleSummary.setCommodityThirdCode(commodityThirdCode);
        return daySaleSummary;
    }

    /**
     * 类别汇总(三类)
     */
    private ThirdSummary getThirdSummary(SalesSummaryReport summaryReport, String commodityFirstCode, String commoditySecondCode, String commodityThirdCode) {
        ThirdSummary thirdSummary = new ThirdSummary();
        BeanUtils.copyProperties(summaryReport,thirdSummary);
        BigDecimal taxRate = new BigDecimal("1").add(summaryReport.getTaxRate());
        thirdSummary.setNoTaxWeightAmount(summaryReport.getWeightAmount().divide(taxRate,2,BigDecimal.ROUND_HALF_UP));
        thirdSummary.setCommodityFirstCode(commodityFirstCode);
        thirdSummary.setCommoditySecondCode(commoditySecondCode);
        thirdSummary.setCommodityThirdCode(commodityThirdCode);
        return thirdSummary;
    }

    /**
     * 异步判断订单sum(收银流水.pay_amount) 是否等于 sum(销售流水.transaction_amount)
     * 不相等则发送微信消息
     * @param consumeReportIDTO
     * @return
     */
    @Async
    public Boolean checkAmount(ConsumeReportIDTO consumeReportIDTO) {
        BigDecimal payAmount = BigDecimal.ZERO;
        BigDecimal transactionAmount = BigDecimal.ZERO;
        for(ReportCashierWater cashierWater : consumeReportIDTO.getCashierWaterReports()){
            payAmount = payAmount.add(cashierWater.getPayAmount());
        }
        for(ReportSalesWater saleWater : consumeReportIDTO.getSalesWaters()){
            transactionAmount = transactionAmount.add(saleWater.getTransactionAmount());
        }

        if(payAmount.compareTo(transactionAmount) != 0){
            StringBuffer sb = new StringBuffer();
            sb.append("【pos报表消费金额不同异常】");
            sb.append("订单号 " + consumeReportIDTO.getOrderCode());

            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }
        return Boolean.TRUE;
    }


    /**
     * 将非当日的日期放入redisson
     * @param orderCode
     */
    @Async
    public void setDateListToRedisson(String orderCode){
        RLock lock = redissonClient.getLock("fixPosDayReportLock");
        lock.lock(2, TimeUnit.SECONDS);
        try {
            String dateString = orderCode.substring(7, 13);
            String date = DateUtil.getDateFormate(DateUtil.parseDate(dateString,"yyMMdd"),"yyyy-MM-dd");
            RBucket<List<String>> bucket = redissonClient.getBucket(RedissKeyConst.FIX_POS_DAY_REPORT);
            List<String> dateList = bucket.get();
            if(CollectionUtils.isNotEmpty(dateList)){
                if(!dateList.contains(date)){
                    dateList.add(date);
                }
            }else {
                dateList = new ArrayList<>();
                dateList.add(date);
            }
            bucket.set(dateList, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);
        }finally{
            lock.unlock();
        }
    }
}
