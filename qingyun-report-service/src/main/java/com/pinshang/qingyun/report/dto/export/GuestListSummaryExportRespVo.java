package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GuestListSummaryExportRespVo {

    @ExcelProperty("时段")
    private String hourTimeStr;

    /** 客单量*/
    @ExcelProperty("客单量")
    private BigDecimal guestQuantity;

    @ExcelProperty("客单占比")
    private String avgQuantity;//客单占比

    /** 客单金额*/
    @ExcelProperty("客单金额")
    private BigDecimal guestAmount;

    @ExcelProperty("金额占比")
    private String avgAmount;//金额占比

    @ExcelProperty("平均客单价")
    private BigDecimal avgGuestPrice;//平均客单价


}
