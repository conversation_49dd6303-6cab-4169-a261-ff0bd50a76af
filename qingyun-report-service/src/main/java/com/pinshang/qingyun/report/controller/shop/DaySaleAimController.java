package com.pinshang.qingyun.report.controller.shop;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.exception.ParamException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.shop.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.shop.DaySaleAimService;
import com.pinshang.qingyun.report.util.ReportUtil;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;


@Slf4j
@RestController
@RequestMapping("/daySaleAim")
public class DaySaleAimController {

    @Autowired
    private DaySaleAimService daySaleAimService;


    /**
     * 日销售目标管理(分页)
     * @param idto
     * @return
     */
    @ApiOperation(value = "日销售目标管理列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("listDaySaleAimPage")
    public PageInfo<DaySaleAimODTO> listDaySaleAimPage(DaySaleAimPageIDTO idto) {
        return daySaleAimService.listDaySaleAimPage(idto);
    }

    /**
     * 新增日销售目标管理
     * @param daySaleAimIDTO
     * @return
     */
    @ApiOperation(value = "新增", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "daySaleAimIDTO", value = "新增日销售目标管理", required = true, paramType = "body", dataType = "DaySaleAimIDTO")
    @PostMapping("saveDaySaleAim")
    public void saveDaySaleAim(@RequestBody DaySaleAimIDTO daySaleAimIDTO){
        daySaleAimService.saveDaySaleAim(daySaleAimIDTO);

    }

    @ApiOperation(value = "根据id查询日销售目标管理", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "id", value = "根据id查询日销售目标管理", required = true, paramType = "query")
    @GetMapping("getDaySaleAimById")
    public DaySaleAimODTO getDaySaleAimById(@RequestParam("id") Long id) {
        return daySaleAimService.getDaySaleAimById(id);
    }

    /**
     * 修改日销售目标管理
     * @param
     * @param daySaleAimIDTO
     * @return
     */
    @ApiOperation(value = "修改", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "daySaleAimIDTO", value = "修改日销售目标管理", required = true, paramType = "body", dataType = "DaySaleAimIDTO")
    @PutMapping("updateDaySaleAim")
    public void updateDaySaleAim(@RequestBody DaySaleAimIDTO daySaleAimIDTO){
        daySaleAimService.updateDaySaleAim(daySaleAimIDTO);
    }

    /**
     * 删除日销售目标管理
     * @param id
     * @return
     */
    @ApiOperation(value = "删除", notes = "删除", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "path")
    @DeleteMapping("/deleteDaySaleAim/{id}")
    public int deleteDaySaleAim(@PathVariable("id") Long id) {
        return daySaleAimService.deleteDaySaleAim(id);
    }

    /**
     * 批量导入 日销售目标管理
     * @param list
     * @return
     */
    @RequestMapping(value = {"/import/importDaySaleAim"}, method = {RequestMethod.POST})
    public Object importDaySaleAim(@RequestBody List<SaleAimIDTO> list)throws ParamException {
        return daySaleAimService.importDaySaleAim(list);
    }

    @ApiOperation(value = "日销售目标管理-导出", notes = "日销售目标管理-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/daySaleAimReport", method = RequestMethod.GET)
    public ModelAndView exportInfoDaySaleAimReportReport(DaySaleAimPageIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (idto.getShopId() == null) {
            idto.setShopId(tokenInfo.getShopId());
        }
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        PageInfo<DaySaleAimODTO> result = daySaleAimService.listDaySaleAimPage(idto);
        List<DaySaleAimODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        if (null != list && !list.isEmpty()) {
            for (DaySaleAimODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getSaleTime());
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getAimSaleAmount()+"");
                dataLst.add(dto.getRemark());
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "日销售目标管理报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.DAY_SAIL_AIM);
        map.put("data", data);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "日销售完成率")
    @PostMapping("/daySalesCompletionRatePage")
    public TablePageInfo<SalesCompletionRateODTO> daySalesCompletionRatePage(@RequestBody SalesCompletionRateIDTO idto){
        return daySaleAimService.daySalesCompletionRatePage(idto);
    }

    @ApiOperation(value = "日销售完成率导出")
    @GetMapping("/exportInfo/daySalesCompletionRatePage")
    public ModelAndView exportDaySalesCompletionRatePage(SalesCompletionRateIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<SalesCompletionRateODTO> result = daySaleAimService.daySalesCompletionRatePage(idto);
        List<SalesCompletionRateODTO> list = result.getList();
        DaySalesCompletionRateSum head = (DaySalesCompletionRateSum)result.getHeader();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0 ;
        if(null != list && !list.isEmpty()){
            dataLst = new ArrayList<>();
            dataLst.add("合计");
            dataLst.add("");
            dataLst.add("");
            dataLst.add("");
            dataLst.add(head.getAimSaleAmountSum().setScale(2,RoundingMode.HALF_UP)+"");
            dataLst.add(head.getOfflineSalesSum().setScale(2, RoundingMode.HALF_UP) + "");
            dataLst.add(head.getOfflineSalesSum().divide(head.getAimSaleAmountSum(),3,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1,RoundingMode.HALF_UP) + "%");
            data.put("key_" + i++, dataLst);
        }
        if (null != list && !list.isEmpty()) {
            for (SalesCompletionRateODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(i + "");
                dataLst.add(dto.getSaleTime());
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getAimSaleAmount().setScale(2,RoundingMode.HALF_UP)+"");
                dataLst.add(dto.getOfflineSales().setScale(2, RoundingMode.HALF_UP) + "");
                dataLst.add(dto.getOfflineSales().divide(dto.getAimSaleAmount(),3,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1,RoundingMode.HALF_UP) + "%");
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "日销售完成率报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.DAY_SALES_COMPLETION_RATE);
        map.put("data", data);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "月销售完成率导出")
    @GetMapping("/exportInfo/monthSalesCompletionRatePage")
    public ModelAndView exportInfoMonthSalesCompletionRatePage(SalesCompletionRateIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<SalesCompletionRateODTO> result = daySaleAimService.monthSalesCompletionRatePage(idto);
        List<SalesCompletionRateODTO> list = result.getList();
        DaySalesCompletionRateSum head = (DaySalesCompletionRateSum)result.getHeader();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 0;
        if(null != list && !list.isEmpty()){
            dataLst = new ArrayList<>();
            dataLst.add("合计");
            dataLst.add("");
            dataLst.add("");
            dataLst.add("");
            dataLst.add(head.getAimSaleAmountSum().setScale(2,RoundingMode.HALF_UP)+"");
            dataLst.add(head.getOfflineSalesSum().setScale(2, RoundingMode.HALF_UP) + "");
            dataLst.add(head.getOfflineSalesSum().divide(head.getAimSaleAmountSum(),3,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1,RoundingMode.HALF_UP) + "%");
            data.put("key_" + i++, dataLst);
        }
        if (null != list && !list.isEmpty()) {
            for (SalesCompletionRateODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(i + "");
                dataLst.add(dto.getSaleTime());
                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getAimSaleAmount().setScale(2,RoundingMode.HALF_UP)+"");
                dataLst.add(dto.getOfflineSales().setScale(2, RoundingMode.HALF_UP) + "");
                dataLst.add(dto.getOfflineSales().divide(dto.getAimSaleAmount(),3,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1,RoundingMode.HALF_UP) + "%");
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "月销售完成率报表" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.MONTH_SALES_COMPLETION_RATE);
        map.put("data", data);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "月销售完成率")
    @PostMapping("monthSalesCompletionRatePage")
    public TablePageInfo<SalesCompletionRateODTO> monthSalesCompletionRatePage(@RequestBody SalesCompletionRateIDTO idto){
        return daySaleAimService.monthSalesCompletionRatePage(idto);
    }


}
