package com.pinshang.qingyun.report.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.dto.mdCheck.DynamicShopCheckSummaryReportODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckGroupODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportODTO;
import com.pinshang.qingyun.report.dto.mdCheck.ShopCheckSummaryReportODTO;
import com.pinshang.qingyun.report.dto.shop.CheckCommodityGroupCompleteIDTO;
import com.pinshang.qingyun.report.service.shop.ShopCheckReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/mdreport/check")
@Api(value = "门店考核报表API", tags = "md-report-check")
@Slf4j
public class ShopCheckReportController {

    @Autowired
    private ShopCheckReportService shopCheckReportService;

    @Autowired
    private com.pinshang.qingyun.report.service.MdCheckService MdCheckService;

    @GetMapping("/checkCommodityGroupCompleteRate")
    @ApiOperation(value = "考核商品组完成率", notes = "考核商品组完成率",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public PageInfo<MdCheckReportODTO> checkCommodityGroupCompleteRate(CheckCommodityGroupCompleteIDTO idto) {
        return shopCheckReportService.checkCommodityGroupCompleteRate(idto);
    }


    @GetMapping("/checkSummaryReport")
    @ApiOperation(value = "考核汇总表", notes = "考核汇总表",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public DynamicShopCheckSummaryReportODTO checkSummaryReport(@RequestParam(value = "beginDate", defaultValue = "2020-11-01") String beginDate,
                                                                @RequestParam(value = "endDate", defaultValue = "2020-11-30") String endDate) {
        return shopCheckReportService.checkSummaryReport(beginDate, endDate);
    }

    @GetMapping("/getMdCheckGroupList")
    @ApiOperation(value = "当前门店未达成的订货目标中，所涉及的考核商品组", notes = "当前门店未达成的订货目标中，所涉及的考核商品组",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MdCheckGroupODTO> getMdCheckGroupList() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return MdCheckService.getMdCheckGroupList(tokenInfo.getShopId());
    }

    @GetMapping("/getMdCheckPanList")
    @ApiOperation(value = "获取当前门店下面的考核方案", notes = "获取当前门店下面的考核方案",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MdCheckReportODTO> getShopCheckPan(@RequestParam(value="checkGroupId", required=false) Long checkGroupId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return MdCheckService.getShopCheckPan(tokenInfo.getShopId(),checkGroupId);
    }
}
