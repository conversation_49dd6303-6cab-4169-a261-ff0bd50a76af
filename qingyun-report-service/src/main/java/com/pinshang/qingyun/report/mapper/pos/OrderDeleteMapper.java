package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.OrderCancelODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.OrderDelete;
import com.pinshang.qingyun.report.model.shop.Shop;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderDeleteMapper extends MyMapper<OrderDelete> {

    List<OrderDeleteODTO> listOrderDelete(@Param("idto") OrderDeleteIDTO orderDeleteIDTO);

    OrderDeleteODTO querySumOrderDelete(@Param("idto") OrderDeleteIDTO orderDeleteIDTO);

    List<OrderCancelODTO> listOrderCancel(@Param("idto") OrderCancelIDTO orderCancelIDTO);

    OrderCancelODTO querySumOrderCancel(@Param("idto") OrderCancelIDTO orderCancelIDTO);

    OrderDeleteDetailODTO orderDeleteDetail(@Param("orderCode") String orderCode);

    List<AIMonitorCommodityDTO> listCommodityByOrderCode(@Param("orderCode") String orderCode);
}
