package com.pinshang.qingyun.report.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommodityShopTaxODTO {
    private Long shopId;
    private String dateTime;
    private BigDecimal onlineSales =BigDecimal.ZERO;//线上销售额
    private BigDecimal offlineSales =BigDecimal.ZERO;//线下销售额
    private BigDecimal totalSales =BigDecimal.ZERO;//总销售额
    private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额
    private BigDecimal costTotal =BigDecimal.ZERO;//成本金额合计
    private BigDecimal onlineCost =BigDecimal.ZERO;//线上成本金额合计
    private BigDecimal offlineCost =BigDecimal.ZERO;//线下成本金额合计
    private BigDecimal saleReturnOrderTotal =BigDecimal.ZERO;//退货金额
    private BigDecimal stockAdjustAmountTotal =BigDecimal.ZERO;//库存更正金额
    private Integer offlineVisitorNumber =0; //线下来客数
    private BigDecimal offlineAverageAmount =BigDecimal.ZERO; //线下客单价
    private BigDecimal totalQuanty =BigDecimal.ZERO;//销售总量
    private BigDecimal orderQuanty =BigDecimal.ZERO;//进货数量
}
