package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.ExceptionMemberODTO;
import com.pinshang.qingyun.report.dto.pos.MemberExceptionUseIDTO;
import com.pinshang.qingyun.report.dto.pos.MemberExceptionUseODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface MemberAccountExecptionMapper {


    List<MemberExceptionUseODTO> listMemeberAccountExecptionUser(@Param("idto") MemberExceptionUseIDTO idto);

    Integer findCount(@Param("shopId") Long shopId, @Param("userId") Long userId, @Param("cashDate") String cashDate);

    void updateSaleCount(@Param("shopId") Long shopId, @Param("userId") Long userId, @Param("cashDate") String cashDate
            , @Param("saleCount") Long saleCount, @Param("saleTotalAmount") BigDecimal saleTotalAmount);

    void insertExecptionMember(@Param("execptionMember") ExceptionMemberODTO execptionMember);

    ExceptionMemberODTO findMemberAccountExecptionInfo(@Param("shopId") Long shopId, @Param("userId") Long userId,
                                                       @Param("cashDate") String cashDate);


    void deleteMemberAccountExecptionBySaleTime(@Param("saleTime")String saleTime);
    void insertMemberAccountExecptionReport(@Param("saleTime")String beginTime);

    void deleteHandDiscountBySaleTime(@Param("saleTime")String saleTime);
    void insertHandDiscountReport(@Param("saleTime")String beginTime);

    void deletePosReportHourSummaryBySaleTime(@Param("saleTime")String saleTime);
    void insertPosReportHourSummary(@Param("saleTime")String beginTime);
}
