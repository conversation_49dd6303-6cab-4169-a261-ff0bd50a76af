package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GiftCardSalesWaterMessage {

//    @ApiModelProperty("门店")
//    private Long shopId;
//
//    @ApiModelProperty("订单编号")
//    private String orderCode;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("序列表")
    private String cardSn;

    @ApiModelProperty("面值")
    private Integer parValue;

    @ApiModelProperty("商品金额")
    private BigDecimal price;

    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("数量")
    private Integer number;

    @ApiModelProperty("代销商户")
    private Long consignmentId;

//    @ApiModelProperty("POS机号")
//    private String macCode;
//
//    @ApiModelProperty("流水时间")
//    private Date saleTime;
//
//    @ApiModelProperty("员工号")
//    private String employeeNumber;
//
//    @ApiModelProperty("员工姓名")
//    private String employeeName;
}
