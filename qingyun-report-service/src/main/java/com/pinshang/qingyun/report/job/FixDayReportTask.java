package com.pinshang.qingyun.report.job;

import com.pinshang.qingyun.report.constant.RedissKeyConst;
import com.pinshang.qingyun.report.service.WeChatSendMessageService;
import com.pinshang.qingyun.report.service.pos.PosReportFixService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2020/10/10
 */
@Component
@Slf4j
public class FixDayReportTask {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PosReportFixService posReportFixService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    // 每30分钟执行一次
    //@Scheduled(cron ="00 0/30 * * * ?")
    @Scheduled(cron ="0 0 0/2 * * ?") // 每2个小时跑一次
    public void scheduleJobs(){
        log.info("修复商品总表(pos非当日数据)开始-----------------------");

        String lockKey = "fixPosDayReport_lock";
         RLock lock = redissonClient.getLock(lockKey);
        if (!lock.isLocked()) {
            lock.lock(5L, TimeUnit.MINUTES);

            RBucket<List<String>> bucket = redissonClient.getBucket(RedissKeyConst.FIX_POS_DAY_REPORT);
            List<String> dateList = bucket.get();
            try{
                if(CollectionUtils.isNotEmpty(dateList)){
                    posReportFixService.fixDayReport(dateList);
                    bucket.delete();
                }
                log.info("修复商品总表(pos非当日数据)-----------------------" + dateList);
            }catch (Exception e){
                log.error("修复商品总表(pos非当日数据)---异常-----------------------", e);

                StringBuffer sb = new StringBuffer();
                sb.append("修复商品总表(pos非当日数据)---异常" + StringUtils.strip(dateList.toString(),"[]"));
                weChatSendMessageService.sendWeChatMessage(sb.toString());
            }finally{
                lock.unlock();
            }
        }

        log.info("修复商品总表(pos非当日数据)结束-----------------------");
    }
}
