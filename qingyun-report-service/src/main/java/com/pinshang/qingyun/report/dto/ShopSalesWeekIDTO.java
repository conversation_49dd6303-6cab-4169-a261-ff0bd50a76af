package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ShopSalesWeekIDTO extends Pagination {

    @ApiModelProperty(position = 1, value ="门店id")
    private Long shopId;

    @Change(value = DataQueryConstant.NOW)
    @ApiModelProperty(position = 2, value ="销售日期:今日(yyyy-MM-dd)")
    private String today;

    @ApiModelProperty(position = 3, value ="销售日期:上周(yyyy-MM-dd)")
    private String lastWeek;

    private List<Long> shopIdList;

    @ApiModelProperty("门店类型")
    private Integer shopType;

    @ApiModelProperty("省id")
    private Long provinceId;

}
