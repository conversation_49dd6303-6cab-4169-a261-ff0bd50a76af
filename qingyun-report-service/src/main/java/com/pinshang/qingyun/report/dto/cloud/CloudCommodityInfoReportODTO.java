package com.pinshang.qingyun.report.dto.cloud;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class CloudCommodityInfoReportODTO {

    private Long shopId;
    private String shopCode;
    private String shopName;
    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("组织名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    @ApiModelProperty("订单号")
    private String orderCode;

    @ApiModelProperty("订单来源")
    private String sourceType;

    @ApiModelProperty("预约日期")
    private Date toShopDate;
    @ApiModelProperty("配送失败、补差退款、退款完成时间")
    private Date billTime;

    public String getToShopDateStr(){
        return DateUtil.getDateFormate(toShopDate,"yyyy-MM-dd");
    }
    public String getBillTimeStr(){
        return DateUtil.getDateFormate(billTime,"yyyy-MM-dd HH:mm:ss");
    }

    private Long commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    private String commodityUnitName;

    @ApiModelProperty("税率")
    private String taxRate;
    private String commodityFirstKindName;
    private String commoditySecondKindName;
    private String commodityThirdKindName;

    private String barCode;
    @ApiModelProperty("商品条形码")
    private List<String> barCodeList;


    @ApiModelProperty("订单数量")
    private BigDecimal quantity;
    @ApiModelProperty("商品金额")
    private BigDecimal amount;
    @ApiModelProperty("实收金额")
    private BigDecimal realAmount;
    @ApiModelProperty("销售成本")
    private BigDecimal weightAmount;
    @ApiModelProperty("实发数量")
    private BigDecimal packageQuantity;
    @ApiModelProperty("实发金额")
    private BigDecimal packageAmount;
    @ApiModelProperty("差异退款金额")
    private BigDecimal realDiffAmount;


}
