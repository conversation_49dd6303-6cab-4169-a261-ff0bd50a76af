package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommodityDeleteIDTO extends Pagination {

	@ApiModelProperty(position = 1, value = "门店id")
	private Long shopId;

	@ApiModelProperty(position = 2, value = "开始时间")
	private String beginTime;

	@ApiModelProperty(position = 3, value = "结束时间")
	private String endTime;

	@ApiModelProperty(position = 4, value = "收银员id")
	private Long casherId;

	@ApiModelProperty(position = 4, value = "1按门店汇总 2按日期汇总 3按收银员汇总")
	private Integer type;

	@ApiModelProperty(position = 6, value = "订单编号")
	private String orderCode;

	@ApiModelProperty(position = 7, value = "门店编码")
	private String shopCode;

	@ApiModelProperty(hidden = true)
	private List<Long> shopIdList;

	private Boolean isCurrentDay = false;

	@ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
	private Integer posType;

	@ApiModelProperty("开始小时")
	private String hourBegin;
	@ApiModelProperty("结束小时")
	private String hourEnd;
	@ApiModelProperty("删除单品金额≥")
	private String compareOrderAmount;

}

