package com.pinshang.qingyun.report.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2023/2/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CloudDeliveryFailExportRespVo {

    @ExcelProperty("部门")
    private String orgName;
    @ExcelProperty("客户编号")
    private String storeCode;
    @ExcelProperty("门店名称")
    private String shopName;
    @ExcelProperty("订单编号")
    private String orderCode;
    @ExcelProperty("订单来源")
    private String sourceType;

    @ExcelProperty("预约日期")
    private String toShopDateStr;
    @ExcelProperty("配送失败时间")
    public String billTimeStr;

    @ExcelProperty("商品编码")
    private String commodityCode;
    @ExcelProperty("商品名称")
    private String commodityName;
    @ExcelProperty("规格")
    private String commoditySpec;
    @ExcelProperty("单位")
    private String commodityUnitName;

    @ExcelProperty("订单数量")
    private BigDecimal quantity;
    @ExcelProperty("商品金额")
    private BigDecimal amount;
    @ExcelProperty("实收金额")
    private BigDecimal realAmount;
    @ExcelProperty("销售成本")
    private BigDecimal weightAmount;
    @ExcelProperty("实发数量")
    private BigDecimal packageQuantity;
    @ExcelProperty("实发金额")
    private BigDecimal packageAmount;

    @ExcelProperty("条码")
    private String barCode;
    @ExcelProperty("后台大类")
    private String commodityFirstKindName;
    @ExcelProperty("后台中类")
    private String commoditySecondKindName;
    @ExcelProperty("后台小类")
    private String commodityThirdKindName;
    @ExcelProperty("税率")
    private String taxRate;
    
}
