package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.mapper.pos.*;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName PosHelpCardService
 * <AUTHOR>
 * @Date 2023/2/22 15:01
 * @Description PosHelpCardService
 * @Version 1.0
 */
@Service
public class PosHelpCardService {
    @Autowired
    private ShopClient shopClient;

    @Autowired
    private HelpCardTraderSummaryMapper helpCardTraderSummaryMapper;

    @Autowired
    private HelpCardAccountCheckMapper helpCardAccountCheckMapper;

    @Autowired
    private HelpCardTransactionFlowMapper helpCardTransactionFlowMapper;

    @Autowired
    private HelpCardCheckDailyMapper helpCardCheckDailyMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    public TablePageInfo<HelpCardCashierWaterPageODTO> helpCardCashierWaterPage(HelpCardCashierWaterPageIDTO idto){
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
        } else {
            throw new BizLogicException("操作日期不能为空！");
        }

        TablePageInfo<HelpCardCashierWaterPageODTO> tablePageInfo = new TablePageInfo<>();

        // 查询部门下门店
        List<Long> shopIdList = this.getShopIdList(idto.getOrgCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }
        idto.setShopIdList(shopIdList);

        PageInfo<HelpCardCashierWaterPageODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardTraderSummaryMapper.helpCardCashierWaterPage(idto);
        });
        List<HelpCardCashierWaterPageODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }

        BigDecimal sum = helpCardTraderSummaryMapper.helpCardCashierWaterSum(idto);
        tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo,TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }

    /**
     * 根据组织code和用户权限获取shopIdList. 无查询结果返回空
     * @param orgCode
     * @return
     */
    private List<Long> getShopIdList(String orgCode){
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return null;
        }

        if (StringUtil.isBlank(orgCode)) {
            return shopIdList;
        }
        List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(orgCode);
        if (CollectionUtils.isEmpty(shopDtos)) {
            return null;
        }
        List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
        shopIdList.retainAll(orgShopIdList);
        return shopIdList;
    }

    public TablePageInfo<HelpCardCommoditySummaryODTO> helpCardCommoditySummary(HelpCardCommoditySummaryIDTO idto){
         if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
        } else {
            throw new BizLogicException("操作日期不能为空！");
        }
        TablePageInfo<HelpCardCommoditySummaryODTO> tablePageInfo = new TablePageInfo<>();

        // 查询部门下门店
        List<Long> shopIdList = this.getShopIdList(idto.getOrgCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }
        idto.setShopIdList(shopIdList);

        PageInfo<HelpCardCommoditySummaryODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardTraderSummaryMapper.helpCardCommoditySummary(idto);
        });
        List<HelpCardCommoditySummaryODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }
        BigDecimal sum = helpCardTraderSummaryMapper.helpCardCommoditySummarySum(idto);
        tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo,TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }

    public TablePageInfo<HelpCardShopSummaryODTO> helpCardShopSummary(HelpCardShopSummaryIDTO idto){
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
        } else {
            throw new BizLogicException("操作日期不能为空！");
        }
        // 查询部门下门店
        TablePageInfo<HelpCardShopSummaryODTO> tablePageInfo = new TablePageInfo<>();

        // 查询部门下门店
        List<Long> shopIdList = this.getShopIdList(idto.getOrgCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }
        idto.setShopIdList(shopIdList);

        PageInfo<HelpCardShopSummaryODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardTraderSummaryMapper.helpCardShopSummary(idto);
        });
        List<HelpCardShopSummaryODTO> list = pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(BigDecimal.ZERO);
            return tablePageInfo;
        }
        BigDecimal sum = helpCardTraderSummaryMapper.helpCardShopSummarySum(idto);
        tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo,TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }

    public TablePageInfo<HelpCardCheckSummaryODTO> helpCardCheckSummary(@RequestBody HelpCardCheckSummaryIDTO idto) {
        if (!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())) {
            idto.setBeginTime(idto.getBeginTime() + " 00:00:00");
            idto.setEndTime(idto.getEndTime() + " 23:59:59");
        } else {
            throw new BizLogicException("操作日期不能为空！");
        }
        TablePageInfo<HelpCardCheckSummaryODTO> tablePageInfo = new TablePageInfo<>();
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return null;
        }
        idto.setShopIdList(shopIdList);
        PageInfo<HelpCardCheckSummaryODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardCheckDailyMapper.helpCardCheckSummary(idto);
        });
        List<HelpCardCheckSummaryODTO> list = pageInfo.getList();
        if (SpringUtil.isEmpty(list)) {
            tablePageInfo.setList(new ArrayList<>());
            tablePageInfo.setHeader(new HelpCardCheckSummaryODTO());
            return tablePageInfo;
        }
        HelpCardCheckSummaryODTO head = helpCardCheckDailyMapper.helpCardCheckSummarySum(idto);
        tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(head);
        return tablePageInfo;
    }

    public PageInfo<HelpCardTradeWaterPageODTO> helpCardTradeWaterPage(@RequestBody HelpCardTradeWaterPageIDTO idto){
        QYAssert.isTrue(null != idto.getBeginTime() && null != idto.getEndTime(), "操作日期不能为空!");
        PageInfo<HelpCardTradeWaterPageODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            helpCardTransactionFlowMapper.helpCardTradeWaterPage(idto);
        });
        return pageInfo;
    }
}
