package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@ToString
@Entity
@Table(name = "t_pos_report_order_delete_item")
@NoArgsConstructor
public class OrderDeleteItem extends BaseModel<OrderDeleteItem> {


    private Long orderDeleteId;

    /** 商品id */
    private Long commodityId;

    /** 商品编码 */
    private String commodityCode;

    /** 商品名称 */
    private String commodityName;

    /** 单位 */
    private String commodityUnit;

    /** 商品规格 */
    private String commoditySpec;

    /** 主条形码 */
    private String barCode;

    /** 数量 */
    private BigDecimal quanty;

    /** 单价 */
    private BigDecimal price;

    /** 售价 */
    private BigDecimal salePrice;

}
