package com.pinshang.qingyun.report.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.exception.BusinessException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.bigdata.dto.MdCommoditySaleIDTO;
import com.pinshang.qingyun.bigdata.dto.MdCommoditySaleODTO;
import com.pinshang.qingyun.bigdata.service.XdCommoditySalesReportClient;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.report.constant.RedissKeyConst;
import com.pinshang.qingyun.report.dto.OrderInfoODTO;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.shop.*;
import com.pinshang.qingyun.report.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.etl.EtlExecute;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.mapper.ReportMapper;
import com.pinshang.qingyun.report.mapper.ShopReportMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.Commodity;
import com.pinshang.qingyun.report.search.service.PosReportSearchClient;
import com.pinshang.qingyun.report.service.settle.ShopStockInService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.mdCheck.MdCheckReportClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopReportService {

    @Autowired
    private ShopReportMapper shopReportMapper;

    @Autowired
    private CommodityPriceClient commodityPriceClient;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private ReportMapper reportMapper;

    @Autowired
    private MdCheckReportClient mdCheckReportClient;

    @Autowired
    private XdCommoditySalesReportClient xdCommoditySalesReportClient;
    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;

    @Autowired
    private PosReportSearchClient posReportSearchClient;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private EtlExecute etlExecute;

    @Autowired
    private MdCheckInfoService mdCheckInfoService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private ShopStockInService shopStockInService;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CloudCommodityReportService cloudCommodityReportService;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private DataQueryChangeService dataQueryChangeService;
    /**
     * 获取短交报表实发总金额
     * @param timeStamp
     * @return
     */
    public  BigDecimal getShortDeliveryRealDeliveryAmount(String timeStamp){
        return shopReportMapper.getShortDeliveryRealDeliveryAmount(timeStamp+" 00:00:00",timeStamp+" 23:59:59");
    }
    /**
     * 获取商品实发汇总 总金额
     * @param timeStamp
     * @return
     */
    public  BigDecimal getCommodityRealDeliveryAmount(String timeStamp){
        return shopReportMapper.getCommodityRealDeliveryAmount(timeStamp+" 00:00:00",timeStamp+" 23:59:59");
    }
    /**
     * 门店短交报表
     * @param vo
     * @return
     */
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto vo) {
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(vo.getBeginDate(), "yyyy-MM-dd"),1), DateTimeUtil.parse(vo.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");

        String today = DateTimeUtil.formatDate(new Date(),"yyyy-MM-dd");
        boolean isToday = vo.getBeginDate().endsWith(today) && vo.getEndDate().equals(today);
        if(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate())){
            vo.setBeginDate(vo.getBeginDate()+ " 00:00:00");
            vo.setEndDate(vo.getEndDate()+ " 23:59:59");
        }

        PageInfo<ShortDeliveryReportODto> pageDate = null;
        if(isToday){
            pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                shopReportMapper.shortDeliveryTodayReport(vo);
            });
        }else{
            pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                shopReportMapper.shortDeliveryBeforeYesterdayReport(vo);
            });
        }

        List<ShortDeliveryReportODto> list=pageDate.getList();
        if(null !=list && !list.isEmpty()){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
            Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);
            for(ShortDeliveryReportODto entry:list){
                String barCodes=barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");

                CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(Long.valueOf(entry.getCommodityId()));
                if(commodityWarehouseODto != null){
                    entry.setWarehouseName(commodityWarehouseODto.getWarehouseName());
                }
            }
        }
        return pageDate ;
    }

    /**
     * 商品总表（含税）
     * @param vo
     * @return
     */
    public PageInfo<CommodityTaxReportODto> commodityTaxReport(CommodityTaxReportIDto vo) {
        //处理无效查询
        if(vo.getCollectType() == 1){
            vo.setCateId1(null);
            vo.setCateId2(null);
            vo.setCateId3(null);
            vo.setCommodityKey(null);
            vo.setBarCode(null);
        }else if(vo.getCollectType() == 3){
            vo.setCateId2(null);
            vo.setCateId3(null);
            vo.setCommodityKey(null);
            vo.setBarCode(null);
        }else if(vo.getCollectType() == 4){
            vo.setCateId3(null);
            vo.setCommodityKey(null);
            vo.setBarCode(null);
        }else if(vo.getCollectType() == 5){
            vo.setCommodityKey(null);
            vo.setBarCode(null);
        }

        QYAssert.isTrue(vo.getBeginDate().equals(vo.getEndDate()) || DateUtil.isBefore(DateTimeUtil.parse(vo.getBeginDate(), "yyyy-MM-dd"), DateTimeUtil.parse(vo.getEndDate(), "yyyy-MM-dd")), "日期有误!");

        //处理天数——>线下客单
        int day = DateUtil.getDayDif(DateUtil.parseDate(vo.getEndDate(), "yyyy-MM-dd"), DateUtil.parseDate(vo.getBeginDate(), "yyyy-MM-dd"));
        PageInfo<CommodityTaxReportODto> pageDate = null;
        //汇总类型:门店
        if(vo.getCollectType() == 1){
            pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                shopReportMapper.commodityTaxReportByShop(vo);
            });
        }else{
            // 商品汇总
            if(vo.getCollectType() == 2){
                pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                    shopReportMapper.commodityTaxCommodityReport(vo);
                });
            }else if(vo.getCollectType() == 3){
                // 大类汇总
                pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                    shopReportMapper.commodityTaxFirstReport(vo);
                });
            }else if(vo.getCollectType() == 4){
                // 中类汇总
                pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                    shopReportMapper.commodityTaxSecondReport(vo);
                });
            }else if(vo.getCollectType() == 5){
                // 小类汇总
                pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                    shopReportMapper.commodityTaxThirdReport(vo);
                });
            }else {
                QYAssert.isFalse("汇总类型错误");
            }

        }

        List<CommodityTaxReportODto> list = pageDate.getList();
        if(CollectionUtils.isNotEmpty(list)){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);

            for (CommodityTaxReportODto entry : list) {
                if(null != entry.getCommodityId()){
                    String barCodes=barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                    entry.setBarCodes(barCodes);
                    entry.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
                }

                if(entry.getTotalSales().compareTo(BigDecimal.ZERO) > 0){
                    entry.setOfflineRatio(entry.getOfflineSales().divide(entry.getTotalSales(), 4 ,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    entry.setGrossProfitRate((entry.getTotalSales().subtract(entry.getCostTotal())).divide(entry.getTotalSales(), 4 ,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
                }else{
                    entry.setOfflineRatio(BigDecimal.ZERO);
                    entry.setGrossProfitRate(BigDecimal.ZERO);
                }
                if(entry.getOfflineAverageAmount() != null && entry.getOfflineAverageAmount().compareTo(BigDecimal.ZERO) > 0 && day > 0){
                    entry.setOfflineAverageAmount(entry.getOfflineSales().divide(entry.getOfflineVisitorNumber(), 2 ,BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return pageDate;
    }

    /**
     * 门店实收商品分析表
     * @param vo
     * @return
     * @throws ParseException
     */
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto vo) throws ParseException{
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate()), "请选择日期");
        PageInfo<ActualReceiptAnalysisODto> pageDate = null;
        vo.setOrderBeginDate(DateUtils.parseDate(vo.getBeginDate() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        vo.setOrderEndDate(DateUtils.parseDate(vo.getEndDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));

        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            shopReportMapper.actualReceiptAnalysisReport(vo);
        });
        setActualReceptList(pageDate);

        return pageDate;
    }

    /**
     * 门店实收商品分析表(月度报表)
     * @param vo
     * @return
     * @throws ParseException
     */
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisMonthReport(ActualReceiptAnalysisIDto vo) throws ParseException{
        QYAssert.isTrue(!StringUtil.isBlank(vo.getMonthBegin()), "请选择开始月份");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getMonthEnd()), "请选择结束月份");
        PageInfo<ActualReceiptAnalysisODto> pageDate = null;

        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            shopReportMapper.actualReceiptAnalysisMonthReport(vo);
        });
        setActualReceptList(pageDate);

        return pageDate;
    }

    /**
     * 门店实收汇总返回值设置
     * @param pageDate
     */
    private void setActualReceptList(PageInfo<ActualReceiptAnalysisODto> pageDate) {
        if(CollectionUtils.isNotEmpty(pageDate.getList())){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);
            Map<Long,BigDecimal> priceMap = commodityPriceClient.getCommoditySupplyPrice();

            List<Long> commodityIdList = pageDate.getList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);

            pageDate.getList().forEach(a -> {
                if(null != priceMap){
                    a.setSupplyPrice(priceMap.get(a.getCommodityId()));
                }
                BigDecimal supplyPrice = a.getSupplyPrice();
                BigDecimal totalRealReceiveQuantity = a.getTotalRealReceiveQuantity();
                if(null != supplyPrice) {
                    a.setTotalSupplyPrice(supplyPrice.multiply(totalRealReceiveQuantity).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                BigDecimal totalRealDeliveryQuantity = a.getTotalRealDeliveryQuantity();
                a.setQuantityDifference(totalRealDeliveryQuantity.subtract(totalRealReceiveQuantity));

                CommoditySupplierODto commoditySupplierODto = commodityIdAndSupplierODTOMap.get(a.getCommodityId());
                if(commoditySupplierODto != null){
                    a.setSupplierName(commoditySupplierODto.getSupplierName());
                    a.setRealName(commoditySupplierODto.getRealName());
                }

                String barCodes = barCodeMap.get(Long.valueOf(a.getCommodityId()));
                a.setBarCodes(barCodes);
                a.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
            });
        }
    }

    public TablePageInfo<OrderSalesAnalysisODTO> orderSalesAnalysis(OrderSalesAnalysisIDTO idto){
        PageInfo<OrderSalesAnalysisODTO> pageDate = null;
        Long shopId = idto.getShopId();
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));
        if(shopId == null){
            idto.setShopIds(shopIdList);
        }else{
            if (SpringUtil.isNotEmpty(shopIdList) && shopIdList.contains(shopId)) {
                idto.getShopIds().add(shopId);
            }
        }

        if (SpringUtil.isEmpty(idto.getShopIds())) {
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }

        // 大类汇总
        if(idto.getSummaryType() == 1) {
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                shopReportMapper.orderSalesAnalysisFirstCateReport(idto);
            });
        }else {
            // 中类汇总
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                shopReportMapper.orderSalesAnalysisSecondCateReport(idto);
            });
        }

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        OrderSalesAnalysisODTO orderSalesAnalysisODTO = shopReportMapper.orderSalesAnalysisReportSum(idto);
        if(orderSalesAnalysisODTO == null){
            orderSalesAnalysisODTO = new OrderSalesAnalysisODTO();
        }
        tablePageInfo.setHeader(orderSalesAnalysisODTO);
        return tablePageInfo;
    }


    /**
     * 类别客单周同比
     * @param idto
     * @return
     */
    public TablePageInfo<CateAverageAmountODTO> cateAverageAmountReport(CateAverageAmountIDTO idto){
        PageInfo<CateAverageAmountODTO> pageDate = null;
        Long shopId = idto.getShopId();
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        if(shopId == null){
            throw new BusinessException("请选择一个门店.");
            //idto.setShopIds(tokenInfo.getShopIdList());
        }else{
            List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
            List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(null, null, shopTypeList, idto.getProvinceId()));
            if(!shopIdList.contains(shopId)){
                TablePageInfo<CateAverageAmountODTO> tablePageInfo  = new TablePageInfo<>();
                tablePageInfo.setList(new ArrayList<>());
                return tablePageInfo;
            }
            idto.getShopIds().add(shopId);
        }
        Date parse = DateTimeUtil.parse(idto.getDate(), "yyyy-MM-dd");
        String lastweek = DateTimeUtil.addDay(parse, -7);
        idto.setLastweek(lastweek);
        TablePageInfo tablePageInfo = null;

        Date now = DateUtil.getNowDate();
        // 如果选择当天之前,则走大数据
        if(parse.before(now) && dataQueryChangeService.enableDataQuery()) {
            Integer pageNo = idto.getPageNo();
            Integer pageSize = idto.getPageSize();
            // 调用大数据查询全部
            idto.initExportPage();
            // 大类
            if(idto.getSummaryType() == 1) {
                // 查询今日客单量、今日客单价
                List<CateAverageAmountBiODTO> todayList = shopReportMapper.cateAverageAmountFirstReport(idto);

                // 查询上周
                idto.setDate(idto.getLastweek());
                List<CateAverageAmountBiODTO> lastweekList = shopReportMapper.cateAverageAmountFirstReport(idto);

                tablePageInfo = cateAverageAmountToPage(todayList, lastweekList,idto.getSummaryType(), pageNo, pageSize);
            }else {
                // 中类
                // 查询今日客单量、今日客单价
                List<CateAverageAmountBiODTO> todayList = shopReportMapper.cateAverageAmountSecondReport(idto);

                // 查询上周
                idto.setDate(idto.getLastweek());
                List<CateAverageAmountBiODTO> lastweekList = shopReportMapper.cateAverageAmountSecondReport(idto);

                tablePageInfo = cateAverageAmountToPage(todayList, lastweekList, idto.getSummaryType(), pageNo, pageSize);
            }

        }else {
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                shopReportMapper.cateAverageAmountReport(idto);
            });

            tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
            CateAverageAmountODTO cateAverageAmountODTO = shopReportMapper.cateAverageAmountSum(idto);
            if(cateAverageAmountODTO == null){
                cateAverageAmountODTO = new CateAverageAmountODTO();
                cateAverageAmountODTO.setOfflineVisitorNumber(null);
                cateAverageAmountODTO.setLastweekOfflineVisitorNumber(null);
                cateAverageAmountODTO.setOfflineAverageAmount(null);
                cateAverageAmountODTO.setLastweekOfflineAverageAmount(null);
            }
            tablePageInfo.setHeader(cateAverageAmountODTO);
        }

        return tablePageInfo;
    }

    /**
     * 查询今天、上周数据转page
     * @param todayList
     * @param lastweekList
     * @return
     */
    private TablePageInfo cateAverageAmountToPage(List<CateAverageAmountBiODTO> todayList, List<CateAverageAmountBiODTO> lastweekList,
                                                  Integer summaryType,Integer pageNo, Integer pageSize){
        Boolean isFirstCate = (summaryType == 1);

        TablePageInfo tablePageInfo = new TablePageInfo();
        // 数据合并汇总
        Set<CateAverageAmountBiODTO> mergeList = new HashSet<>();
        Map<String, List<CateAverageAmountBiODTO>> todayMap = new HashMap<>();
        Map<String, List<CateAverageAmountBiODTO>> lastWeekMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(todayList)) {
            todayMap = todayList.stream().collect(Collectors.groupingBy(item -> {
                if(isFirstCate) {
                    return item.getShopId() + "" + item.getCategoryFirstId();
                }else {
                    return item.getShopId() + "" + item.getCategorySecondId();
                }
            }));
            todayList.forEach(item -> {
                CateAverageAmountBiODTO cateAverageAmountBiODTO = new CateAverageAmountBiODTO();
                cateAverageAmountBiODTO.setShopId(item.getShopId());
                cateAverageAmountBiODTO.setShopNo(item.getShopNo());
                cateAverageAmountBiODTO.setShopName(item.getShopName());
                if(isFirstCate) {
                    cateAverageAmountBiODTO.setCategoryFirstId(item.getCategoryFirstId());
                }else {
                    cateAverageAmountBiODTO.setCategorySecondId(item.getCategorySecondId());
                }
                cateAverageAmountBiODTO.setCateName(item.getCateName());
                mergeList.add(cateAverageAmountBiODTO);
            });
        }

        if(CollectionUtils.isNotEmpty(lastweekList)) {
            lastWeekMap = lastweekList.stream().collect(Collectors.groupingBy(item -> {
                if(isFirstCate) {
                    return item.getShopId() + "" + item.getCategoryFirstId();
                }else {
                    return item.getShopId() + "" + item.getCategorySecondId();
                }
            }));
            lastweekList.forEach(item -> {
                CateAverageAmountBiODTO cateAverageAmountBiODTO = new CateAverageAmountBiODTO();
                cateAverageAmountBiODTO.setShopId(item.getShopId());
                cateAverageAmountBiODTO.setShopNo(item.getShopNo());
                cateAverageAmountBiODTO.setShopName(item.getShopName());
                if(isFirstCate) {
                    cateAverageAmountBiODTO.setCategoryFirstId(item.getCategoryFirstId());
                }else {
                    cateAverageAmountBiODTO.setCategorySecondId(item.getCategorySecondId());
                }
                cateAverageAmountBiODTO.setCateName(item.getCateName());
                mergeList.add(cateAverageAmountBiODTO);
            });
        }

        if(CollectionUtils.isEmpty(mergeList)) {
            return new TablePageInfo<>();
        }else {
            List<CateAverageAmountBiODTO> cateAverageAmountBiList = new ArrayList<>();
            for(CateAverageAmountBiODTO item : mergeList){
                CateAverageAmountBiODTO biODTO = BeanCloneUtils.copyTo(item, CateAverageAmountBiODTO.class);
                String key = "";
                if(isFirstCate) {
                    key = item.getShopId() + "" + item.getCategoryFirstId();
                }else {
                    key = item.getShopId() + "" + item.getCategorySecondId();
                }

                if(todayMap.containsKey(key)) {
                    List<CateAverageAmountBiODTO> list = todayMap.get(key);
                    biODTO.setOfflineVisitorNumber(list.get(0).getOfflineVisitorNumber());
                    biODTO.setOfflineAverageAmount(list.get(0).getOfflineAverageAmount());
                    biODTO.setOfflineSales(list.get(0).getOfflineSales());
                }

                if(lastWeekMap.containsKey(key)){
                    List<CateAverageAmountBiODTO> list = lastWeekMap.get(key);
                    biODTO.setLastweekOfflineVisitorNumber(list.get(0).getOfflineVisitorNumber());
                    biODTO.setLastweekOfflineAverageAmount(list.get(0).getOfflineAverageAmount());
                    biODTO.setLastOfflineSales(list.get(0).getOfflineSales());
                }
                cateAverageAmountBiList.add(biODTO);
            }

            // 求sum
            Long offlineVisitorNumber = 0L;
            BigDecimal offlineAverageAmount = BigDecimal.ZERO;
            Long lastweekOfflineVisitorNumber = 0L;
            BigDecimal lastweekOfflineAverageAmount = BigDecimal.ZERO;
            for(CateAverageAmountBiODTO biODTO : cateAverageAmountBiList){
                offlineVisitorNumber = offlineVisitorNumber + biODTO.getOfflineVisitorNumber();
                offlineAverageAmount = offlineAverageAmount.add(biODTO.getOfflineSales());
                lastweekOfflineVisitorNumber = lastweekOfflineVisitorNumber + biODTO.getLastweekOfflineVisitorNumber();
                lastweekOfflineAverageAmount = lastweekOfflineAverageAmount.add(biODTO.getLastOfflineSales());
            }

            CateAverageAmountODTO headSum = new CateAverageAmountODTO();
            headSum.setOfflineVisitorNumber(offlineVisitorNumber);
            headSum.setLastweekOfflineVisitorNumber(lastweekOfflineVisitorNumber);
            headSum.setOfflineAverageAmount(offlineAverageAmount);
            headSum.setLastweekOfflineAverageAmount(lastweekOfflineAverageAmount);

            // 转page
            List<CateAverageAmountODTO> pageList = BeanCloneUtils.copyTo(cateAverageAmountBiList, CateAverageAmountODTO.class);
            PageInfo<CateAverageAmountODTO> pageDate = ListToPageInfoUtil.convert(pageList, pageSize, pageNo);

            tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
            tablePageInfo.setHeader(headSum);
        }

        return tablePageInfo;
    }

    /**
     * 商品实发汇总表
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReport(RealDeliveryReportIDto vo) {
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");
       // Assert.isTrue(DateUtil.isAfter(DateUtil.addDay(DateTimeUtil.parse(vo.getBeginDate(), "yyyy-MM-dd"),93), DateTimeUtil.parse(vo.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过三个月!");
        if(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate())){
            vo.setBeginDate(vo.getBeginDate()+ " 00:00:00");
            vo.setEndDate(vo.getEndDate()+ " 23:59:59");
        }

        PageInfo<RealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            shopReportMapper.realDeliveryReport(vo);
        });
        List<RealDeliveryReportODto> list = pageDate.getList();
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(null !=list && !list.isEmpty()){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);
            for(RealDeliveryReportODto entry:list){
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
                //entry.setRealDeliveryAmount(entry.getPrice().multiply(entry.getDeliveryNum()));
                //entry.setDifferNum(entry.getOrderNum().subtract(entry.getDeliveryNum()));
            }
            //获取实发总金额
            realTotalAmount = shopReportMapper.realTotalDeliveryReport(vo);
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount.setScale(2,BigDecimal.ROUND_HALF_UP));
        return tablePageInfo;
    }


    /**
     * 品类实发汇总表
     * @param vo
     * @return
     */
    public TablePageInfo<ThirdRealDeliveryReportODto> thirdRealDeliveryReport(RealDeliveryReportIDto vo) {
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");
        /*if(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate())){
            vo.setBeginDate(vo.getBeginDate()+ " 00:00:00");
            vo.setEndDate(vo.getEndDate()+ " 23:59:59");
        }*/


        if(CollectionUtils.isNotEmpty(vo.getShopTypeList()) || CollectionUtils.isNotEmpty(vo.getManagementModeList())) {
            List<Long> shopIdList = shopMapper.queryShopIdListByParam(vo.getShopTypeList(), vo.getManagementModeList());
            if(CollectionUtils.isEmpty(shopIdList)) {
                return new TablePageInfo<>();
            }else {
                vo.setShopIdList(shopIdList);
            }
        }

        PageInfo<ThirdRealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            if(vo.getGroupCate() == 1){
                shopReportMapper.thirdRealDeliveryFirstReport(vo);
            }else if(vo.getGroupCate() == 2){
                shopReportMapper.thirdRealDeliverySecondReport(vo);
            }else if(vo.getGroupCate() == 3){
                shopReportMapper.thirdRealDeliveryThirdReport(vo);
            }else {
                QYAssert.isTrue(false, "汇总类型异常");
            }
        });
        List<ThirdRealDeliveryReportODto> list = pageDate.getList();
        ThirdRealDeliveryReportODto odto = new ThirdRealDeliveryReportODto();
        if(CollectionUtils.isNotEmpty(list)){

            for(ThirdRealDeliveryReportODto dto:list){
                dto.setOrderAmount(null != dto.getOrderAmount() ? dto.getOrderAmount().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
                dto.setRealAmount(null != dto.getRealAmount() ? dto.getRealAmount().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
                dto.setReturnAmount(null != dto.getReturnAmount() ? dto.getReturnAmount().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

            }

            RealDeliveryReportIDto sumVo = BeanCloneUtils.copyTo(vo,RealDeliveryReportIDto.class);
            sumVo.setPageNo(1);
            sumVo.setPageSize(Integer.MAX_VALUE);
            odto = shopReportMapper.thirdRealDeliverySumReport(sumVo).get(0);
            odto.setOrderAmount(null != odto.getOrderAmount() ? odto.getOrderAmount().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            odto.setRealAmount(null != odto.getRealAmount() ? odto.getRealAmount().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            odto.setReturnAmount(null != odto.getReturnAmount() ? odto.getReturnAmount().setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(odto);
        return tablePageInfo;
    }


    /**
     * 订单表(月度汇总)
     * @param beginTime
     * @param endTime
     */
    @Async
    public void orderReportMonth(String beginTime,String endTime){
        //删除数据
        shopReportMapper.deleteOrderReportMonth(beginTime.substring(0,7));
        //新增数据
        shopReportMapper.insertOrderReportMonth(beginTime,endTime);
    }


    /**
     * 商品实发汇总表(按客户类型)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReport(RealDeliveryReportIDto vo) {
        Assert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        Assert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");
        // Assert.isTrue(DateUtil.isAfter(DateUtil.addDay(DateTimeUtil.parse(vo.getBeginDate(), "yyyy-MM-dd"),93), DateTimeUtil.parse(vo.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过三个月!");
        if(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate())){
            vo.setBeginDate(vo.getBeginDate()+ " 00:00:00");
            vo.setEndDate(vo.getEndDate()+ " 23:59:59");
        }

        PageInfo<RealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            shopReportMapper.realDeliveryStoreTypeReport(vo);
        });
        List<RealDeliveryReportODto> list = pageDate.getList();
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(null !=list && !list.isEmpty()){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);
            for(RealDeliveryReportODto entry:list){
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            }
            //获取实发总金额
            realTotalAmount = shopReportMapper.realTotalDeliveryStoreTypeReport(vo);
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount.setScale(2,BigDecimal.ROUND_HALF_UP));
        return tablePageInfo;
    }


    /**
     * 门店订货汇总表》商品导入
     * @return
     */
    public List<String> findCommodityByCommodityCodes(ShopOrderGoodCommodityIDto shopOrderGoodCommodityIDto) {
        String commodityCodes = shopOrderGoodCommodityIDto.getCommodityCodes();
        QYAssert.isTrue(StringUtils.isNotBlank(commodityCodes), "商品编号不能为空!");
        List<String> codesList =  Arrays.asList(commodityCodes.split("\n"));
        QYAssert.isTrue(SpringUtil.isNotEmpty(codesList), "请以回车分割商品编码!");

        List<Commodity> commodityList = commodityMapper.getCommodityByCommodityCodes(codesList);
        StringBuffer sb = new StringBuffer("商品信息不存在\n");
        int i = 0;
        if(CollectionUtils.isEmpty(commodityList)){
            for(String code : codesList){
                if(i < 3){
                    sb.append(code + "\n");
                }
                i ++;
            }

        }else {
            List<String> commodityCodeList = commodityList.stream().map(item -> item.getCommodityCode()).collect(Collectors.toList());
            for(String code : codesList) {
                if (!commodityCodeList.contains(code)) {
                    if(i < 3){
                       sb.append(code + "\n");
                    }
                    i ++;
                }
            }
        }

        QYAssert.isTrue(sb.length() <= 8, sb.append("...").toString());

        return commodityList.stream().map(item -> item.getCommodityId()+"").collect(Collectors.toList());
    }


    /**
     * 门店订货汇总表
     * @param idto
     * @return
     */
    public TablePageInfo<ShopOrderGoodReportODto> shopOrderGoodReport(ShopOrderGoodReportIDto idto) {
        QYAssert.isTrue(!StringUtil.isBlank(idto.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(idto.getEndDate()), "请选择送货日期");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(idto.getCommodityIdList()), "商品不能为空");

        idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
        idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 9, "送货日期的跨度不能超过10天");

        PageInfo<ShopOrderGoodReportODto> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            shopReportMapper.shopOrderGoodReport(idto);
        });
        List<ShopOrderGoodReportODto> list = pageDate.getList();
        if(null !=list && !list.isEmpty()){
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(null);
            for(ShopOrderGoodReportODto entry : list){
                String[] barCode = barCodeMap.get(Long.valueOf(entry.getCommodityId())).split(",");
                entry.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                entry.setBarCodeList(barCodeList);
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            }
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        return tablePageInfo;
    }

    /**
     * 汇总月短交数据，汇总月考核信息
     * @param beginTime
     * @param endTime
     * @return
     */
    @Async
    @Transactional
    public Boolean mdCheckReportMonth(String beginTime, String endTime) {
        Calendar cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE,   -1);
        String yesterday = DateUtil.getDateFormate(cal.getTime(),"yyyy-MM-dd");

        // 如果是当月第一天,跑上个月第一天到最后一天的
        if(com.pinshang.qingyun.report.util.DateUtils.isFirstDayOfMonth()){
            beginTime = com.pinshang.qingyun.report.util.DateUtils.getFirstDay();
            endTime = com.pinshang.qingyun.report.util.DateUtils.getLastDay();
            yesterday = endTime;
        }
        // 如果跑的时间结束日期在 昨天之前
        if(DateUtil.parseDate(endTime,"yyyy-MM-dd").before(cal.getTime())){
            yesterday = endTime;
        }
        // 获取当月第一天到当月最后一天的所有启用的考核范围内的商品
        List<Long> commodityIdList = mdCheckReportClient.getMdCheckCommodityIdList(beginTime,endTime);
        if(CollectionUtils.isNotEmpty(commodityIdList)){
            List<String> dayList = com.pinshang.qingyun.report.util.DateUtils.getBetweenDate(beginTime,yesterday);
            if(CollectionUtils.isNotEmpty(dayList)){
                for(String day : dayList){
                    // 删除
                    reportMapper.deleteMdCheckCommodity(day);

                    MdCommoditySaleIDTO mdCommoditySaleIDTO = new MdCommoditySaleIDTO();
                    mdCommoditySaleIDTO.setDayTime(day);
                    mdCommoditySaleIDTO.setCommodityIdList(commodityIdList);
                    List<MdCommoditySaleODTO> mdCommoditySaleODTOList = xdCommoditySalesReportClient.queryBOrderQuantityAmount(mdCommoditySaleIDTO);
                    if(CollectionUtils.isNotEmpty(mdCommoditySaleODTOList)){
                        // 批量新增
                        int index = 0;
                        int count = 500;
                        while (true) {
                            List<MdCommoditySaleODTO> items = mdCommoditySaleODTOList.stream().skip(index).limit(count).collect(Collectors.toList());
                            if (items.size() > 0) {
                                // 一次插入500条
                                reportMapper.batchInsertMdCheckCommodityList(items);

                                index += items.size();
                            } else {
                                break;
                            }
                        }
                    }
                    // 新增
                    //reportMapper.insertMdCheckCommodityList(day,commodityIdList);
                }
            }
        }

        // 调用 月考核信息汇总
        String finalBeginTime = beginTime;
        String finalEndTime = endTime;
        String finalYesterday = yesterday;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try{
                    mdCheckInfoService.mdCheckInfoMonthReport(finalBeginTime, finalEndTime, finalYesterday);
                } catch (Exception e){
                    StringBuffer sb = new StringBuffer();
                    sb.append("月考核信息汇总异常");
                    //发送微信模板信息
                    weChatSendMessageService.sendWeChatMessage(sb.toString());
                }
            }
        });

        return true;
    }

    /**
     * B订单实发补偿
     * @param orderInfoList
     * @return
     */
    @Transactional
    public Boolean bOrderFix(List<OrderInfoODTO> orderInfoList) throws Exception {

        List<String> orderCodeList = orderInfoList.stream().distinct().map(item -> item.getOrderCode()).collect(Collectors.toList());
        List<String> orderTimeList = orderInfoList.stream().map(item -> DateUtil.getDateFormate(item.getOrderTime(),"yyyy-MM-dd")).collect(Collectors.toList());

        // 修复短交报表数据 > 根据订单号删除
        shopReportMapper.deleteDayOrderReportByCodes(orderCodeList);
        // 批量新增
        shopReportMapper.batchInsertDayOrderReport(orderInfoList);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                RLock lock = redissonClient.getLock("fixBOrderReportESLock");
                lock.lock(2, TimeUnit.SECONDS);
                try {
                    // 将orderTime,orderCode放入rediss(修复短交报表ES、修复品类实发报表)
                    setBOrderRedisValue(orderCodeList, orderTimeList);
                }finally{
                    lock.unlock();
                }
            }
        });
        return true;
    }

    /**
     * 将orderTime,orderCode放入rediss
     * @param orderCodeList
     * @param orderTimeList
     */
    private void setBOrderRedisValue(List<String> orderCodeList, List<String> orderTimeList) {
        //修复短交报表ES
        RBucket<List<String>> bucketES = redissonClient.getBucket(RedissKeyConst.FIX_B_ORDER_REPORT_ES);
        List<String> orderCodes = bucketES.get();
        if(CollectionUtils.isNotEmpty(orderCodes)){
            for(String orderCode : orderCodeList){
                if(!orderCodes.contains(orderCode)){
                    orderCodes.add(orderCode);
                }
            }
        }else {
            orderCodes = new ArrayList<>();
            orderCodes.addAll(orderCodeList);
        }
        bucketES.set(orderCodes, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);


        //修复品类实发报表
        RBucket<List<String>> bucket = redissonClient.getBucket(RedissKeyConst.FIX_B_ORDER_REPORT);
        List<String> dateList = bucket.get();
        if(CollectionUtils.isNotEmpty(dateList)){
            for(String orderTime : orderTimeList){
                if(!dateList.contains(orderTime)){
                    dateList.add(orderTime);
                }
            }
        }else {
            dateList = new ArrayList<>();
            dateList.addAll(orderTimeList);
        }
        bucket.set(dateList, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 将toShopDate放入rediss
     */
    public void setXdOrderDelayRedisValue(String toShopDate) {
        //修复 xd-order送货日期之后配送成功、失败。进行数据补偿
        RBucket<Set<String>> bucket = redissonClient.getBucket(RedissKeyConst.FIX_XD_ORDER_DELAY_COMPLETE);
        Set<String> toShopDateList = bucket.get();
        if(CollectionUtils.isNotEmpty(toShopDateList)){
            if(!toShopDateList.contains(toShopDate)){
                toShopDateList.add(toShopDate);
            }
        }else {
            toShopDateList = new HashSet<>();
            toShopDateList.add(toShopDate);
        }
        bucket.set(toShopDateList, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 修复 门店实际入库汇总（门店实收结算商品表、门店实收结算汇总表）
     */
    public void fixBOrderReport(){
        RBucket<List<String>> bucket = redissonClient.getBucket(RedissKeyConst.FIX_B_ORDER_REPORT);
        List<String> dateList = bucket.get();
        try {
            if(CollectionUtils.isNotEmpty(dateList)){
                Set<String> newDayList = new HashSet<>(dateList);

                for(String day : newDayList){
                    // 修复 门店实际入库汇总（门店实收结算商品表、门店实收结算汇总表）
                    shopStockInService.shopStockInTax(day);
                }

                bucket.delete();
                log.warn("B订单实发补偿修复门店实际入库汇总-----------------------" + StringUtils.strip(newDayList.toString(),"[]"));
            }
        } catch (Exception e) {
            log.error("B订单实发补偿修复门店实际入库汇总异常", e);

            StringBuffer sb = new StringBuffer();
            sb.append("B订单实发补偿修复门店实际入库汇总异常 " + StringUtils.strip(dateList.toString(),"[]"));
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }

    }

    /**
     * 修复短交ES
     */
    public void fixShortESReport(){
        RBucket<List<String>> bucketES = redissonClient.getBucket(RedissKeyConst.FIX_B_ORDER_REPORT_ES);
        List<String> orderCodeList = bucketES.get();
        try {
            if(CollectionUtils.isNotEmpty(orderCodeList)){
                // B订单实发补偿进ES
                posReportSearchClient.bOrderFixToES(orderCodeList);
                bucketES.delete();
                log.info("修复短交ES-----------------------" + StringUtils.strip(orderCodeList.toString(),"[]"));
            }
        } catch (Exception e) {
            log.error("B订单实发补偿修复短交ES异常:{}", e);

            StringBuffer sb = new StringBuffer();
            sb.append("B订单实发补偿修复短交ES异常 ");
            // 错误根据订单号查询订单时间
            List<String> orderTimeList = shopReportMapper.getShortOrderTimeList(orderCodeList);
            if(CollectionUtils.isNotEmpty(orderTimeList)){
                sb.append(StringUtils.strip(orderTimeList.toString(),"[]"));
            }
            //发送微信模板信息
            weChatSendMessageService.sendWeChatMessage(sb.toString());
        }

    }

    /**
     * 修复 xd-order送货日期之后配送成功、失败。进行数据补偿
     */
    public void fixXdOrderDelayComplete(){
        RBucket<Set<String>> bucketES = redissonClient.getBucket(RedissKeyConst.FIX_XD_ORDER_DELAY_COMPLETE);
        Set<String> toShopDateList = bucketES.get();
        try {
            if(CollectionUtils.isNotEmpty(toShopDateList)){
               for(String toShopDate : toShopDateList){
                    // 云超实发汇总信息
                    cloudCommodityReportService.cloudCommodityReportSummary(toShopDate);

                    // 云超商品报表汇总
                    cloudCommodityReportService.cloudCommodityInfoReport(toShopDate);

                   // 修复 门店实际入库汇总（门店实收结算商品表、门店实收结算汇总表）
                   shopStockInService.shopStockInTax(toShopDate);
                }
                log.warn("修复 xd-order送货日期之后配送成功、失败.完成-----------------------" + StringUtils.strip(toShopDateList.toString(),"[]"));
                bucketES.delete();
            }
        } catch (Exception e) {
            log.error("修复 xd-order送货日期之后配送成功、失败。进行数据补偿.异常:{}", StringUtils.strip(toShopDateList.toString(),"[]"));

        }

    }
}
