package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GiftCardCashierWaterMessage {

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("销售渠道 1POS 2团购 3APP")
    private Integer cardSourceType;

//    @ApiModelProperty("订单金额")
//    private BigDecimal totalAmount;

    @ApiModelProperty("POS机号")
    private String macCode;

    @ApiModelProperty("流水时间")
    private Date saleTime;

    @ApiModelProperty("员工号")
    private String employeeNumber;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("1清美卡售卖 2香烟售卖")
    private Integer type;

    @ApiModelProperty("销售流水")
    private List<GiftCardSalesWaterMessage> salesWater;

    @ApiModelProperty("支付方式")
    private List<GiftCardPayTypeMessage> payTypes;
}
