package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.finance.PosSalesOrderIDTO;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.dto.pos.joinShop.AnalysisBySynthesisIDTO;
import com.pinshang.qingyun.report.model.pos.ReportSalesWater;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SalesWaterReportMapper {
    void insertSalesOfTheWaterReport(@Param("salesWaters") List<ReportSalesWater> salesWaters);

    List<SalesWaterODTO> listSalesWaterIDTOReport(@Param("salesWaterIDTO") SalesWaterIDTO salesWaterIDTO);

    SalesWaterHeaderODTO listSalesWaterIDTOHeaderReport(@Param("salesWaterIDTO") SalesWaterIDTO salesWaterIDTO);

    int deleteSalesWaterReportByOrderCodes(@Param("orderCode") String orderCodes);

    int deleteSalesWaterReportByTimeRange(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);


    List<WeightPriceODTO> listWeightPriceReport(@Param("vo") WeightPriceIDTO vo);

    WeightPriceODTO listlistWeightPriceHeaderReport(@Param("vo") WeightPriceIDTO vo);

    List<WeightPriceODTO> getWeightPriceList(@Param("orderCodeList") List<String> orderCodeList, @Param("commodityIdList") List<Long> commodityIdList);


    List<WeightOverPriceODTO> listWeightOverPriceReport(@Param("vo") WeightPriceIDTO vo);
    WeightOverPriceODTO listWeightOverPriceHeaderReport(@Param("vo") WeightPriceIDTO vo);

    List<WeightOverPriceODTO> listWeightOverPriceDetail(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<Long> selectOriginOrderNUll(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    int batchUpdateSaleWaterOriginOrder(List<OrderCodeODTO> list);

    List<CloudAllocationODTO> queryPosCateSummary(@Param("vo") CloudAllocationIDTO idto);

    List<PosSaleWaterODTO> queryPosSalesWaterData(@Param("vo") PosSaleWaterQueryIDTO idto);

    List<ConsignmentOrderODTO> queryConsignmentOrderSettle(@Param("dto") ConsignmentOrderIDTO idto);

    List<ReportSalesWater> salesWaterList(@Param("dto") AnalysisBySynthesisIDTO dto);

    List<InventoryCompPosReportODTO> queryPosReportShopCommoditySumQuantity(@Param("dto") InventoryCompPosReportIDTO idto);

    List<PosSalesOrderODTO> selectShopPosSalesOrderList(@Param("vo") PosSalesOrderIDTO idto);
}
