package com.pinshang.qingyun.report.mapper.shop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.SalesCompletionRateIDTO;
import com.pinshang.qingyun.report.dto.SalesCompletionRateODTO;
import com.pinshang.qingyun.report.dto.DaySalesCompletionRateSum;
import com.pinshang.qingyun.report.dto.shop.DaySaleAimODTO;
import com.pinshang.qingyun.report.dto.shop.DaySaleAimPageIDTO;
import com.pinshang.qingyun.report.model.shop.DaySaleAim;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface DaySaleAimMapper extends MyMapper<DaySaleAim> {

    /**
     * 判断门店日期唯一性
     * @param shopId
     * @param saleTime
     * @return
     */
    Integer countDaySaleAimByShopSaleTime(@Param("shopId")Long shopId,@Param("saleTime") String saleTime);

    /**
     * 日销售目标管理(分页)
     * @param idto
     * @return
     */
    List<DaySaleAimODTO> listDaySaleAimPage(DaySaleAimPageIDTO idto);

    List<String> queryDayKeyListRepeat(@Param("keyList") List<String> keyList);

    List<SalesCompletionRateODTO> daySalesCompletionRatePage(SalesCompletionRateIDTO idto);

    List<SalesCompletionRateODTO> monthSalesCompletionRatePage(SalesCompletionRateIDTO idto);

    DaySalesCompletionRateSum daySalesCompletionRateSum(SalesCompletionRateIDTO idto);

    DaySalesCompletionRateSum monthSalesCompletionRateSum(SalesCompletionRateIDTO idto);

    List<Long> getDaySaleAimShopIdList(SalesCompletionRateIDTO idto);
}
