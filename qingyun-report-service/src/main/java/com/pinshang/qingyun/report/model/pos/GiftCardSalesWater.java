package com.pinshang.qingyun.report.model.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_gift_card_sales_water")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GiftCardSalesWater {

    private Long id;

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("销售渠道 2POS 6团购 1APP")
    private Integer cardSourceType;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("序列表")
    private String cardSn;

    @ApiModelProperty("面值")
    private Integer parValue;

    @ApiModelProperty("商品金额")
    private BigDecimal price;

    @ApiModelProperty("POS机号")
    private String macCode;

    @ApiModelProperty("流水时间")
    private Date saleTime;

    @ApiModelProperty("员工号")
    private String employeeNumber;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("数量")
    private Integer number;

    @ApiModelProperty("1清美卡售卖 2香烟售卖")
    private Integer type;

    @ApiModelProperty("代销商户")
    private Long consignmentId;
}
