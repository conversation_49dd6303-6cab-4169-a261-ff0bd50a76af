package com.pinshang.qingyun.report.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterODTO;
import com.pinshang.qingyun.report.dto.shop.CateAverageAmountBiODTO;
import com.pinshang.qingyun.report.dto.shop.CateAverageAmountODTO;
import com.pinshang.qingyun.report.dto.shop.ShopNegativeStockODTO;
import com.pinshang.qingyun.report.dto.shop.ShopNegativeStockQueryIDto;
import com.pinshang.qingyun.report.enums.CollectTypeEnum;
import com.pinshang.qingyun.report.enums.PaytypeEnum;
import com.pinshang.qingyun.report.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.mapper.*;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.*;
import com.pinshang.qingyun.report.model.shop.Shop;
import com.pinshang.qingyun.report.service.shop.ShopService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.report.ShopUnsalableIDTO;
import com.pinshang.qingyun.shop.dto.report.ShopUnsalableODTO;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.XsShopCommodityAppStatusIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.XsShopCommodityAppStatusODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.ShopCommodityUpDownManagerClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockItemODTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Row;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR> Zhang
 * @version 1.0
 * @since 2018/9/26
 */
@Slf4j
@Service
public class ReportService {

    @Autowired
    private ReportMapper reportMapper;

    @Autowired
    ClearingChargesMapper clearingChargesMapper;

    @Autowired
    ClearingChargesItemMapper clearingChargesItemMapper;

    @Autowired
    ShopReportMapper shopReportMapper;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private XdStockClient xdStockClient;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private ShopNegativeStockMapper shopNegativeStockMapper;
    @Autowired
    private ShopCommodityUpDownManagerClient shopCommodityUpDownManagerClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private ShopService shopService;
    @Autowired
    private DataQueryChangeService dataQueryChangeService;

    /**
     * 类别销售周同比
     * @param idto
     * @return
     */
    public TablePageInfo<CategorySalesWeekPercentODTO> categorySalesWeekPercent(CategorySalesWeekPercentIDTO idto) {
        log.info("1 :" + DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss SSS"));
        // 当门店选择为全部时，查询该用户所能允许查询的门店列表

        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));

        TablePageInfo info=new TablePageInfo();
        info.setList(null);
        info.setHeader(new CategorySalesWeekPercentODTO());
        if(CollectionUtils.isEmpty(shopIdList)){
            return info;
        }
        if (null != idto.getShopId() && !shopIdList.contains(idto.getShopId())) {
            return info;
        }
        idto.setShopIdList(shopIdList);

        log.info(idto.toString());
        for (Long aLong : idto.getShopIdList()) {
            log.info(aLong.toString());
        }

        PageInfo<CategorySalesWeekPercentODTO> pageInfo = new PageInfo<>();
        CategorySalesWeekPercentODTO header = new CategorySalesWeekPercentODTO();
        double totalTodaySalesMonery = 0;

        // 历史数据走DataQuery 大数据
        Date parse = DateTimeUtil.parse(idto.getSalesDate(), "yyyy-MM-dd");
        Date now = DateUtil.getNowDate();
        if(parse.before(now) && dataQueryChangeService.enableDataQuery()) {
            Integer pageNo = idto.getPageNo();
            Integer pageSize = idto.getPageSize();
            // 调用大数据查询全部
            idto.initExportPage();

            List<CategorySalesWeekPercentODTO> todayList = reportMapper.categorySalesWeekPercent(idto);

            String lastweek = DateTimeUtil.addDay(parse, -7);
            idto.setSalesDate(lastweek);
            List<CategorySalesWeekPercentODTO> lastWeekList = reportMapper.categorySalesWeekPercent(idto);

            Map<String, Object> resultMap = categorySalesWeekPercentToPage(todayList, lastWeekList, header,pageNo, pageSize);
            if(resultMap.get("totalTodaySalesMonery") != null) {
                totalTodaySalesMonery = (double) resultMap.get("totalTodaySalesMonery");
            }
            pageInfo = (PageInfo<CategorySalesWeekPercentODTO>) resultMap.get("pageInfo");
        }else {
            // 获取所有数据不带分页的合计数据(header)
            List<CategorySalesWeekPercentODTO> headerODTOS = reportMapper.categorySalesWeekPercent(idto);
            if (SpringUtil.isNotEmpty(headerODTOS)) {
                totalTodaySalesMonery = headerODTOS.stream()
                        .mapToDouble(item2 -> item2.getTodaySalesMonery().doubleValue())
                        .sum();
                double totalLastWeekSalesMonery = headerODTOS.stream()
                        .mapToDouble(item2 -> item2.getLastWeekSalesMonery().doubleValue())
                        .sum();
                BigDecimal subtract = new BigDecimal(totalTodaySalesMonery).subtract(new BigDecimal(totalLastWeekSalesMonery));
                String salesMoneryPercentStr = "100.00%";
                if (totalLastWeekSalesMonery != 0) {
                    BigDecimal salesMoneryPercent = subtract.divide(new BigDecimal(totalLastWeekSalesMonery), RoundingMode.HALF_UP);
                    salesMoneryPercentStr = salesMoneryPercent.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
                }
                header.setTodaySalesMonery(new BigDecimal(totalTodaySalesMonery).setScale(2, BigDecimal.ROUND_HALF_UP));
                header.setLastWeekSalesMonery(new BigDecimal(totalLastWeekSalesMonery).setScale(2, BigDecimal.ROUND_HALF_UP));
                header.setSalesMoneryPercent(salesMoneryPercentStr);
            }

            // 查询今日销售额和上周销售额
            pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
                reportMapper.categorySalesWeekPercent(idto);
            });
        }

        List<CategorySalesWeekPercentODTO> newList = new ArrayList<>();
        List<CategorySalesWeekPercentODTO> list = pageInfo.getList();
        for (CategorySalesWeekPercentODTO item : list) {
            // 今日销售额占比=查询该大类的今日销售额/所有大类销售额的合计
            BigDecimal todaySalesMonery = item.getTodaySalesMonery();
            String todaySalesMoneryPercentStr = null;
            if (BigDecimal.ZERO.compareTo(item.getTodaySalesMonery()) == 0 || totalTodaySalesMonery == 0) {
                todaySalesMoneryPercentStr = "0.00%";
            } else {
                BigDecimal todaySalesMoneryPercent = todaySalesMonery.divide(new BigDecimal(totalTodaySalesMonery), 5, RoundingMode.HALF_UP);
                todaySalesMoneryPercentStr = todaySalesMoneryPercent.multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP) + "%";
            }


            // 销售额同比=（今日销售额-上周销售额）/上周销售额
            BigDecimal lastWeekSalesMonery = item.getLastWeekSalesMonery();
            String salesMoneryPercentStr = "100.00%";
            if (BigDecimal.ZERO.compareTo(lastWeekSalesMonery) != 0) {
                BigDecimal subtract = todaySalesMonery.subtract(lastWeekSalesMonery);
                BigDecimal salesMoneryPercent = subtract.divide(lastWeekSalesMonery, 5, RoundingMode.HALF_UP);
                salesMoneryPercentStr = salesMoneryPercent.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
            }

            // 组装数据
            CategorySalesWeekPercentODTO odto = new CategorySalesWeekPercentODTO();
            odto.setFirstKindName(item.getFirstKindName());
            odto.setTodaySalesMonery(item.getTodaySalesMonery().setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setTodaySalesMoneryPercent(todaySalesMoneryPercentStr);
            odto.setLastWeekSalesMonery(lastWeekSalesMonery.setScale(2, BigDecimal.ROUND_HALF_UP));
            odto.setSalesMoneryPercent(salesMoneryPercentStr);

            newList.add(odto);
        }
        pageInfo.setList(newList);
        log.info("6 :" + DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss SSS"));


        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(header);
        log.info("7 :" + DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss SSS"));

        return tablePageInfo;
    }

    
    private Map<String, Object> categorySalesWeekPercentToPage(List<CategorySalesWeekPercentODTO> todayList, List<CategorySalesWeekPercentODTO> lastWeekList,
                               CategorySalesWeekPercentODTO header,Integer pageNo, Integer pageSize){
        Map<String, Object> resultMap = new HashMap<>();
        double totalTodaySalesMonery = 0;

        Set<CategorySalesWeekPercentODTO> mergeList = new HashSet<>();
        Map<Long, List<CategorySalesWeekPercentODTO>> todayMap = new HashMap<>();
        Map<Long, List<CategorySalesWeekPercentODTO>> lastWeekMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(todayList)) {
            todayMap = todayList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityFirstId();
            }));
            todayList.forEach(item -> {
                CategorySalesWeekPercentODTO categorySalesWeekPercentODTO = new CategorySalesWeekPercentODTO();
                categorySalesWeekPercentODTO.setCommodityFirstId(item.getCommodityFirstId());
                categorySalesWeekPercentODTO.setFirstKindName(item.getFirstKindName());
                mergeList.add(categorySalesWeekPercentODTO);
            });
        }

        if(CollectionUtils.isNotEmpty(lastWeekList)) {
            lastWeekMap = lastWeekList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityFirstId();
            }));
            lastWeekList.forEach(item -> {
                CategorySalesWeekPercentODTO categorySalesWeekPercentODTO = new CategorySalesWeekPercentODTO();
                categorySalesWeekPercentODTO.setCommodityFirstId(item.getCommodityFirstId());
                categorySalesWeekPercentODTO.setFirstKindName(item.getFirstKindName());
                mergeList.add(categorySalesWeekPercentODTO);
            });
        }

        if(CollectionUtils.isEmpty(mergeList)) {
            TablePageInfo info = new TablePageInfo();
            info.setList(new ArrayList());
            resultMap.put("pageInfo", info);
        }else {
            List<CategorySalesWeekPercentODTO> pageList = new ArrayList<>();
            double totalLastWeekSalesMonery = 0;
            for (CategorySalesWeekPercentODTO item : mergeList) {
                CategorySalesWeekPercentODTO salesWeekPercentODTO = new CategorySalesWeekPercentODTO();
                salesWeekPercentODTO.setCommodityFirstId(item.getCommodityFirstId());
                salesWeekPercentODTO.setFirstKindName(item.getFirstKindName());

                Long key = item.getCommodityFirstId();
                if (todayMap.containsKey(key)) {
                    List<CategorySalesWeekPercentODTO> list = todayMap.get(key);
                    salesWeekPercentODTO.setTodaySalesMonery(list.get(0).getTodaySalesMonery());
                } else {
                    salesWeekPercentODTO.setTodaySalesMonery(BigDecimal.ZERO);
                }

                if (lastWeekMap.containsKey(key)) {
                    List<CategorySalesWeekPercentODTO> list = lastWeekMap.get(key);
                    salesWeekPercentODTO.setLastWeekSalesMonery(list.get(0).getTodaySalesMonery());
                } else {
                    salesWeekPercentODTO.setLastWeekSalesMonery(BigDecimal.ZERO);
                }
                pageList.add(salesWeekPercentODTO);

                totalTodaySalesMonery += salesWeekPercentODTO.getTodaySalesMonery().doubleValue();
                totalLastWeekSalesMonery += salesWeekPercentODTO.getLastWeekSalesMonery().doubleValue();
            }

            BigDecimal subtract = new BigDecimal(totalTodaySalesMonery).subtract(new BigDecimal(totalLastWeekSalesMonery));
            String salesMoneryPercentStr = "100.00%";
            if (totalLastWeekSalesMonery != 0) {
                BigDecimal salesMoneryPercent = subtract.divide(new BigDecimal(totalLastWeekSalesMonery), RoundingMode.HALF_UP);
                salesMoneryPercentStr = salesMoneryPercent.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
            }
            header.setTodaySalesMonery(new BigDecimal(totalTodaySalesMonery).setScale(2, BigDecimal.ROUND_HALF_UP));
            header.setLastWeekSalesMonery(new BigDecimal(totalLastWeekSalesMonery).setScale(2, BigDecimal.ROUND_HALF_UP));
            header.setSalesMoneryPercent(salesMoneryPercentStr);

            PageInfo<CategorySalesWeekPercentODTO> pageInfo = ListToPageInfoUtil.convert(pageList, pageSize, pageNo);

            resultMap.put("pageInfo", pageInfo);
            resultMap.put("totalTodaySalesMonery", totalTodaySalesMonery);
        }
        return resultMap;
    }

    private <T extends Pagination, R> TablePageInfo<R> buildTablePageInfo(Function<T, R> listFunc, T idto, Function<T, R> headFunc) {
        PageInfo<R> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            listFunc.apply(idto);
        });

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        if (headFunc != null) {
            R header = headFunc.apply(idto);
            tablePageInfo.setHeader(header);
        }

        return tablePageInfo;
    }

    /**
     * 门店销售周同比
     * @param idto
     * @return
     */
    public TablePageInfo<ShopSalesWeekODTO> shopSalesWeekPercent(ShopSalesWeekIDTO idto) {
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));

        TablePageInfo info=new TablePageInfo();
        info.setList(null);
        info.setHeader(new ShopSalesWeekODTO());
        if(CollectionUtils.isEmpty(shopIdList)){
            return info;
        }
        //选择的单门店和门店列表没有交集
        if (null != idto.getShopId() && !shopIdList.contains(idto.getShopId())) {
            return info;
        }
        idto.setShopIdList(shopIdList);

        Assert.notNull(idto.getToday(), "今日日期不能为空");
        Assert.notNull(idto.getLastWeek(), "上周日期不能为空");
        PageInfo<ShopSalesWeekODTO> pageDate = null;
        ShopSalesWeekODTO total = new ShopSalesWeekODTO();

        Date parse = DateTimeUtil.parse(idto.getToday(), "yyyy-MM-dd");
        Date now = DateUtil.getNowDate();

        // 如果选择当天之前,则走大数据
        if(parse.before(now) && dataQueryChangeService.enableDataQuery()) {
            Integer pageNo = idto.getPageNo();
            Integer pageSize = idto.getPageSize();
            // 调用大数据查询全部
            idto.initExportPage();

            // 查询今日
            List<ShopSalesWeekODTO> todayList = reportMapper.shopSalesWeekPercent(idto);

            // 查询上周
            idto.setToday(idto.getLastWeek());
            List<ShopSalesWeekODTO> lastWeekList = reportMapper.shopSalesWeekPercent(idto);

            Map<String, Object> resultMap = shopSalesWeekPercentToPage(todayList, lastWeekList, pageNo, pageSize);
            if(resultMap.get("total") != null) {
                total = (ShopSalesWeekODTO) resultMap.get("total");
            }
            pageDate = (PageInfo<ShopSalesWeekODTO>) resultMap.get("pageInfo");
        }else {
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
                reportMapper.shopSalesWeekPercent(idto);
            });

            if(!CollectionUtils.isEmpty(pageDate.getList())){
                //计算合计
                total = setTotalShopSalesPercent(idto,total);
            }
        }

        List<ShopSalesWeekODTO> list = pageDate.getList();
        if(!CollectionUtils.isEmpty(list)){
            //计算各同比
            setShopSalesPercent(list);
        }

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }

    private Map<String, Object> shopSalesWeekPercentToPage(List<ShopSalesWeekODTO> todayList, List<ShopSalesWeekODTO> lastWeekList,
                                                           Integer pageNo, Integer pageSize) {

        Map<String, Object> resultMap = new HashMap<>();
        // 数据合并汇总
        Set<ShopSalesWeekODTO> mergeList = new HashSet<>();
        Map<Long, List<ShopSalesWeekODTO>> todayMap = new HashMap<>();
        Map<Long, List<ShopSalesWeekODTO>> lastWeekMap = new HashMap<>();
        ShopSalesWeekODTO total = new ShopSalesWeekODTO();

        if(CollectionUtils.isNotEmpty(todayList)) {
            todayMap = todayList.stream().collect(Collectors.groupingBy(item -> {
                return item.getShopId();
            }));
            todayList.forEach(item -> {
                ShopSalesWeekODTO salesWeekODTO = new ShopSalesWeekODTO();
                salesWeekODTO.setShopId(item.getShopId());
                salesWeekODTO.setShopName(item.getShopName());
                mergeList.add(salesWeekODTO);
            });
        }

        if(CollectionUtils.isNotEmpty(lastWeekList)) {
            lastWeekMap = lastWeekList.stream().collect(Collectors.groupingBy(item -> {
                return item.getShopId();
            }));
            lastWeekList.forEach(item -> {
                ShopSalesWeekODTO salesWeekODTO = new ShopSalesWeekODTO();
                salesWeekODTO.setShopId(item.getShopId());
                salesWeekODTO.setShopName(item.getShopName());
                mergeList.add(salesWeekODTO);
            });
        }

        if(CollectionUtils.isEmpty(mergeList)) {
            PageInfo info = new PageInfo();
            info.setList(new ArrayList());
            resultMap.put("pageInfo", info);
            resultMap.put("total", new ShopSalesWeekODTO());
            return resultMap;
        }else {
            List<ShopSalesWeekODTO> shopSalesWeekList = new ArrayList<>();
            for(ShopSalesWeekODTO item : mergeList){
                ShopSalesWeekODTO salesWeekODTO = BeanCloneUtils.copyTo(item, ShopSalesWeekODTO.class);
                Long key = item.getShopId();

                if(todayMap.containsKey(key)) {
                    List<ShopSalesWeekODTO> list = todayMap.get(key);
                    salesWeekODTO.setTodaySalesAmount(list.get(0).getSaleAmount());
                    salesWeekODTO.setTodayVisitNum(list.get(0).getVisitNum());
                }else {
                    salesWeekODTO.setTodaySalesAmount(BigDecimal.ZERO);
                    salesWeekODTO.setTodayVisitNum(BigDecimal.ZERO);
                }

                if(lastWeekMap.containsKey(key)){
                    List<ShopSalesWeekODTO> list = lastWeekMap.get(key);
                    salesWeekODTO.setLastWeekSalseAmount(list.get(0).getSaleAmount());
                    salesWeekODTO.setLastWeekVisitNum(list.get(0).getVisitNum());
                }else {
                    salesWeekODTO.setLastWeekSalseAmount(BigDecimal.ZERO);
                    salesWeekODTO.setLastWeekVisitNum(BigDecimal.ZERO);
                }

                shopSalesWeekList.add(salesWeekODTO);
            }

            // 求sum
            BigDecimal totalTodaySalesAmount=BigDecimal.ZERO;
            BigDecimal totalLastWeekSalseAmount=BigDecimal.ZERO;
            BigDecimal totalTodayVisitNum=BigDecimal.ZERO;
            BigDecimal totalLastWeekVisitNum=BigDecimal.ZERO;
            for(ShopSalesWeekODTO dto : shopSalesWeekList) {
                totalTodaySalesAmount=totalTodaySalesAmount.add(dto.getTodaySalesAmount());
                totalLastWeekSalseAmount=totalLastWeekSalseAmount.add(dto.getLastWeekSalseAmount());
                totalTodayVisitNum=totalTodayVisitNum.add(dto.getTodayVisitNum());
                totalLastWeekVisitNum=totalLastWeekVisitNum.add(dto.getLastWeekVisitNum());
            }
            total.setTodaySalesAmount(totalTodaySalesAmount);
            total.setLastWeekSalseAmount(totalLastWeekSalseAmount);
            total.setTodayVisitNum(totalTodayVisitNum);
            total.setLastWeekVisitNum(totalLastWeekVisitNum);

            List<ShopSalesWeekODTO> setTotalList = new ArrayList<>();
            setTotalList.add(total);
            setShopSalesPercent(setTotalList);
            total = setTotalList.get(0);

            PageInfo<ShopSalesWeekODTO> pageDate = ListToPageInfoUtil.convert(shopSalesWeekList, pageSize, pageNo);

            resultMap.put("pageInfo", pageDate);
            resultMap.put("total", total);
        }

        return resultMap;
    }

    //计算合计
    public ShopSalesWeekODTO setTotalShopSalesPercent(ShopSalesWeekIDTO idto,ShopSalesWeekODTO total){
        List<ShopSalesWeekODTO> totalList= reportMapper.shopSalesWeekPercent(idto);
        BigDecimal totalTodaySalesAmount=BigDecimal.ZERO;
        BigDecimal totalLastWeekSalseAmount=BigDecimal.ZERO;
        BigDecimal totalTodayVisitNum=BigDecimal.ZERO;
        BigDecimal totalLastWeekVisitNum=BigDecimal.ZERO;
        for(ShopSalesWeekODTO dto:totalList){
            totalTodaySalesAmount=totalTodaySalesAmount.add(dto.getTodaySalesAmount());
            totalLastWeekSalseAmount=totalLastWeekSalseAmount.add(dto.getLastWeekSalseAmount());
            totalTodayVisitNum=totalTodayVisitNum.add(dto.getTodayVisitNum());
            totalLastWeekVisitNum=totalLastWeekVisitNum.add(dto.getLastWeekVisitNum());
        }
        total.setTodaySalesAmount(totalTodaySalesAmount);
        total.setLastWeekSalseAmount(totalLastWeekSalseAmount);
        total.setTodayVisitNum(totalTodayVisitNum);
        total.setLastWeekVisitNum(totalLastWeekVisitNum);
        List<ShopSalesWeekODTO> setTotalList=new ArrayList<>();
        setTotalList.add(total);
        setShopSalesPercent(setTotalList);
        return setTotalList.get(0);
    }
    //计算各同比
    private void setShopSalesPercent(List<ShopSalesWeekODTO> list) {
        for(ShopSalesWeekODTO dto:list){
            //计算销售额同比
            BigDecimal saleAmountper=dto.getTodaySalesAmount().subtract(dto.getLastWeekSalseAmount());
            if(dto.getLastWeekSalseAmount().compareTo(BigDecimal.ZERO)==0){
                dto.setSalesPercent("100.00%");
            }else if(saleAmountper.compareTo(BigDecimal.ZERO)==0){
                dto.setSalesPercent("0.00%");
            }else{
                dto.setSalesPercent(saleAmountper.divide(dto.getLastWeekSalseAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }
            //计算来客同比
            BigDecimal visitNumper=dto.getTodayVisitNum().subtract(dto.getLastWeekVisitNum());
            if(dto.getLastWeekVisitNum().compareTo(BigDecimal.ZERO)==0){
                dto.setVisitNumPercent("100.00%");
            }else if(visitNumper.compareTo(BigDecimal.ZERO)==0){
                dto.setVisitNumPercent("0.00%");
            }else{
                dto.setVisitNumPercent(visitNumper.divide(dto.getLastWeekVisitNum(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }
            //计算客单价同比
            if(dto.getTodayVisitNum().compareTo(BigDecimal.ZERO)>0){
                dto.setTodayGuestPrice(dto.getTodaySalesAmount().divide(dto.getTodayVisitNum(),2,BigDecimal.ROUND_HALF_UP));
            }else{
                dto.setTodayGuestPrice(dto.getTodaySalesAmount());
            }
            if(dto.getLastWeekVisitNum().compareTo(BigDecimal.ZERO)>0){
                dto.setLastWeekGuestPrice(dto.getLastWeekSalseAmount().divide(dto.getLastWeekVisitNum(),2,BigDecimal.ROUND_HALF_UP));
            }else{
                dto.setLastWeekGuestPrice(dto.getLastWeekSalseAmount());
            }
            BigDecimal gusetPriceper=dto.getTodayGuestPrice().subtract(dto.getLastWeekGuestPrice());
            if(dto.getLastWeekGuestPrice().compareTo(BigDecimal.ZERO)==0){
                dto.setGuestPricePercent("100.00%");
            }else if(gusetPriceper.compareTo(BigDecimal.ZERO)==0){
                dto.setGuestPricePercent("0.00%");
            }else{
                dto.setGuestPricePercent(gusetPriceper.divide(dto.getLastWeekGuestPrice(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            }
        }
    }

    public PageInfo<ClearingCharges> findMonthlyCashierReportList(ClearingChargesIDTO idto){
        ClearingCharges clearingCharges = new ClearingCharges();
        clearingCharges.setImportDateMonth(idto.getImportDateMonth());
        clearingCharges.setIsDelete(YesOrNoEnums.NO.getCode());
        PageInfo<ClearingCharges> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            clearingChargesMapper.select(clearingCharges);
        });
        return pageInfo;
    }

    public TablePageInfo<ReconciliationOfRurnoverODTO > reconciliationOfRurnover(ClearingChargesItemIDTO idto){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (!setShopIdList(idto, tokenInfo)) {
            return null;
        }
        PageInfo<ReconciliationOfRurnoverODTO > pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            clearingChargesItemMapper.reconciliationOfRurnover(idto);
        });
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        ReconciliationOfRurnoverODTO  header = clearingChargesItemMapper.reconciliationOfRurnoverHearder(idto);
        tablePageInfo.setHeader(header);
        return tablePageInfo;
    }

    private boolean setShopIdList(ClearingChargesItemIDTO idto, TokenInfo tokenInfo) {
        /**
        idto.setShopIdList(Arrays.asList(16L,44L,45L));
        return false;
         **/
        if (idto.getShopId() == null) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if (CollectionUtils.isEmpty(shopIdList)) {
                return false;
            }
            idto.setShopIdList(shopIdList);
        } else {
            idto.setShopIdList(Arrays.asList(idto.getShopId()));
        }
        return true;
    }


    public TablePageInfo<ReconciliationStatementODTO> reconciliationStatement(ClearingChargesItemIDTO idto){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (!setShopIdList(idto, tokenInfo)) {
            return null;
        }
        PageInfo<ReconciliationStatementODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            clearingChargesItemMapper.reconciliationStatement(idto);
        });
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        return tablePageInfo;
    }

    public TablePageInfo<ClearingChargesItem> monthCashierReportItem(ClearingChargesItemIDTO idto){
        ClearingChargesItem clearingChargesItem = new ClearingChargesItem();
        clearingChargesItem.setClearingChargesId(idto.getClearingChargesId());
        clearingChargesItem.setIsDelete(YesOrNoEnums.NO.getCode());
        PageInfo<ClearingChargesItem> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            clearingChargesItemMapper.select(clearingChargesItem);
        });
        ClearingChargesItem headerSum = clearingChargesItemMapper.getHeaderSum(idto.getClearingChargesId());
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(headerSum);
        return tablePageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id){
        QYAssert.isTrue(id != null,"id 不能为null");
        Example exampleClearingCharges = new Example(ClearingCharges.class);
        exampleClearingCharges.createCriteria().andEqualTo("id",id);
        ClearingCharges clearingCharges = new ClearingCharges();
        clearingCharges.setIsDelete(YesOrNoEnums.YES.getCode());
        clearingChargesMapper.updateByExampleSelective(clearingCharges,exampleClearingCharges);
        ClearingChargesItem updateClearingChargesItem = new ClearingChargesItem();
        updateClearingChargesItem.setIsDelete(YesOrNoEnums.YES.getCode());
        Example example = new Example(ClearingChargesItem.class);
        example.createCriteria().andEqualTo("clearingChargesId",id);
        clearingChargesItemMapper.updateByExampleSelective(updateClearingChargesItem,example);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void importMonthlyCashierReport(Long userId, String userName, List<ClearingChargesItem> chargesItems){
        //decompression
        ClearingCharges clearingCharges = new ClearingCharges();
        clearingCharges.setIsDelete(YesOrNoEnums.NO.getCode());
        clearingCharges.setCreatedId(userId);
        clearingCharges.setCreatedName(userName);
//        List<ClearingChargesItem> items = new ArrayList<>();
        decompression(clearingCharges,chargesItems);
        //end
        //check
        Date importDate = clearingCharges.getImportDate();
        clearingCharges.setCreateTime(new Date());
        ClearingCharges selectClearingCharges = new ClearingCharges();
        selectClearingCharges.setImportDate(importDate);
        selectClearingCharges.setIsDelete(YesOrNoEnums.NO.getCode());
        List<ClearingCharges> selectByImportDate = clearingChargesMapper.select(selectClearingCharges);
        QYAssert.isTrue(selectByImportDate.size() == 0,"该日期数据已导入，请先删除再重新导入");
        //end
        clearingChargesMapper.insertSelective(clearingCharges);
        clearingChargesItemMapper.insertList(chargesItems);
    }

    private void decompression(ClearingCharges clearingCharges, List<ClearingChargesItem> items){
        clearingCharges.setId(IdWorker.getId());

        for (ClearingChargesItem clearingChargesItem : items) {
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalPoundage = BigDecimal.ZERO;
            totalAmount = totalAmount.add(clearingChargesItem.getBankCard())
                    .add(clearingChargesItem.getCash())
                    .add(clearingChargesItem.getUnionPay())
                    .add(clearingChargesItem.getTallyAli())
                    .add(clearingChargesItem.getTallyWechat())
                    .add(clearingChargesItem.getOkCard());
            totalPoundage = totalPoundage.add(clearingChargesItem.getBankCardPoundage())
                    .add(clearingChargesItem.getUnionPoundage())
                    .add(clearingChargesItem.getTallyAliPoundage())
                    .add(clearingChargesItem.getTallyWechatPoundage())
                    .add(clearingChargesItem.getOkCardPoundage());
            clearingChargesItem.setTotalAmount(totalAmount);
            clearingChargesItem.setTotalPoundage(totalPoundage);
            clearingChargesItem.setId(IdWorker.getId());
            clearingChargesItem.setClearingChargesId(clearingCharges.getId());
            clearingChargesItem.setIsDelete(YesOrNoEnums.NO.getCode());
//            clearingChargesItem.setImportDateMonth(DateFormatUtils.format(importDate,"yyyy-MM"));
//            clearingChargesItem.setShopId(shopId);
            BigDecimal cash = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.MONEY.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosCash(cash);

            BigDecimal moLing = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.MO_LING.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setMoLing(moLing);

            BigDecimal okCard = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.OK_CARD.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosOkCard(okCard);

            BigDecimal bank = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.BANK.getCode(),PaytypeEnum.BANK_DIRECT.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosBankCard(bank);

            BigDecimal unionPay = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.UNIONPAY_QUICK_PASS.getCode(),PaytypeEnum.UNIONPAY_WECHAT.getCode(),PaytypeEnum.UNIONPAY_ALI.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosUnionPay(unionPay);

            BigDecimal aliPayAccount = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.ALIPAY_ACCOUNT.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosTallyAli(aliPayAccount);

            BigDecimal wechatAccount = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.WECHAT_ACCOUNT.getCode()), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosTallyWechat(wechatAccount);

            BigDecimal posTotalAmount = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(
                    PaytypeEnum.WECHAT_ACCOUNT.getCode(),
                    PaytypeEnum.MONEY.getCode(),
                    PaytypeEnum.ALIPAY.getCode(),
                    PaytypeEnum.WECHAT.getCode(),
                    PaytypeEnum.ALIPAY_ACCOUNT.getCode(),
                    PaytypeEnum.BANK.getCode(),
                    PaytypeEnum.UNIONPAY_ALI.getCode(),
                    PaytypeEnum.UNIONPAY_WECHAT.getCode(),
                    PaytypeEnum.BANK_DIRECT.getCode(),
                    PaytypeEnum.OK_CARD.getCode(),
                    PaytypeEnum.UNIONPAY_QUICK_PASS.getCode()
                                                                                        ), clearingChargesItem.getImportDate(),clearingChargesItem.getShopId());
            clearingChargesItem.setPosTotalAmount(posTotalAmount);
        }

//        for (Row row : sheet) {
//            int rowNum = row.getRowNum();
//            if (rowNum == 0) {
//                Assert.isTrue(row.getLastCellNum() == 13, "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 0, "日期"), row + "," + 0 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 1, "门店编码"), row + "," + 1 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 2, "现金实收"), row + "," + 2 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 3, "银行卡手续费"), row + "," + 3 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 4, "银行卡实收"), row + "," + 4 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 5, "聚合手续费"), row + "," + 5 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 6, "聚合实收"), row + "," + 6 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 7, "支付宝（记账）手续费"), row + "," + 7 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 8, "支付宝（记账）实收"), row + "," + 8 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 9, "微信（记账）手续费"), row + "," + 9 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 10, "微信（记账）实收"), row + "," + 10 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 11, "ok卡手续费"), row + "," + 11 + "模板不正确");
//                Assert.isTrue(checkRowNumZero(row, 12, "ok卡实收"), row + "," + 12 + "模板不正确");
//            }else{
//                ClearingChargesItem clearingChargesItem = new ClearingChargesItem();
//                Cell c0 = row.getCell(0);
//                Assert.notNull(c0, rowNum + "行，日期不能为空");
//                String value0 = c0.getStringCellValue();
//                Date importDate = DateUtils.parseDate(value0, "yyyy-MM-dd", "yyyy-M-d");
//                clearingChargesItem.setImportDate(importDate);
//
//                Cell c1 = row.getCell(1);
//                Assert.notNull(c1, rowNum + "行，门店编码不能为空");
//                String value1 = c1.getStringCellValue();
//                String shopName = shopCodeMapName.get(value1);
//                Long shopId = shopCodeMapId.get(value1);
//                Assert.isTrue(StringUtils.isNotBlank(shopName),"门店编码不存在");
//                clearingChargesItem.setShopCode(value1);
//                clearingChargesItem.setShopName(shopName);
//
//                Cell c2 = row.getCell(2);
//                Assert.notNull(c2, rowNum + "行，现金实收不能为空");
//                String value2 = c2.getStringCellValue();
//                clearingChargesItem.setCash(new BigDecimal(value2));
//
//                Cell c3 = row.getCell(3);
//                Assert.notNull(c3, rowNum + "行，银行卡手续费不能为空");
//                String value3 = c3.getStringCellValue();
//                clearingChargesItem.setBankCardPoundage(new BigDecimal(value3));
//
//                Cell c4 = row.getCell(4);
//                Assert.notNull(c4, rowNum + "行，银行卡实收不能为空");
//                String value4 = c4.getStringCellValue();
//                clearingChargesItem.setBankCard(new BigDecimal(value4));
//
//                Cell c5 = row.getCell(5);
//                Assert.notNull(c5, rowNum + "行，聚合手续费不能为空");
//                String value5 = c5.getStringCellValue();
//                clearingChargesItem.setUnionPoundage(new BigDecimal(value5));
//
//                Cell c6 = row.getCell(6);
//                Assert.notNull(c6, rowNum + "行，聚合实收不能为空");
//                String value6 = c6.getStringCellValue();
//                clearingChargesItem.setUnionPay(new BigDecimal(value6));
//
//                Cell c7 = row.getCell(7);
//                Assert.notNull(c7, rowNum + "行，支付宝（记账）手续费不能为空");
//                String value7 = c7.getStringCellValue();
//                clearingChargesItem.setTallyAliPoundage(new BigDecimal(value7));
//
//                Cell c8 = row.getCell(8);
//                Assert.notNull(c8, rowNum + "行，支付宝（记账）实收不能为空");
//                String value8 = c8.getStringCellValue();
//                clearingChargesItem.setTallyAli(new BigDecimal(value8));
//
//                Cell c9 = row.getCell(9);
//                Assert.notNull(c9, rowNum + "行，微信（记账）手续费不能为空");
//                String value9 = c9.getStringCellValue();
//                clearingChargesItem.setTallyWechatPoundage(new BigDecimal(value9));
//
//                Cell c10 = row.getCell(10);
//                Assert.notNull(c10, rowNum + "行，微信（记账）实收不能为空");
//                String value10 = c10.getStringCellValue();
//                clearingChargesItem.setTallyWechat(new BigDecimal(value10));
//
//
//                Cell c11 = row.getCell(11);
//                Assert.notNull(c11, rowNum + "行，ok卡实收不能为空");
//                String value11 = c11.getStringCellValue();
//                clearingChargesItem.setOkCardPoundage(new BigDecimal(value11));
//
//                Cell c12 = row.getCell(12);
//                Assert.notNull(c12, rowNum + "行，ok卡手续费不能为空");
//                String value12 = c12.getStringCellValue();
//                clearingChargesItem.setOkCard(new BigDecimal(value12));
//
//                BigDecimal totalAmount = BigDecimal.ZERO;
//                BigDecimal totalPoundage = BigDecimal.ZERO;
//                totalAmount = totalAmount.add(clearingChargesItem.getBankCard())
//                        .add(clearingChargesItem.getCash())
//                        .add(clearingChargesItem.getUnionPay())
//                        .add(clearingChargesItem.getTallyAli())
//                        .add(clearingChargesItem.getTallyWechat())
//                        .add(clearingChargesItem.getOkCard());
//                totalPoundage = totalPoundage.add(clearingChargesItem.getBankCardPoundage())
//                        .add(clearingChargesItem.getUnionPoundage())
//                        .add(clearingChargesItem.getTallyAliPoundage())
//                        .add(clearingChargesItem.getTallyWechatPoundage())
//                        .add(clearingChargesItem.getOkCardPoundage());
//                clearingChargesItem.setTotalAmount(totalAmount);
//                clearingChargesItem.setTotalPoundage(totalPoundage);
//                clearingChargesItem.setId(IdWorker.getId());
//                clearingChargesItem.setClearingChargesId(clearingCharges.getId());
//                clearingChargesItem.setIsDelete(YesOrNoEnums.NO.getCode());
//                clearingChargesItem.setImportDateMonth(DateFormatUtils.format(importDate,"yyyy-MM"));
//                clearingChargesItem.setShopId(shopId);
//                BigDecimal cash = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.MONEY.getCode()), importDate,shopId);
//                clearingChargesItem.setPosCash(cash);
//
//                BigDecimal moLing = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.MO_LING.getCode()), importDate,shopId);
//                clearingChargesItem.setMoLing(moLing);
//
//                BigDecimal okCard = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.OK_CARD.getCode()), importDate,shopId);
//                clearingChargesItem.setPosOkCard(okCard);
//
//                BigDecimal bank = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.BANK.getCode(),PaytypeEnum.BANK_DIRECT.getCode()), importDate,shopId);
//                clearingChargesItem.setPosBankCard(bank);
//
//                BigDecimal unionPay = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.UNIONPAY_QUICK_PASS.getCode(),PaytypeEnum.UNIONPAY_WECHAT.getCode(),PaytypeEnum.UNIONPAY_ALI.getCode()), importDate,shopId);
//                clearingChargesItem.setPosUnionPay(unionPay);
//
//                BigDecimal aliPayAccount = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.ALIPAY_ACCOUNT.getCode()), importDate,shopId);
//                clearingChargesItem.setPosTallyAli(aliPayAccount);
//
//                BigDecimal wechatAccount = clearingChargesItemMapper.selectPaymentSumByType(Arrays.asList(PaytypeEnum.WECHAT_ACCOUNT.getCode()), importDate,shopId);
//                clearingChargesItem.setPosTallyWechat(wechatAccount);
//
//                BigDecimal posTotalAmount = clearingChargesItemMapper.selectPaymentSumByType(null, importDate,shopId);
//                clearingChargesItem.setPosTotalAmount(posTotalAmount);
//
//                items.add(clearingChargesItem);
//            }
//        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalPoundage = BigDecimal.ZERO;

        for (ClearingChargesItem item : items) {
            totalAmount = totalAmount.add(item.getBankCard())
                    .add(item.getCash())
                    .add(item.getUnionPay())
                    .add(item.getTallyAli())
                    .add(item.getTallyWechat())
                    .add(item.getOkCard());
            totalPoundage = totalPoundage.add(item.getBankCardPoundage())
                    .add(item.getUnionPoundage())
                    .add(item.getTallyAliPoundage())
                    .add(item.getTallyWechatPoundage())
                    .add(item.getOkCardPoundage());
        }
        clearingCharges.setActualTotal(totalAmount);
        clearingCharges.setPoundage(totalPoundage);
        clearingCharges.setImportDate(items.get(0).getImportDate());
        clearingCharges.setImportDateMonth(DateFormatUtils.format(items.get(0).getImportDate(),"yyyy-MM"));

    }

    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }


    public TablePageInfo<ShopActualSummaryODTO> shopActualSummary(ShopActualSummaryIDTO idto) {
        if (idto.getShopId() == null) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if(CollectionUtils.isEmpty(shopIdList)){
                return null;
            }
            idto.setShopIdList(shopIdList);
        }


        PageInfo<ShopActualSummaryODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            clearingChargesItemMapper.shopActualSummary(idto);
        });
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        ShopActualSummaryODTO header = clearingChargesItemMapper.shopActualSummaryHeader(idto);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }


    /**
     * 滞销品报表
     * @param idto
     * @return
     */
    public PageInfo<UnsalableODTO> unsalableReport(UnsalableIDTO idto) {
        Assert.notNull(idto.getShopId(), "门店不能为空");

        if(idto.getUnsalableDay() != null){
            Calendar cal1 = Calendar.getInstance();
            cal1.add(Calendar.DATE,-1);
            String yesterDay = DateUtil.getDateFormate(cal1.getTime(),"yyyy-MM-dd");

            Calendar cal2 = Calendar.getInstance();
            cal2.add(Calendar.DATE,-idto.getUnsalableDay());
            String unsalableDay = DateUtil.getDateFormate(cal2.getTime(),"yyyy-MM-dd");

            // 查询门店时间段内销售的商品idList
            List<Long> commodityIdList = reportMapper.querySaleCommodityIds(idto.getShopId(),yesterDay,unsalableDay);
            idto.setCommodityIdList(commodityIdList);
        }
        ShopUnsalableIDTO unsalableIDTO = new ShopUnsalableIDTO();
        BeanUtils.copyProperties(idto,unsalableIDTO);
        PageInfo<ShopUnsalableODTO> shopPageInfo = shopCommodityClient.unsalableReport(unsalableIDTO);

        PageInfo<UnsalableODTO> unsalablePageInfo = BeanUtil.copyProperties(shopPageInfo, UnsalableODTO.class);
        List<UnsalableODTO>  unsalableList = unsalablePageInfo.getList();
        if (CollectionUtils.isNotEmpty(unsalableList)) {
            List<Long> commodityIdList = unsalableList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<String, ShopCommodityStockDTO> stockMap = getXdShopCommodityStockMap(idto.getShopId(),commodityIdList);
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList);

            for (UnsalableODTO odto : unsalableList) {
                ShopCommodityStockDTO stockDTO = stockMap.get(odto.getShopId() + "" + odto.getCommodityId());
                odto.setStockQuantity(null != stockDTO ? stockDTO.getStockQuantity() : null);
                odto.setStockNum(null != stockDTO ? new BigDecimal(stockDTO.getStockNumber()) : null);

                String[] barCode = barCodeMap.get(odto.getCommodityId()).split(",");
                odto.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                odto.setBarCodeList(barCodeList);
            }
        }
        return unsalablePageInfo;
    }


    /**
     * 调用xs-wms 获取门店商品库存
     * @param shopId
     * @param commodityList
     * @return
     */
    public Map<String, ShopCommodityStockDTO> getXdShopCommodityStockMap(Long shopId,List<Long> commodityList){
        Map<String, ShopCommodityStockDTO> xdStockMap = new HashMap<>();
        StockQueryIDTO queryDTO = new StockQueryIDTO();
        queryDTO.setWarehouseId(shopId);
        queryDTO.setCommodityList(commodityList);
        List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStock2(queryDTO);
        if(CollectionUtils.isEmpty(stockList)){
            return new HashMap<>();
        }
        for(ShopCommodityStockDTO stock : stockList){
            xdStockMap.put(stock.getShopId() + "" + stock.getCommodityId(),stock);
        }
        return xdStockMap;
    }

    /**
     * 调用xs-wms 获取门店商品库存
     * @return
     */
    public Map<String, ShopCommodityStockDTO> getXdShopCommodityStockMap(Long commodityId){
        Map<String, ShopCommodityStockDTO> xdStockMap = new HashMap<>();
        List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStockByCommodityId(commodityId);
        if(CollectionUtils.isEmpty(stockList)){
            return new HashMap<>();
        }
        for(ShopCommodityStockDTO stock : stockList){
            xdStockMap.put(stock.getShopId() + "" + stock.getCommodityId(),stock);
        }
        return xdStockMap;
    }

    /**
     * 畅销品月度汇总表
     * @param beginTime
     * @param endTime
     */
    @Async
    public void bestsellerGoodMonth(String beginTime, String endTime) {
        Assert.notNull(beginTime, "开始时间不能为空");
        Assert.notNull(endTime, "结束时间不能为空");

        String month = beginTime.substring(0,7);
        // 删除历史
        reportMapper.deleteBestsellerGoodMonth(month);

        // 查询关联的门店idList
        List<Shop> allShopList = shopMapper.getAllShopList();
        List<Long> shopIdList = allShopList.stream().map(item -> item.getId()).collect(Collectors.toList());

        long currentTimeMillis = System.currentTimeMillis();

        // 构造一个线程池
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopIdList.size())
        );

        for(Long shopId: shopIdList){
            try {
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        reportMapper.insertBestsellerGoodMonth(shopId,beginTime,endTime);
                    }
                });

                Thread.sleep(10);
            } catch (Exception e) {
                log.error("畅销品月度汇总表异常: ",e);

                StringBuffer sb = new StringBuffer();
                sb.append("畅销品月度汇总表异常");
                //发送微信模板信息
                weChatSendMessageService.sendWeChatMessage(sb.toString());
            }
        }

        // 等待所有线程执行完毕当前任务。
        threadPool.shutdown();
        try {
            boolean loop = true;
            do {
                //等待所有线程执行完毕当前任务结束
                loop = !threadPool.awaitTermination(2, TimeUnit.SECONDS);//等待2秒
            } while (loop);

            if (loop != true) {
                log.info("畅销品月度汇总表"+ " 所有线程执行完毕");
            }

        } catch (InterruptedException e) {
            log.error("畅销品月度汇总表"+" 线程执行时间异常",e);
        } finally {
            log.info("畅销品月度汇总表"+" 耗时：" + (System.currentTimeMillis() - currentTimeMillis) + " 毫秒");
        }

    }

    /**
     * 畅销品月度缺货报表
     * @param idto
     * @return
     */
    public TablePageInfo<BestsellerGoodODTO> bestsellerGoodMonthReport(BestsellerGoodIDTO idto) {
        QYAssert.isTrue(!(idto.getShopId() == null && idto.getCommodityId() == null && idto.getBarCode() == null), "门店和商品不能同时为空!");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        String monthTime = DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM");
        idto.setSaleMonth(monthTime);

        PageInfo<BestsellerGoodODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            reportMapper.bestsellerGoodMonthReport(idto);
        });

        TablePageInfo pageData = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        List<BestsellerGoodODTO>  pageList = pageData.getList();

        BigDecimal shortNum = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(pageList)){
            List<BestsellerGoodODTO>  sellerList =  reportMapper.bestsellerGoodMonthReport(idto);
            List<Long> commodityIdList = sellerList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

            // 获取门店商品库存数，库存份数
            Map<String, ShopCommodityStockDTO> stockMap = new HashMap<>();
            if(idto.getShopId() != null){
                stockMap = getXdShopCommodityStockMap(idto.getShopId(),commodityIdList);
            }else {
                stockMap = getXdShopCommodityStockMap(commodityIdList.get(0));
            }

            // 获取门店商品是否可售状态
            ShopCommodityInfoIDTO shopCommodityInfoIDTO = new ShopCommodityInfoIDTO();
            shopCommodityInfoIDTO.setShopId(idto.getShopId());
            shopCommodityInfoIDTO.setCommodityId(idto.getCommodityId());

            Map<String, ShopCommodityInfoODTO> shopCommodityMap = new HashMap<>();
            if(StringUtils.isNotBlank(idto.getBarCode())){
                CommodityInfoODTO commodityInfoODTO = commodityMapper.findCommodityInfoForHand(idto.getBarCode());
                if(null != commodityInfoODTO){
                    shopCommodityInfoIDTO.setCommodityId(commodityInfoODTO.getCommodityId());
                    shopCommodityMap = shopCommodityClient.getShopCommodityByShopOrCommodity(shopCommodityInfoIDTO);
                }else {
                    shopCommodityMap = new HashMap<>();
                }
            }else {
                shopCommodityMap = shopCommodityClient.getShopCommodityByShopOrCommodity(shopCommodityInfoIDTO);
            }


            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList);

            for(BestsellerGoodODTO odto : pageList){
                ShopCommodityStockDTO stockDTO = stockMap.get(odto.getShopId() + "" + odto.getCommodityId());
                odto.setStockQuantity(null != stockDTO ? stockDTO.getStockQuantity() : null);
                odto.setStockNum(null != stockDTO ? new BigDecimal(stockDTO.getStockNumber()) : null);

                String[] barCode = barCodeMap.get(odto.getCommodityId()).split(",");
                odto.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                odto.setBarCodeList(barCodeList);

                ShopCommodityInfoODTO shopCommodityODTO = shopCommodityMap.get(odto.getShopId() + "" + odto.getCommodityId());
                odto.setCommoditySaleStatus(null != shopCommodityODTO ? shopCommodityODTO.getCommoditySaleStatus() : null);
            }

            BestsellerGoodODTO bestsellerGoodODTO = new BestsellerGoodODTO();
            for(BestsellerGoodODTO odto : sellerList){
                ShopCommodityStockDTO stockDTO = stockMap.get(odto.getShopId() + "" + odto.getCommodityId());
                odto.setStockQuantity(null != stockDTO ? stockDTO.getStockQuantity() : null);
                if(odto.getStockQuantity() == null  || odto.getStockQuantity().compareTo(BigDecimal.ZERO) <= 0 ){
                    shortNum = shortNum.add(BigDecimal.ONE);
                }
            }
            bestsellerGoodODTO.setShortCount(shortNum);
            bestsellerGoodODTO.setTotalCount(new BigDecimal(sellerList.size()));
            bestsellerGoodODTO.setShortPercent(shortNum.multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(sellerList.size()),1,RoundingMode.HALF_UP) + "%");
            pageData.setHeader(bestsellerGoodODTO);
        }
        return pageData;
    }

    /**
     * 导入混合收款
     * @param collectReportList
     * @return
     */
    public Object importBlendCollectReport(List<BlendCollectIDTO> collectReportList) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(collectReportList),"导入数据不能为空");

        List<String> storeCodeList = collectReportList.stream().map(item -> item.getStoreCode()).collect(Collectors.toList());
        // 查询客户门店信息map
        List<ShopODTO> shopList = shopMapper.getShopListByStoreCodes(storeCodeList);
        Map<String, ShopODTO> storeMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(shopList)){
            storeMap = shopList.stream().collect(Collectors.toMap(ShopODTO::getStoreCode, Function.identity()));
        }

        // 校验
        List<BlendCollect> list = new ArrayList<>();
        List<String> errorMsgList = new ArrayList<>();
        errorMsgList.add("导入失败");

        validImportBlendCollect(collectReportList,storeMap,list,errorMsgList);
        if(errorMsgList.size() > 1){
            return new BlendImportResDTO(false,errorMsgList);
        }
        // 保存/覆盖
        reportMapper.batchSaveOrUpdateBlendCollect(list);

        return  new BlendImportResDTO(true,new ArrayList<>());
    }

    /**
     * 导入混合收款 校验
     * @param blendCollectList
     * @return
     */
    public List<String> validImportBlendCollect(List<BlendCollectIDTO> blendCollectList,Map<String, ShopODTO> storeMap,List<BlendCollect> list,List<String> errorMsgList){
        Map<String,String> dataMap = new HashMap<>();

        for(int i = 0; i < blendCollectList.size(); i++){
            BlendCollect blendCollect = new BlendCollect();
            BlendCollectIDTO idto = blendCollectList.get(i);
            BeanUtils.copyProperties(idto, blendCollect);

            String key = idto.getCollectDate() + idto.getStoreCode() + idto.getCollectName();
            if(dataMap != null && dataMap.get(key) != null){
                errorMsgList.add("第"+(i+2)+"行模板内容有重复");
            }
            dataMap.put(key,key);

            ShopODTO shopODTO = storeMap.get(idto.getStoreCode());
            if(shopODTO == null){
                errorMsgList.add("第"+(i+2)+"行"+idto.getStoreCode()+" 客户编码不存在");
            }else {
                blendCollect.setStoreId(shopODTO.getStoreId());

                if(shopODTO.getShopId() == null){
                    errorMsgList.add("第"+(i+2)+"行"+idto.getStoreCode()+" 不存在对应门店");
                }else {
                    blendCollect.setShopId(shopODTO.getShopId());
                }
            }

             blendCollect.setCollectDate(DateUtil.parseDate(idto.getCollectDate(),"yyyy-MM-dd"));
             if(null == blendCollect.getCollectDate()){
                 errorMsgList.add("第"+(i+2)+"行日期格式不正确(yyyy-MM-dd)");
             }

            if(CollectTypeEnum.CONVERGE.getName().equals(idto.getCollectName())){
                blendCollect.setCollectType(CollectTypeEnum.CONVERGE.getCode());
            }else if(CollectTypeEnum.MONEY.getName().equals(idto.getCollectName())){
                blendCollect.setCollectType(CollectTypeEnum.MONEY.getCode());
            }else {
                errorMsgList.add("第"+(i+2)+"行收款项不正确(聚合/现金)");
            }

            if(!isNumber(idto.getCollectAmount()) || !isNumber(idto.getFeeAmount())){
                errorMsgList.add("第"+(i+2)+"行实收金额、手续费必须大于0并且长度最多15位，允许2位小数");
            }

            blendCollect.setCollectAmount(new BigDecimal(idto.getCollectAmount()));
            blendCollect.setFeeAmount(new BigDecimal(idto.getFeeAmount()));

            list.add(blendCollect);
        }

        return errorMsgList;
    }

    public  boolean isNumber(String str){
        // 长度最多15位，允许2位小数
        Pattern pattern= Pattern.compile("^(\\d{1,15}|\\d{1,15}\\.\\d{1,2})$");
        Matcher match=pattern.matcher(str);
        if(match.matches() == false){
            return false;
        }else{
            return true;
        }
    }

    /**
     * 混合收款(分页)
     * @param ito
     * @return
     */
    public TablePageInfo<BlendCollectODTO> blendCollectPage(BlendCollectPageIDTO ito) {
        PageInfo<BlendCollectODTO> pageInfo = getBlendCollectPageInfo(ito);
        List<BlendCollectODTO> list = pageInfo.getList();

        // 合计
        BlendCollectODTO blendCollectODTO = new BlendCollectODTO();
        if(CollectionUtils.isNotEmpty(list)){
            blendCollectODTO = reportMapper.getBlendCollectSum(ito);
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(blendCollectODTO);
        return tablePageInfo;
    }

    @Nullable
    private PageInfo<BlendCollectODTO> getBlendCollectPageInfo(BlendCollectPageIDTO ito) {
        QYAssert.isTrue(StringUtils.isNotBlank(ito.getBeginDate()) && StringUtils.isNotBlank(ito.getEndDate()), "日期不能为空");

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            TablePageInfo info = new TablePageInfo();
            info.setList(null);
            return info;
        }
        ito.setShopIdList(shopIdList);
        ito.setBeginDate(ito.getBeginDate() + " 00:00:00");
        ito.setEndDate(ito.getEndDate() + " 23:59:59");

        int diff = DateUtil.getDayDif(DateUtil.parseDate(ito.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(ito.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
        Assert.isTrue(diff <= 30, "日期的跨度不能超过31天");

        PageInfo<BlendCollectODTO> pageInfo = PageHelper.startPage(ito.getPageNo(), ito.getPageSize()).doSelectPageInfo(() ->{
            reportMapper.blendCollectPage(ito);
        });

        return pageInfo;
    }

    /**
     * collectType 1: 聚合区分表  2:现金区分表
     * @param ito
     * @return
     */
    public TablePageInfo<BlendCollectODTO> blendCollectConvergeCashPage(BlendCollectPageIDTO ito) {
        PageInfo<BlendCollectODTO> pageInfo = getBlendCollectPageInfo(ito);
        List<BlendCollectODTO> list = pageInfo.getList();

        // 获取聚合手续费比例
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("ConvergeFeeRate");
        BigDecimal convergeFeeRate = new BigDecimal(dictionaryODTO.getOptionValue());

        // 合计
        BlendCollectODTO sumDto = new BlendCollectODTO();
        if(CollectionUtils.isNotEmpty(list)){
            sumDto = reportMapper.getBlendCollectSum(ito);
            // 查询售卡流水
            Map<String,BigDecimal> giftCardCashierMap = getGiftCardCashierWaterMap(ito);
            BigDecimal totalAmount = BigDecimal.ZERO;

            List<BlendCollectODTO> keyODTOList = reportMapper.getShopDateKey(ito);
            for(BlendCollectODTO keyODTO : keyODTOList){
                String key = keyODTO.getShopId() + keyODTO.getCollectDate();
                BigDecimal amount = giftCardCashierMap.get(key) == null ? BigDecimal.ZERO : giftCardCashierMap.get(key);
                totalAmount = totalAmount.add(amount);
            }

            for(BlendCollectODTO result : list){
                String key = result.getShopId() + result.getCollectDate();
                BigDecimal amount = giftCardCashierMap.get(key) == null ? BigDecimal.ZERO : giftCardCashierMap.get(key);
                setBlendCollectODTO(ito.getCollectType(),result,convergeFeeRate,amount);
            }

            // 计算合计
            setBlendCollectODTO(ito.getCollectType(),sumDto,convergeFeeRate,totalAmount);

        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(sumDto);
        return tablePageInfo;
    }

    /**
     * 设置金额
     * @param sumDto
     * @param convergeFeeRate
     */
    public void setBlendCollectODTO(Integer collectType,BlendCollectODTO sumDto,BigDecimal convergeFeeRate,BigDecimal totalAmount){
        sumDto.setConvergeCollectAmount(sumDto.getCollectAmount());
        sumDto.setConvergeFeeAmount(sumDto.getFeeAmount());
        sumDto.setConvergeFeeTaxRate(convergeFeeRate + "%");

        if(CollectTypeEnum.CONVERGE.getCode().equals(collectType)){
            sumDto.setConvergeGiftCardAmount(totalAmount);
            sumDto.setConvergeGiftCardFeeAmount(totalAmount.multiply(convergeFeeRate).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
            sumDto.setConvergeGiftCardRealAmount(totalAmount.subtract(sumDto.getConvergeGiftCardFeeAmount()));

            sumDto.setConvergePosRealAmount(sumDto.getConvergeCollectAmount().subtract(sumDto.getConvergeGiftCardRealAmount()));
            sumDto.setConvergePosFeeAmount(sumDto.getConvergeFeeAmount().subtract(sumDto.getConvergeGiftCardFeeAmount()));
        }

        if(CollectTypeEnum.MONEY.getCode().equals(collectType)){
            sumDto.setCashCollectAmount(sumDto.getCollectAmount());
            sumDto.setCashGiftCardCollectAmount(totalAmount);
            sumDto.setCashPosCollectAmount(sumDto.getCashCollectAmount().subtract(sumDto.getCashGiftCardCollectAmount()));
        }
    }

    /**
     * 获取售卡收银流水
     * @param ito
     * @return
     */
    public Map<String,BigDecimal> getGiftCardCashierWaterMap(BlendCollectPageIDTO ito){
        Map<String,BigDecimal> giftCardCashierMap = new HashMap<>();
        List<String> payTypeList = new ArrayList<>();
        if(CollectTypeEnum.CONVERGE.getCode().equals(ito.getCollectType())){
            payTypeList.add(PaytypeEnum.UNIONPAY_ALI.getCode());
            payTypeList.add(PaytypeEnum.UNIONPAY_WECHAT.getCode());
            payTypeList.add(PaytypeEnum.UNIONPAY_QUICK_PASS.getCode());
        }
        if(CollectTypeEnum.MONEY.getCode().equals(ito.getCollectType())){
            payTypeList.add(PaytypeEnum.MONEY.getCode());
        }
        ito.setPayTypeList(payTypeList);
        List<GiftCardCashierWaterODTO> list = reportMapper.getGiftCardCashierWaterList(ito);
        if(CollectionUtils.isNotEmpty(list)){
            for(GiftCardCashierWaterODTO odto : list){
                giftCardCashierMap.put(odto.getShopId() + odto.getSaleTime(),odto.getTotalAmount());
            }
        }
        return giftCardCashierMap;
    }
    /**
     * @param ito
     * @return
     */
    public PageInfo<CheckingExceptionODTO> checkingExceptionPage(CheckingExceptionPageIDTO ito) {
        QYAssert.isTrue(StringUtils.isNotBlank(ito.getMonth()), "日期不能为空");
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            PageInfo info = new PageInfo();
            info.setList(null);
            return info;
        }
        ito.setShopIdList(shopIdList);

        ito.setBeginDate(ito.getMonth()+"-01");
        ito.setEndDate(ito.getMonth()+"-31");
        PageInfo<CheckingExceptionODTO> pageInfo = PageHelper.startPage(ito.getPageNo(), ito.getPageSize()).doSelectPageInfo(() ->{
            reportMapper.checkingExceptionPage(ito);
        });

        return pageInfo;
    }

    /**
     * 及时达负库存报表汇总
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean shopNegativeStockReportSummary(Long shopId, List<Long> mustSellCommodityIdList) {
        Date nowTime = new Date();

        // 查询上架商品
        XsShopCommodityAppStatusIDTO xsShopCommodityAppStatusIDTO = new XsShopCommodityAppStatusIDTO();
        xsShopCommodityAppStatusIDTO.setShopId(shopId);
        xsShopCommodityAppStatusIDTO.setAppStatus(0);
        List<XsShopCommodityAppStatusODTO> appStatusList = shopCommodityUpDownManagerClient.selectXsShopCommodityAppStatus(xsShopCommodityAppStatusIDTO);
        if(CollectionUtils.isEmpty(appStatusList)){
            log.info("及时达负库存报表汇总--门店无上架商品,shopId: " + shopId);
            return Boolean.FALSE;
        }
        List<Long> appStatusCommodityIdList = appStatusList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        // 根据门店查询门店库存 <= 0 的
        List<StockItemODTO> stockList = xdStockClient.queryNegativeShopStockList(shopId);
        if(CollectionUtils.isEmpty(stockList)){
            log.info("及时达负库存报表汇总--门店没有库存 <= 0,shopId: " + shopId);
            return Boolean.FALSE;
        }

        stockList = stockList.stream().filter(p -> appStatusCommodityIdList.contains(p.getCommodityId())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(stockList)){
            log.info("及时达负库存报表汇总--库存小于0和上架商品无交集,shopId: " + shopId);
            return Boolean.FALSE;
        }

        List<Long> commodityIdList = stockList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<ShopODTO> shopODTOS = shopMapper.getStoreListByShopIdList(Arrays.asList(shopId));
        List<Commodity> commodityList = commodityMapper.findCommodityByIdList(commodityIdList);
        Map<Long, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityId, Function.identity()));

        ShopODTO shopODTO = shopODTOS.get(0);
        List<ShopNegativeStock> shopNegativeStockList = new ArrayList<>();
        for(StockItemODTO stockODTO : stockList){
            ShopNegativeStock negativeStock = new ShopNegativeStock();
            negativeStock.setExtractTime(nowTime);
            negativeStock.setShopId(shopId);
            negativeStock.setShopCode(shopODTO.getShopCode());
            negativeStock.setShopName(shopODTO.getShopName());
            negativeStock.setShopShortName(shopODTO.getShopShortName());
            negativeStock.setStoreCode(shopODTO.getStoreCode());
            negativeStock.setStoreName(shopODTO.getStoreName());
            negativeStock.setCommodityId(stockODTO.getCommodityId());

            Commodity commodity = commodityMap.get(stockODTO.getCommodityId());
            if(commodity != null){
                negativeStock.setCommodityCode(commodity.getCommodityCode());
                negativeStock.setCommodityName(commodity.getCommodityName());
                negativeStock.setCommoditySpec(commodity.getCommoditySpec());
                negativeStock.setCommodityUnit(commodity.getCommodityUnitName());
                negativeStock.setIsWeight(commodity.getIsWeight());
            }

            negativeStock.setStockQuantity(stockODTO.getQuantity());
            Integer sellStatus = CollectionUtils.isNotEmpty(mustSellCommodityIdList) && mustSellCommodityIdList.contains(stockODTO.getCommodityId()) ? 1 : 0;
            negativeStock.setSellStatus(sellStatus);
            negativeStock.setCreateId(1L);
            negativeStock.setCreateTime(nowTime);
            shopNegativeStockList.add(negativeStock);
        }

        // 保存及时达负库存报表
        batchInsertShopNegativeStock(shopNegativeStockList);

        return Boolean.TRUE;
    }

    /**
     * 批量保存及时达负库存报表
     * @param cs
     */
    public void batchInsertShopNegativeStock(List<ShopNegativeStock> cs) {
        int index = 0;
        int count = 2000;
        while (true) {
            List<ShopNegativeStock> items = cs.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                // 一次插入2000条
                shopNegativeStockMapper.insertList(items);
                index += items.size();
            } else {
                break;
            }
        }
    }

    /**
     * 及时达负库存报表
     * @param idto
     * @return
     */
    public PageInfo<ShopNegativeStockODTO> shopNegativeStockReport(ShopNegativeStockQueryIDto idto) {
        PageInfo info = new PageInfo();

        Assert.isTrue(!StringUtil.isBlank(idto.getBeginDate()), "请选择日期");
        Assert.isTrue(!StringUtil.isBlank(idto.getEndDate()), "请选择日期");
        idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
        idto.setEndDate(idto.getEndDate()+ " 23:59:59");

        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
        Assert.isTrue(diff <= 30, "日期的跨度不能超过31天");

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if (!StringUtil.isNullOrEmpty(idto.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(idto.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (CollectionUtils.isEmpty(shopIdList)) {
                    info.setList(null);
                    return info;
                }
            }else {
                info.setList(null);
                return info;
            }
        }

        // 查询今日销售额和上周销售额
        PageInfo<ShopNegativeStockODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            reportMapper.shopNegativeStockReport(idto);
        });

//        List<ShopNegativeStockODTO> list = pageInfo.getList();
//        if(CollectionUtils.isNotEmpty(list)){
//            List<Long> shopIds = pageInfo.getList().stream().map(ShopNegativeStockODTO::getShopId).collect(Collectors.toList());
//            Map<Long, OrgAndParentInfoODTO> orgMap = shopService.getOrgMap(shopIds);
//
//            for (ShopNegativeStockODTO item : list) {
//                OrgAndParentInfoODTO orgDto = orgMap.get(item.getShopId());
//                if(orgDto != null){
//                    item.setOrgName(orgDto.getParentOrgName());
//                }
//            }
//        }

        return pageInfo;
    }

    /**
     * 品类销售周同比
     * @param idto
     * @return
     */
    public TablePageInfo<CategoryAllSalesPercentODTO> categoryAllSalesWeekPercent(CategoryAllSalesPercentIDTO idto) {
        List<Long> shopIdList = getShopIdList(idto.getOrgCode());
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo<>();
        }
        idto.setShopIdList(shopIdList);
        if(null != idto.getShopId() && !shopIdList.contains(idto.getShopId())){
            return new TablePageInfo<>();
        }

        PageInfo<CategoryAllSalesPercentODTO> pageInfo = null;
        CategoryAllSalesPercentODTO header = null;

        Date parse = DateTimeUtil.parse(idto.getSalesDate(), "yyyy-MM-dd");
        String lastweek = DateTimeUtil.addDay(parse, -7);
        Date now = DateUtil.getNowDate();
        // 如果选择当天之前,则走大数据
        if(parse.before(now) && dataQueryChangeService.enableDataQuery()) {
            Integer pageNo = idto.getPageNo();
            Integer pageSize = idto.getPageSize();
            // 调用大数据查询全部
            idto.initExportPage();
            // 查询今日线下、线上销售额
            List<CategoryAllSalesPercentODTO> todayList = reportMapper.categoryAllSalesWeekPercent(idto);

            // 查询上周线下、线上销售额
            idto.setSalesDate(lastweek);
            List<CategoryAllSalesPercentODTO> lastweekList = reportMapper.categoryAllSalesWeekPercent(idto);

            Map<String, Object> resultMap = categoryAllPercentToPage(todayList, lastweekList, pageNo, pageSize);
            if(resultMap.get("header") != null) {
                header = (CategoryAllSalesPercentODTO) resultMap.get("header");
            }
            pageInfo = (PageInfo<CategoryAllSalesPercentODTO>) resultMap.get("pageInfo");
        }else {
            // 获取所有数据不带分页的合计数据(header)
            List<CategoryAllSalesPercentODTO> headerODTOS = reportMapper.categoryAllSalesWeekPercent(idto);
            if(SpringUtil.isEmpty(headerODTOS)){
                return new TablePageInfo<>();
            }
            header = this.getHeaderForCategoryAllSalesPercent(headerODTOS);

            // 查询今日销售额和上周销售额
            pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
                reportMapper.categoryAllSalesWeekPercent(idto);
            });
        }

        List<CategoryAllSalesPercentODTO> list = pageInfo.getList();
        this.handlePageListForCategoryAllSalesPercent(list,header);
        pageInfo.setList(list);

        TablePageInfo<CategoryAllSalesPercentODTO> tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }

    private Map<String, Object> categoryAllPercentToPage(List<CategoryAllSalesPercentODTO> todayList, List<CategoryAllSalesPercentODTO> lastweekList,
                                              Integer pageNo, Integer pageSize){

        Map<String, Object> resultMap = new HashMap<>();
        // 数据合并汇总
        Set<CategoryAllSalesPercentODTO> mergeList = new HashSet<>();
        Map<Long, List<CategoryAllSalesPercentODTO>> todayMap = new HashMap<>();
        Map<Long, List<CategoryAllSalesPercentODTO>> lastWeekMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(todayList)) {
            todayMap = todayList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityFirstId();
            }));
            todayList.forEach(item -> {
                CategoryAllSalesPercentODTO categoryAllSalesPercentODTO = new CategoryAllSalesPercentODTO();
                categoryAllSalesPercentODTO.setCommodityFirstId(item.getCommodityFirstId());
                categoryAllSalesPercentODTO.setFirstKindName(item.getFirstKindName());
                mergeList.add(categoryAllSalesPercentODTO);
            });
        }

        if(CollectionUtils.isNotEmpty(lastweekList)) {
            lastWeekMap = lastweekList.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityFirstId();
            }));
            lastweekList.forEach(item -> {
                CategoryAllSalesPercentODTO categoryAllSalesPercentODTO = new CategoryAllSalesPercentODTO();
                categoryAllSalesPercentODTO.setCommodityFirstId(item.getCommodityFirstId());
                categoryAllSalesPercentODTO.setFirstKindName(item.getFirstKindName());
                mergeList.add(categoryAllSalesPercentODTO);
            });
        }

        if(CollectionUtils.isEmpty(mergeList)) {
            resultMap.put("header", new CategoryAllSalesPercentODTO());

            PageInfo info = new PageInfo();
            info.setList(new ArrayList());
            resultMap.put("pageInfo", info);
            return resultMap;
        }else {
            List<CategoryAllSalesPercentODTO> pageList = new ArrayList<>();
            for(CategoryAllSalesPercentODTO item : mergeList) {
                CategoryAllSalesPercentODTO categoryAllSalesPercentODTO = BeanCloneUtils.copyTo(item, CategoryAllSalesPercentODTO.class);
                Long key = item.getCommodityFirstId();

                if(todayMap.containsKey(key)) {
                    List<CategoryAllSalesPercentODTO> list = todayMap.get(key);
                    categoryAllSalesPercentODTO.setThisSalesMoneyOffLine(list.get(0).getThisSalesMoneyOffLine());
                    categoryAllSalesPercentODTO.setThisSalesMoneyOnLine(list.get(0).getThisSalesMoneyOnLine());
                }else{
                    categoryAllSalesPercentODTO.setThisSalesMoneyOffLine(BigDecimal.ZERO);
                    categoryAllSalesPercentODTO.setThisSalesMoneyOnLine(BigDecimal.ZERO);
                }

                if(lastWeekMap.containsKey(key)) {
                    List<CategoryAllSalesPercentODTO> list = lastWeekMap.get(key);
                    categoryAllSalesPercentODTO.setLastSalesMoneyOffLine(list.get(0).getThisSalesMoneyOffLine());
                    categoryAllSalesPercentODTO.setLastSalesMoneyOnLine(list.get(0).getThisSalesMoneyOnLine());
                }else{
                    categoryAllSalesPercentODTO.setLastSalesMoneyOffLine(BigDecimal.ZERO);
                    categoryAllSalesPercentODTO.setLastSalesMoneyOnLine(BigDecimal.ZERO);
                }
                pageList.add(categoryAllSalesPercentODTO);
            }

            CategoryAllSalesPercentODTO header = this.getHeaderForCategoryAllSalesPercent(pageList);
            PageInfo<CategoryAllSalesPercentODTO> pageInfo = ListToPageInfoUtil.convert(pageList, pageSize, pageNo);
            resultMap.put("header", header);
            resultMap.put("pageInfo", pageInfo);
        }
        return resultMap;
    }

    /**
     * 品类销售月同比
     * @param idto
     * @return
     * @throws ParseException
     */
    public TablePageInfo<CategoryAllSalesPercentODTO> categoryAllSalesMonthPercent(CategoryAllSalesPercentIDTO idto) throws ParseException {
        List<Long> shopIdList = getShopIdList(idto.getOrgCode());
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo<>();
        }
        idto.setShopIdList(shopIdList);
        if(null != idto.getShopId() && !shopIdList.contains(idto.getShopId())){
            return new TablePageInfo<>();
        }

        this.handleDatesForCategoryAllSalesMonthPercent(idto);

        PageInfo<CategoryAllSalesPercentODTO> pageInfo = null;
        CategoryAllSalesPercentODTO header = null;

        Date parse = DateTimeUtil.parse(idto.getEndDate(), "yyyy-MM-dd");
        Date now = DateUtil.getNowDate();
        // 如果选择当天之前,则走大数据
        if(parse.before(now) && dataQueryChangeService.enableDataQuery()) {
            Integer pageNo = idto.getPageNo();
            Integer pageSize = idto.getPageSize();
            // 调用大数据查询全部
            idto.initExportPage();

            // 查询今日线下、线上销售额
            List<CategoryAllSalesPercentODTO> todayList = reportMapper.categoryAllSalesMonthPercent(idto);

            // 查询上周线下、线上销售额
            idto.setBeginDate(idto.getLastMonthBeginDate());
            idto.setEndDate(idto.getLastMonthEndDate());
            List<CategoryAllSalesPercentODTO> lastweekList = reportMapper.categoryAllSalesMonthPercent(idto);

            Map<String, Object> resultMap = categoryAllPercentToPage(todayList, lastweekList, pageNo, pageSize);
            if(resultMap.get("header") != null) {
                header = (CategoryAllSalesPercentODTO) resultMap.get("header");
            }
            pageInfo = (PageInfo<CategoryAllSalesPercentODTO>) resultMap.get("pageInfo");

        }else {
            // 获取所有数据不带分页的合计数据(header) q
            List<CategoryAllSalesPercentODTO> headerODTOS = reportMapper.categoryAllSalesMonthPercent(idto);
            if(SpringUtil.isEmpty(headerODTOS)){
                return new TablePageInfo<>();
            }
            header = this.getHeaderForCategoryAllSalesPercent(headerODTOS);

            // 查询今日销售额和上周销售额
            pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
                reportMapper.categoryAllSalesMonthPercent(idto);
            });
        }


        List<CategoryAllSalesPercentODTO> list = pageInfo.getList();
        this.handlePageListForCategoryAllSalesPercent(list,header);
        pageInfo.setList(list);

        TablePageInfo<CategoryAllSalesPercentODTO> tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }

    /**
     * 处理月同比需要的日期
     * @param idto
     * @throws ParseException
     */
    private void handleDatesForCategoryAllSalesMonthPercent(CategoryAllSalesPercentIDTO idto) throws ParseException {
        String endDateStr = idto.getSalesDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate = sdf.parse(idto.getSalesDate());
        Calendar cal = Calendar.getInstance();

        // 本月第一天
        cal.setTime(endDate);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        String beginDateStr = sdf.format(cal.getTime());
        //本月最后一天
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date monthLastDay = cal.getTime();

        // 上个月同一天, 如果当月的日大于上个月最后一天, 则会取上月最后一天
        cal.setTime(endDate);
        cal.add(Calendar.MONTH, -1);
        Date lastMonthEndDay = cal.getTime();
        //上月第一天
        cal.set(Calendar.DAY_OF_MONTH, 1);
        String lastMonthBeginDateStr = sdf.format(cal.getTime());
        //上月最后一天
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastMonthLastDay = cal.getTime();

        idto.setBeginDate(beginDateStr);
        idto.setLastMonthBeginDate(lastMonthBeginDateStr);

        // 如果结束日期等于本月最后一天,  上个月也取上个月最后一天
        if(endDate.equals(monthLastDay)) {
            lastMonthEndDay = lastMonthLastDay;
        }
        idto.setEndDate(endDateStr);
        idto.setLastMonthEndDate(sdf.format(lastMonthEndDay));
    }

    /**
     * 根据部门code和用户权限取shopIdList查询条件
     * @param orgCode
     * @return
     */
    private List<Long> getShopIdList(String orgCode){
        // 当门店选择为全部时，查询该用户所能允许查询的门店列表
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            return null;
        }
        // 查询组织下的门店列表
        if(!StringUtil.isBlank(orgCode)){
            List<ShopDto> shopDtosList = shopClient.selectShopListByParentOrgCode(orgCode);
            if(SpringUtil.isNotEmpty(shopDtosList)){
                shopIdList = shopDtosList.stream().map(ShopDto::getId).filter(shopIdList::contains).collect(Collectors.toList());
            }
            if(CollectionUtils.isEmpty(shopIdList)){
                return null;
            }
        }
        return shopIdList;
    }

    /**
     * 周/月销售同比 合计数据
     * @param headerODTOS
     * @return
     */
    private CategoryAllSalesPercentODTO getHeaderForCategoryAllSalesPercent(List<CategoryAllSalesPercentODTO> headerODTOS){
        CategoryAllSalesPercentODTO header = new CategoryAllSalesPercentODTO();
        if (SpringUtil.isNotEmpty(headerODTOS)) {
            BigDecimal totalTodaySalesMoneyOffLine = BigDecimal.ZERO;
            BigDecimal totalTodaySalesMoneyOnLine = BigDecimal.ZERO;
            BigDecimal totalLastWeekSalesMoneyOffLine = BigDecimal.ZERO;
            BigDecimal totalLastWeekSalesMoneyOnLine = BigDecimal.ZERO;
            for(CategoryAllSalesPercentODTO item : headerODTOS){
                totalTodaySalesMoneyOffLine = totalTodaySalesMoneyOffLine.add(item.getThisSalesMoneyOffLine());
                totalTodaySalesMoneyOnLine = totalTodaySalesMoneyOnLine.add(item.getThisSalesMoneyOnLine());
                totalLastWeekSalesMoneyOffLine = totalLastWeekSalesMoneyOffLine.add(item.getLastSalesMoneyOffLine());
                totalLastWeekSalesMoneyOnLine = totalLastWeekSalesMoneyOnLine.add(item.getLastSalesMoneyOnLine());
            }
            BigDecimal totalTodaySalesMoney = totalTodaySalesMoneyOffLine.add(totalTodaySalesMoneyOnLine);
            BigDecimal totalLastWeekSalesMoney = totalLastWeekSalesMoneyOffLine.add(totalLastWeekSalesMoneyOnLine);
            header.setThisSalesMoneyOnLine(totalTodaySalesMoneyOnLine);
            header.setThisSalesMoneyOffLine(totalTodaySalesMoneyOffLine);
            header.setLastSalesMoneyOnLine(totalLastWeekSalesMoneyOnLine);
            header.setLastSalesMoneyOffLine(totalLastWeekSalesMoneyOffLine);
            BigDecimal salesMoneyDiff = totalTodaySalesMoney.subtract(totalLastWeekSalesMoney);
            header.setSalesMoneyDiff(salesMoneyDiff);
            if(totalLastWeekSalesMoney.compareTo(BigDecimal.ZERO) == 0){
                header.setCategorySalesPercent(null);
                header.setCategorySalesPercentStr("");
            }else{
                BigDecimal categorySalesPercent = salesMoneyDiff.divide(totalLastWeekSalesMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
                header.setCategorySalesPercent(categorySalesPercent);
                header.setCategorySalesPercentStr(categorySalesPercent.toString()+"%");
            }
            header.setSalesMoneyPercent(new BigDecimal("100"));
            header.setSalesMoneyPercentStr("100.00%");
        }
        return header;
    }

    /**
     * 周/月销售同比 分页list装配
     * @param list
     * @param header
     */
    private void handlePageListForCategoryAllSalesPercent(List<CategoryAllSalesPercentODTO> list, CategoryAllSalesPercentODTO header){
        if(SpringUtil.isEmpty(list)){
            return;
        }
        for (CategoryAllSalesPercentODTO item : list) {
            BigDecimal todaySalesMoney = item.getThisSalesMoneyOnLine().add(item.getThisSalesMoneyOffLine());
            BigDecimal lastWeekSalesMoney = item.getLastSalesMoneyOnLine().add(item.getLastSalesMoneyOffLine());
            // 销售差额 = 今日销售额合计-上周销售额合计
            BigDecimal salesMoneyDiff = todaySalesMoney.subtract(lastWeekSalesMoney);
            item.setSalesMoneyDiff(salesMoneyDiff);
            // 品类销售周同比 = 销售差额/上周销售额合计
            if(lastWeekSalesMoney.compareTo(BigDecimal.ZERO) == 0){
                item.setCategorySalesPercent(null);
                item.setCategorySalesPercentStr("");
            }else{
                BigDecimal categorySalesMoneyPercentWeekly = salesMoneyDiff.divide(lastWeekSalesMoney,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
                item.setCategorySalesPercent(categorySalesMoneyPercentWeekly);
                item.setCategorySalesPercentStr(categorySalesMoneyPercentWeekly.toString()+"%");
            }
            // 今日销售额占比 = 今日销售额单品类合计/今日总销售额
            BigDecimal totalTodaySalesMoney = header.getThisSalesMoneyOffLine().add(header.getThisSalesMoneyOnLine());
            if(totalTodaySalesMoney.compareTo(BigDecimal.ZERO) == 0){
                item.setSalesMoneyPercent(null);
                item.setSalesMoneyPercentStr("");
            }else{
                BigDecimal salesMoneyPercentToday = todaySalesMoney.divide(totalTodaySalesMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
                item.setSalesMoneyPercent(salesMoneyPercentToday);
                item.setSalesMoneyPercentStr(salesMoneyPercentToday.toString()+"%");
            }

        }
    }


}
