package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
public class AppOperateReportIDto extends Pagination{
	/**
	 * 
	 */
	private static final long serialVersionUID = -9030380644603795904L;
	private Long shopId;
	private String beginDate;
	private String endDate;
	private Date orderBeginDate;
	private Date orderEndDate;
}
