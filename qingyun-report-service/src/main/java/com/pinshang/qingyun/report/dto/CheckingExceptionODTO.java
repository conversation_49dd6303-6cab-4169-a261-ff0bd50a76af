package com.pinshang.qingyun.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2021/5/21 11:30
 */
@Data
public class CheckingExceptionODTO {

    private Long shopId;
    @ApiModelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("门店")
    private String shopName;

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("收款项编码")
    private String receivableCode;

    @ApiModelProperty("收款项名称")
    private String receivableName;

    @ApiModelProperty("销售金额")
    private BigDecimal totalSale;

    @ApiModelProperty("手续费")
    private BigDecimal poundage;

    @ApiModelProperty("实收金额")
    private BigDecimal amount;

    @ApiModelProperty("差异")
    private BigDecimal difference;

    @ApiModelProperty("操作id")
    private Long createId;

    @ApiModelProperty("导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
