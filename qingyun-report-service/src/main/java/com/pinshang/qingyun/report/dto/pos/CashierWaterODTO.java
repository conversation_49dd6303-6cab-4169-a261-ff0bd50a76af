package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/5/30
 */
@Data
public class CashierWaterODTO {

    @ApiModelProperty(position = 1, value ="门店编码")
    private String shopCode;

    @ApiModelProperty(position = 2, value ="门店名称")
    private String shopName;

    @ApiModelProperty(position = 3, value ="订单号")
    private String orderCode;

    @ApiModelProperty(position = 4,  value = "收银渠道:联网收银,本地收银")
    private String cashChannel;

    @ApiModelProperty(position = 5, value ="交易时间")
    private Date createTime;

    @ApiModelProperty(position = 6, value ="销售金额")
    private String totalAmount;

    @ApiModelProperty(position = 7, value ="销售方式(销售、退款)")
    private String saleType;

    @ApiModelProperty(position = 8, value ="付款金额")
    private String payAmount;

    @ApiModelProperty(position = 9, value ="付款方式")
    private String payName;

    @ApiModelProperty(position = 10, value ="第三方订单号")
    private String thirdPartyOrderId;

    @ApiModelProperty(position = 11, value ="会员卡号")
    private String memberCardNo;

    @ApiModelProperty(position = 12, value ="会员姓名")
    private String memberName;

    @ApiModelProperty(position = 13, value ="收银员编号")
    private String employeeNumber;

    @ApiModelProperty(position = 14, value ="员工姓名")
    private String createName;

    @ApiModelProperty(position = 15, value = "pos机类型名称")
    private String posTypeName;

    @ApiModelProperty(position = 16, value ="POS机号")
    private String macCode;

    @ApiModelProperty(position = 17, value ="退货原单号")
    private String returnOrderCode;


//    @ApiModelProperty(position = 16, value ="第三方订单号")
//    private String thirdPartyOrderId;

}
