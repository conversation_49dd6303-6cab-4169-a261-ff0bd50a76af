package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.report.dto.LastMonthSaleDTO;
import com.pinshang.qingyun.report.dto.shop.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.mapper.shop.CommodityTaxMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CommodityTaxService {

    @Autowired
    private CommodityTaxMapper commodityTaxMapper;

    /**
     * 上个月pos有销售的商品
     * @param lastMonthSaleDTO
     */
    public List<ShopCommoditySaleStatisticsODTO> lastMonthSale(LastMonthSaleDTO lastMonthSaleDTO) {
        return commodityTaxMapper.lastMonthSale(lastMonthSaleDTO);
    }
}
