package com.pinshang.qingyun.report.dto.pos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommodityDeleteMessage {
    private Long id;
    private Long shopId;
    private String shopName;
    private String posMacCode;
    private Long commodityId;
    private BigDecimal quantity;
    private Long employeeId;
    private Date date;
    private BigDecimal price;
    private Long orderCode;
    private String authorizerCode;
    private String authorizerName;

    /** pos机id **/
    private Long posMacId;

    /** pos机类型 1-收银pos, 2-自助pos **/
    private Integer posType;
}
