package com.pinshang.qingyun.report.controller.pos;

import com.pinshang.qingyun.report.dto.pos.TradeODTO;
import com.pinshang.qingyun.report.service.pos.AIMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/aIMonitor/")
public class AIMonitorController {

    @Autowired
    private AIMonitorService aIMonitorService;

    /**
     * AI监控查询小票信息
     * @param orderCode
     * @param operateType
     * @return
     */
    @GetMapping("abnormalReceipt")
    public TradeODTO abnormalReceipt(@RequestParam(value = "orderCode") String orderCode, @RequestParam(value = "operateType") Integer operateType) {
        return aIMonitorService.abnormalReceipt(orderCode, operateType);
    }
}
