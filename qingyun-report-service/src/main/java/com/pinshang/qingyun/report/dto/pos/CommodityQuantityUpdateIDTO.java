package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CommodityQuantityUpdateIDTO extends Pagination {

    private List<Long> shopIdList;

    private Long shopId;

    private String shopCode;

    private String startOperateDate;

    private String endOperateDate;

    private String orderCode;

    @ApiModelProperty("开始小时")
    private String hourBegin;
    @ApiModelProperty("结束小时")
    private String hourEnd;
    @ApiModelProperty("修改单品金额≥")
    private String compareOrderAmount;

}
