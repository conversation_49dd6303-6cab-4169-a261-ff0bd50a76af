package com.pinshang.qingyun.report.model.settle;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/5/24
 */

@Entity
@Table(name = "t_md_stock_in_shop")
public class StockInShop extends BaseSimplePO {

    private String dateTime;
    private Long shopId;

    private BigDecimal orderQuantity = BigDecimal.ZERO;  // 订货数量
    private BigDecimal orderAmount = BigDecimal.ZERO;  // 订货金额

    private BigDecimal deliveryQuantity = BigDecimal.ZERO; // 实发数量
    private BigDecimal deliveryAmount = BigDecimal.ZERO; // 实发金额

    private BigDecimal allotInQuantity = BigDecimal.ZERO; // 调拨入库数量
    private BigDecimal allotInAmount = BigDecimal.ZERO; // 调拨入库金额

    private BigDecimal allotOutQuantity = BigDecimal.ZERO; // 调拨出库数量
    private BigDecimal allotOutAmount = BigDecimal.ZERO; // 调拨出去金额

    private BigDecimal returnQuantity = BigDecimal.ZERO; // 实退数量
    private BigDecimal returnAmount = BigDecimal.ZERO; // 实退金额

    private BigDecimal cloudInQuantity = BigDecimal.ZERO; // 云超转入数量(订单取消数量、配送失败数量、退款数量)
    private BigDecimal cloudInAmount = BigDecimal.ZERO; // 云超转入金额(订单取消金额、配送失败金额、退款金额)

    public BigDecimal getCloudInQuantity() {
        return cloudInQuantity;
    }
    public void setCloudInQuantity(BigDecimal cloudInQuantity) {
        this.cloudInQuantity = cloudInQuantity;
    }
    public BigDecimal getCloudInAmount() {
        return cloudInAmount;
    }
    public void setCloudInAmount(BigDecimal cloudInAmount) {
        this.cloudInAmount = cloudInAmount;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public BigDecimal getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(BigDecimal orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getDeliveryQuantity() {
        return deliveryQuantity;
    }

    public void setDeliveryQuantity(BigDecimal deliveryQuantity) {
        this.deliveryQuantity = deliveryQuantity;
    }

    public BigDecimal getDeliveryAmount() {
        return deliveryAmount;
    }

    public void setDeliveryAmount(BigDecimal deliveryAmount) {
        this.deliveryAmount = deliveryAmount;
    }

    public BigDecimal getAllotInQuantity() {
        return allotInQuantity;
    }

    public void setAllotInQuantity(BigDecimal allotInQuantity) {
        this.allotInQuantity = allotInQuantity;
    }

    public BigDecimal getAllotInAmount() {
        return allotInAmount;
    }

    public void setAllotInAmount(BigDecimal allotInAmount) {
        this.allotInAmount = allotInAmount;
    }

    public BigDecimal getAllotOutQuantity() {
        return allotOutQuantity;
    }

    public void setAllotOutQuantity(BigDecimal allotOutQuantity) {
        this.allotOutQuantity = allotOutQuantity;
    }

    public BigDecimal getAllotOutAmount() {
        return allotOutAmount;
    }

    public void setAllotOutAmount(BigDecimal allotOutAmount) {
        this.allotOutAmount = allotOutAmount;
    }

    public BigDecimal getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(BigDecimal returnQuantity) {
        this.returnQuantity = returnQuantity;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }
}
