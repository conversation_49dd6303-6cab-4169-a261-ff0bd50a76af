package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:44
 */
@Data
public class CommodityInfoODTO {
    @ApiModelProperty(position = 1, value = "id")
    private Long commodityId;
    @ApiModelProperty(position = 2, value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(position = 3, value = "商品名称")
    private String commodityName;
    @ApiModelProperty(position = 4, value = "商品规格")
    private String commoditySpec;
    @ApiModelProperty(position = 5, value = "商品计量单位")
    private String commodityUnit;
    @ApiModelProperty(position = 6, value = "报损数量(条码为称重码时才会有值)")
    private BigDecimal breakageNum;

    @ApiModelProperty("正常库数量")
    private BigDecimal stockQuantity;
    @ApiModelProperty("正常库份数")
    private Integer stockNumber;

    @ApiModelProperty("临时库份数")
    private BigDecimal qualityQuantity;
    @ApiModelProperty("临时库数量")
    private Integer qualityNumber;

    @ApiModelProperty("条形码(主码)")
    private String barCode;

    @ApiModelProperty("产地")
    private String origin;

    @ApiModelProperty("门店零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty("特价")
    private BigDecimal promotionPrice;

    @ApiModelProperty("会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty("商品价格")
    private BigDecimal commodityPrice;

    @ApiModelProperty("是否称重")
    private Integer isWeight;

    @ApiModelProperty("包装的规格")
    private BigDecimal commodityPackageSpec;
}
