package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:44
 */
@Data
public class BreakageEnteringShopExcelODTO {
    @ApiModelProperty(position = 1, value = "门店名称")
    private String shopName;
    @ApiModelProperty(position = 2, value = "报损渠道")
    private String breakageChannelName;
    @ApiModelProperty(position = 3, value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(position = 4, value = "条形码")
    private String barCode;
    @ApiModelProperty(position = 5, value = "品名和规格")
    private String commodityNameAndSpec;
    @ApiModelProperty(position = 6, value = "计量单位")
    private String commodityUnitName;
    @ApiModelProperty(position = 8, value = "报损数量")
    private BigDecimal breakageNum;
    @ApiModelProperty(position = 9, value = "零售价")
    private String retailPrice;
    @ApiModelProperty(position = 10, value = "报损售价金")
    private String breakageXretailPrice;
    @ApiModelProperty(position = 11, value = "成本价")
    private String costPrice;
    @ApiModelProperty(position = 12, value = "报损成本金")
    private String breakageXcostPrice;
    @ApiModelProperty(position = 13, value = "报损人")
    private String createName;
    @ApiModelProperty(position = 14, value = "报损时间")
    private String createTime;
    @ApiModelProperty(position = 15, value = "监控位置")
    private String monitorLocationName;
    @ApiModelProperty(position = 16, value = "报损原因")
    private String reasonName;
    @ApiModelProperty(position = 17, value = "上产日期")
    private String makeDate;
    @ApiModelProperty(position = 18, value = "到期日期")
    private String expireDate;
    @ApiModelProperty(position = 20, value = "退货单号")
    private String referCode;

}
