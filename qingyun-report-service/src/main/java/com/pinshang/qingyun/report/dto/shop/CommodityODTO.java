package com.pinshang.qingyun.report.dto.shop;

import java.math.BigDecimal;

public class CommodityODTO {
    private Long       commodityId;
    /** 企业ID */
    private Long       enterpriseId;
    /** 产品代码 */
    private String     commodityCode;
    /** 产品名称 */
    private String     commodityName;
    /** 助记码 */
    private String     commodityAid;
    /** 型号规格 */
    private String     commoditySpec;
    /** 0-停用,1-启用 */
    private String     commodityState;
    /** 一级分类名称 */
    private String     cateName1;
    private String     cateId1;
    /** 分类名称 */
    private String     cateName3;
    /** 现价 */
    private BigDecimal price;
    /** 老价格 */
    private BigDecimal oldPrice;
    /** 计量单位 */
    private String     unitName;

    private Integer    logisticsModel; //物流模式

    /** 条形码 */
    private String barCode;

    private String value;

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getCommodityCode() {
        return commodityCode;
    }
    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }
    public String getCommodityName() {
        return commodityName;
    }
    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }
    public String getCommodityAid() {
        return commodityAid;
    }
    public void setCommodityAid(String commodityAid) {
        this.commodityAid = commodityAid;
    }
    public String getCommoditySpec() {
        return commoditySpec;
    }
    public void setCommoditySpec(String commoditySpec) {
        this.commoditySpec = commoditySpec;
    }
    public String getCommodityState() {
        return commodityState;
    }
    public void setCommodityState(String commodityState) {
        this.commodityState = commodityState;
    }
    public String getCateName1() {
        return cateName1;
    }
    public void setCateName1(String cateName1) {
        this.cateName1 = cateName1;
    }
    public String getCateName3() {
        return cateName3;
    }
    public void setCateName3(String cateName3) {
        this.cateName3 = cateName3;
    }
    public BigDecimal getPrice() {
        return price;
    }
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    public BigDecimal getOldPrice() {
        return oldPrice;
    }
    public void setOldPrice(BigDecimal oldPrice) {
        this.oldPrice = oldPrice;
    }
    public String getUnitName() {
        return unitName;
    }
    public void setUnitName(String unitName) {this.unitName = unitName;}
    public String getCateId1() {
        return cateId1;
    }
    public void setCateId1(String cateId1) {
        this.cateId1 = cateId1;
    }
    public Integer getLogisticsModel() {
        return logisticsModel;
    }
    public void setLogisticsModel(Integer logisticsModel) {
        this.logisticsModel = logisticsModel;
    }
    public String getBarCode() {
        return barCode;
    }
    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
    public String getValue() {return value;}
    public void setValue(String value) {this.value = value;}
}
