package com.pinshang.qingyun.report.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2021/4/13
 */
@Data
public class OrderInfoODTO {

    private Long id;
    private String orderCode;

    private Integer orderStatus;
    private Date orderTime;

    private Long storeId;
    private Long commodityId;
    private Integer logisticsModel;

    private BigDecimal quantity;
    private BigDecimal totalPrice;
    private BigDecimal price;

    private BigDecimal realDeliveryQuantity;
    private BigDecimal realReceiveQuantity;

    private Long createId;
    private Date createTime;
    private Long updateId;
    private Date updateTime;

    private Long  orderItemId;
    private Integer status;

    private String workshopCode;
    private String workshopName;
    private String factoryCode;
    private String factoryName;
    private String commodityUnitName;
    private String realName;
}
