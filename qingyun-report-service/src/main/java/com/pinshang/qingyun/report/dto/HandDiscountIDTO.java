package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;


/**
* 手动优惠报表
*
* <AUTHOR>
* @since 2019-07-02
*/
@Data
public class HandDiscountIDTO extends Pagination  {

	@ApiModelProperty(position = 1, value ="门店id")
	private Long shopId;

	@ApiModelProperty(position = 2, value ="开始日期yyyy-MM-dd")
	private String beginTime;

	@ApiModelProperty(position = 3, value ="结束日期yyyy-MM-dd")
	private String endTime;

	@ApiModelProperty(position = 4, value ="收银员id")
	private Long casherId;

	@ApiModelProperty(position = 4, value ="门店编码")
	private String shopCode;

	@ApiModelProperty(hidden = true)
	private List<Long> shopIdList;
}
