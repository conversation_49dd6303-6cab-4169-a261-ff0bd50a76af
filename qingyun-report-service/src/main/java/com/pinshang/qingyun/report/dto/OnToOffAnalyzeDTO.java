package com.pinshang.qingyun.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OnToOffAnalyzeDTO {

    @ApiModelProperty("渠道  1线上  2线下")
    private Integer channel;

    @ApiModelProperty("日期  1今日  2上周 3本月  4上月")
    private Integer time;

    @ApiModelProperty("销售额")
    private BigDecimal sales;

    @ApiModelProperty("客单量")
    private BigDecimal visitorNumber;

    @ApiModelProperty("客单价")
    private BigDecimal averageAmount;

    @ApiModelProperty("门店数量")
    private BigDecimal shopNum;

    /**
     * 销售额/门店数/数据天数，支持2位小数（四舍五入）
     */
    @ApiModelProperty("单门店日均销售额")
    private BigDecimal singleAverageSale;

    /**
     * 客单量/门店数/数据天数，支持2位小数（四舍五入）
     */
    @ApiModelProperty("单门店日均客单量")
    private BigDecimal singleAverageVisitor;

    private String timeName;

    public String getTimeName() {
        if(this.time != null) {
            if (this.time.equals(1)) {
                this.timeName = "今日";
            } else if (this.time.equals(2)) {
                this.timeName = "上周";
            } else if (this.time.equals(3)) {
                this.timeName = "本月";
            } else if (this.time.equals(4)) {
                this.timeName = "上月";
            }
        }
        return this.timeName;
    }


}
