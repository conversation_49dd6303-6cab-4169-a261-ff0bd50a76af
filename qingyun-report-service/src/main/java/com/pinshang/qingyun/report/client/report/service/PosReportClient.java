package com.pinshang.qingyun.report.client.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.report.client.report.hystrix.PosReportClientHystrix;
import com.pinshang.qingyun.report.dto.ExceptionMemberODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_POS_REPORT_SERVICE, fallbackFactory = PosReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PosReportClient {

	@RequestMapping(value = "/common/getAllPayTypeList", method = RequestMethod.POST)
	List<PayTypeODTO> getAllPayTypeList();

	/**
	 * 收银员对账--对账记录
	 * @return
	 */
	@RequestMapping(value = "/common/selectAccountRecordList", method = RequestMethod.POST)
	TablePageInfo<CashierReconciledODTO> selectAccountRecordList(@RequestBody CashierReconciledIDTO cashierReconciledIDTO);


	@RequestMapping(value = "/reports/findMemberAccountExecptionInfo", method = RequestMethod.POST)
	ExceptionMemberODTO findMemberAccountExecptionInfo(@RequestParam(value = "shopId") Long shopId, @RequestParam(value = "userId") Long userId, @RequestParam(value = "cashDate") String cashDate);

	/**
	 * 根据退货单号查询订单号
	 * @param returnCodeList
	 * @return
	 */
	@RequestMapping(value = "/reports/findOrderCodeByReturnCode", method = RequestMethod.POST)
	List<OrderCodeODTO> findOrderCodeByReturnCode(@RequestBody List<Long> returnCodeList);
}
