package com.pinshang.qingyun.report.model.pos;


import com.pinshang.qingyun.report.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_pos_report_cashier_discount")
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CashierDiscountReport extends BaseModel<CashierDiscountReport> {

    /** 订单编码*/
    private  Long orderCode;
    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    /** 原价金额*/
    private BigDecimal realTotalAmount;

    /** 实收金额*/
    private BigDecimal totalAmount;

    /** 让利金额*/
    private BigDecimal discountAmount;

    /** 销售日期*/
    private Date saleTime;

    /** 收银员编号*/
    private String cashierNo;

    /** 收银员名称*/
    private String cashierName;

    //private Date createTime;
    //private Long createId;
}
