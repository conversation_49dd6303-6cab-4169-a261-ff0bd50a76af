package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ClearingChargesItemIDTO extends Pagination {

    @ApiModelProperty(position = 1, value ="收银月报id")
    private Long clearingChargesId;

    @ApiModelProperty(position = 2, value ="查询月份yyyy-MM格式")
    private String date;

    @ApiModelProperty(position = 3, value ="门店id")
    private Long shopId;

    private List<Long> shopIdList;

}
