package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName HelpCardheckDetail4Invoice
 * <AUTHOR>
 * @Date 2023/3/9 14:55
 * @Description HelpCardheckDetail4Invoice
 * @Version 1.0
 */
@Data
public class HelpCardCheckDetail4InvoiceODTO {
    @ApiModelProperty("手动对账表t_pos_help_card_account_check  id")
    private Long id;

    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("公司id")
    private Long companyId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("对账期间-开始")
    private Date checkDateBegin;

    @ApiModelProperty("对账期间-结束")
    private Date checkDateEnd;

    @ApiModelProperty("对账期间")
    private String checkDateRange;

    @ApiModelProperty("对账金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("px消费金额")
    private BigDecimal pxAmount;

    @ApiModelProperty("区域日期范围 下 px消费总金额")
    private BigDecimal pxTotalAmount;

    @ApiModelProperty("开票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty("提交状态")
    private Integer status;

    @ApiModelProperty("提交人")
    private String createName;

    @ApiModelProperty("提交时间")
    private Date createTime;

    @ApiModelProperty("明细")
    private List<HelpCardCheckDetail4InvoiceItemODTO> itemList;
}
