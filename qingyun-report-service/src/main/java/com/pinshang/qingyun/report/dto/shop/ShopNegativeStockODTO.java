package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/6/29
 */
@Data
public class ShopNegativeStockODTO {
    @ExcelIgnore
    private Long shopId;

    @ApiModelProperty("部门")
    @ExcelProperty(value = "部门")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    @ApiModelProperty("门店名称")
    @ExcelProperty(value = "门店名称")
    private String shopName;

    @ApiModelProperty("门店简称")
    @ExcelProperty(value = "门店简称")
    private String shopShortName;

    @ApiModelProperty("客户编码")
    @ExcelProperty(value = "客户编码")
    private String storeCode;

    @ExcelIgnore
    private Long commodityId;

    @ApiModelProperty("商品编码")
    @ExcelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    @ExcelProperty(value = "规格")
    private String commoditySpec;

    @ApiModelProperty("是否称重")
    @ExcelProperty(value = "是否称重")
    private String isWeightName;

    @ApiModelProperty("库存数量")
    @ExcelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty("计量单位")
    @ExcelProperty(value = "计量单位")
    private String commodityUnit;

    @ApiModelProperty("库存提取时间点")
    @ExcelProperty(value = "库存提取时间点")
    private String extractTimeStr;

    @ApiModelProperty("及时达必售")
    @ExcelProperty(value = "品上生活必售")
    private String sellStatusName;

    @ApiModelProperty("经营模式")
    @ExcelProperty(value = "经营模式")
    private String managementModeName;

}
