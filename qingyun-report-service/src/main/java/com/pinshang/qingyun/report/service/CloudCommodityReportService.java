package com.pinshang.qingyun.report.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.ShopODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportQueryIDTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportODTO;
import com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportQueryIDTO;
import com.pinshang.qingyun.report.mapper.CloudCommodityReportMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.service.shop.ShopService;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.report.dto.CloudCommodityInfoXDReportODTO;
import com.pinshang.qingyun.xd.report.dto.CloudCommodityReportSummaryODTO;
import com.pinshang.qingyun.xd.report.service.CloudCommodityLackJobClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/3/7
 */
@Service
public class CloudCommodityReportService {

    @Autowired
    private CloudCommodityReportMapper cloudCommodityReportMapper;
    @Autowired
    private SMMUserClient sMMUserClient;
    @Autowired
    private CloudCommodityLackJobClient cloudCommodityLackJobClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private IRenderService renderService;

    /**
     * 云超实发汇总报表
     * @param vo
     * @return
     */
    public TablePageInfo<CloudCommodityReportODTO> realDeliveryReport(CloudCommodityReportQueryIDTO vo) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        // 查询用户工厂
        SelectUserFactoryIdListIDTO idto = SelectUserFactoryIdListIDTO.firstCacheThenDb(tokenInfo.getUserId());
        List<Long> factoryIdList = sMMUserClient.selectUserFactoryIdList(idto);
        if(CollectionUtils.isEmpty(factoryIdList)){
            return new TablePageInfo<>();
        }
        vo.setFactoryIdList(factoryIdList);

        if(StringUtils.isNotBlank(vo.getOrderTimeBegin()) && StringUtils.isNotBlank(vo.getOrderTimeEnd())){
            DateTime beginDate = DateTime.parse(vo.getOrderTimeBegin(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            DateTime endDate = DateTime.parse(vo.getOrderTimeEnd(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "查询时间段不能大于31天");
        }

        if (!StringUtil.isNullOrEmpty(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                vo.setShopIdList(orgShopIdList);
            }else {
                TablePageInfo info = new TablePageInfo();
                info.setList(null);
                return info;
            }
        }

        if(CollectionUtils.isNotEmpty(vo.getShopTypeList()) || CollectionUtils.isNotEmpty(vo.getManagementModeList())) {
            List<Long> shopIdList = shopMapper.queryShopIdListByParam(vo.getShopTypeList(), vo.getManagementModeList());
            if(CollectionUtils.isEmpty(shopIdList)) {
                return new TablePageInfo<>();
            }else {
                List<Long> ids = vo.getShopIdList();
                if(CollectionUtils.isEmpty(ids)) {
                    vo.setShopIdList(shopIdList);
                }else {
                    ids.retainAll(shopIdList);
                    if(CollectionUtils.isEmpty(ids)) {
                        return new TablePageInfo<>();
                    }else {
                        vo.setShopIdList(ids);
                    }
                }
            }
        }

        PageInfo<CloudCommodityReportODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->{
            cloudCommodityReportMapper.realDeliveryReport2(vo);
        });

        // 查询sum值
        CloudCommodityReportODTO sum = new CloudCommodityReportODTO();
        if(CollectionUtils.isNotEmpty(pageInfo.getList())){
            List<Long> commodityIds = pageInfo.getList().stream().map(CloudCommodityReportODTO::getCommodityId).collect(Collectors.toList());
//            List<Long> shopIds = pageInfo.getList().stream().map(CloudCommodityReportODTO::getShopId).collect(Collectors.toList());

//            Map<Long, OrgAndParentInfoODTO> orgMap = shopService.getOrgMap(shopIds);
            // 一品多码
            Map<Long, String> commodityMap = commodityService.getCommodityBarCodeMap(commodityIds);

            pageInfo.getList().forEach(e -> {
                if(StringUtils.isNotBlank(commodityMap.get(e.getCommodityId()))){
                    String[] barCode = commodityMap.get(e.getCommodityId()).split(",");
                    e.setBarCode(barCode[0]);
                    List barCodeList = java.util.Arrays.asList(barCode);
                    e.setBarCodeList(barCodeList);
                }

//                OrgAndParentInfoODTO orgDto = orgMap.get(e.getShopId());
//                if(orgDto != null){
//                    e.setOrgName(orgDto.getParentOrgName());
//                }
            });

            // 查询sum值
            sum = cloudCommodityReportMapper.realDeliveryReport2Sum(vo);
        }
//        renderService.render(pageInfo.getList(),"commodityReport/realDeliveryReport");
        TablePageInfo tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }


    /**
     * 云超实发汇总信息
     * @param orderTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cloudCommodityReportSummary(String orderTime) {

        // 先删除
        cloudCommodityReportMapper.deleteCloudCommodityReportSummary(orderTime);

        // 查询
        List<CloudCommodityReportSummaryODTO> cloudCommoditySummaryList = cloudCommodityLackJobClient.queryCloudCommodityReportSummary(orderTime);
        if(CollectionUtils.isNotEmpty(cloudCommoditySummaryList)){
            List<Long> storeIdList = cloudCommoditySummaryList.stream().distinct().map(item -> item.getStoreId()).collect(Collectors.toList());
            List<ShopODTO>  storeList = shopMapper.getShopListByStoreIds(storeIdList);
            Map<Long, ShopODTO> storeMap = storeList.stream().collect(Collectors.toMap(ShopODTO::getStoreId, Function.identity()));

            for(CloudCommodityReportSummaryODTO odto : cloudCommoditySummaryList){
                ShopODTO shopDTO = storeMap.get(odto.getStoreId());
                if(shopDTO != null){
                    odto.setStoreCode(shopDTO.getStoreCode());
                    odto.setStoreName(shopDTO.getStoreName());
                }
            }

            // 批量新增
            int index = 0;
            int count = 500;
            while (true) {
                List<CloudCommodityReportSummaryODTO> items = cloudCommoditySummaryList.stream().skip(index).limit(count).collect(Collectors.toList());
                if (items.size() > 0) {
                    // 一次插入500条
                    cloudCommodityReportMapper.batchInsertCloudCommodityReportSummary(items);

                    index += items.size();
                } else {
                    break;
                }
            }
        }

        return Boolean.TRUE;
    }



    /**
     * 云超商品报表汇总
     * @param orderTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cloudCommodityInfoReport(String orderTime) {

        // 先删除
        cloudCommodityReportMapper.deleteCloudCommodityInfoReport(orderTime);

        // 查询
        List<CloudCommodityInfoXDReportODTO> cloudCommodityInfoReportList = cloudCommodityLackJobClient.queryCloudCommodityInfoReport(orderTime);
        if(CollectionUtils.isNotEmpty(cloudCommodityInfoReportList)){
            List<Long> storeIdList = cloudCommodityInfoReportList.stream().distinct().map(item -> item.getStoreId()).collect(Collectors.toList());
            List<ShopODTO>  storeList = shopMapper.getShopListByStoreIds(storeIdList);
            Map<Long, ShopODTO> storeMap = storeList.stream().collect(Collectors.toMap(ShopODTO::getStoreId, Function.identity()));

            for(CloudCommodityInfoXDReportODTO odto : cloudCommodityInfoReportList){
                ShopODTO shopDTO = storeMap.get(odto.getStoreId());
                if(shopDTO != null){
                    odto.setStoreCode(shopDTO.getStoreCode());
                    odto.setStoreName(shopDTO.getStoreName());
                }
            }

            // 批量新增
            int index = 0;
            int count = 500;
            while (true) {
                List<CloudCommodityInfoXDReportODTO> items = cloudCommodityInfoReportList.stream().skip(index).limit(count).collect(Collectors.toList());
                if (items.size() > 0) {
                    // 一次插入500条
                    cloudCommodityReportMapper.batchInsertCloudCommodityInfoReport(items);

                    index += items.size();
                } else {
                    break;
                }
            }
        }

        return Boolean.TRUE;
    }



    /**
     * 云超报表(订单取消明细表、补差退款明细表、云超配送失败明细表)
     * refer_type 关联的类型，1＝订单付款，2＝订单取消退款，3＝申请退款，4=补差退款，5=手工退款
     * order_status  0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败
     * @param vo
     * @return
     */
    public TablePageInfo<CloudCommodityInfoReportODTO> cloudCommodityReport(CloudCommodityInfoReportQueryIDTO vo) {
        Assert.notNull(vo.getReportType(), "参数错误");

       List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info = new TablePageInfo();
            info.setList(null);
            return info;
        }
        if (!StringUtil.isNullOrEmpty(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (null != vo.getShopId()) {
                    shopIdList.retainAll(Arrays.asList(vo.getShopId()));
                }
                if (CollectionUtils.isEmpty(shopIdList)) {
                    TablePageInfo info = new TablePageInfo();
                    info.setList(null);
                    return info;
                }
            }else {
                TablePageInfo info = new TablePageInfo();
                info.setList(null);
                return info;
            }
        }
        vo.setShopIdList(shopIdList);

        if(StringUtils.isNotBlank(vo.getOrderTimeBegin()) && StringUtils.isNotBlank(vo.getOrderTimeEnd())){
            DateTime beginDate = DateTime.parse(vo.getOrderTimeBegin(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            DateTime endDate = DateTime.parse(vo.getOrderTimeEnd(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "查询时间段不能大于31天");

            vo.setOrderTimeBegin(vo.getOrderTimeBegin() + " 00:00:00");
            vo.setOrderTimeEnd(vo.getOrderTimeEnd() + " 23:59:59");
        }

        PageInfo<CloudCommodityInfoReportODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->{
            cloudCommodityReportMapper.cloudCommodityReport(vo);
        });

        // 查询sum值
        CloudCommodityInfoReportODTO sum = new CloudCommodityInfoReportODTO();
        if(CollectionUtils.isNotEmpty(pageInfo.getList())){
//            List<Long> shopIds = pageInfo.getList().stream().map(CloudCommodityInfoReportODTO::getShopId).collect(Collectors.toList());
            List<Long> commodityIds = pageInfo.getList().stream().map(CloudCommodityInfoReportODTO::getCommodityId).collect(Collectors.toList());

//            Map<Long, OrgAndParentInfoODTO> orgMap = shopService.getOrgMap(shopIds);
            // 一品多码
            Map<Long, String> commodityMap = commodityService.getCommodityBarCodeMap(commodityIds);

            pageInfo.getList().forEach(e -> {
                if(StringUtils.isNotBlank(commodityMap.get(e.getCommodityId()))){
                    String[] barCode = commodityMap.get(e.getCommodityId()).split(",");
                    e.setBarCode(barCode[0]);
                    List barCodeList = java.util.Arrays.asList(barCode);
                    e.setBarCodeList(barCodeList);
                }

//                OrgAndParentInfoODTO orgDto = orgMap.get(e.getShopId());
//                if(orgDto != null){
//                    e.setOrgName(orgDto.getParentOrgName());
//                }

            });

            // 查询sum值
            sum = cloudCommodityReportMapper.cloudCommodityReportSum(vo);
        }
//        renderService.render(pageInfo.getList(),"commodityReport/cloudCommodityReport");
        TablePageInfo tablePageInfo  = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        tablePageInfo.setHeader(sum);
        return tablePageInfo;
    }

    /**
     * 客户端云超实发汇总报表 不校验权限
     * @param vo
     * @return
     */
    public List<CloudCommodityReportODTO> clientRealDeliveryReport(CloudCommodityReportQueryIDTO vo) {
        List<CloudCommodityReportODTO> cloudCommodityReportODTOS = cloudCommodityReportMapper.realDeliveryReport2(vo);
        return cloudCommodityReportODTOS;
    }
}
