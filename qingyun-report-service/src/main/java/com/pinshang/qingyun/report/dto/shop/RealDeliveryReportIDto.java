package com.pinshang.qingyun.report.dto.shop;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RealDeliveryReportIDto extends Pagination{

	private static final long serialVersionUID = 1L;

	private String commodityKey;//商品编码，名称

	private String barCode; //条形码

	/** 一级分类id */
    private  Long categoryId;

	private String factoryCode;//工厂

	private String workshopCodeOrName;//生产组

	private String flowshopCodeOrName;//车间

	private String warehouseCodeOrName;//仓库

	private String beginDate;
	@Change(value = DataQueryConstant.NOW)
	private String endDate;

	private Boolean differ;

	private  Long commodityId;

	private Long shopId;

	private Long storeId;

	private Long storeTypeId;//客户类型


	@ApiModelProperty("大类id")
	private Long cate1;
	@ApiModelProperty("中类id")
	private Long cate2;
	@ApiModelProperty("小类id")
	private Long cate3;

	@ApiModelProperty("1:大类汇总  2:中类汇总   3:小类汇总")
	private Integer groupCate;

	@ApiModelProperty("经营模式：1-直营、2-外包")
	private Integer managementMode;

	@ApiModelProperty("门店类型")
	private Integer shopType;

	private List<Long> shopIdList;


	@ApiModelProperty("门店类型List")
	private List<Integer> shopTypeList;

	@ApiModelProperty("经营模式List：1-直营、2-外包")
	private List<Integer> managementModeList;
}
