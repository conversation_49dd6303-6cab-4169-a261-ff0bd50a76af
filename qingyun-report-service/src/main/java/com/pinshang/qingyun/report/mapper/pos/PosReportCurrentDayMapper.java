package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2021/4/27
 */
@Repository
public interface PosReportCurrentDayMapper {

    List<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(@Param("idto") SalesSummaryReportOrderIDTO idto);

    BigDecimal queryFirstCateWeekSaleAmount(@Param("shopId")Long shopId, @Param("beginDate")String beginDate, @Param("endDate")String endDate);
}
