package com.pinshang.qingyun.report.dto.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PosHelpCardAccountCheckingODTO
 * <AUTHOR>
 * @Date 2023/2/23 17:55
 * @Description PosHelpCardAccountCheckingODTO
 * @Version 1.0
 */
@Data
public class HelpCardAccountCheckPageODTO {
    @ApiModelProperty("对账id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("区域id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("对账开始时间 yyyy-MM-dd")
    private Date checkDateBegin;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("对账结束时间 yyyy-MM-dd")
    private Date checkDateEnd;

    @ApiModelProperty("对账期间")
    private String checkDateRange;

    @ApiModelProperty("对账年月 yyyy-MM")
    private String checkDateMonth;

    @ApiModelProperty("对账金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal checkAmount;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty("品鲜交易金额")
    private BigDecimal pxTraderAmount;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty("差异金额")
    private BigDecimal diffAmount;

    @ApiModelProperty("对账状态, 1-未对账, 2-已对账, 3-已取消")
    private Integer status;

    private Long createId;

    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "createId")
    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Long updateId;

    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "updateId")
    private String updateName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
