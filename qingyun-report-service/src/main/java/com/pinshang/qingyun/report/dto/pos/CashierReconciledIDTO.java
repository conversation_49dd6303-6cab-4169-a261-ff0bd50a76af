package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CashierReconciledIDTO extends Pagination {

    @ApiModelProperty(position = 1, value = "门店编码(如果总部查询全部门店传 '' 或者null)")
    private String shopCode;

    @ApiModelProperty(position = 2, value = "开始时间")
    private String  startTime;

    @ApiModelProperty(position = 3, value = "结束时间")
    private String EndTime;

    @ApiModelProperty(position = 4, value = "收银员")
    private String saleNumber;

    @ApiModelProperty(position = 5, value = "付款方式")
    private String payType;

    @ApiModelProperty(position = 6, value = "是否自动抹零(1、是 2、否)")
    private String sumOfMoney;

    @ApiModelProperty(position = 7, value = "搜索类型 1、收银员对账 2、对账记录 3、日对账")
    private String searchCriteria;

    @ApiModelProperty(position = 8,  value = "销售方式(1:销售, 2:退货)")
    private Integer saleType;

    @ApiModelProperty(position = 9,  value = "订单来源（1线上，2线下）")
    private Integer sourceType;

    private List<Long> shopIdList;

    @ApiModelProperty(position = 10,  value = "POS机号")
    private String macCode;

    private Boolean isCurrentDay = false;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

}
