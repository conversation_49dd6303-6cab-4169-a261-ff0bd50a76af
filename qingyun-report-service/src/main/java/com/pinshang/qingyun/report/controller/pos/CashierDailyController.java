package com.pinshang.qingyun.report.controller.pos;

import com.pinshang.qingyun.base.controller.BaseController;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.service.pos.CashierDailyService;
import com.pinshang.qingyun.shop.dto.ShopODTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 收银日报
 * Name Chenqiang
 * Date 2018_06_01
 */
@RestController
@RequestMapping("/CollectingSilver")
public class CashierDailyController extends BaseController {
    @Autowired
    private CashierDailyService cashierDailyService;


    /**
     * 收银日报
     *
     * @param cashierDailyIDTO
     * @return
     */
    @ApiOperation(value = "收银日报", notes = "收银日报", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/cashierDaily")
    public TablePageInfo<CashierDailyODTO> selectCashierDailyList(@RequestBody CashierDailyIDTO cashierDailyIDTO) {
        TablePageInfo <CashierDailyODTO> result = null;
        cashierDailyIDTO.setStartTime( cashierDailyIDTO.getStartTime() + " 00:00:00" );
        cashierDailyIDTO.setEndTime( cashierDailyIDTO.getEndTime() + " 23:59:59" );
        List<PayTypeODTO> payTypeList = cashierDailyService.getPayTypeList();
        cashierDailyIDTO.setPayTypeList(payTypeList);

        return cashierDailyService.selectCashierDailyList(cashierDailyIDTO);
    }

    /**
     * 收银员对账
     *
     * 注意：该接口不能迁移出去，该接口直接查询了t_ps_balance
     * 收银员对账--对账记录查询t_ps_balance 调用pos_report client 解决
     *
     * @param cashierReconciledIDTO
     * @return
     */
    @ApiOperation(value = "收银员对账", notes = "收银员对账", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/cashierReconciled")
    public TablePageInfo <CashierReconciledODTO> selectCashierReconciledList(@RequestBody CashierReconciledIDTO cashierReconciledIDTO) {
        cashierReconciledIDTO.setShopCode( cashierReconciledIDTO.getShopCode() );
        cashierReconciledIDTO.setSaleNumber( cashierReconciledIDTO.getSaleNumber() );
        cashierReconciledIDTO.setStartTime( cashierReconciledIDTO.getStartTime() + " 00:00:00" );
        cashierReconciledIDTO.setEndTime( cashierReconciledIDTO.getEndTime() + " 23:59:59" );
        if (!cashierReconciledIDTO.getPayType().equals( "" )) {
            cashierReconciledIDTO.setSumOfMoney( "" );
        }
        TablePageInfo <CashierReconciledODTO> result = null;
        if (cashierReconciledIDTO.getSearchCriteria().equals( "1" )) {
            //收银员对账
            result = cashierDailyService.selectCashierReconciledList( cashierReconciledIDTO );
        } else if (cashierReconciledIDTO.getSearchCriteria().equals( "2" )) {
            //前台收银对账记录
            result = cashierDailyService.selectAccountRecordList( cashierReconciledIDTO );
        } else if (cashierReconciledIDTO.getSearchCriteria().equals( "3" )) {
            //收银员日对账
            result = cashierDailyService.selectDiurnalReconciliationList( cashierReconciledIDTO );
        }
        return result;
    }

    @ApiOperation(value = "门店列表", notes = "门店列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectShopList")
    public List <ShopODTO> selectShopList() {
        return cashierDailyService.selectShopList();
    }

    @ApiOperation(value = "收银员列表", notes = "收银员列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/selectCashierList")
    public List <CashierODTO> selectCashierList(@RequestBody UserIDTO userIDTO) {
        return cashierDailyService.selectCashierList( userIDTO );
    }
}