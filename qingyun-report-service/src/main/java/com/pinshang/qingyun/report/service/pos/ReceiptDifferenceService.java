package com.pinshang.qingyun.report.service.pos;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.dto.pos.ReceiptDifferenceDTO;
import com.pinshang.qingyun.report.mapper.pos.ReceiptDifferenceMapper;
import com.pinshang.qingyun.report.model.pos.GiftCardSalesWater;
import com.pinshang.qingyun.report.model.pos.ReceiptDifference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ReceiptDifferenceService {

    @Autowired
    private ReceiptDifferenceMapper receiptDifferenceMapper;

    public Boolean insert(ReceiptDifferenceDTO dto) {
        Example example = new Example(ReceiptDifference.class);
        example.createCriteria().andEqualTo("date", dto.getDate())
                .andEqualTo("shopId", dto.getShopId())
                .andEqualTo("receivableCode", dto.getReceivableCode());
        List<ReceiptDifference> list = receiptDifferenceMapper.selectByExample(example);
        if (null != list && list.size() > 0) {
            receiptDifferenceMapper.deleteByExample(example);
        }
        ReceiptDifference receiptDifference = new ReceiptDifference();
        BeanUtils.copyProperties(dto, receiptDifference);
        receiptDifference.setCreateTime(new Date());
        receiptDifferenceMapper.insert(receiptDifference);
        return Boolean.TRUE;
    }
}
