package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.model.pos.HelpCardAccountCheck;
import com.pinshang.qingyun.report.model.pos.HelpCardAccountCheckItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName HelpCardAccountCheckingMapper
 * <AUTHOR>
 * @Date 2023/2/24 11:00
 * @Description HelpCardAccountCheckingMapper
 * @Version 1.0
 */
@Repository
public interface HelpCardAccountCheckMapper extends MyMapper<HelpCardAccountCheck> {
    List<HelpCardAccountCheckPageODTO> page(HelpCardAccountCheckPageIDTO dito);

    List<HelpCardAccountCheckTraderAmountDetailODTO> traderAmountDetail(@Param("checkId") Long checkId, @Param("shopId") Long shopId);

    BigDecimal traderAmountDetailSum(@Param("checkId") Long checkId, @Param("shopId") Long shopId);

    BigDecimal selectPxTraderAmount(@Param("areaId")Long areaId, @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<Long> selectUnionDateList(@Param("areaId")Long areaId, @Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("id") Long id);

    List<HelpCardCheckDetail4InvoiceItemODTO> selectCheckDetail4Invoice(HelpCardCheckDetail4InvoiceItemIDTO dito);

}
