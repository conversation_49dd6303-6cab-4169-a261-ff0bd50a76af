package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_pos_report_guest_list_summary")
@EqualsAndHashCode(callSuper = false)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GuestListSummaryReport extends BaseModel<GuestListSummaryReport> {
   /* @Id
    private Long id;
*/
    /** 订单编码*/
    private  Long orderCode;
    /** 门店ID*/
    private Long shopId;

    /** 门店code*/
    private String shopCode;

    /** 门店名称*/
    private String shopName;

    private Long macId;

    /** 机器号*/
    private String macCode;

    /** 机器名称*/
    private String macName;

    /** 客单量*/
    private BigDecimal guestQuantity;

    /** 客单金额*/
    private BigDecimal guestAmount;

    /** 销售日期*/
    private Date saleTime;

    /** 时间段*/
    private Integer hourTime;

    //private Date createTime;
    //private Long createId;
}
