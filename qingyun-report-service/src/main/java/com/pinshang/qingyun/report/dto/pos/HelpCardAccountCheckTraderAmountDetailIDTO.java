package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName PosHelpCardAccountCheckingTraderAmountDetailODTO
 * <AUTHOR>
 * @Date 2023/2/24 13:38
 * @Description PosHelpCardAccountCheckingTraderAmountDetailODTO
 * @Version 1.0
 */
@Data
public class HelpCardAccountCheckTraderAmountDetailIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("对账id")
    private Long checkId;
}
