package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;


/**
* 收银流水报表
*
* <AUTHOR>
* @since 2018-05-31
*/
@Data
@ToString
@Entity
@Table(name = "t_pos_report_cashier_water")
@NoArgsConstructor
public class ReportCashierWater extends BaseModel<ReportCashierWater> {

	/** 门店id */
	private Long shopId;

	/** 门店编码 */
	private String shopCode;

	/** 门店名称 */
	private String shopName;

	/** 订单号 */
	private String orderCode;

	/** 收银时间 */
	private Date createTime;

	/** 销售金额 */
	private BigDecimal totalAmount;

	/** 销售方式(销售、退款) */
	private String saleType;

	/** 付款类型 */
	private String payType;

	/** 付款方式 */
	private String payName;

	/** 会员卡号 */
	private String memberCardNo;

	/** 会员姓名 */
	private String memberName;

	/** 支付金额 */
	private BigDecimal payAmount;

	/** 员工号 */
	private String employeeNumber;

	/** 员工姓名 */
	private String createName;

	/** 退货原单号 */
	private String returnOrderCode;

	/** POS机编号 */
	private String macCode;

	/** 订单支付金额 */
	private BigDecimal orderTotalAmount;

	/** 第三方订单号 */
	private String thirdPartyOrderId;

	/** pos机id */
	private Long posMacId;

	/** POS类型 */
	private Integer posType;
}
