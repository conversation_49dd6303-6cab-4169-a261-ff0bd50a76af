package com.pinshang.qingyun.report.mapper;

import com.pinshang.qingyun.infrastructure.data.query.annotate.DataQuery;
import com.pinshang.qingyun.report.dto.OrderInfoODTO;
import com.pinshang.qingyun.report.dto.pos.ShopODTO;
import com.pinshang.qingyun.report.dto.shop.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@Repository
public interface ShopReportMapper {

    BigDecimal getShortDeliveryRealDeliveryAmount(@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    BigDecimal getCommodityRealDeliveryAmount(@Param("beginTime") String beginTime,@Param("endTime") String endTime);

    List<ShortDeliveryReportODto> shortDeliveryBeforeYesterdayReport(ShortDeliveryReportIDto vo);

    List<ShortDeliveryReportODto> shortDeliveryTodayReport(ShortDeliveryReportIDto vo);

    // 商品总表-门店汇总
    @DataQuery(value = "commodityTaxShopCode")
    List<CommodityTaxReportODto> commodityTaxReportByShop(CommodityTaxReportIDto vo);
    // 商品总表-大类汇总
    @DataQuery(value = "commodityTaxFirstCode")
    List<CommodityTaxReportODto> commodityTaxFirstReport(CommodityTaxReportIDto vo);
    // 商品总表-中类汇总
    @DataQuery(value = "commodityTaxSecondCode")
    List<CommodityTaxReportODto> commodityTaxSecondReport(CommodityTaxReportIDto vo);
    // 商品总表-类别汇总
    @DataQuery(value = "commodityTaxThirdCode")
    List<CommodityTaxReportODto> commodityTaxThirdReport(CommodityTaxReportIDto vo);
    // 商品总表-商品汇总
    @DataQuery(value = "commodityTaxCommodityCode")
    List<CommodityTaxReportODto> commodityTaxCommodityReport(CommodityTaxReportIDto vo);

    //List<CommodityTaxReportODto> commodityTaxReport(CommodityTaxReportIDto vo);

    List<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto vo);

    List<ActualReceiptAnalysisODto> actualReceiptAnalysisMonthReport(ActualReceiptAnalysisIDto vo);

    List<ShopODTO> getAllShopNameList();

    List<OrderSalesAnalysisODTO> orderSalesAnalysisReport(OrderSalesAnalysisIDTO idto);

    // 订货销售分析-大类汇总
    @DataQuery(value = "orderSalesAnalysisFirstCateCode")
    List<OrderSalesAnalysisODTO> orderSalesAnalysisFirstCateReport(OrderSalesAnalysisIDTO idto);
    // 订货销售分析-中类汇总
    @DataQuery(value = "orderSalesAnalysisSecondCateCode")
    List<OrderSalesAnalysisODTO> orderSalesAnalysisSecondCateReport(OrderSalesAnalysisIDTO idto);
    // 订货销售分析-sum
    @DataQuery(value = "orderSalesAnalysisSumCode")
    OrderSalesAnalysisODTO orderSalesAnalysisReportSum(OrderSalesAnalysisIDTO idto);

    // 类别客单周同比
    List<CateAverageAmountODTO> cateAverageAmountReport(CateAverageAmountIDTO idto);
    // 类别客单周同比 - 大类
    @DataQuery(value = "cateAverageAmountFirstCode")
    List<CateAverageAmountBiODTO> cateAverageAmountFirstReport(CateAverageAmountIDTO idto);
    // 类别客单周同比 - 中类
    @DataQuery(value = "cateAverageAmountSecondCode")
    List<CateAverageAmountBiODTO> cateAverageAmountSecondReport(CateAverageAmountIDTO idto);

    // 类别客单周同比sum
    CateAverageAmountODTO cateAverageAmountSum(CateAverageAmountIDTO idto);

    List<RealDeliveryReportODto> realDeliveryReport(@Param("vo") RealDeliveryReportIDto vo);

    BigDecimal realTotalDeliveryReport(@Param("vo") RealDeliveryReportIDto vo);

    List<ThirdRealDeliveryReportODto> thirdRealDeliveryReport(@Param("vo") RealDeliveryReportIDto vo);

    @DataQuery(value = "shopCateRealDeliveryFirstCode")
    List<ThirdRealDeliveryReportODto> thirdRealDeliveryFirstReport(RealDeliveryReportIDto vo);
    @DataQuery(value = "shopCateRealDeliverySecondCode")
    List<ThirdRealDeliveryReportODto> thirdRealDeliverySecondReport(RealDeliveryReportIDto vo);
    @DataQuery(value = "shopCateRealDeliveryThirdCode")
    List<ThirdRealDeliveryReportODto> thirdRealDeliveryThirdReport(RealDeliveryReportIDto vo);
    @DataQuery(value = "shopCateRealDeliverySumCode")
    List<ThirdRealDeliveryReportODto> thirdRealDeliverySumReport(RealDeliveryReportIDto vo);

    void insertOrderReportMonth(@Param("beginTime")String beginTime,@Param("endTime") String endTime);

    void deleteOrderReportMonth(@Param("month") String month);

    List<RealDeliveryReportODto> realDeliveryStoreTypeReport(@Param("vo") RealDeliveryReportIDto vo);

    BigDecimal realTotalDeliveryStoreTypeReport(@Param("vo") RealDeliveryReportIDto vo);

    List<ShopOrderGoodReportODto> shopOrderGoodReport(@Param("vo") ShopOrderGoodReportIDto vo);

    int deleteDayOrderReportByCodes(@Param("orderCodeList") List<String> orderCodeList);

    int batchInsertDayOrderReport(@Param("orderInfoList") List<OrderInfoODTO> orderInfoList);

    List<String> getShortOrderTimeList(@Param("orderCodeList") List<String> orderCodeList);
}
