package com.pinshang.qingyun.report.dto.shop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;

import java.math.BigDecimal;

@Data
public class ThirdRealDeliveryReportODto {

	@ExcelIgnore
	private Long shopId;

	@ExcelProperty("客户编码")
	private String storeCode;

	@ExcelProperty("门店名称")
	private String shopName;

	@ApiModelProperty("大类名称")
	@ExcelProperty("大类")
	private String commodityFirstName;

	@ApiModelProperty("中类名称")
	@ExcelProperty("中类")
	private String commoditySecondName;

	@ApiModelProperty("小类名称")
	@ExcelProperty("小类")
	private String commodityThirdName;

	@ExcelProperty("订货金额")
	private BigDecimal orderAmount;

	@ExcelProperty("实发金额")
	private BigDecimal realAmount;

	@ExcelProperty("实退金额")
	private BigDecimal returnAmount;

	@FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgCode,keyName = "shopId")
	@ExcelIgnore
	private String orgCode;

	@ApiModelProperty("所属部门")
	@FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
	@ExcelProperty("所属部门")
	private String orgName;

	@ApiModelProperty("经营模式：1-直营、2-外包")
	@ExcelProperty("经营模式")
	private String managementModeName;
}
