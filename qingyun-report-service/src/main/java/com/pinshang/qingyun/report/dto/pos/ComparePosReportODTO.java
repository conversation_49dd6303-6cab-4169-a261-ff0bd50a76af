package com.pinshang.qingyun.report.dto.pos;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ComparePosReportODTO {
      private  Long paymentOrderCount;

      private  Long cashWaterOrderCount;

      private  Long saleWaterOrderCount;

      private  Long guestOrderCount;

      private  Long saleSummaryOrderCount;

      private  Long discountOrderCount;

      /** 收银流水日汇总 总金额 **/
      private BigDecimal cashierWaterDayAmount;

      /** 客单分析日汇总 总金额 **/
      private BigDecimal guestListSummaryDayAmount;

      /** 销售汇总日汇总 总金额 **/
      private BigDecimal salesSummaryDayAmount;
}
