package com.pinshang.qingyun.report.enums;

import lombok.Getter;

/**
 * @ClassName BreakageChannelEnum
 * <AUTHOR>
 * @Date 2021/10/26 19:02
 * @Description BreakageChannelEnum
 * @Version 1.0
 */
@Getter
public enum BreakageChannelEnum {
    /**
     * 报损渠道, 默认掌上门店PDA
     */
    PDA_CHANNEL(1, "掌上门店PDA"),
    POS_CHANNEL(2, "餐饮POS");

    private int code;
    private String desc;

    BreakageChannelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String getChannelEnum(int code){
        for(BreakageChannelEnum channelEnum : BreakageChannelEnum.values()){
            if(channelEnum.getCode() == code){
                return channelEnum.getDesc();
            }
        }
        return null;
    }
}
