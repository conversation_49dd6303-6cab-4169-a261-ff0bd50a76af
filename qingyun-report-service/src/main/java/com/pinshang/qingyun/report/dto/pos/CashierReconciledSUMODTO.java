package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CashierReconciledSUMODTO {
    @ApiModelProperty(position = 1, value = "金额")
    private String payAmount;

    @ApiModelProperty(position = 2, value = "销售金额")
    private String salesAmount;

    @ApiModelProperty(position = 3, value = "退款金额")
    private String refundAmount;

    @ApiModelProperty(position = 4, value = "交易金额")
    private String balanceAmount;

    @ApiModelProperty("销售笔数")
    private Long saleCount;
    @ApiModelProperty("退回笔数")
    private Long returnCount;

    /** 合计总金额 */
    private BigDecimal totalAmount;
    /** 现金小计金额 */
    private  BigDecimal cashTotalAmount;
    /** 银行卡小计金额 */
    private BigDecimal bankTotalAmount;
    /** OK卡小计金额 */
    private BigDecimal okTotalAmount;
    /** 记账(聚合)小计金额 */
    private BigDecimal chargeConvergeTotalAmount;
    /** 记账(索迪斯)小计金额 */
    private BigDecimal chargeSdsTotalAmount;
    /** 聚合支付小计金额 */
    private BigDecimal convergeTotalAmount;
    /** 索迪斯小计金额 */
    private BigDecimal sdsTotalAmount;
    /** 积分支付小计金额 */
    private BigDecimal jfTotalAmount;
    /** 抹零小计金额 */
    private BigDecimal moLinTotalAmount;

    /**
     * 代销总金额
     */
    private BigDecimal consignmentAmount;

    /**
     * 代销现金总金额
     */
    private BigDecimal consignmentCashAmount;

    /**
     * 代销聚合支付总金额
     */
    private BigDecimal consignmentAggregationAmount;

    /**
     * 聚合现金+售卡现金+现金
     */
    @ApiModelProperty("聚合现金+售卡现金+现金")
    private BigDecimal totalCashAmount;
}
