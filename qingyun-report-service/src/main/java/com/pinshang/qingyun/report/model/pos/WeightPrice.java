package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;


@Table(name = "t_pos_weight_price")
@Data
public class WeightPrice extends BaseSimplePO {

    /** 销售方式:  1 销售  2 退货 */
    private Integer saleType;

    /** 门店id */
    private Long  shopId;

    /** '订单号' */
    private String orderCode;

    /** '交易时间' */
    private Date saleTime;

    /**  */
    private Long commodityId;

    /** '称重码' */
    private String weightCode;

    /** 称重码价格 */
    private BigDecimal  weightCodePrice;

    /** 称重码金额 */
    private BigDecimal  weightCodeAmount;

    /** '数量' */
    private BigDecimal quantity;

    /** '成交单价' */
    private BigDecimal  transactionPrice;

    /** '成交金额' */
    private BigDecimal transactionAmount;

    /**  '后台零售价' */
    private BigDecimal  retailPrice;

    /** '后台特价' */
    private BigDecimal  promotionPrice;

}
