package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.shop.dto.ShopODTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CashierDailyMapper {

    /**
     * 日报
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);
    CashierDailySUMODTO selectSUMCashierDailyList(@Param("cashierDailyIDTO") CashierDailyIDTO cashierDailyIDTO);


    /**
     * 按门店日报
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyListByShop(@Param("cashierDailyShopIDTO") CashierDailyIDTO cashierDailyIDTO);
    CashierDailySUMODTO selectSUMCashierDailyListByShop(@Param("cashierDailyShopIDTO") CashierDailyIDTO cashierDailyIDTO);

    /**
     * 按日期
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyListByDate(@Param("cashierDailyDateIDTO") CashierDailyIDTO cashierDailyIDTO);
    CashierDailySUMODTO selectSUMCashierDailyListByDate(@Param("cashierDailyDateIDTO") CashierDailyIDTO cashierDailyIDTO);

    /**
     * 按付款方式
     * @param cashierDailyIDTO
     * @return
     */
    List<CashierDailyODTO> selectCashierDailyListByPayment(@Param("cashierDailyPaymentIDTO") CashierDailyIDTO cashierDailyIDTO);
    CashierDailySUMODTO selectSUMCashierDailyListByPayment(@Param("cashierDailyPaymentIDTO") CashierDailyIDTO cashierDailyIDTO);


    /**
     * 收银员对账
     * @param cashierReconciledIDTO
     * @return
     */
    List<CashierReconciledODTO> selectCashierReconciledList(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    CashierReconciledSUMODTO selectSUMCashierReconciledList(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    List<CashierWaterCountODTO> selectCashierWaterCount(@Param("cashierReconciledIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    /**
     * 收银员对账--日对账
     * @param cashierReconciledIDTO
     * @return
     */
    List<CashierReconciledODTO> selectDiurnalReconciliationList(@Param("diurnalReconciliationIDTO") CashierReconciledIDTO cashierReconciledIDTO);
    CashierReconciledSUMODTO selectSUMDiurnalReconciliationList(@Param("diurnalReconciliationIDTO") CashierReconciledIDTO cashierReconciledIDTO);

    List<ShopODTO> selectShopList();

    List<CashierODTO> selectCashierList(@Param("userIDTO") UserIDTO userIDTO);

}
