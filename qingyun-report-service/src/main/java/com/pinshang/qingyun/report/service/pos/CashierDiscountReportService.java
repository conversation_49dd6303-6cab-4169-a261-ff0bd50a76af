package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.pos.CashierDiscountReportOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.CashierDiscountReportOrderODTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.TotalCashierDiscountODTO;
import com.pinshang.qingyun.report.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.mapper.pos.CashierDiscountReportMapper;
import com.pinshang.qingyun.report.model.pos.CashierDiscountReport;
import com.pinshang.qingyun.report.service.BaseService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class CashierDiscountReportService extends BaseService<CashierDiscountReportMapper, CashierDiscountReport> {

    @Autowired
    private CashierDiscountReportMapper cashierDiscountReportMapper;

    @Autowired
    private ShopClient shopClient;

    /**
     * 收银员折让
     * @param idto
     * @return
     */
    public TablePageInfo<CashierDiscountReportOrderODTO> cashierDiscountReport(CashierDiscountReportOrderIDTO idto){
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, shopTypeList, idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList)|| (null != idto.getShopId() && !shopIdList.contains(idto.getShopId()))){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }
        PageInfo<CashierDiscountReportOrderODTO> pageDate = null;
        idto.setIsTotal(0);
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            cashierDiscountReportMapper.cashierDiscountReport(idto);
        });
        List<CashierDiscountReportOrderODTO> list=pageDate.getList();
        TotalCashierDiscountODTO total=new TotalCashierDiscountODTO();
        if(!CollectionUtils.isEmpty(list)){
            //设置总值
            CashierDiscountReportOrderODTO totalDto=getTotalcashierDiscountReport(idto);
            total.setTotalFromAmount(totalDto.getRealTotalAmount());
            total.setTotalToAmount(totalDto.getTotalAmount());
            total.setTotalDiscount(totalDto.getDiscountAmount());
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }
    public CashierDiscountReportOrderODTO getTotalcashierDiscountReport(CashierDiscountReportOrderIDTO idto){
        CashierDiscountReportOrderIDTO dto=new CashierDiscountReportOrderIDTO();
        BeanUtils.copyProperties(idto,dto);
        dto.setIsTotal(1);
        List<CashierDiscountReportOrderODTO> list=cashierDiscountReportMapper.cashierDiscountReport(dto);
        return list.get(0);
    }
}
