package com.pinshang.qingyun.report.controller;

import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.BreakageEnteringService;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.report.util.ReportUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 *
 */
@RestController
@RequestMapping("/hand/breakage")
public class HandBreakageController {

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private BreakageEnteringService breakageEntingService;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    /**
     *
     * 手持保存-根据条码查询商品基本信息
     * @param barcode
     * @return
     */
    @GetMapping(value = "/pda/commodities/{barcode}")
    @ApiOperation(value = "根据条码查询商品基本信息", notes = "pda根据条码查询商品基本信息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public CommodityInfoODTO handBreakageBarcode(@PathVariable("barcode") String barcode,
                                                 @RequestParam(value = "shopId", required = false) Long shopId,
                                                 @RequestParam(value = "stallId", required = false) Long stallId) {
        //校验档口权限
        ddTokenShopIdService.processReadDdTokenShopId(shopId, stallId);
        return commodityService.findCommodity(barcode, shopId, stallId);
    }

    /**
     *
     * 价签绑定-根据条码查询商品基本信息
     * @param barcode
     * @return
     */
    @GetMapping(value = "/commodities/{barcode}")
    @ApiOperation(value = "根据条码查询商品基本信息", notes = "根据条码查询商品基本信息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public CommodityInfoODTO priceTagBarcode(@PathVariable("barcode") String barcode){
        return commodityService.findCommodityForPriceTag(barcode);
    }

    /**
     * 获取报损原因 http://xxx:9050/v1/common/dictionary/listDictionaryByOptionName?optionName=breakageReason
     * @param idto
     */
    @PostMapping(value = "/entering")
    @ApiOperation(value = "报损录入", notes = "报损录入", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean entering(@RequestBody BreakageEnteringIDTO idto) throws Throwable {
        //校验档口权限
        ddTokenShopIdService.processDdTokenShopId(idto.getShopId(), idto.getStallId());
        breakageEntingService.saveBreakageEnting(idto);
        return Boolean.TRUE;
    }

    /**
     * 已报损商品
     * @return
     */
    @GetMapping(value = "/breakagedCommodityForPass24Hours")
    @ApiOperation(value = "已报损商品", notes = "已报损商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BreakagedCommodityListODTO breakagedCommodityForPass24Hours() {
        return breakageEntingService.breakagedCommodityForPass24Hours();
    }


    @MethodRender
    @PostMapping(value = "/breakageEnteringList")
    @ApiOperation(value = "查看报损记录", notes = "查看报损记录", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<BreakageEnteringODTO> breakageEnteringList(@RequestBody BreakageEnteringSearchIDTO idto) {
        //校验档口权限
        ddTokenShopIdService.processReadDdTokenShopId(idto.getShopId(), idto.getStallId());
        return breakageEntingService.breakageEnteringList(idto);
    }

    @ApiOperation(value = "查询报损记录-导出", notes = "查询报损记录-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/breakageEnteringList")
    public ModelAndView exportCategorySalesWeekPercent(BreakageEnteringSearchIDTO idto) {
        //校验档口权限
        ddTokenShopIdService.processReadDdTokenShopId(idto.getShopId(), idto.getStallId());
        return breakageEntingService.exportBreakageEnteringList(idto);
    }

    /**
     * 汇总报损记录
     */
    @ApiOperation(value = "汇总报损记录", notes = "汇总报损记录",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("breakageEnteringDay")
    public Boolean breakageEnteringDay(@RequestParam(value = "dateTime") String dateTime){
        return breakageEntingService.breakageEnteringDay(dateTime);
    }

    @PostMapping(value = "/breakageEnteringDayList")
    @ApiOperation(value = "查看报损记录(日汇总)", notes = "查看报损记录(日汇总)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TablePageInfo<BreakageEnteringDayODTO> breakageEnteringDayList(@RequestBody BreakageEnteringSearchDayIDTO idto) {
        return breakageEntingService.breakageEnteringDayList(idto);
    }


    @ApiOperation(value = "查询报损记录-导出(日汇总)", notes = "查询报损记录-导出(日汇总)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/exportBreakageEnteringDayList")
    public ModelAndView exportBreakageEnteringDayList(BreakageEnteringSearchDayIDTO idto) {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<BreakageEnteringDayODTO> pageInfo = breakageEntingService.breakageEnteringDayList(idto);
        List<BreakageEnteringDayODTO> list = pageInfo.getList();
        BreakageEnteringDayODTO header = (BreakageEnteringDayODTO)pageInfo.getHeader();
        if(header != null){
            header.setShopName("合计");
        }

        List<BreakageEnteringDayODTO> tables = new ArrayList<>();
        tables.add(header);
        tables.addAll(list);

        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(tables, data);

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.BREAKAGE_ENTERING_DAY, data, "");
    }

}
