package com.pinshang.qingyun.report.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.RepeatSubmitBusinessCode;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.constant.CommonConst;
import com.pinshang.qingyun.report.dto.*;
import com.pinshang.qingyun.report.dto.export.GuestListSummaryExportRespVo;
import com.pinshang.qingyun.report.dto.shop.ShopNegativeStockODTO;
import com.pinshang.qingyun.report.dto.shop.ShopNegativeStockQueryIDto;
import com.pinshang.qingyun.report.enums.CollectTypeEnum;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.etl.EtlExecute;
import com.pinshang.qingyun.report.model.ClearingCharges;
import com.pinshang.qingyun.report.model.ClearingChargesItem;
import com.pinshang.qingyun.report.service.ReportAsyncService;
import com.pinshang.qingyun.report.service.ReportService;
import com.pinshang.qingyun.report.util.ReportUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.*;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR> Zhang
 * @version 1.0
 * @since 2018/9/26
 */
@Slf4j
@RestController
@RequestMapping("/reports")
public class ReportController extends BaseController {

    @Autowired
    private ReportService reportService;

    @Autowired
    private EtlExecute etlExecute;
    @Autowired
    private ReportAsyncService reportAsyncService;

    @Autowired
    private IRenderService renderService;

    @ApiOperation(value = "类别销售周同比", notes = "类别销售周同比", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/categorySalesPercent")
    public TablePageInfo<CategorySalesWeekPercentODTO> categorySalesWeekPercent(CategorySalesWeekPercentIDTO idto) {

        return reportService.categorySalesWeekPercent(idto);
    }

    @ApiOperation(value = "类别销售周同比-导出", notes = "类别销售周同比-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/categorySalesPercent")
    @FileCacheQuery(bizCode = "POS_SR_CATEGORY")
    public void exportCategorySalesWeekPercent(CategorySalesWeekPercentIDTO idto, HttpServletResponse response) throws IOException {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<CategorySalesWeekPercentODTO> pageInfo = reportService.categorySalesWeekPercent(idto);
        List<CategorySalesWeekPercentODTO> list = pageInfo.getList();
        CategorySalesWeekPercentODTO header = (CategorySalesWeekPercentODTO)pageInfo.getHeader();
        header.setFirstKindName("合计");
        List<CategorySalesWeekPercentODTO> tables = new ArrayList<>();
        { //变更旧逻辑, 放置表头下一行
            CategorySalesWeekPercentODTO rowExcelDto = new CategorySalesWeekPercentODTO();
            String lastWeek = DateUtil.getDateFormate(DateUtil.addDay(DateUtil.parseDate(idto.getSalesDate(), CommonConst.yyyyMMdd), -7), CommonConst.yyyyMMdd);
            rowExcelDto.setFirstKindName(String.format("今日: %s    上周日期: %s", idto.getSalesDate(), lastWeek));
            tables.add(rowExcelDto);
        }
        tables.add(header);
        if (null != list) {
            tables.addAll(list);
        }

        String fileName = String.format("类别销售周同比报表_%s", new DateTime().toString(DatePattern.PURE_DATETIME_PATTERN));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        EasyExcel.write(response.getOutputStream(), CategorySalesWeekPercentODTO.class)
                .autoCloseStream(Boolean.TRUE).sheet("类别销售周同比报表").doWrite(tables);
        /* 已重构, 后续稳定后可删除
        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(tables, data);

        String lastWeek = DateUtil.getDateFormate(DateUtil.addDay(DateUtil.parseDate(idto.getSalesDate(), CommonConst.yyyyMMdd), -7), CommonConst.yyyyMMdd);
        String tableHeader = "今日:" + idto.getSalesDate() + "   " + "上周日期:" + lastWeek;

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.CATEGORY_SALES_PERCENT_REPORT, data, tableHeader);
        */
    }


    @ApiOperation(value = "门店销售周同比", notes = "门店销售周同比", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/shopSalesWeekPercent")
    public TablePageInfo<ShopSalesWeekODTO> shopSalesWeekPercent(ShopSalesWeekIDTO idto) {
        return reportService.shopSalesWeekPercent(idto);
    }

    @ApiOperation(value = "门店销售周同比-导出", notes = "门店销售周同比-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/shopSalesWeekPercent")
    @FileCacheQuery(bizCode = "POS_SR_SHOP_WEEK")
    public void shopSalesWeekPercentExport(ShopSalesWeekIDTO idto, HttpServletResponse response) throws IOException {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);

        TablePageInfo<ShopSalesWeekODTO> pageInfo = reportService.shopSalesWeekPercent(idto);
        List<ShopSalesWeekODTO> list = pageInfo.getList();
        List<ShopSalesWeekODTO> tables = new ArrayList<>();
        { //变更旧逻辑, 放置表头下一行
            ShopSalesWeekODTO rowExcelDto = new ShopSalesWeekODTO();
            rowExcelDto.setShopName(String.format("今日: %s    上周日期: %s", idto.getToday(), idto.getLastWeek()));
            tables.add(rowExcelDto);
        }
        ShopSalesWeekODTO header = (ShopSalesWeekODTO)pageInfo.getHeader();
        header.setShopName("合计");
        tables.add(header);
        if (null != list) {
            tables.addAll(list);
        }

        String fileName = String.format("门店销售周同比报表_%s", new DateTime().toString(DatePattern.PURE_DATETIME_PATTERN));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        EasyExcel.write(response.getOutputStream(), ShopSalesWeekODTO.class)
                .autoCloseStream(Boolean.TRUE).sheet("门店销售周同比报表").doWrite(tables);
        /* 已重构, 后续稳定后可删除代码
        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(tables, data);

        String tableHeader = "今日:" + idto.getToday() + "    " + "上周日期:" + idto.getLastWeek();

        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.SHOP_SALES_PERCENT_REPORT, data, tableHeader);
        */
    }

    @GetMapping("etlrun")
    public Boolean etlRun(@RequestParam(value = "timeStamp",required = true) String timeStamp,
                         @RequestParam(value = "fileName",required = false) String fileName
    ){
        etlExecute.execute(timeStamp,fileName);
        return true;
    }

    @ApiOperation(value = "收银月报", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("monthCashierReport")
    public PageInfo<ClearingCharges> monthCashierReport(ClearingChargesIDTO idto){
        idto.setPageSize(Integer.MAX_VALUE);
        return reportService.findMonthlyCashierReportList(idto);
    }

    @ApiOperation(value = "收银月报删除", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("monthCashierDeleteById/{id}")
    public boolean monthCashierDeleteById(@PathVariable("id") Long id){
        return reportService.deleteById(id);
    }

    @ApiOperation(value = "收银月报详细(导出)")
    @GetMapping("/export/monthCashierReportItem")
    public ModelAndView exportMonthCashierReportItem(ClearingChargesItemIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<ClearingChargesItem> pageInfo = reportService.monthCashierReportItem(idto);
        List<ClearingChargesItem> list = pageInfo.getList();
        List<ClearingChargesItemExportODTO> odtoList = new ArrayList<>(list.size());
        list.forEach(l -> {
            ClearingChargesItemExportODTO clearingChargesItemExportODTO = new ClearingChargesItemExportODTO();
            BeanUtils.copyProperties(l,clearingChargesItemExportODTO);
            odtoList.add(clearingChargesItemExportODTO);
        });

        ClearingChargesItem header = (ClearingChargesItem)pageInfo.getHeader();
        List<ClearingChargesItemExportODTO> tables = new ArrayList<>();
        ClearingChargesItemExportODTO odtoHeader = new ClearingChargesItemExportODTO();
        BeanUtils.copyProperties(header,odtoHeader);
        tables.add(odtoHeader);
        tables.addAll(odtoList);
        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(tables, data);
        String tableHeader = "";
        if(CollectionUtils.isNotEmpty(list)){
            tableHeader = "日期:" + DateFormatUtils.format(list.get(0).getImportDate(),"yyyy-MM-dd");
        }
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.MONTH_CASHIER_REPORT, data, tableHeader);
    }


    @ApiOperation(value = "收银月报详细")
    @GetMapping("monthCashierReportItem")
    public TablePageInfo<ClearingChargesItem> monthCashierReportItem(ClearingChargesItemIDTO idto){
        idto.setPageSize(Integer.MAX_VALUE);
        return reportService.monthCashierReportItem(idto);
    }


    @ApiOperation(value = "收银月报导入")
    @PostMapping("import/monthlyCashierReport")
    public void importMonthlyCashierReport(@RequestParam("userId") Long userId, @RequestParam("userName") String userName,@RequestBody List<ClearingChargesItem> chargesItems) {
        reportService.importMonthlyCashierReport(userId, userName, chargesItems);
        return;
    }

    @ApiOperation(value = "门店实收汇总表")
    @GetMapping("shopActualSummary")
    public TablePageInfo<ShopActualSummaryODTO> shopActualSummary(ShopActualSummaryIDTO idto) {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        return reportService.shopActualSummary(idto);
    }

    @ApiOperation(value = "门店营业款对账明细")
    @GetMapping("reconciliationOfRurnover")
    public TablePageInfo<ReconciliationOfRurnoverODTO> reconciliationOfRurnover(ClearingChargesItemIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        return reportService.reconciliationOfRurnover(idto);
    }


    @ApiOperation(value = "门店营业款对账明细(导出)")
    @GetMapping("/export/reconciliationOfRurnover")
    public ModelAndView exportReconciliationOfRurnover(ClearingChargesItemIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);TablePageInfo<ReconciliationOfRurnoverODTO> pageInfo = reportService.reconciliationOfRurnover(idto);
        List<ReconciliationOfRurnoverODTO> list = pageInfo.getList();
        ReconciliationOfRurnoverODTO header = (ReconciliationOfRurnoverODTO) pageInfo.getHeader();
        Map<String, List<String>> data = new HashMap<>();
        list.add(0, header);
        ReportUtil.buildData(list, data,4,"yyyy-MM-dd");
        List<String> key4 = data.get("key_4");
        if(CollectionUtils.isNotEmpty(key4)){
            key4.set(0,"合计");
        }
        data.put("key_3", Arrays.asList("","销售金额","抹零","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售额合计","抹零合计","手续费合计","实收合计","差异合计"));
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.RECONCILIATION_OF_RURNOVER, data);
    }



    @ApiOperation(value = "门店营业款对账总表")
    @GetMapping("reconciliationStatement")
    public TablePageInfo<ReconciliationStatementODTO> reconciliationStatement(ClearingChargesItemIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        return reportService.reconciliationStatement(idto);
    }

    @ApiOperation(value = "门店营业款对账总表(导出)")
    @GetMapping("/export/reconciliationStatement")
    public ModelAndView exportReconciliationStatement(ClearingChargesItemIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<ReconciliationStatementODTO> pageInfo = reportService.reconciliationStatement(idto);
        List<ReconciliationStatementODTO> list = pageInfo.getList();
        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(list, data,4,"yyyy-MM-dd");
        data.put("key_3", Arrays.asList("","销售金额","抹零","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售金额","手续费","实收金额","差异","销售额合计","抹零合计","手续费合计","实收合计","差异合计"));
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.EXPORT_RECONCILIATION_STATEMENT, data);
    }




    @ApiOperation(value = "门店实收汇总表(导出)")
    @GetMapping("/export/shopActualSummary")
    public ModelAndView exportShopActualSummary(ShopActualSummaryIDTO idto){
        TablePageInfo<ShopActualSummaryODTO> pageInfo = reportService.shopActualSummary(idto);
        List<ShopActualSummaryODTO> list = pageInfo.getList();
        ShopActualSummaryODTO header = (ShopActualSummaryODTO) pageInfo.getHeader();
        header.setShopName("合计");

        Map<String, List<String>> data= new HashMap<>();
        list.add(0, header);
        ReportUtil.buildData(list, data);
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.SHOP_ACTUAL_SUMMARY, data);
    }


    @ApiOperation(value = "滞销品报表", notes = "滞销品报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/unsalableReport")
    public PageInfo<UnsalableODTO> unsalableReport(@RequestBody UnsalableIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(tokenInfo.getShopId() != null){
            idto.setShopId(tokenInfo.getShopId());
        }
        return reportService.unsalableReport(idto);
    }

    @ApiOperation(value = "滞销品报表", notes = "滞销品报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/unsalableReport")
    @FileCacheQuery(bizCode = "UNSALABLE_REPOR")
    public void unsalableReportExport(UnsalableIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(tokenInfo.getShopId() != null){
            idto.setShopId(tokenInfo.getShopId());
        }
        PageInfo<UnsalableODTO> result = reportService.unsalableReport(idto);
        List<UnsalableODTO> dataList = result.getList();
        if(null == dataList){
            dataList = new ArrayList<>();
        }
        try {
            ExcelUtil.setFileNameAndHead( response,"滞销品报表");
            EasyExcel.write(response.getOutputStream(), UnsalableODTO.class).autoCloseStream(Boolean.FALSE).sheet("滞销品报表")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("滞销品报表--导出异常",e);
        }
    }

    @ApiOperation(value = "畅销品月度汇总表", notes = "畅销品月度汇总表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/bestsellerGoodMonth")
    public Boolean bestsellerGoodMonth(@RequestParam("beginTime") String beginTime,@RequestParam("endTime") String endTime) {
        reportService.bestsellerGoodMonth(beginTime,endTime);
        return true;
    }

    @ApiOperation(value = "畅销品月度缺货报表", notes = "畅销品月度缺货报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/bestsellerGoodMonthReport")
    public TablePageInfo<BestsellerGoodODTO> bestsellerGoodMonthReport(BestsellerGoodIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(tokenInfo.getShopId() != null){
            idto.setShopId(tokenInfo.getShopId());
        }
        return reportService.bestsellerGoodMonthReport(idto);
    }


    @ApiOperation(value = "导入混合收款")
    @PostMapping("import/importBlendCollectReport")
    public Object importBlendCollectReport(@RequestBody List<BlendCollectIDTO> collectReportList) {
        return reportService.importBlendCollectReport(collectReportList);
    }

    /**
     * 混合收款(分页)
     * @param ito
     * @return
     */
    @ApiOperation(value = "混合收款(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("blendCollectPage")
    public TablePageInfo<BlendCollectODTO> blendCollectPage(BlendCollectPageIDTO ito){
        return reportService.blendCollectPage(ito);
    }

    /**
     * 支付宝聚合、微信聚合、云闪付聚合
     * 聚合区分表(分页)
     * @param ito
     * @return
     */
    @ApiOperation(value = "聚合区分表(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("blendCollectConvergePage")
    public TablePageInfo<BlendCollectODTO> blendCollectConvergePage(BlendCollectPageIDTO ito){
        ito.setCollectType(CollectTypeEnum.CONVERGE.getCode());
        return reportService.blendCollectConvergeCashPage(ito);
    }

    /**
     * 现金区分表(分页)
     * @param ito
     * @return
     */
    @ApiOperation(value = "现金区分表(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("blendCollectCashPage")
    public TablePageInfo<BlendCollectODTO> blendCollectCashPage(BlendCollectPageIDTO ito){
        ito.setCollectType(CollectTypeEnum.MONEY.getCode());
        return reportService.blendCollectConvergeCashPage(ito);
    }
    /**
     * 对账异常详情(分页)
     * @param ito
     * @return
     */
    @ApiOperation(value = "对账异常详情(分页)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("checkingExceptionPage")
    public PageInfo<CheckingExceptionODTO> checkingExceptionPage(CheckingExceptionPageIDTO ito){

        return      reportService.checkingExceptionPage(ito);
    }


    /**
     * 及时达负库存报表汇总
     * @return
     */
    @ApiOperation(value = "及时达负库存报表汇总", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("shopNegativeStockReportSummary")
    public Boolean shopNegativeStockReportSummary(){
        return  reportAsyncService.shopNegativeStockReportSummary();
    }

    @ApiOperation(value = "及时达负库存监控报表", notes = "及时达负库存监控报表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/shopNegativeStockReport")
    @MethodRender
    public PageInfo<ShopNegativeStockODTO> shopNegativeStockReport(@RequestBody ShopNegativeStockQueryIDto idto) {
        return reportService.shopNegativeStockReport(idto);
    }

    @ApiOperation(value = "及时达负库存监控报表-导出", notes = "及时达负库存监控报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/shopNegativeStockReport")
    @RepeatSubmitAnno(value = RepeatSubmitBusinessCode.COMMODITY_EXPORT,expireTime = 10)
    public void exportShopNegativeStockReport(ShopNegativeStockQueryIDto dto, HttpServletResponse response) throws IOException {
        dto.initExportPage();

        PageInfo<ShopNegativeStockODTO> page = reportService.shopNegativeStockReport(dto);
        List<ShopNegativeStockODTO> dataList = new ArrayList<>();
        renderService.render(page.getList(), "/export/shopNegativeStockReport");
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"品上生活负库存监控");
            EasyExcel.write(response.getOutputStream(), ShopNegativeStockODTO.class).autoCloseStream(Boolean.FALSE).sheet("品上生活负库存监控")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("及时达负库存监控--导出异常",e);
        }
    }

    @ApiOperation(value = "品类销售周同比", notes = "品类销售周同比", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/categoryAllSalesWeekPercent")
    public TablePageInfo<CategoryAllSalesPercentODTO> categoryAllSalesWeekPercent(@RequestBody CategoryAllSalesPercentIDTO idto) {
        return reportService.categoryAllSalesWeekPercent(idto);
    }

    @ApiOperation(value = "品类销售月同比", notes = "品类销售月同比", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/categoryAllSalesMonthPercent")
    public TablePageInfo<CategoryAllSalesPercentODTO> categoryAllSalesMonthPercent(@RequestBody CategoryAllSalesPercentIDTO idto) throws ParseException {
        return reportService.categoryAllSalesMonthPercent(idto);
    }


    @ApiOperation(value = "品类销售周同比-导出", notes = "品类销售周同比-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/categoryAllSalesWeekPercent/export")
    public ModelAndView categoryAllSalesWeekPercentExport(CategoryAllSalesPercentIDTO idto) {
        idto.initExportPage();
        TablePageInfo<CategoryAllSalesPercentODTO> pageInfo = reportService.categoryAllSalesWeekPercent(idto);
        List<CategoryAllSalesPercentODTO> list = pageInfo.getList();
        CategoryAllSalesPercentODTO header = (CategoryAllSalesPercentODTO)pageInfo.getHeader();
        if(null == header){
            header = new CategoryAllSalesPercentODTO();
        }
        header.setFirstKindName("合计");
        CategoryAllSalesPercentODTO headerLine = BeanCloneUtils.copyTo(header, CategoryAllSalesPercentODTO.class);
        List<CategoryAllSalesPercentODTO> tables = new ArrayList<>();
        tables.add(headerLine);
        if(SpringUtil.isNotEmpty(list)) {
            tables.addAll(list);
        }

        Map<String, List<String>> dataList = new HashMap<>();
        int i = 1;
        List<String> dataLst;
        for(CategoryAllSalesPercentODTO dto:tables){
            dataLst = new ArrayList<>();
            dataLst.add(dto.getFirstKindName());
            dataLst.add(null == dto.getThisSalesMoneyOffLine() ? "" : dto.getThisSalesMoneyOffLine().toString());
            dataLst.add(null == dto.getThisSalesMoneyOnLine() ? "" :dto.getThisSalesMoneyOnLine().toString());
            dataLst.add(null == dto.getLastSalesMoneyOffLine() ? "" : dto.getLastSalesMoneyOffLine().toString());
            dataLst.add(null == dto.getLastSalesMoneyOnLine() ? "" : dto.getLastSalesMoneyOnLine().toString());
            dataLst.add(null == dto.getSalesMoneyDiff() ? "" :dto.getSalesMoneyDiff().toString());
            dataLst.add(dto.getCategorySalesPercentStr());
            dataLst.add(dto.getSalesMoneyPercentStr());
            dataList.put("key_"+ i++, dataLst);
        }
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.CATEGORY_ALL_SALES_WEEK_PERCENT, dataList);
    }


    @ApiOperation(value = "品类销售月同比-导出", notes = "品类销售月同比-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/categoryAllSalesMonthPercent/export")
    public ModelAndView categoryAllSalesMonthPercentExport(CategoryAllSalesPercentIDTO idto) throws ParseException {
        idto.initExportPage();
        TablePageInfo<CategoryAllSalesPercentODTO> pageInfo = reportService.categoryAllSalesMonthPercent(idto);
        List<CategoryAllSalesPercentODTO> list = pageInfo.getList();
        CategoryAllSalesPercentODTO header = (CategoryAllSalesPercentODTO)pageInfo.getHeader();
        if(null == header){
            header = new CategoryAllSalesPercentODTO();
        }
        header.setFirstKindName("合计");
        CategoryAllSalesPercentODTO headerLine = new CategoryAllSalesPercentODTO();
        BeanUtils.copyProperties(header, headerLine);
        List<CategoryAllSalesPercentODTO> tables = new ArrayList<>();
        tables.add(headerLine);
        if(SpringUtil.isNotEmpty(list)) {
            tables.addAll(list);
        }
        Map<String, List<String>> dataList = new HashMap<>();
        int i = 1;
        List<String> dataLst;
        for(CategoryAllSalesPercentODTO dto:tables){
            dataLst = new ArrayList<>();
            dataLst.add(dto.getFirstKindName());
            dataLst.add(null == dto.getThisSalesMoneyOffLine() ? "" : dto.getThisSalesMoneyOffLine().toString());
            dataLst.add(null == dto.getThisSalesMoneyOnLine() ? "" :dto.getThisSalesMoneyOnLine().toString());
            dataLst.add(null == dto.getLastSalesMoneyOffLine() ? "" : dto.getLastSalesMoneyOffLine().toString());
            dataLst.add(null == dto.getLastSalesMoneyOnLine() ? "" : dto.getLastSalesMoneyOnLine().toString());
            dataLst.add(null == dto.getSalesMoneyDiff() ? "" :dto.getSalesMoneyDiff().toString());
            dataLst.add(dto.getCategorySalesPercentStr());
            dataLst.add(dto.getSalesMoneyPercentStr());
            dataList.put("key_"+ i++, dataLst);
        }
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.CATEGORY_ALL_SALES_MONTH_PERCENT, dataList);
    }
}
