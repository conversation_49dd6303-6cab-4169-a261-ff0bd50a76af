package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.pos.PosTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.client.report.service.PosAdminClient;
import com.pinshang.qingyun.report.dto.OrderCancelODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.OrderDeleteTypeEnum;
import com.pinshang.qingyun.report.mapper.CommodityMapper;
import com.pinshang.qingyun.report.mapper.EmployeeUserMapper;
import com.pinshang.qingyun.report.mapper.pos.CommodityDeleteMapper;
import com.pinshang.qingyun.report.mapper.pos.OrderDeleteItemMapper;
import com.pinshang.qingyun.report.mapper.pos.OrderDeleteMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.Commodity;
import com.pinshang.qingyun.report.model.EmployeeUser;
import com.pinshang.qingyun.report.model.pos.CommodityDelete;
import com.pinshang.qingyun.report.model.pos.OrderDelete;
import com.pinshang.qingyun.report.model.pos.OrderDeleteItem;
import com.pinshang.qingyun.report.model.shop.Shop;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.PosIdWorker;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.report.dto.ShopODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderDeleteService {

    @Autowired
    private OrderDeleteMapper orderDeleteMapper;

    @Autowired
    private PosAdminClient posAdminClient;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private EmployeeUserMapper employeeUserMapper;

    @Autowired
    private CommodityDeleteMapper commodityDeleteMapper;

    @Autowired
    private OrderDeleteItemMapper orderDeleteItemMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    /**
     * 订单作废记录(分页)
     * @param idto
     * @return
     */
    public TablePageInfo<OrderDeleteODTO> listOrderDelete(OrderDeleteIDTO idto){
        Assert.isTrue(StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate()), "操作日期不能为空！");
        idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
        idto.setEndDate(idto.getEndDate()+ " 23:59:59");

        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if (idto.getShopId() == null) {
            Assert.isTrue(diff <= 3, "操作日期的跨度不能超过3天");
            idto.setShopIdList(shopIdList);
        } else {
            Assert.isTrue(diff <= 31, "操作日期的跨度不能超过31天");
            if(!shopIdList.contains(idto.getShopId())){
                return new TablePageInfo<>();
            }
        }

        PageInfo<OrderDeleteODTO> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderDeleteMapper.listOrderDelete(idto);
        });
        List<OrderDeleteODTO> list=pageDate.getList();
        OrderDeleteODTO total = new OrderDeleteODTO();
        if(!CollectionUtils.isEmpty(list)){
            total = orderDeleteMapper.querySumOrderDelete(idto);

            List<Long> shopIds = pageDate.getList().stream().map(OrderDeleteODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));
            pageDate.getList().forEach(e -> {
                e.setStoreCode(shopMap.get(e.getShopId()));
            });
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }

    /**
     * 订单作废报表(分页)
     * @param idto
     * @return
     */
    public TablePageInfo<OrderCancelODTO> listOrderCancelPage(OrderCancelIDTO idto){
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        if(idto.getShopId() != null ){
            if(!shopIdList.contains(idto.getShopId())){
                TablePageInfo info=new TablePageInfo();
                info.setList(null);
                return info;
            }
            List<Long> shopIds = new ArrayList<>();
            shopIds.add(idto.getShopId());
            idto.setShopIdList(shopIds);
        }else{

            idto.setShopIdList(shopIdList);
        }
        if(!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }
        if(idto.getCasherId() == null){
            Assert.isTrue(StringUtils.isNotBlank(idto.getBeginDate()) && StringUtils.isNotBlank(idto.getEndDate()), "请选择收银日期");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
            Assert.isTrue(diff <= 91, "收银日期的跨度不能超过92天");
        }
        // 获取零售设置
        RetailSettingODTO retailSettingODTO = posAdminClient.getRetailSettingODTO();
        if(null != retailSettingODTO && retailSettingODTO.getCancelMorethanException() != null){
            idto.setCancelMorethanException(retailSettingODTO.getCancelMorethanException());
        }
        PageInfo<OrderCancelODTO> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderDeleteMapper.listOrderCancel(idto);
        });
        List<OrderCancelODTO> list=pageDate.getList();
        OrderCancelODTO total = new OrderCancelODTO();
        if(!CollectionUtils.isEmpty(list)){
            total = orderDeleteMapper.querySumOrderCancel(idto);

            List<Long> shopIds = pageDate.getList().stream().map(OrderCancelODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));
            pageDate.getList().forEach(e -> {
                e.setStoreCode(shopMap.get(e.getShopId()));
            });
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }

    public OrderDeleteDetailODTO orderDeleteDetail(String orderCode) {
        return orderDeleteMapper.orderDeleteDetail(orderCode);
    }

    /**
     * 保存单品删除
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCommodityDelete(CommodityDeleteMessage vo) {
        CommodityDelete commodityDelete = new CommodityDelete();
        BeanUtils.copyProperties(vo,commodityDelete);

        commodityDelete.setId(vo.getId());
        commodityDelete.setRefId(vo.getId());
        commodityDelete.setMacCode(vo.getPosMacCode());
        commodityDelete.setCommodityPrice(vo.getPrice());
        commodityDelete.setOperateTime(vo.getDate());
        commodityDelete.setCreateTime(new Date());
        commodityDelete.setOrderCode(vo.getOrderCode());
        commodityDelete.setAuthorizerCode(vo.getAuthorizerCode());
        commodityDelete.setAuthorizerName(vo.getAuthorizerName());

        commodityDelete.setPosMacId(vo.getPosMacId());
        commodityDelete.setPosType(null == vo.getPosType() ? PosTypeEnum.CASHIER_POS.getCode() : vo.getPosType());
        //查询商品
        Commodity commodity = commodityMapper.getCommodityByComodityId(vo.getCommodityId());
        if(null != commodity){
            BeanUtils.copyProperties(commodity,commodityDelete);
            commodityDelete.setCommodityUnit(commodity.getCommodityUnitName());
        }

        //收银员信息
        if (null != vo.getPosType() && vo.getPosType() == PosTypeEnum.SELF_POS.getCode()) {
            MacODTO macODTO = posAdminClient.getMacById(vo.getEmployeeId());
            commodityDelete.setCasherId(macODTO.getId());
            commodityDelete.setCasherCode(macODTO.getMacCode());
            commodityDelete.setCasherName(macODTO.getMacName());
        } else {
            EmployeeUser employeeUser =  employeeUserMapper.getEmployeeUserByUserId(vo.getEmployeeId());
            commodityDelete.setCasherId(employeeUser.getUserId());
            commodityDelete.setCasherCode(employeeUser.getEmployeeCode());
            commodityDelete.setCasherName(employeeUser.getEmployeeName());
        }

        commodityDeleteMapper.insert(commodityDelete);
    }

    /**
     * 保存订单作废记录
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderDelete(OrderDeleteMessage vo) {
        //新增主信息
        OrderDelete orderDelete = new OrderDelete();
        Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
        if(null != shop){
            orderDelete.setShopId(shop.getId());
            orderDelete.setShopCode(shop.getShopCode());
            orderDelete.setShopName(shop.getShopName());
        }
        orderDelete.setMacCode(vo.getPosMacCode());
        orderDelete.setOrderCode(vo.getOrderCode());
        orderDelete.setQuantity(vo.getTotalQuantity());
        orderDelete.setTotalAmount(vo.getTotalAmount());

        orderDelete.setOperateType(vo.getDeleteType()/10);
        orderDelete.setAuthorizerCode(vo.getAuthorizerCode());
        orderDelete.setAuthorizerName(vo.getAuthorizerName());

        orderDelete.setPosMacId(vo.getPosMacId());
        orderDelete.setPosType(null == vo.getPosType()? PosTypeEnum.CASHIER_POS.getCode() : vo.getPosType());
        // 挂单删除  、   挂单未结算
        if(OrderDeleteTypeEnum.SUSPEND.getCode().equals(vo.getDeleteType()) || OrderDeleteTypeEnum.AUTOMATIC.getCode().equals(vo.getDeleteType())){

            if (null != vo.getPosType() && vo.getPosType() == PosTypeEnum.SELF_POS.getCode()) {
                MacODTO macODTO = posAdminClient.getMacById(vo.getSuspendId());
                orderDelete.setOrderRestTime(vo.getSuspendDate());
                orderDelete.setOrderRestCasherId(macODTO.getId());
                orderDelete.setOrderRestCasherCode(macODTO.getMacCode());
                orderDelete.setOrderRestCasherName(macODTO.getMacName());

                orderDelete.setOperateId(macODTO.getId());
                orderDelete.setOperateCode(macODTO.getMacCode());
                orderDelete.setOperateName(macODTO.getMacName());
                orderDelete.setOperateDate(vo.getSuspendDate());
                orderDelete.setOperateTime(vo.getSuspendDate());
            } else {
                EmployeeUser employeeUser =  employeeUserMapper.getEmployeeUserByUserId(vo.getSuspendId());
                orderDelete.setOrderRestTime(vo.getSuspendDate());
                orderDelete.setOrderRestCasherId(employeeUser.getUserId());
                orderDelete.setOrderRestCasherCode(employeeUser.getEmployeeCode());
                orderDelete.setOrderRestCasherName(employeeUser.getEmployeeName());

                orderDelete.setOperateId(employeeUser.getUserId());
                orderDelete.setOperateCode(employeeUser.getEmployeeCode());
                orderDelete.setOperateName(employeeUser.getEmployeeName());
                orderDelete.setOperateDate(vo.getSuspendDate());
                orderDelete.setOperateTime(vo.getSuspendDate());
            }
        }
        // 挂单删除
        if(OrderDeleteTypeEnum.SUSPEND.getCode().equals(vo.getDeleteType())){
            EmployeeUser employeeUser =  employeeUserMapper.getEmployeeUserByUserId(vo.getEmployeeId());
            orderDelete.setOrderDeleteTime(vo.getDeleteDate());
            orderDelete.setOrderDeleteCasherId(employeeUser.getUserId());
            orderDelete.setOrderDeleteCasherCode(employeeUser.getEmployeeCode());
            orderDelete.setOrderDeleteCasherName(employeeUser.getEmployeeName());
        }
        // 挂单未结算
        if(OrderDeleteTypeEnum.AUTOMATIC.getCode().equals(vo.getDeleteType())){
            orderDelete.setOrderDeleteTime(vo.getDeleteDate());
            orderDelete.setOrderDeleteCasherId(1L);
            orderDelete.setOrderDeleteCasherCode("系统");
            orderDelete.setOrderDeleteCasherName("系统");
        }
        // 整单取消
        if(OrderDeleteTypeEnum.HAND_DRIVE.getCode().equals(vo.getDeleteType())){
            EmployeeUser employeeUser =  employeeUserMapper.getEmployeeUserByUserId(vo.getEmployeeId());
            orderDelete.setOrderDeleteTime(vo.getDeleteDate());
            if (null != vo.getPosType() && vo.getPosType() == PosTypeEnum.SELF_POS.getCode()) {
                MacODTO macODTO = posAdminClient.getMacById(vo.getEmployeeId());
                orderDelete.setOrderDeleteCasherId(macODTO.getId());
                orderDelete.setOrderDeleteCasherCode(macODTO.getMacCode());
                orderDelete.setOrderDeleteCasherName(macODTO.getMacName());

                orderDelete.setOperateId(macODTO.getId());
                orderDelete.setOperateCode(macODTO.getMacCode());
                orderDelete.setOperateName(macODTO.getMacName());
            } else {
                orderDelete.setOrderDeleteCasherId(employeeUser.getUserId());
                orderDelete.setOrderDeleteCasherCode(employeeUser.getEmployeeCode());
                orderDelete.setOrderDeleteCasherName(employeeUser.getEmployeeName());

                orderDelete.setOperateId(employeeUser.getUserId());
                orderDelete.setOperateCode(employeeUser.getEmployeeCode());
                orderDelete.setOperateName(employeeUser.getEmployeeName());
            }

            orderDelete.setOperateDate(vo.getDeleteDate());
            orderDelete.setOperateTime(vo.getDeleteDate());
        }
        orderDelete.setId(PosIdWorker.getId());
        orderDelete.setRefId(vo.getId());
        orderDelete.setCreateId(1L);
        orderDelete.setCreateTime(new Date());
        orderDelete.setUpdateId(1L);
        orderDelete.setUpdateTime(new Date());
        orderDeleteMapper.insert(orderDelete);

        //新增明细
        List<OrderDeleteItem> items = new ArrayList<>();
        if(!CollectionUtils.isEmpty(vo.getOrderItems())){
            for(OrderItem item:vo.getOrderItems()){
                OrderDeleteItem orderDeleteItem = new OrderDeleteItem();
                Commodity commodity = commodityMapper.getCommodityByComodityId(item.getCommodityId());
                if(null != commodity){
                    BeanUtils.copyProperties(commodity,orderDeleteItem);
                    orderDeleteItem.setCommodityUnit(commodity.getCommodityUnitName());
                }

                orderDeleteItem.setCommodityId(item.getCommodityId());
                orderDeleteItem.setQuanty(item.getQuantity());
                orderDeleteItem.setPrice(item.getPrice());
                orderDeleteItem.setSalePrice(item.getSalePrice());

                orderDeleteItem.setId(PosIdWorker.getId());
                orderDeleteItem.setOrderDeleteId(orderDelete.getId());
                orderDeleteItem.setCreateId(1L);
                orderDeleteItem.setCreateTime(new Date());
                orderDeleteItem.setUpdateId(1L);
                orderDeleteItem.setUpdateTime(new Date());
                items.add(orderDeleteItem);
            }
        }
        if(!CollectionUtils.isEmpty(items)){
            orderDeleteItemMapper.insertList(items);
        }
    }
}
