package com.pinshang.qingyun.report.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.mdCheck.DayOrderReportODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckGroupODTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportIDTO;
import com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportODTO;
import com.pinshang.qingyun.report.mapper.ReportMapper;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.shop.dto.mdCheck.MdCheckReportClientIDTO;
import com.pinshang.qingyun.shop.dto.mdCheck.MdCheckReportClientODTO;
import com.pinshang.qingyun.shop.service.mdCheck.MdCheckReportClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: sk
 * @Date: 2020/11/14
 */
@Slf4j
@Service
public class MdCheckService {

    @Autowired
    private ReportMapper reportMapper;

    @Autowired
    private MdCheckReportClient mdCheckReportClient;

    /**
     * 查询考核信息
     * @return
     */
    public PageInfo<MdCheckReportODTO> getMdCheckPlanPage(MdCheckReportIDTO idto) {
        QYAssert.isTrue(!StringUtil.isBlank(idto.getBeginDate()), "考核日期不能为空");
        QYAssert.isTrue(!StringUtil.isBlank(idto.getEndDate()), "考核日期不能为空");

        MdCheckReportClientIDTO mdCheckReportClientIDTO = new MdCheckReportClientIDTO();
        BeanUtils.copyProperties(idto,mdCheckReportClientIDTO);
        PageInfo<MdCheckReportClientODTO> pageInfoClient = mdCheckReportClient.getMdCheckPlanPage(mdCheckReportClientIDTO);

        PageInfo<MdCheckReportODTO> page = BeanUtil.copyProperties(pageInfoClient, MdCheckReportODTO.class);
        List<MdCheckReportODTO> list = page.getList();
        if(CollectionUtils.isNotEmpty(list)){
            Map<String,DayOrderReportODTO> reportMap = getDayOrderReportList(idto.getBeginDate(),idto.getEndDate());

            // 考核信息转换计算
            getRealCompleteList(reportMap,list,idto.getBeginDate(),idto.getEndDate());
        }
        return page;
    }
    /**
     * 获取短交报表map数据(key: orderTime + shopId + commodity_id)
     * @param beginDate 查询开始日期
     * @param endDate   查询结束日期
     * @return
     */
    public Map<String,DayOrderReportODTO> getDayOrderReportList(String beginDate,String endDate){
        Map<String,DayOrderReportODTO> reportMap = new HashMap<>();
        List<DayOrderReportODTO> list = reportMapper.getMdCheckCommodityList(beginDate,endDate);
        if(CollectionUtils.isNotEmpty(list)){
            for(DayOrderReportODTO reportODTO : list){
                String key = reportODTO.getOrderTime() + "" + reportODTO.getShopId() + "" + reportODTO.getCommodityId();
                reportMap.put(key,reportODTO);
            }
        }
        return reportMap;
    }

    /**
     * 批量
     * @param reportMap
     * @param checkList
     * @param beginDate
     * @param endDate
     * @return
     */
    public List<MdCheckReportODTO> getRealCompleteList(Map<String,DayOrderReportODTO> reportMap,List<MdCheckReportODTO> checkList,String beginDate,String endDate){
        List<MdCheckReportODTO> mdCheckReportList = new ArrayList<>();
        for(MdCheckReportODTO check : checkList){
            getRealComplete(reportMap,check,beginDate,endDate);
            mdCheckReportList.add(check);
        }
        return mdCheckReportList;
    }
    /**
     *
     * @param reportMap 短交数据
     * @param beginDate 查询开始日期
     * @param endDate  查询结束日期
     * @return
     */
    public void getRealComplete(Map<String,DayOrderReportODTO> reportMap,MdCheckReportODTO check,String beginDate,String endDate){

        // 获取考核日期和查询日期的交集
        List<String> joinDateList = DateUtils.getDateRangeCoincidence(check.getPlanBeginDate(),check.getPlanEndDate(),beginDate,endDate);

        // 设置 实际日期
        check.setRealBeginDate(joinDateList.get(0));
        check.setRealEndDate(joinDateList.get(joinDateList.size()-1));

        // 考核方案里面考核日期总天数
        Long planDay = DateUtils.getDiffDay(check.getPlanBeginDate(),check.getPlanEndDate());

        // 日均考核目标
        check.setDayPlanAim(check.getTotalPlanAim().divide(new BigDecimal(planDay + ""),2,BigDecimal.ROUND_HALF_UP));
        // 期间考核目标 (总考核目标/总考核天数*实际查询天数)
        BigDecimal planTotal = check.getTotalPlanAim().multiply(new BigDecimal(joinDateList.size())).divide(new BigDecimal(planDay + ""),2,BigDecimal.ROUND_HALF_UP);

        // 期间实际完成 (实际日期范围内，对应的送货日期内，考核商品组包含商品的实际发货数量或实际发货金额的合计)
        BigDecimal realTotal = BigDecimal.ZERO;
        for(String commodityId : check.getCheckCommodityList()){
            for(String date : joinDateList){
                String key = date + check.getShopId() + commodityId;
                DayOrderReportODTO dayOrderReportODTO = reportMap.get(key);
                if(dayOrderReportODTO != null){
                    realTotal = realTotal.add(check.getType() == 0 ? dayOrderReportODTO.getDeliveryTotalAmount() : dayOrderReportODTO.getDeliveryQuantity());
                }
            }
        }

        check.setDurationPlanAim(planTotal);
        check.setCompleteAim(realTotal);
        // 期间完成率=期间实际完成/期间考核目标
        if(null != planTotal && planTotal.compareTo(BigDecimal.ZERO) > 0){
            check.setCompleteAimPercent(check.getCompleteAim().multiply(new BigDecimal(100)).divide(check.getDurationPlanAim(),2,BigDecimal.ROUND_HALF_UP) + "%");
        }
    }

    /**
     * 当前门店未达成的订货目标中，所涉及的考核商品组
     * @param shopId
     * @return
     */
    public List<MdCheckGroupODTO> getMdCheckGroupList(Long shopId) {
        String today = DateTimeUtil.formatDate(new Date(),"yyyy-MM-dd");
        return reportMapper.getMdCheckGroupList(today,shopId);
    }

    /**
     * 获取当前门店下面的考核方案
     * @param shopId
     * @param checkGroupId
     * @return
     */
    public List<MdCheckReportODTO> getShopCheckPan(Long shopId, Long checkGroupId) {
        String today = DateTimeUtil.formatDate(new Date(),"yyyy-MM-dd");
        return reportMapper.getShopCheckPan(today,shopId,checkGroupId);
    }
}
