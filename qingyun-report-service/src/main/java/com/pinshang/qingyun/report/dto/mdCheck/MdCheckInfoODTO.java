package com.pinshang.qingyun.report.dto.mdCheck;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2020/11/16
 */
@Data
public class MdCheckInfoODTO {

    private Long shopId;

    @ApiModelProperty("考核方案开始日期")
    private String planBeginDate;
    @ApiModelProperty("考核方案结束日期")
    private String planEndDate;

    @ApiModelProperty("考核商品组名称")
    private String checkGroupName;

    @ApiModelProperty("考核类型（金额0 / 数量1）")
    private Integer type;

    @ApiModelProperty("考核总目标")
    private BigDecimal totalPlanAim;
    @ApiModelProperty("日均考核目标")
    private BigDecimal dayPlanAim;

    @ApiModelProperty("期间考核目标")
    private BigDecimal durationPlanAim;
    @ApiModelProperty("期间实际完成")
    private BigDecimal completeAim;
    @ApiModelProperty("期间实际完成率")
    private String completeAimPercent;
}
