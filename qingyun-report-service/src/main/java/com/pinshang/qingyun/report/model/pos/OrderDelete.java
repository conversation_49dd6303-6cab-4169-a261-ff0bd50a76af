package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@Entity
@Table(name = "t_pos_report_order_delete")
@NoArgsConstructor
public class OrderDelete extends BaseModel<OrderDelete> {

    private Long refId;

    /**  门店id */
    private Long shopId;

    /**  门店code */
    private String  shopCode;

    /** 门店名称 */
    private String  shopName;

    /** pos机号 */
    private String  macCode;

    /**  订单号 */
    private Long  orderCode;

    /** 商品数量 */
    private BigDecimal quantity;

    /** 订单金额 */
    private BigDecimal  totalAmount;

    /** 操作类型：1挂单删除 2挂单未结算 3整单取消 */
    private Integer operateType;

    /** 挂单时间 */
    private Date orderRestTime;

    /**  */
    private Long orderRestCasherId;

    /** 挂单人账号 */
    private String orderRestCasherCode;

    /** 挂单人姓名 */
    private String orderRestCasherName;

    /** 作废时间 */
    private Date  orderDeleteTime;

    /**  */
    private Long orderDeleteCasherId;

    /** 作废人账号 */
    private String orderDeleteCasherCode;

    /** 作废人姓名 */
    private String orderDeleteCasherName;

    /**  操作人：挂单删除，挂单未结算以挂单人为主;整单取消以作废人为主 */
    private Long  operateId;

    /** 操作人编码：挂单删除，挂单未结算以挂单人为主;整单取消以作废人为主 */
    private String  operateCode;

    /**  操作人姓名：挂单删除，挂单未结算以挂单人为主;整单取消以作废人为主 */
    private String  operateName;

    /**  操作时间：挂单删除，挂单未结算以挂单时间为主;整单取消以作废时间为主*/
    private Date  operateDate;

    /** 操作时间：挂单删除，挂单未结算以挂单时间为主;整单取消以作废时间为主 */
    private Date operateTime;

    /**
     * 授权人账号
     */
    private String authorizerCode;
    /**
     * 授权人姓名
     */
    private String authorizerName;

    /** pos机id **/
    private Long posMacId;

    /** pos机类型 1-收银pos, 2-自助pos **/
    private Integer posType;
}
