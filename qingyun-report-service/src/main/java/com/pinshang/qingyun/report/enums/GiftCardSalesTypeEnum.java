package com.pinshang.qingyun.report.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/02
 * @Version 1.0
 */
public enum GiftCardSalesTypeEnum {
    POS(2, "POS"),
    DY_GROUPON(30, "抖音团购");

    private Integer code;

    private String desc;

    public static GiftCardSalesTypeEnum getEnumByCOde(Integer code){
        return Arrays.stream(GiftCardSalesTypeEnum.values()).filter(it -> it.getCode().equals(code)).findFirst().orElse(null);
    }

    GiftCardSalesTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
