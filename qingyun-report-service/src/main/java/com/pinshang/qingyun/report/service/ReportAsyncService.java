package com.pinshang.qingyun.report.service;

import com.pinshang.qingyun.shop.service.ShopClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2022/6/28
 */
@Slf4j
@Service
public class ReportAsyncService {
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private ReportService reportService;
    @Autowired
    private ShopClient shopClient;

    /**
     * 及时达负库存报表汇总
     * @return
     */
    @Async
    public Boolean shopNegativeStockReportSummary() {
        // 查询线上可见的及时达门店
        List<Long> shopIdList = shopClient.getOnLineShopIdList();
        if(CollectionUtils.isEmpty(shopIdList)){
            log.info("及时达负库存报表汇总--无线上可见的门店");
            return Boolean.FALSE;
        }

        // 查询必售商品信息
        List<Long> mustSellCommodityIdList = shopClient.queryMustSellCommodityList();

        // 构造一个线程池
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(8, 20, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopIdList.size())
        );

        for(Long shopId : shopIdList){
            try {
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        reportService.shopNegativeStockReportSummary(shopId, mustSellCommodityIdList);
                    }
                });

            } catch (Exception e) {
                log.error("及时达负库存报表汇总异常: " + shopId,e);
                weChatSendMessageService.sendWeChatMessage("及时达负库存报表汇总异常,门店id: " + shopId);
            }
        }

        // 关闭线程池
        threadPool.shutdown();

        return Boolean.TRUE;
    }

}
