package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName SelectPxTraderAmountIDTO
 * <AUTHOR>
 * @Date 2023/2/24 17:51
 * @Description SelectPxTraderAmountIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HelpCardSelectPxTraderAmountIDTO {
    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("对账开始日期 yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("对账结束日期 yyyy-MM-dd")
    private String endDate;
}
