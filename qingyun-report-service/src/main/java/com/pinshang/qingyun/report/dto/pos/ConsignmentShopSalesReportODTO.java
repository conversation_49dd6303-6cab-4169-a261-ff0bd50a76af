package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ConsignmentShopSalesReportODTO {

    @ExcelIgnore
    private Long storeId;

    @ExcelIgnore
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode , keyName = "storeId")
    private String storeCode;

    @ApiModelProperty("门店编码")
    @ExcelProperty(value = "门店编码")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopCode,keyName = "shopId")
    private String shopCode;

    @ApiModelProperty("门店名称")
    @ExcelProperty(value = "门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ApiModelProperty("销售数量")
    @ExcelProperty(value = "销售数量")
    private Integer salesQuantity;

    @ApiModelProperty("销售金额")
    @ExcelProperty(value = "销售金额")
    private  BigDecimal salesAmount;
}
