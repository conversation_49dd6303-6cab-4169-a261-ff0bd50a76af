package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CashierDailyIDTO extends Pagination {

    @ApiModelProperty(position = 1, value = "门店id(注意：是门店id，不是门店编码值，如果总部查询全部门店传 '' 或者null)")
    private String shopCode;

    @ApiModelProperty(position = 2, value = "开始时间")
    private String  startTime;

    @ApiModelProperty(position = 3, value = "结束时间")
    private String EndTime;

    @ApiModelProperty(position = 4, value = "收银员")
    private String saleNumber;

    @ApiModelProperty(position = 5, value = "POS机号(适用于'按POS机号')")
    private String macCode;
    @ApiModelProperty(position = 6, value = "销售方式 1:销售 2：退货(适用于'按POS机号')")
    private Integer saleType;

    @ApiModelProperty(position = 7, value = "搜索类型 0、按POS机 1、收银日报 2、按门店 3、按日期 4、按付款")
    private String searchCriteria;

    private List<Long> shopIdList;

    private List<PayTypeODTO> payTypeList;

    @ApiModelProperty(position = 8, value = "款项类型 1、线下售卡 2、线下营业款 3、代销营业额")
    private Integer moneyType;

    private Boolean isCurrentDay = false;

    @ApiModelProperty("pos机类型 1-收银pos, 2-自助pos")
    private Integer posType;

}
