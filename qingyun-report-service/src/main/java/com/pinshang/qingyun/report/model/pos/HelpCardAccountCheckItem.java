package com.pinshang.qingyun.report.model.pos;

import com.pinshang.qingyun.report.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * @ClassName HelpCardAccountCheckingItem
 * <AUTHOR>
 * @Date 2023/2/23 18:01
 * @Description HelpCardAccountCheckingItem
 * @Version 1.0
 */
@Data
@Table(name = "t_pos_help_card_account_check_item")
public class HelpCardAccountCheckItem extends BaseModel<HelpCardAccountCheckItem> {
    @ApiModelProperty("t_pos_help_card_account_check对账表id")
    private Long checkId;

    @ApiModelProperty("门店对应公司id")
    private Long companyId;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("门店下总数量")
    private BigDecimal quantity;

    @ApiModelProperty("门店下总金额")
    private BigDecimal amount;

    @ApiModelProperty("客户id")
    @Transient
    private Long storeId;
}
