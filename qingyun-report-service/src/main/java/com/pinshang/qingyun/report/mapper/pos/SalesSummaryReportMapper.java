package com.pinshang.qingyun.report.mapper.pos;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.pos.OrderSaleTypeODTO;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO;
import com.pinshang.qingyun.report.dto.pos.joinShop.AnalysisBySynthesisIDTO;
import com.pinshang.qingyun.report.dto.pos.joinShop.SingleProductSalesDTO;
import com.pinshang.qingyun.report.dto.pos.joinShop.SingleProductSalesIDTO;
import com.pinshang.qingyun.report.model.pos.SalesSummaryReport;
import com.pinshang.qingyun.report.model.pos.ThirdSummary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SalesSummaryReportMapper extends MyMapper<SalesSummaryReport> {

    List<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(@Param("idto") SalesSummaryReportOrderIDTO idto);

    List<SalesSummaryReportOrderODTO> bestSellerReport(@Param("idto") SalesSummaryReportOrderIDTO idto);

    List<SalesSummaryReportOrderODTO> salesPromotionReport(@Param("idto") SalesSummaryReportOrderIDTO idto);

    int deleteSalesSummaryReportByOrderCodes(@Param("orderCode") Long orderCodes);

    int deleteSalesSummaryReportByTimeRange(@Param("saleTime") String saleTime,@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<SalesSummaryReportOrderODTO> cateShopSalesSummaryReport(@Param("idto") SalesSummaryReportOrderIDTO idto);

    List<ThirdSummary> cateShopSalesDayReport(@Param("saleTime") String saleTime);

    void deleteDaySalesSummaryReportBySaleTime(@Param("saleTime") String saleTime);

    void insertPosReportDaySalesSummary(@Param("saleTime") String saleTime);

    void deleteGuestListSummaryDayReportBySaleTime(@Param("saleTime") String saleTime);

    void insertGuestListSummaryDayReportBySaleTime(@Param("saleTime") String saleTime);

    void deleteCashierWaterDayReportBySaleTime(@Param("saleTime") String saleTime);

    void insertCashierWaterDayReportBySaleTime(@Param("saleTime") String saleTime);

    List<OrderSaleTypeODTO> comparePosReportAmount(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<OrderSaleTypeODTO> queryPosRepeatOrderCode(@Param("saleTime") String saleTime);

    void deleteAllPosReportUnique();

    void deleteGuestListSummary10Day(@Param("saleTime") String saleTime);

    /**
     * 钱大妈加盟  单品实时销售列表
     * @param dto
     * @return
     */
    List<SingleProductSalesDTO> singleProductSalesList(@Param("dto") SingleProductSalesIDTO dto);

    /**
     * 钱大妈加盟  单品实时销售汇总
     * @param dto
     * @return
     */
    SingleProductSalesDTO singleProductSalesSum(@Param("dto") SingleProductSalesIDTO dto);

    List<SalesSummaryReport> salesSummaryList(@Param("dto") AnalysisBySynthesisIDTO dto);
}
