package com.pinshang.qingyun.report.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName CategoryAllSalesPercentODTO
 * <AUTHOR>
 * @Date 2022/10/27 10:16
 * @Description CategoryAllSalesPercentODTO
 * @Version 1.0
 */
@Data
public class CategoryAllSalesPercentODTO {

    private Long commodityFirstId;

    @ApiModelProperty(position = 1, value ="大类")
    private String firstKindName;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 2, value ="当前线下销售额")
    private BigDecimal thisSalesMoneyOffLine;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 2, value ="当前线上销售额")
    private BigDecimal thisSalesMoneyOnLine;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 4, value ="上期线下销售额")
    private BigDecimal lastSalesMoneyOffLine;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 4, value ="上期线上销售额")
    private BigDecimal lastSalesMoneyOnLine;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 5, value ="销售差额")
    private BigDecimal salesMoneyDiff;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 3, value ="销售额同比-百分比的值")
    private BigDecimal categorySalesPercent;

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ApiModelProperty(position = 5, value ="销售额占比")
    private BigDecimal salesMoneyPercent;

    @ApiModelProperty(position = 3, value ="销售额同比-百分比的值")
    private String categorySalesPercentStr;

    @ApiModelProperty(position = 5, value ="销售额占比")
    private String salesMoneyPercentStr;
}
