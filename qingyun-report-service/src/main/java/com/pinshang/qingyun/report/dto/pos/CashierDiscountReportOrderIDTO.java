package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CashierDiscountReportOrderIDTO extends Pagination {

    /** 门店ID*/
    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("销售日期开始时间yyyy-MM-dd HH:mm:ss")
    private String beginDate;//销售日期开始

    @ApiModelProperty("销售日期结束时间yyyy-MM-dd HH:mm:ss")
    private String endDate;//销售日期结束

    @ApiModelProperty("收银员编码或者名称")
    private String cashierKey;//收银员编码或者名称

    private Integer isTotal;//0:不查询总和   1：查询总和

    private List<Long> shopIdList;

    @ApiModelProperty("门店类型")
    private Integer shopType;

    private Long provinceId;
}
