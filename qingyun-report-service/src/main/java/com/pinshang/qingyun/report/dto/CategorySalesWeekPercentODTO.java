package com.pinshang.qingyun.report.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @see ExcelSheetTitleEnum#CATEGORY_SALES_PERCENT_REPORT
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR> Zhang
 * @version 1.0
 * @since 2018/9/26
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class CategorySalesWeekPercentODTO {
    @ExcelIgnore
    private Long commodityFirstId;

    @ApiModelProperty(position = 1, value ="大类")
    @ExcelProperty("大类")
    private String firstKindName;

    @ApiModelProperty(position = 2, value ="今日销售额")
    @ExcelProperty("今日销售额")
    private BigDecimal todaySalesMonery;

    @ApiModelProperty(position = 3, value ="今日销售额占比")
    @ExcelProperty("今日销售额占比")
    private String todaySalesMoneryPercent;

    @ApiModelProperty(position = 4, value ="上周销售额")
    @ExcelProperty("上周销售额")
    private BigDecimal lastWeekSalesMonery;

    @ApiModelProperty(position = 5, value ="销售额同比")
    @ExcelProperty("销售额同比")
    private String salesMoneryPercent;
}
