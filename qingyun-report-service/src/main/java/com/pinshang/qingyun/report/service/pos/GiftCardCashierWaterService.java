package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.gift.GiftSaleTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.mapper.pos.GiftCardCashierWaterMapper;
import com.pinshang.qingyun.report.model.pos.GiftCardCashierWater;
import com.pinshang.qingyun.report.model.pos.GiftCardSalesWater;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class GiftCardCashierWaterService {

    @Autowired
    private GiftCardCashierWaterMapper giftCardCashierWaterMapper;

    @Autowired
    private GiftCardSalesWaterService giftCardSalesWaterService;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopClient shopClient;

    @Transactional(rollbackFor = Exception.class)
    public Integer addCashierWater(GiftCardCashierWaterMessage message) {
        //收银流水，如果用两种支付方式会有两条收银记录
        if (CollectionUtils.isNotEmpty(message.getPayTypes())) {
            List<GiftCardCashierWater> list = new ArrayList<>();
            GiftCardCashierWater cardCashierWater = null;
            for (GiftCardPayTypeMessage payType : message.getPayTypes()) {
                cardCashierWater = new GiftCardCashierWater();
                BeanUtils.copyProperties(message, cardCashierWater);
                cardCashierWater.setPayType(payType.getPayType());
                cardCashierWater.setPayName(payType.getPayName());
                cardCashierWater.setTotalAmount(payType.getTotalAmount());
                cardCashierWater.setType(message.getType());
                list.add(cardCashierWater);
            }
            Example exampleCashier = new Example(GiftCardCashierWater.class);
            exampleCashier.createCriteria().andEqualTo("orderCode", message.getOrderCode());
            List<GiftCardCashierWater> cardCashierWaterList = giftCardCashierWaterMapper.selectByExample(exampleCashier);
            if (CollectionUtils.isEmpty(cardCashierWaterList)) {
                giftCardCashierWaterMapper.insertList(list);
            }
        }
        //销售流水
        if (CollectionUtils.isNotEmpty(message.getSalesWater())) {
            List<GiftCardSalesWater> list = new ArrayList<>();
            GiftCardSalesWater giftCardSalesWater = null;
            for (GiftCardSalesWaterMessage salesWater:  message.getSalesWater()) {
                giftCardSalesWater = new GiftCardSalesWater();
                BeanUtils.copyProperties(message, giftCardSalesWater);
                giftCardSalesWater.setCardNo(salesWater.getCardNo());
                giftCardSalesWater.setCardSn(salesWater.getCardSn());
                giftCardSalesWater.setParValue(salesWater.getParValue());
                giftCardSalesWater.setPrice(salesWater.getPrice());
                giftCardSalesWater.setCommodityId(salesWater.getCommodityId());
                giftCardSalesWater.setNumber(salesWater.getNumber());
                giftCardSalesWater.setConsignmentId(salesWater.getConsignmentId());
                giftCardSalesWater.setType(message.getType());
                list.add(giftCardSalesWater);
            }
            List<GiftCardSalesWater> cardSalesWaterList = giftCardSalesWaterService.listByCode(message.getOrderCode());
            if (CollectionUtils.isEmpty(cardSalesWaterList)) {
                giftCardSalesWaterService.addSalesWater(list);
            }
        }
        return 1;
    }

    /**
     * 收银流水
     * @param dto
     * @return
     */
    public PageInfo<GiftCardCashierWaterPage> cashierWaterPage(QueryGiftCardWaterDTO dto) {
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if(!CollectionUtils.isEmpty(shopIdList)){
                dto.setShopIdList(shopIdList);
            }
        }
        PageInfo<GiftCardCashierWaterPage> page = PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() ->{
            giftCardCashierWaterMapper.cashierWaterPage(dto);
        });
        return page;
    }

    public GiftCardCashierWaterPage cashierWaterSum(QueryGiftCardWaterDTO dto) {
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if(!CollectionUtils.isEmpty(shopIdList)){
                dto.setShopIdList(shopIdList);
            }
        }
        return giftCardCashierWaterMapper.cashierWaterSum(dto);
    }

    /**
     * 代销收银流水
     * @param dto
     * @return
     */
    public PageInfo<GiftCardCashierWaterPage> consignmentCashierWaterPage(QueryGiftCardWaterDTO dto) {
        QYAssert.isTrue(GiftSaleTypeEnum.CONSIGNMENT.getCode().equals(dto.getType()), "代销收银流水类型错误");
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if (CollectionUtils.isEmpty(shopIdList)) {
                return new PageInfo<>();
            }
            dto.setShopIdList(shopIdList);
            if (StringUtils.isNotBlank(dto.getOrgNum())) {
                List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(dto.getOrgNum());
                if (CollectionUtils.isEmpty(shopDtos)) {
                    return new PageInfo<>();
                }
                dto.setShopIdList(shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList()));
            }
        }

        PageInfo<GiftCardCashierWaterPage> page = PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() ->{
            giftCardCashierWaterMapper.cashierWaterPage(dto);
        });
        return page;
    }

    public GiftCardCashierWaterPage consignmentCashierWaterSum(QueryGiftCardWaterDTO dto) {
        if (null == dto.getShopId()) {
            List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
            if (CollectionUtils.isEmpty(shopIdList)) {
                return new GiftCardCashierWaterPage();
            }
            dto.setShopIdList(shopIdList);
            if (StringUtils.isNotBlank(dto.getOrgNum())) {
                List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(dto.getOrgNum());
                if (CollectionUtils.isEmpty(shopDtos)) {
                    return new GiftCardCashierWaterPage();
                }
                dto.setShopIdList(shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList()));
            }
        }

        return giftCardCashierWaterMapper.cashierWaterSum(dto);
    }
}
