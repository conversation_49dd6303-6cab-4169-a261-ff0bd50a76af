package com.pinshang.qingyun.report.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String get$yyMMdd(LocalDate localDate){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy-MM-dd");
        return localDate.format(formatter);
    }

    public static String get$yyMM(LocalDate localDate){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy-MM");
        return localDate.format(formatter);
    }

    public static String get$yy(LocalDate localDate){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy");
        return localDate.format(formatter);
    }

    public static int compareDate(String DATE1, String DATE2,String format) {
        DateFormat df = new SimpleDateFormat(format);
        try {
            Date dt1 = df.parse(DATE1);
            Date dt2 = df.parse(DATE2);
            if (dt1.getTime() > dt2.getTime()) {
                return 1;
            } else if (dt1.getTime() < dt2.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
          * 判断时间是否在时间段内
          * @param nowTime
          * @param beginTime
          * @param endTime
          * @return
          */
    public static boolean isBetweenNowDate(String beginTimeStr, String endTimeStr) {
        //设置日期格式
        SimpleDateFormat df = new SimpleDateFormat("HH:mm");
        //初始化
        Date nowTime =null;
        Date beginTime = null;
        Date endTime = null;
        try {
            //格式化当前时间格式
            nowTime = df.parse(df.format(new Date()));
            //定义开始时间
            beginTime = df.parse(beginTimeStr);
            //定义结束时间
            endTime = df.parse(endTimeStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //设置当前时间
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        //设置开始时间
        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);
        //设置结束时间
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        //处于开始时间之后，和结束时间之前的判断
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取两个日期之间的所有日期
     * @param startTime 开始日期
     * @param endTime 结束日期
     * @return
     */
    public static List<String> getBetweenDate(String startTime, String endTime) {
        // 返回的日期集合
        List<String> days = new ArrayList<String>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return days;
    }

    /**
     * 获取两个时间段的交集
     * @param planBeginStr
     * @param planEndStr
     * @param checkBeginStr
     * @param checkEndStr
     * @return
     * @throws Exception
     */
    public static List<String> getDateRangeCoincidence(String planBeginStr, String planEndStr, String checkBeginStr, String checkEndStr){
        List<String> planDate = getBetweenDate(planBeginStr,planEndStr);
        List<String> checkDate = getBetweenDate(checkBeginStr,checkEndStr);
        planDate.retainAll(checkDate);
       /* List<String> dateList = new ArrayList<>();
        dateList.add(planDate.get(0));
        dateList.add(planDate.get(planDate.size()-1));*/
        return planDate;
    }

    public static Long getDiffDay(String begin,String end){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Long betweenDate = 0L;
        try{
            //开始时间
            Date startDate = sdf.parse(begin);
            //结束时间
            Date endDate = sdf.parse(end);

            //得到相差的天数 betweenDate
            betweenDate = (endDate.getTime() - startDate.getTime())/(60*60*24*1000);

        }catch (Exception e) {
            e.printStackTrace();
        }
        return betweenDate + 1;
    }

    /**
     * 判断当前日期是否是该月的第一天
     * @return
     */
    public static boolean isFirstDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_MONTH) == 1;
    }

    /**
     *获取本月第一天
     * @return
     */
    public static String getMonthFirstDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar1 = Calendar.getInstance();
        calendar1.set(Calendar.DAY_OF_MONTH,1);
        return sdf.format(calendar1.getTime());
    }

    /**
     *获取前一个月第一天
     * @return
     */
    public static String getFirstDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar1 = Calendar.getInstance();
        calendar1.add(Calendar.MONTH, -1);
        calendar1.set(Calendar.DAY_OF_MONTH,1);
        return sdf.format(calendar1.getTime());
    }

    /**
     *获取前一个月最后一天
     * @return
     */
    public static String getLastDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar2 = Calendar.getInstance();
        calendar2.set(Calendar.DAY_OF_MONTH, 0);
        return sdf.format(calendar2.getTime());
    }

    /**
     * 判断开始日期、结束日期是否是当日
     * @param startTime
     * @param endTime
     * @return
     */
    public static Boolean isCurrentDay(String startTime, String endTime){
        if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            return false;
        }
        if(startTime.length() > 10){
            startTime = startTime.substring(0,10);
        }
        if(endTime.length() > 10){
            endTime = endTime.substring(0,10);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate = sdf.format(new Date());
        return  startTime.equals(endTime) && startTime.equals(nowDate);
    }

/*
    public static void main(String[] args) {
        System.out.println(get$yy(LocalDate.now()));
        System.out.println(get$yyMM(LocalDate.now()));
        System.out.println(get$yyMMdd(LocalDate.now()));
    }
*/


   /* public static Long getDelayTime(String loopTime){
        Date newDate = new Date();
        Date nowHHmmss = DateUtil.parseDate(DateUtil.getDateFormate(newDate, "HH:mm:ss"),"HH:mm:ss");
        Date loopHHmmss = DateUtil.parseDate(loopTime, "HH:mm:ss");
        Long delayTime;
        if(loopHHmmss.getTime() > nowHHmmss.getTime()){
            delayTime = loopHHmmss.getTime() - nowHHmmss.getTime();
        }else {
            String tomorrowDateStr = DateUtil.getDateFormate(DateUtil.addDay(new Date(),1),"yyyy-MM-dd") + " " + loopTime;
            Date tomorrowDate = DateUtil.parseDate(tomorrowDateStr, "yyyy-MM-dd HH:mm:ss");
            delayTime = tomorrowDate.getTime() - newDate.getTime();
        }
        return delayTime;
    }*/

    /*public static void main(String[] args) {
        System.out.println(getDelayTime("00:00:00"));
        System.out.println(getDelayTime("00:00:00") / 1000 / 60);
        System.out.println(getDelayTime("00:00:00") / 1000 / (60 * 60));
    }*/
}
