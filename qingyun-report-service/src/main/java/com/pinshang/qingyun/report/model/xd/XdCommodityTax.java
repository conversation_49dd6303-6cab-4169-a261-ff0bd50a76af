package com.pinshang.qingyun.report.model.xd;

import com.pinshang.qingyun.base.po.BaseIDPO;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

@Entity
@Table(name = "t_xd_commodity_tax")
public class XdCommodityTax extends BaseIDPO{
	private Long shopId;
	private String dateTime;
	private Long commodityId;
	private Long commodityFirstId;
	private Long commoditySecondId;
	private Long commodityThirdId;

	private BigDecimal totalSales =BigDecimal.ZERO;//总销售额
	private BigDecimal totalQuanty =BigDecimal.ZERO;//销售总量
	private BigDecimal costTotal =BigDecimal.ZERO;//成本金额合计

	private BigDecimal orderTotal =BigDecimal.ZERO;//进货金额
	private BigDecimal orderQuanty =BigDecimal.ZERO;//进货数量

	private BigDecimal normalBreakageQuantity; // 损(过保/临保)
	private BigDecimal overNormalBreakageQuantity; //损(非过保/非临保)

	@Transient
	private BigDecimal commodityPackageSpec;//包装规格

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getDateTime() {
		return dateTime;
	}

	public void setDateTime(String dateTime) {
		this.dateTime = dateTime;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public Long getCommodityFirstId() {
		return commodityFirstId;
	}

	public void setCommodityFirstId(Long commodityFirstId) {
		this.commodityFirstId = commodityFirstId;
	}

	public Long getCommoditySecondId() {
		return commoditySecondId;
	}

	public void setCommoditySecondId(Long commoditySecondId) {
		this.commoditySecondId = commoditySecondId;
	}

	public Long getCommodityThirdId() {
		return commodityThirdId;
	}

	public void setCommodityThirdId(Long commodityThirdId) {
		this.commodityThirdId = commodityThirdId;
	}

	public BigDecimal getTotalSales() {
		return totalSales;
	}

	public void setTotalSales(BigDecimal totalSales) {
		this.totalSales = totalSales;
	}

	public BigDecimal getTotalQuanty() {
		return totalQuanty;
	}

	public void setTotalQuanty(BigDecimal totalQuanty) {
		this.totalQuanty = totalQuanty;
	}

	public BigDecimal getCostTotal() {
		return costTotal;
	}

	public void setCostTotal(BigDecimal costTotal) {
		this.costTotal = costTotal;
	}

	public BigDecimal getOrderTotal() {
		return orderTotal;
	}

	public void setOrderTotal(BigDecimal orderTotal) {
		this.orderTotal = orderTotal;
	}

	public BigDecimal getOrderQuanty() {
		return orderQuanty;
	}

	public void setOrderQuanty(BigDecimal orderQuanty) {
		this.orderQuanty = orderQuanty;
	}

	public BigDecimal getNormalBreakageQuantity() {
		return normalBreakageQuantity;
	}

	public void setNormalBreakageQuantity(BigDecimal normalBreakageQuantity) {
		this.normalBreakageQuantity = normalBreakageQuantity;
	}

	public BigDecimal getOverNormalBreakageQuantity() {
		return overNormalBreakageQuantity;
	}

	public void setOverNormalBreakageQuantity(BigDecimal overNormalBreakageQuantity) {
		this.overNormalBreakageQuantity = overNormalBreakageQuantity;
	}
}
