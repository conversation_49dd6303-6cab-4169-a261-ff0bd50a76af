package com.pinshang.qingyun.report.client.report.hystrix;

import com.pinshang.qingyun.report.client.report.service.PosHelpCardClient;
import com.pinshang.qingyun.report.dto.pos.*;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName PosHelpCardHystrix
 * <AUTHOR>
 * @Date 2023/2/22 17:12
 * @Description PosHelpCardHystrix
 * @Version 1.0
 */
@Component
public class PosHelpCardHystrix implements FallbackFactory<PosHelpCardClient> {
    @Override
    public PosHelpCardClient create(Throwable throwable) {
        return new PosHelpCardClient() {
            @Override
            public List<HelpCardAvailableAreaListODTO> getAreaList(HelpCardAvailableAreaListIDTO idto) {
                return null;
            }

            @Override
            public HelpCardAreaDetailODTO areaSettingDetail(Long id) {
                return null;
            }

            @Override
            public List<HelpCardAreaSettingPageODTO> areaDetailListByIds(HelpCardAreaSettingPageIDTO idto) {
                return null;
            }
        };
    }
}
