package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.LoginTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO;
import com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO;
import com.pinshang.qingyun.report.dto.pos.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.TotalSalesSummaryODTO;
import com.pinshang.qingyun.report.enums.PosReportSummaryTypeEnum;
import com.pinshang.qingyun.report.enums.ShopTypeConditionEnum;
import com.pinshang.qingyun.report.mapper.CategoryMapper;
import com.pinshang.qingyun.report.mapper.pos.PosReportCurrentDayMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.model.Category;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.ShopTypeUtil;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.org.SelectShopParentOrgInfoIDTO;
import com.pinshang.qingyun.smm.dto.org.ShopAndShopParentOrgInfoODTO;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.UserStallClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/4/27
 */


@Slf4j
@Service
public class PosReportCurrentDayService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private PosReportCurrentDayMapper posReportCurrentDayMapper;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private OrgClient orgClient;
    @Autowired
    private UserStallClient userStallClient;
    @Autowired
    private ShopMapper shopMapper;

    /**
     * 1
     * 商品销售汇总,类别销售汇总
     * 门店，类别，商品销售汇总分析
     * @param idto
     * @return
     */
    public TablePageInfo<SalesSummaryReportOrderODTO> commoditySalesSummaryReport(SalesSummaryReportOrderIDTO idto, Integer type){
        List<Integer> shopTypeList =  ShopTypeUtil.getShopType(idto.getShopType(), ShopTypeConditionEnum.POS_LIST_01.getCode());
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), idto.getOrgCode(), shopTypeList , idto.getManagementMode(), idto.getProvinceId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info = new TablePageInfo();
            info.setList(null);
            return info;
        }

        if (null != idto.getShopId() && !idto.getShopId().equals("")) {
            shopIdList.retainAll(Arrays.asList(idto.getShopId()));
        }
        if (CollectionUtils.isEmpty(shopIdList)) {
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }

        if(!CollectionUtils.isEmpty(idto.getShopTypeList()) || !CollectionUtils.isEmpty(idto.getManagementModeList())) {
            List<Long> idList = shopMapper.queryShopIdListByParam(idto.getShopTypeList(), idto.getManagementModeList());
            if(CollectionUtils.isEmpty(idList)) {
                TablePageInfo info=new TablePageInfo();
                info.setList(null);
                return info;
            }else {
                shopIdList.retainAll(idList);
                if(CollectionUtils.isEmpty(shopIdList)) {
                    TablePageInfo info=new TablePageInfo();
                    info.setList(null);
                    return info;
                }
            }
        }

        idto.setShopIdList(shopIdList);

        // 商品销售汇总 、商品销售汇总分析
        // （汇总类型选择商品汇总（区分门店）时，选择全部门店时，必须输入商品；选择门店时，可以查询全部商品的销售数据）
        if(idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode()){
            if(idto.getShopId() == null && idto.getCommodityId() == null && StringUtils.isBlank(idto.getBarCode())){
                QYAssert.isTrue(false,"门店和商品不能全部为空");
            }
        }
        //费总部的档口查询需要判断用户档口权限
        Integer loginType = FastThreadLocalUtil.getQY().getLoginType();
        if(Objects.equals(idto.getQueryType(),1) && !LoginTypeEnums.总部.getCode().equals(loginType)){
            SelectUserStallIdListIDTO userStallIdListIDTO = new SelectUserStallIdListIDTO();
            userStallIdListIDTO.setUserId(FastThreadLocalUtil.getQY().getUserId());
            userStallIdListIDTO.setShopId(FastThreadLocalUtil.getQY().getShopId());
            userStallIdListIDTO.setStallStatus(1);
            List<Long> stallList = userStallClient.selectUserStallIdList(userStallIdListIDTO);
            if(!CollectionUtils.isEmpty(stallList)){
                if(idto.getStallId() == null){
                    idto.setStallList(stallList);
                }
                if(idto.getStallId() != null && !stallList.contains(idto.getStallId())){
                    return new TablePageInfo();
                }
            }else{
                return new TablePageInfo();
            }
        }

        PageInfo<SalesSummaryReportOrderODTO> pageDate = null;

        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            posReportCurrentDayMapper.commoditySalesSummaryReport(idto);
        });

        List<SalesSummaryReportOrderODTO> list = pageDate.getList();
        TotalSalesSummaryODTO total = new TotalSalesSummaryODTO();
        if(!CollectionUtils.isEmpty(list)){

            Map<Long,String> barCodeMap = new HashMap<>();
            //isCommodity 查询是否包括商品信息展示
            boolean isCommodity = null != idto.getSummaryType() && (idto.getSummaryType() == PosReportSummaryTypeEnum.ONE.getCode() || idto.getSummaryType()== PosReportSummaryTypeEnum.TWO.getCode());
            if(isCommodity){
                barCodeMap = commodityService.getCommodityBarCodeMap(null);
            }
            //查询销售金额合计
            SalesSummaryReportOrderODTO totalODTO = getTotalSaleAmount(idto);
            BigDecimal totalSalesAmount = new BigDecimal(BigInteger.ZERO);
            if(type == 0){
                totalSalesAmount = totalODTO.getSaleAmount();
            }else if(type == 1){
                totalSalesAmount = totalODTO.getTatalAmount();
            }
            for(SalesSummaryReportOrderODTO odto : list){
                if(isCommodity){
                    String barCodes = barCodeMap.get(Long.valueOf(odto.getCommodityId()));
                    odto.setBarCodeList(barCodes);
                    odto.setBarCode(barCodes != null ? barCodes.split(",")[0]:"");
                }

                odto.setGrossprofitmarginAmount(odto.getTatalAmount().subtract(odto.getWeightAmount()));
                if(null != odto.getTatalAmount() && odto.getTatalAmount().compareTo(BigDecimal.ZERO) != 0){
                    odto.setGrossprofitrate(odto.getGrossprofitmarginAmount().divide(odto.getTatalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                }

                odto.setNoTaxWeightAmount(odto.getNoTaxWeightAmount().setScale(2,BigDecimal.ROUND_HALF_UP));
                odto.setNoTaxSaleAmount(odto.getNoTaxRateAmount());

                if(null != totalSalesAmount && totalSalesAmount.compareTo(BigDecimal.ZERO) != 0){
                    if(type == 0){
                        odto.setSalesAmountPercent(odto.getSaleAmount().divide(totalSalesAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                    }else if(type == 1){
                        odto.setSalesAmountPercent(odto.getTatalAmount().divide(totalSalesAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
                    }
                }
            }
            //设置总值
            if(type == 0){
                total.setTotalSaleQuantity(totalODTO.getSaleQuantity());
                total.setTotalReturnQuantity(totalODTO.getReturnQuantity());
                total.setTotalGiveQuantity(totalODTO.getGiveQuantity());
                total.setTotalSaleAmount(totalODTO.getSaleAmount());
                total.setTotalReturnAmount(totalODTO.getReturnAmount());
                total.setTotalGiveAmount(totalODTO.getGiveAmount());
                total.setTotalDiscountAmount(totalODTO.getDiscountAmount());
                total.setTotalQuantity(totalODTO.getTatalQuantity());
                total.setTotalAmount(totalODTO.getTatalAmount());
            }else if(type == 1){
                total.setTotalSaleQuantity(totalODTO.getTatalQuantity());
                total.setTotalSaleAmount(totalODTO.getTatalAmount());
                total.setTotalSaleWeight(totalODTO.getWeightAmount());
                total.setTotalGrossprofitmarginAmount(totalODTO.getTatalAmount().subtract(totalODTO.getWeightAmount()));
            }

        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(total);
        return tablePageInfo;
    }

    private Map<Long, ShopAndShopParentOrgInfoODTO> getOrgInformation(List<Long> shopIdList) {
        Map<Long, ShopAndShopParentOrgInfoODTO> map = null;
        SelectShopParentOrgInfoIDTO shopParentOrgInfoIDTO = new SelectShopParentOrgInfoIDTO();
        shopParentOrgInfoIDTO.setShopIdList(shopIdList);
        List<ShopAndShopParentOrgInfoODTO> orgInfoList = orgClient.selectShopAndShopParentOrgInfoList(shopParentOrgInfoIDTO);
        if (null != orgInfoList && orgInfoList.size() > 0) {
            map = orgInfoList.stream().collect(Collectors.toMap(ShopAndShopParentOrgInfoODTO::getShopId, e -> e));
        }
        return map;
    }

    /**
     * 获取类别 map
     * @return
     */
    public Map<BigDecimal,Category> getCategoryMap(){
        Map<BigDecimal,Category> categoryMap = new HashMap<>();
        List <Category> cateList = categoryMapper.selectAll();
        for(Category dto : cateList){
            categoryMap.put(new BigDecimal(dto.getId()),dto);
        }
        return categoryMap;
    }
    /**
     * 销售汇总  总数
     * @param idto
     * @return
     */
    private SalesSummaryReportOrderODTO getTotalSaleAmount(SalesSummaryReportOrderIDTO idto) {
        idto.setSummaryType(null);
        idto.setIsWithShop(null);
        List<SalesSummaryReportOrderODTO> saleList = posReportCurrentDayMapper.commoditySalesSummaryReport(idto);
        return saleList.get(0);
    }


    /**
     * 查询香烟大类一周销售额
     * @return
     */
    public BigDecimal queryFirstCateWeekSaleAmount(Long shopId, Date orderTime) {
        String endDate = DateUtil.getDateFormate(DateUtil.addDay(orderTime,-1), "yyyy-MM-dd");
        String beginDate = DateUtil.getDateFormate(DateUtil.addDay(orderTime,-7), "yyyy-MM-dd");
        return posReportCurrentDayMapper.queryFirstCateWeekSaleAmount(shopId,beginDate,endDate);
    }

}
