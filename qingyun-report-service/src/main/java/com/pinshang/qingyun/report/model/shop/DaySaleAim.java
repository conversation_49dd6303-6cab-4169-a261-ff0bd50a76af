package com.pinshang.qingyun.report.model.shop;

import com.pinshang.qingyun.report.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "t_day_sale_aim")
public class DaySaleAim extends BaseModel<DaySaleAim> {

    /**
     * 门店
     */
    private  Long shopId;

    private String shopCode;

    private String shopName;

    /**
     * 日期:(yyyy-MM-dd)
     */
    private String saleTime;

    /**
     * 目标销售额
     */
    private BigDecimal aimSaleAmount;

    /**
     * 备注
     */
    private  String remark;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getSaleTime() {
        return saleTime;
    }

    public void setSaleTime(String saleTime) {
        this.saleTime = saleTime;
    }

    public BigDecimal getAimSaleAmount() {
        return aimSaleAmount;
    }

    public void setAimSaleAmount(BigDecimal aimSaleAmount) {
        this.aimSaleAmount = aimSaleAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
