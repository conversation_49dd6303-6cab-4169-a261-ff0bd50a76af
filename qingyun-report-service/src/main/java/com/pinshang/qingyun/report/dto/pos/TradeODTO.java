package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class TradeODTO {

    @ApiModelProperty(position = 1, value = "订单code")
    private Long orderCode;

    @ApiModelProperty(position = 2, value = "收银员编码")
    private String createName;

    @ApiModelProperty(position = 3, value = "收银员名称")
    private String employeeName;

    @ApiModelProperty(position = 4, value = "收银日期")
    private Date createTime;

    @ApiModelProperty(position = 5, value = "门店编码")
    private String shopCode;

    @ApiModelProperty(position = 6, value = "门店")
    private String shopName;

    @ApiModelProperty(position = 7, value = "门店地址")
    private String detailAddress;

    @ApiModelProperty(position = 8, value = "门店联系电话")
    private String mobile;

    @ApiModelProperty(position = 9, value = "总数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(position = 9, value = "总份数")
    private Integer totalNum;

    @ApiModelProperty(position = 11, value = "合计")
    private BigDecimal payAmount;

    @ApiModelProperty(position = 12, value = "已优惠")
    private BigDecimal preferentialAmount;

    @ApiModelProperty(position = 13, value = "实收或者实退金额")
    private BigDecimal receiptAmount;

    @ApiModelProperty(position = 14, value = "1:销售  -1:退货")
    private String saleType;

    @ApiModelProperty(position = 17, value = "找零金额")
    private BigDecimal changeAmount;

    @ApiModelProperty(position = 18, value = "用户支付金额")
    private BigDecimal userPayment;

    @ApiModelProperty(position = 19, value = "会员手机号")
    private String phone;

    @ApiModelProperty(position = 20, value = "支付类型")
    private String payType;

    @ApiModelProperty(position = 25, value = "优惠券抵扣")
    private BigDecimal changePriceSum;

    @ApiModelProperty(position = 26, value = "实际应收/退-减去线下优惠")
    private BigDecimal receiptAmountWithPromotion;

    @ApiModelProperty("经营模式：1-直营、2-外包、3-大店")
    private Integer managementMode;


    @ApiModelProperty(position = 16, value = "明细列表")
    private List<AIMonitorCommodityDTO> list;

    @ApiModelProperty("支付列表")
    private List<AIMonitorPaymentDTO> payments;

    @ApiModelProperty("1整单改价和整单折扣")
    private Integer operateType;

    public TradeODTO(Long orderCode, String createName, String employeeName, Date createTime, String shopCode, String shopName, Integer totalNum, BigDecimal payAmount, BigDecimal preferentialAmount,
                     BigDecimal receiptAmount, BigDecimal changeAmount, BigDecimal changePriceSum, BigDecimal receiptAmountWithPromotion) {
        this.orderCode = orderCode;
        this.createName = createName;
        this.employeeName = employeeName;
        this.createTime = createTime;
        this.shopCode = shopCode;
        this.shopName = shopName;
        this.totalNum = totalNum;
        this.payAmount = payAmount;
        this.preferentialAmount = preferentialAmount;
        this.receiptAmount = receiptAmount;
        this.changeAmount = changeAmount;
        this.changePriceSum = changePriceSum;
        this.receiptAmountWithPromotion = receiptAmountWithPromotion;
    }
}
