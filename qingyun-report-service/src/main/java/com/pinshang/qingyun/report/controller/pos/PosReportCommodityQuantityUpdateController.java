package com.pinshang.qingyun.report.controller.pos;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.pos.PosReportCommodityQuantityUpdateService;
import com.pinshang.qingyun.report.util.ViewExcel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/commodityQuantityUpdate/")
@Slf4j
public class PosReportCommodityQuantityUpdateController {

    @Autowired
    private PosReportCommodityQuantityUpdateService posReportCommodityQuantityUpdateService;

    @ApiOperation("单品修改数量明细分页")
    @PostMapping("page")
    public TablePageInfo<CommodityQuantityUpdateDTO> page(@RequestBody CommodityQuantityUpdateIDTO idto) {
         return posReportCommodityQuantityUpdateService.page(idto);
    }

    @ApiOperation("单品修改数量明细导出")
    @PostMapping("exportPage")
    public void exportPage(@RequestBody CommodityQuantityUpdateIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();

        List<CommodityQuantityUpdateDTO> dataList = new ArrayList<>();
        TablePageInfo<CommodityQuantityUpdateDTO> page = posReportCommodityQuantityUpdateService.page(idto);
        CommodityQuantityUpdateDTO head = (CommodityQuantityUpdateDTO) page.getHeader();
        if(head == null){
            head = new CommodityQuantityUpdateDTO();
        }
        head.setShopCode("合计");
        dataList.add(head);
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"单品修改数量明细导出");
            EasyExcel.write(response.getOutputStream(), CommodityQuantityUpdateDTO.class).autoCloseStream(Boolean.FALSE).sheet("单品修改数量明细导出")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("单品修改数量明细导出",e);
        }
    }

    @ApiOperation("单品修改数量明细报表")
    @PostMapping("commodityQuantityUpdateReport")
    public TablePageInfo<CommodityDeleteODTO> commodityQuantityUpdateReport(@RequestBody CommodityDeleteIDTO idto) {
        return posReportCommodityQuantityUpdateService.commodityQuantityUpdateReport(idto);
    }

    @ApiOperation("单品修改数量明细报表导出")
    @PostMapping("commodityQuantityUpdateReportExport")
    public ModelAndView commodityQuantityUpdateReportExport(@RequestBody CommodityDeleteIDTO idto) {
        idto.initExportPage();
        TablePageInfo<CommodityDeleteODTO> pageInfo = posReportCommodityQuantityUpdateService.commodityQuantityUpdateReport(idto);

        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst = new ArrayList<>();
        int i = 1;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "单品修改数量报表" + sdf.format(new Date()) + ".xlsx";

        if (null != pageInfo && pageInfo.getSize() > 0) {
            if (idto.getType() == 1) {
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(5,getFieldValueByName("netSales",pageInfo.getHeader()));
                totalMap.put(6,getFieldValueByName("addNum",pageInfo.getHeader()));
                totalMap.put(7,getFieldValueByName("addAmount",pageInfo.getHeader()));
                totalMap.put(8,getFieldValueByName("reduceNum",pageInfo.getHeader()));
                totalMap.put(9,getFieldValueByName("reduceAmount",pageInfo.getHeader()));
                totalMap.put(10,getFieldValueByName("addRatio",pageInfo.getHeader()));
                totalMap.put(11,getFieldValueByName("reduceRatio",pageInfo.getHeader()));
                setTotalRow(data, totalMap,11);
            }
            if (idto.getType() == 2) {
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(6,getFieldValueByName("netSales",pageInfo.getHeader()));
                totalMap.put(7,getFieldValueByName("addNum",pageInfo.getHeader()));
                totalMap.put(8,getFieldValueByName("addAmount",pageInfo.getHeader()));
                totalMap.put(9,getFieldValueByName("reduceNum",pageInfo.getHeader()));
                totalMap.put(10,getFieldValueByName("reduceAmount",pageInfo.getHeader()));
                totalMap.put(11,getFieldValueByName("addRatio",pageInfo.getHeader()));
                totalMap.put(12,getFieldValueByName("reduceRatio",pageInfo.getHeader()));
                setTotalRow(data, totalMap,12);
            }
            if (idto.getType() == 3) {
                Map<Integer,String> totalMap=new HashMap<>();
                totalMap.put(8,getFieldValueByName("netSales",pageInfo.getHeader()));
                totalMap.put(9,getFieldValueByName("addNum",pageInfo.getHeader()));
                totalMap.put(10,getFieldValueByName("addAmount",pageInfo.getHeader()));
                totalMap.put(11,getFieldValueByName("reduceNum",pageInfo.getHeader()));
                totalMap.put(12,getFieldValueByName("reduceAmount",pageInfo.getHeader()));
                totalMap.put(13,getFieldValueByName("addRatio",pageInfo.getHeader()));
                totalMap.put(14,getFieldValueByName("reduceRatio",pageInfo.getHeader()));
                setTotalRow(data, totalMap,16);
            }

            for (CommodityDeleteODTO dto : pageInfo.getList()) {
                dataLst = new ArrayList<>();

                dataLst.add(dto.getShopCode());
                dataLst.add(dto.getShopName());
                dataLst.add(dto.getStoreCode());
                if (idto.getType() == 3) {
                    dataLst.add(dto.getCasherCode());
                    dataLst.add(dto.getCasherName());
                }
                if (idto.getType() == 2 || idto.getType() == 3) {
                    dataLst.add(dto.getOperateTime());
                }
                dataLst.add(dto.getPosTypeName());
                dataLst.add(dto.getNetSales() + "");
                dataLst.add(null == dto.getAddNum() ? "0" : dto.getAddNum().toString());
                dataLst.add(null == dto.getAddAmount() ? "0" : dto.getAddAmount().toString() );
                dataLst.add(null == dto.getReduceNum() ? "0" : dto.getReduceNum().toString());
                dataLst.add(null == dto.getReduceAmount() ? "0" : dto.getReduceAmount().toString());
                dataLst.add(null == dto.getAddRatio() ? "0" : dto.getAddRatio().toString()+"%");
                dataLst.add(null == dto.getReduceRatio() ? "0" : dto.getReduceRatio().toString()+"%");

                data.put("key_"+ i++, dataLst);
            }
        }

        ExcelSheetTitleEnum sheetTitle = null;
        if (idto.getType() == 1) {
            sheetTitle = ExcelSheetTitleEnum.COMMODITY_QUANTITY_UPDATE_SHOP;
        }
        if (idto.getType() == 2) {
            sheetTitle = ExcelSheetTitleEnum.COMMODITY_QUANTITY_UPDATE_TIME;
        }
        if (idto.getType() == 3) {
            sheetTitle = ExcelSheetTitleEnum.COMMODITY_QUANTITY_UPDATE_CASHER;
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        String filename = fileName+"_"+ sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);

//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        String fileName = "单品修改数量明细报表" + sdf.format(new Date()) + ".xlsx";
//        List<String> tableHeader = new ArrayList<>();
//        tableHeader.add("门店编码");
//        tableHeader.add("门店名称");
//        tableHeader.add("客户编码");
//        if (idto.getType() == 3) {
//            tableHeader.add("收银员编码");
//            tableHeader.add("收银员名称");
//        }
//        if (idto.getType() == 2 || idto.getType() == 3) {
//            tableHeader.add("操作日期");
//        }
//        tableHeader.add("POS机类型");
//        tableHeader.add("净销售额");
//        tableHeader.add("增加单品次数");
//        tableHeader.add("增加单品总金额");
//        tableHeader.add("减少单品次数");
//        tableHeader.add("减少单品总金额");
//        tableHeader.add("增加单品与净销售额比");
//        tableHeader.add("减少单品与净销售额比");
//        List<List<String>> dataList = new ArrayList<>();
//
//        if(pageInfo != null && SpringUtil.isNotEmpty(pageInfo.getList())) {
//            for (CommodityDeleteODTO dto : pageInfo.getList()) {
//                List<String> row = new ArrayList<>();
//                row.add(dto.getShopCode());
//                row.add(dto.getShopName());
//                row.add(dto.getStoreCode());
//                if (idto.getType() == 3) {
//                    row.add(dto.getCasherCode());
//                    row.add(dto.getCasherName());
//                }
//                if (idto.getType() == 2 || idto.getType() == 3) {
//                    row.add(dto.getOperateTime());
//                }
//                row.add(dto.getPosTypeName());
//                row.add(dto.getNetSales() + "");
//                row.add(dto.getAddNum() + "");
//                row.add(dto.getAddAmount() + "");
//                row.add(dto.getReduceNum() + "");
//                row.add(dto.getReduceAmount() + "");
//                row.add(dto.getAddRatio() + "");
//                row.add(dto.getReduceRatio() + "");
//                dataList.add(row);
//            }
//        }
//
//        XSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbook(fileName, tableHeader, dataList, null);
//        ExcelExportUtils.exportExcel(response,fileName,xb);
    }


    private String getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            if (fieldName.equals("addRatio") || fieldName.equals("reduceRatio")) {
                return value==null?"":value.toString()+"%";
            } else {
                return value==null?"":value.toString();
            }

        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return null;
        }
    }
    private void setTotalRow(Map<String, List<String>> data, Map<Integer, String> totalMap, Integer length) {
        List<String> dataLst = new ArrayList<>();
        totalMap.put(1,"合计");
        for(int j=1;j<=length;j++){
            dataLst.add(totalMap.get(j));
        }
        data.put("key_0", dataLst);
    }
}
