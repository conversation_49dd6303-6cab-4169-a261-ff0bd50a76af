package com.pinshang.qingyun.report.enums;

/**
 * 报表类型
 * Created by megnday zhang on 2018/5/24.
 */
public enum ReportTypeEnum {
    CASHIER_WATER(1, "收银流水报表", "t_pos_report_cashier_water"),
    SALES_WATER(2, "销售流水报表", "t_pos_report_sales_water"),
    SALES_SUMMARY(3, "销售汇总", "t_pos_report_sales_summary"),
    GUEST_LIST_SUMMARY(4, "客单分析", "t_pos_report_guest_list_summary"),
    CASHIER_DISCOUNT(5, "收银员让利", "t_pos_report_cashier_discount"),
    ;

    /** 编码 */
    private Integer code;

    /** 名称 */
    private String name;

    /** 名称 */
    private String table;

    ReportTypeEnum(Integer code, String name, String table) {
        this.code = code;
        this.name = name;
        this.table = table;
    }


    public static ReportTypeEnum getReportEnum(Integer code){
        for(ReportTypeEnum reportTypeEnum : ReportTypeEnum.values()){
            if(reportTypeEnum.getCode().equals(code)){
                return reportTypeEnum;
            }
        }
        return null;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }
}
