package com.pinshang.qingyun.report.controller.pos;

import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterIDTO;
import com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterODTO;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.service.pos.PosReportReturnSalesService;
import com.pinshang.qingyun.report.util.ReportUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2021/7/7 20:12
 */
@Slf4j
@RestController
@RequestMapping("/posReportReturn")
public class PosReportReturnSalesController {
@Autowired
private PosReportReturnSalesService reportReturnSalesService;


    @ApiOperation(value = "整理某天退货信息", notes = "整理某天退货信息")
    @GetMapping("/replaceByTime/{oneDay}")
    public Boolean replaceByTime(@PathVariable("oneDay") String oneDay){
        return reportReturnSalesService.replaceByTime(oneDay);
    }


    @ApiOperation(value = "退货列表", notes = "退货列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/list")
    public TablePageInfo<ReturnSalesWaterODTO> list(@RequestBody ReturnSalesWaterIDTO idto){


        return  reportReturnSalesService.queryList(idto);

    }
    @ApiOperation(value = "退货列表导出", notes = "退货列表导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/listExport")
    public ModelAndView listExport(ReturnSalesWaterIDTO idto){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<ReturnSalesWaterODTO> pageInfo = reportReturnSalesService.queryListExport(idto);
        List<ReturnSalesWaterODTO> list = pageInfo.getList();
        ReturnSalesWaterODTO header = (ReturnSalesWaterODTO) pageInfo.getHeader();
        list.add(0,header);
        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(list,data);
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.RETURN_SALES_WATER_REPORT, data);

    }
}
