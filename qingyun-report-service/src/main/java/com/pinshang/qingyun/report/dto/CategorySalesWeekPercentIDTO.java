package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;


/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/9/26
 */
@Data
@ToString
public class CategorySalesWeekPercentIDTO extends Pagination {

    @ApiModelProperty(position = 1, value ="门店id")
    private Long shopId;

    @Change(value = DataQueryConstant.NOW)
    @ApiModelProperty(position = 2, required = true, value ="销售日期(yyyy-MM-dd)")
    private String salesDate;


    @ApiModelProperty(hidden = true)
    private List<Long> shopIdList;

    @ApiModelProperty("门店类型")
    private Integer shopType;

    @ApiModelProperty("省id")
    private Long provinceId;

}
