package com.pinshang.qingyun.report.mapper.shop;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.report.dto.ShopODTO;
import com.pinshang.qingyun.report.model.shop.Shop;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ShopMapper extends MyMapper<Shop>{

    List<Shop> getAllShopList();

    List<Shop> getAllShopListByShopType(@Param("shopType") Integer shopType);

    List<ShopODTO> getShopListByStoreCodes(@Param("storeCodeList") List<String> storeCodeList);

    List<ShopODTO> getShopListByStoreIds(@Param("storeIdList") List<Long> storeIdList);

    List<ShopODTO> getStoreListByShopIdList(@Param("shopIdList") List<Long> shopIdList);

    List<Long> shopIdByManagementMode(@Param("managementMode") Integer managementMode);

    List<Long> queryShopIdListByParam(@Param("shopTypeList") List<Integer> shopTypeList, @Param("managementModeList") List<Integer> managementModeList);
}