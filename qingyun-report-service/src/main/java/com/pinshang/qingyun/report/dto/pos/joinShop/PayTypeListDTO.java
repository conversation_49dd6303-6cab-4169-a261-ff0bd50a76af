package com.pinshang.qingyun.report.dto.pos.joinShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayTypeListDTO {
    private Long id;

    @ApiModelProperty("编码")
    private String payCode;

    @ApiModelProperty("名称")
    private String payName;

    @ApiModelProperty("提现费率 0=<x<1")
    private BigDecimal withdrawRate;

    @ApiModelProperty("是否允许提现 0不允许提现 1允许提现")
    private Integer withdrawStatus;
}
