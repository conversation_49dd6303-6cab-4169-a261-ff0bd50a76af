package com.pinshang.qingyun.report.service.pos;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.report.client.report.service.PosReportClient;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.report.enums.PaytypeEnum;
import com.pinshang.qingyun.report.mapper.pos.CashierDailyGiftCardMapper;
import com.pinshang.qingyun.report.mapper.pos.CashierDailyMapper;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.report.util.DateUtils;
import com.pinshang.qingyun.report.util.ReflectionUtil;
import com.pinshang.qingyun.shop.dto.ShopODTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CashierDailyService {
    @Autowired
    private CashierDailyMapper cashierDailyMapper;

    @Autowired
    private PosReportClient posReportClient;

    @Autowired
    private CashierDailyGiftCardMapper cashierDailyGiftCardMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    /**
     * 获取支付方式
     * “微信”和“支付宝”需要始终显示出来，不受后台启用停用的影响
     * @return
     */
    public List<PayTypeODTO> getPayTypeList() {
        List<PayTypeODTO> payTypeList = posReportClient.getAllPayTypeList();

        AtomicReference<PayTypeODTO> discountCouponCodeFlag = new AtomicReference<>();
        payTypeList = payTypeList.stream().filter(p -> {

            if (PaytypeEnum.DISCOUNT_COUPON_CODE.getCode().equals(p.getPayTypeCode())) {
                discountCouponCodeFlag.set(p);
            }

            return !PaytypeEnum.WECHAT.getCode().equals(p.getPayTypeCode())
                    && !PaytypeEnum.ALIPAY.getCode().equals(p.getPayTypeCode());
        }).collect(Collectors.toList());

        payTypeList.add(new PayTypeODTO(PaytypeEnum.WECHAT.getCode(), PaytypeEnum.WECHAT.getName()));
        payTypeList.add(new PayTypeODTO(PaytypeEnum.ALIPAY.getCode(), PaytypeEnum.ALIPAY.getName()));

        PayTypeODTO discountCoupon = discountCouponCodeFlag.get();
        if (Objects.nonNull(discountCoupon)) {
            payTypeList.remove(discountCoupon);
            payTypeList.add(discountCoupon);
        }
        return payTypeList;
    }

    /**
     * PageInfo<ReportSalesWater> pageInfo = PageHelper.startPage(salesWaterIDTO.getPageNo(), salesWaterIDTO.getPageSize()).doSelectPageInfo(() ->{
     salesWaterReportMapper.listSalesWaterIDTOReport(salesWaterIDTO);
     });

     PageInfo<SalesWaterODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageInfo, SalesWaterODTO.class);
     TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
     SalesWaterHeaderODTO header = salesWaterReportMapper.listSalesWaterIDTOHeaderReport(salesWaterIDTO);
     tablePageInfo.setHeader(header);
     return tablePageInfo;
     */
    /**
     * 收银日报
     * @param cashierDailyIDTO
     * @return
     */
    public TablePageInfo<CashierDailyODTO> selectCashierDailyList(CashierDailyIDTO cashierDailyIDTO) {
        cashierDailyIDTO.setIsCurrentDay(DateUtils.isCurrentDay(cashierDailyIDTO.getStartTime(),cashierDailyIDTO.getEndTime()));
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierDailyIDTO.setShopIdList(shopIdList);
        QYAssert.isTrue(null != cashierDailyIDTO.getStartTime(), "收银日期不能为空！");
        QYAssert.isTrue(null != cashierDailyIDTO.getEndTime(), "收银日期不能为空！");

        PageInfo<CashierDailyODTO> pageDate = new PageInfo<>();
        TablePageInfo tablePageInfo = new TablePageInfo();
        CashierDailySUMODTO header = new CashierDailySUMODTO();

        // 线下售卡、线下营业款
        if(null == cashierDailyIDTO.getMoneyType()){
            pageDate = PageHelper.startPage(cashierDailyIDTO.getPageNo(), cashierDailyIDTO.getPageSize()).doSelectPageInfo(() -> {
                cashierDailyGiftCardMapper.selectCashierDailyAndGiftCardList(cashierDailyIDTO);
            });

            PageInfo<CashierDailyODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierDailyODTO.class);
            tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
            header =  cashierDailyGiftCardMapper.selectSUMCashierDailyAndGiftCardList(cashierDailyIDTO);

        }else if(cashierDailyIDTO.getMoneyType().equals(1) || cashierDailyIDTO.getMoneyType().equals(3)){
            // 1 线下售卡
            pageDate = PageHelper.startPage(cashierDailyIDTO.getPageNo(), cashierDailyIDTO.getPageSize()).doSelectPageInfo(() -> {
                cashierDailyGiftCardMapper.selectCashierDailyGiftCardList(cashierDailyIDTO);
            });

            PageInfo<CashierDailyODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierDailyODTO.class);
            tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
            header =  cashierDailyGiftCardMapper.selectSUMCashierDailyGiftCardList(cashierDailyIDTO);

        }else{
            // 2 线下营业款
            pageDate = PageHelper.startPage(cashierDailyIDTO.getPageNo(), cashierDailyIDTO.getPageSize()).doSelectPageInfo(() -> {
                cashierDailyGiftCardMapper.selectCashierDailyList(cashierDailyIDTO);
            });

            PageInfo<CashierDailyODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierDailyODTO.class);
            tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
            header =  cashierDailyGiftCardMapper.selectSUMCashierDailyList(cashierDailyIDTO);
        }
        if (header == null) {
            header = new CashierDailySUMODTO();
        }
        tablePageInfo.setHeader(header);

        List<TableColumnODTO> tableColumns = null;
        String searchCriteria = cashierDailyIDTO.getSearchCriteria();
        if ("0".equals(searchCriteria)) { // 按pos机
            tableColumns = this.buildTableColumns(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_POS_DY_QUERY, cashierDailyIDTO, header);
        } else if ("1".equals(searchCriteria)) { //按收银员
            tableColumns = this.buildTableColumns(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_DY_QUERY, cashierDailyIDTO, header);
        }else if ("2".equals(searchCriteria)) { //按门店
            tableColumns = this.buildTableColumns(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_SHOP_DY_QUERY, cashierDailyIDTO, header);
        }else if("3".equals(searchCriteria)){ //按日期
            tableColumns = this.buildTableColumns(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_DATE_DY_QUERY, cashierDailyIDTO, header);
        }

        tablePageInfo.setTableColumns(tableColumns);

        return tablePageInfo;
    }


    private List<TableColumnODTO> buildTableColumns(ExcelSheetTitleEnum excelSheetTitle, CashierDailyIDTO cashierDailyIDTO, CashierDailySUMODTO header) {
        //System.out.println(excelSheetTitle.hashCode());
        List<String> props = Arrays.asList(excelSheetTitle.getProps());
        List<String> titles = Arrays.asList(excelSheetTitle.getTitles());

//        log.error("code={}", excelSheetTitle.getCode());
//        log.error("name={}", excelSheetTitle.getName());
//        log.error("getProps={}", Arrays.asList(excelSheetTitle.getProps()));
//        log.error("getTitles={}", Arrays.asList(excelSheetTitle.getTitles()));

        int lastIndex = props.size() - 1;
        List<TableColumnODTO> columns = new ArrayList<>();
        for (int i = 0; i < lastIndex; i++) {
            TableColumnODTO column = new TableColumnODTO(props.get(i), props.get(i), titles.get(i));
            columns.add(column);
        }

        Map<String, Object> headerMap = ReflectionUtil.getKeyAndValue(header);

        // 将统计放到最后
        TableColumnODTO totalColumn = new TableColumnODTO(props.get(lastIndex), props.get(lastIndex), titles.get(lastIndex), headerMap.get("total"));
        columns.add(totalColumn);

        // 添加所有支付方式
        cashierDailyIDTO.getPayTypeList().forEach(item -> {
            String payTypeCode = item.getPayTypeCode();
            Object sum = headerMap.get(payTypeCode);
            sum = sum != null ? sum : "0.00";
            TableColumnODTO column = new TableColumnODTO(payTypeCode.toLowerCase(), payTypeCode.toLowerCase(),
                    item.getPayTypeName(), sum);
            columns.add(column);
        });

        return columns;
    }


    /**
     * 收银日报按门店
     * @param cashierDailyIDTO
     * @return
     */
    /*public TablePageInfo<CashierDailyODTO> selectCashierDailyListByShop(CashierDailyIDTO cashierDailyIDTO) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(CollectionUtils.isEmpty(tokenInfo.getShopIdList())){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierDailyIDTO.setShopIdList(tokenInfo.getShopIdList());

        Assert.isTrue(null != cashierDailyIDTO.getStartTime(), "收银日期不能为空！");
        Assert.isTrue(null != cashierDailyIDTO.getEndTime(), "收银日期不能为空！");
        PageInfo<CashierDailyODTO> pageInfo = PageHelper.startPage(cashierDailyIDTO.getPageNo(), cashierDailyIDTO.getPageSize()).doSelectPageInfo(() -> {
            cashierDailyMapper.selectCashierDailyListByShop(cashierDailyIDTO);
        });
        PageInfo<CashierDailyODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageInfo, CashierDailyODTO.class);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
        CashierDailySUMODTO header =  cashierDailyMapper.selectSUMCashierDailyListByShop(cashierDailyIDTO);
        if (header == null) {
            header = new CashierDailySUMODTO();
        }
        tablePageInfo.setHeader(header);

        List<TableColumnODTO> tableColumns = this.buildTableColumns(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_SHOP_DY_QUERY, cashierDailyIDTO, header);
        tablePageInfo.setTableColumns(tableColumns);
        return tablePageInfo;

    }*/
    /**
     * 收银日报按日期
     * @param cashierDailyIDTO
     * @return
     */
    /*public TablePageInfo<CashierDailyODTO> selectCashierDailyListByDate(CashierDailyIDTO cashierDailyIDTO) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(CollectionUtils.isEmpty(tokenInfo.getShopIdList())){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierDailyIDTO.setShopIdList(tokenInfo.getShopIdList());

        Assert.isTrue(null != cashierDailyIDTO.getStartTime(), "收银日期不能为空！");
        Assert.isTrue(null != cashierDailyIDTO.getEndTime(), "收银日期不能为空！");
        PageInfo<CashierDailyODTO> pageDate = PageHelper.startPage(cashierDailyIDTO.getPageNo(), cashierDailyIDTO.getPageSize()).doSelectPageInfo(() -> {
            cashierDailyMapper.selectCashierDailyListByDate(cashierDailyIDTO);
        });
        PageInfo<CashierDailyODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierDailyODTO.class);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
        CashierDailySUMODTO header =  cashierDailyMapper.selectSUMCashierDailyListByDate(cashierDailyIDTO);
        if (header == null) {
            header = new CashierDailySUMODTO();
        }
        tablePageInfo.setHeader(header);

        List<TableColumnODTO> tableColumns = this.buildTableColumns(ExcelSheetTitleEnum.CASHIER_DAILY_LIST_BY_DATE_DY_QUERY, cashierDailyIDTO, header);
        tablePageInfo.setTableColumns(tableColumns);
        return tablePageInfo;
    }*/

    /**
     * 收银日报按付款方式
     * @param cashierDailyIDTO
     * @return
     */
    /*public TablePageInfo<CashierDailyODTO> selectCashierDailyListByPayment(CashierDailyIDTO cashierDailyIDTO) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(CollectionUtils.isEmpty(tokenInfo.getShopIdList())){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierDailyIDTO.setShopIdList(tokenInfo.getShopIdList());

        Assert.isTrue(null != cashierDailyIDTO.getStartTime(), "收银日期不能为空！");
        Assert.isTrue(null != cashierDailyIDTO.getEndTime(), "收银日期不能为空！");
        PageInfo<CashierDailyODTO> pageDate = PageHelper.startPage(cashierDailyIDTO.getPageNo(), cashierDailyIDTO.getPageSize()).doSelectPageInfo(() -> {
            cashierDailyMapper.selectCashierDailyListByPayment(cashierDailyIDTO);
        });
        PageInfo<CashierDailyODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierDailyODTO.class);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
        CashierDailySUMODTO header =  cashierDailyMapper.selectSUMCashierDailyListByPayment(cashierDailyIDTO);
        if (header == null) {
            header = new CashierDailySUMODTO();
        }
        tablePageInfo.setHeader(header);
        return tablePageInfo;
    }
*/
    /**
     * 收银员对账
     * @param cashierReconciledIDTO
     * @return
     */
    public TablePageInfo<CashierReconciledODTO> selectCashierReconciledList(CashierReconciledIDTO cashierReconciledIDTO) {
        cashierReconciledIDTO.setIsCurrentDay(DateUtils.isCurrentDay(cashierReconciledIDTO.getStartTime(),cashierReconciledIDTO.getEndTime()));

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierReconciledIDTO.setShopIdList(shopIdList);

        if(StringUtils.isEmpty(cashierReconciledIDTO.getStartTime()) || StringUtils.isEmpty(cashierReconciledIDTO.getEndTime()) ){
            QYAssert.isTrue(false, "收银日期不能为空!");
        }

        int diff = DateUtil.getDayDif(DateUtil.parseDate(cashierReconciledIDTO.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(cashierReconciledIDTO.getStartTime(), DateUtil.DEFAULT_DATE_FORMAT));
        Assert.isTrue(diff <= 2, "收银日期的跨度不能超过3天");

        PageInfo<CashierReconciledODTO> pageDate = PageHelper.startPage(cashierReconciledIDTO.getPageNo(), cashierReconciledIDTO.getPageSize()).doSelectPageInfo(() -> {
            cashierDailyGiftCardMapper.selectCashierReconciledList(cashierReconciledIDTO);
        });

        Map<String,CashierWaterCountODTO> cashierWaterCountMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(pageDate.getList())){
            // 查询销售笔数和退货笔数
            cashierWaterCountMap = getCashierWaterCountMap(cashierReconciledIDTO);
            List<CashierReconciledODTO> list = pageDate.getList();
            for(CashierReconciledODTO odo : list){
                String key = odo.getShopId() + odo.getSaleNumber() + odo.getMacCode() + odo.getPayType();
                CashierWaterCountODTO countODTO = cashierWaterCountMap.get(key);
                if(null != countODTO){
                    odo.setSaleCount(countODTO.getSaleCount());
                    odo.setReturnCount(countODTO.getReturnCount());
                }
            }
        }
        PageInfo<CashierReconciledODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierReconciledODTO.class);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
        CashierReconciledSUMODTO header =  cashierDailyGiftCardMapper.selectSUMCashierReconciledList(cashierReconciledIDTO);
        if (header == null) {
            header = new CashierReconciledSUMODTO();
        }else {
            Long saleCount = 0L;
            Long returnCount = 0L;
            for (CashierWaterCountODTO oto : cashierWaterCountMap.values()) {
                saleCount = saleCount + oto.getSaleCount();
                returnCount = returnCount+ oto.getReturnCount();
            }
            header.setSaleCount(saleCount);
            header.setReturnCount(returnCount);
        }
        tablePageInfo.setHeader(header);
        return tablePageInfo;
    }

    /**
     * 获取收银员对账销售笔数和退货笔数
     * @param cashierReconciledIDTO
     * @return
     */
    public Map<String,CashierWaterCountODTO> getCashierWaterCountMap(CashierReconciledIDTO cashierReconciledIDTO){
        Map<String,CashierWaterCountODTO> cashierWaterCountMap = new HashMap<>(1024);
        List<CashierWaterCountODTO> cashierWaterCountList = cashierDailyGiftCardMapper.selectCashierWaterCount(cashierReconciledIDTO);
        if(CollectionUtils.isNotEmpty(cashierWaterCountList)){
            for(CashierWaterCountODTO oto : cashierWaterCountList){
                String key = oto.getShopId() + oto.getEmployeeNumber() + oto.getMacCode() + oto.getPayType();
                cashierWaterCountMap.put(key,oto);
            }
        }
        return cashierWaterCountMap;
    }
    /**
     * 前台收银对账记录
     * @param cashierReconciledIDTO
     * @return
     */
    public TablePageInfo<CashierReconciledODTO> selectAccountRecordList(CashierReconciledIDTO cashierReconciledIDTO) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierReconciledIDTO.setShopIdList(shopIdList);
        return  posReportClient.selectAccountRecordList(cashierReconciledIDTO);
    }
    /**
     * 收银员日对账
     * @param cashierReconciledIDTO
     * @return
     */
    public TablePageInfo<CashierReconciledODTO> selectDiurnalReconciliationList(CashierReconciledIDTO cashierReconciledIDTO) {
        cashierReconciledIDTO.setIsCurrentDay(DateUtils.isCurrentDay(cashierReconciledIDTO.getStartTime(),cashierReconciledIDTO.getEndTime()));

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierReconciledIDTO.setShopIdList(shopIdList);

        PageInfo<CashierReconciledODTO> pageDate = PageHelper.startPage(cashierReconciledIDTO.getPageNo(), cashierReconciledIDTO.getPageSize()).doSelectPageInfo(() -> {
            List<CashierReconciledODTO>  list = cashierDailyGiftCardMapper.selectDiurnalReconciliationList(cashierReconciledIDTO);
            list.forEach( p->{
                if(null!= p.getNewDate()){
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = null;
                    try {
                        date = dateFormat.parse(p.getNewDate());
                        p.setCreateTime(date);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
            });
        });
        PageInfo<CashierReconciledODTO> salesWaterODTOPageInfo = BeanUtil.copyProperties(pageDate, CashierReconciledODTO.class);
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);
        CashierReconciledSUMODTO header =  cashierDailyGiftCardMapper.selectSUMDiurnalReconciliationList(cashierReconciledIDTO);
        if (header == null) {
            header = new CashierReconciledSUMODTO();
        }
        tablePageInfo.setHeader(header);
        return tablePageInfo;
    }

    public List<ShopODTO> selectShopList(){
        return cashierDailyMapper.selectShopList();
    }

    public List<CashierODTO> selectCashierList(UserIDTO userIDTO){
        return  cashierDailyMapper.selectCashierList(userIDTO);
    }
}
