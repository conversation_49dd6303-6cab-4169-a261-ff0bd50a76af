package com.pinshang.qingyun.report.util;

import com.jcraft.jsch.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;


public class SftpUtils {

    private static ChannelSftp sftp = null;
    private static Session session = null;
    // 登录
    public static ChannelSftp login(String host, int port, String userName, String password) throws JSchException{
        JSch jSch = new JSch();
        // 设置用户名和主机，端口号一般都是22
        session = jSch.getSession(userName, host, port);
        // 设置密码
        session.setPassword(password);
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        session.connect();
        Channel channel = session.openChannel("sftp");
        channel.connect();

        sftp = (ChannelSftp) channel;
        System.out.println("connect success....");
        return sftp;
    }

    // 退出登录
    public static void logout() {
        if (sftp != null) {
            if (sftp.isConnected()) {
                sftp.disconnect();
            }
        }
        if (session != null) {
            if (session.isConnected()) {
                session.disconnect();
            }
        }
    }

    public static List<String> readFile(String host, int port, String userName, String password, String fPath){
        BufferedReader reader = null;
        List<String> ls = new ArrayList<>();
        // 登录sftp服务器
        try {
            ChannelSftp sftp = SftpUtils.login(host, port, userName, password);
        // 构建文件输入流，读取文件内容取
            reader = new BufferedReader(new InputStreamReader(sftp.get(fPath), StandardCharsets.UTF_8));
            String str;
            while((str = reader.readLine()) != null) {
                ls.add(str);
            }
            reader.close();
            //退出sftp
            SftpUtils.logout();

        } catch (JSchException e) {
            e.printStackTrace();
        } catch (SftpException e) {
            e.printStackTrace();
        } catch (IOException e){
            e.printStackTrace();
        }
        return ls;
    }
}