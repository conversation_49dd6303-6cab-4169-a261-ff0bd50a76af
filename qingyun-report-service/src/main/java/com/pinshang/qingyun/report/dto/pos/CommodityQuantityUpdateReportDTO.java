package com.pinshang.qingyun.report.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CommodityQuantityUpdateReportDTO {

    private Long shopId;

    private String shopCode;

    private String shopName;

    private String storeCode;

    private String operateTime;

    private Integer operateType;

    @ApiModelProperty("POS机类型")
    private String posTypeName;

    private Long casherId;

    @ApiModelProperty("收银员编号")
    private String casherCode;

    @ApiModelProperty("收银员名称")
    private String casherName;

    private Long num;

    private BigDecimal amount;

//    @ApiModelProperty("增加次数")
//    private Long addNum;
//
//    @ApiModelProperty("增加总金额")
//    private BigDecimal addAmount;
//
//    @ApiModelProperty("减少次数")
//    private Long reduceNum;
//
//    @ApiModelProperty("减少总金额")
//    private BigDecimal reduceAmount;
//
//    @ApiModelProperty("增加的比率")
//    private BigDecimal addRatio;
//
//    @ApiModelProperty("减少的比率")
//    private BigDecimal reduceRatio;
}
