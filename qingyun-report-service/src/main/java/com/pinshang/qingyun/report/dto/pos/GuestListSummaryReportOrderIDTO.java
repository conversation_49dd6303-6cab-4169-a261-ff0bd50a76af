package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GuestListSummaryReportOrderIDTO extends Pagination {

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("销售日期开始时间yyyy-MM-dd HH:mm:ss")
    private String beginDate;//销售日期开始

    @ApiModelProperty("销售日期结束时间yyyy-MM-dd HH:mm:ss")
    private String endDate;//销售日期结束

    @ApiModelProperty("pos机号")
    private String macCode;//pos机号

    /**
     * 1:按pos机
     * 2:门店
     * 3:按时间段
     */
    @ApiModelProperty("汇总类型：1:按pos机2:门店3:按时间段")
    private Integer summaryType;//汇总类型

    private List<Long> shopIdList;

    private Boolean isCurrentDay = false;

    private Integer shopType;

    private Long provinceId;
}
