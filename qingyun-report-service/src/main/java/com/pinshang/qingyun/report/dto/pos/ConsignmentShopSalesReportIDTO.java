package com.pinshang.qingyun.report.dto.pos;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConsignmentShopSalesReportIDTO extends Pagination {
    @ApiModelProperty("销售时间开始 yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("销售时间结束 yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("部门Code")
    private String orgCode;

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;
}
