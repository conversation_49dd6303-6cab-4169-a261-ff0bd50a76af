package com.pinshang.qingyun.report.service.pos;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.SystemIdNewEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.report.dto.HandDiscountIDTO;
import com.pinshang.qingyun.report.dto.HandDiscountODTO;
import com.pinshang.qingyun.report.dto.export.GuestListSummaryExportRespVo;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderIDTO;
import com.pinshang.qingyun.report.dto.finance.PosSalesOrderODTO;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.mapper.HandDiscountMapper;
import com.pinshang.qingyun.report.mapper.pos.CashierDiscountReportMapper;
import com.pinshang.qingyun.report.mapper.pos.CashierWaterReportMapper;
import com.pinshang.qingyun.report.mapper.pos.SalesWaterReportMapper;
import com.pinshang.qingyun.report.mapper.shop.ShopMapper;
import com.pinshang.qingyun.report.service.CommodityService;
import com.pinshang.qingyun.report.util.BeanUtil;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.CheckUserPermissionIDTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.report.dto.ShopODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class PosReportService {

    @Autowired
    private CashierWaterReportMapper cashierWaterReportMapper;

    @Autowired
    private SalesWaterReportMapper salesWaterReportMapper;

    @Autowired
    private HandDiscountMapper handDiscountMapper;

    @Autowired
    private PosOperationRecordService operationRecordService;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CashierDiscountReportMapper cashierDiscountReportMapper;
    @Autowired
    private ShopClient shopClient;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private SMMUserClient sMMUserClient;


    public TablePageInfo<SalesWaterODTO> listSalesWaterIDTOReport(SalesWaterIDTO salesWaterIDTO) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        salesWaterIDTO.setShopIdList(shopIdList);

        PageInfo<SalesWaterODTO> salesWaterODTOPageInfo = PageHelper.startPage(salesWaterIDTO.getPageNo(), salesWaterIDTO.getPageSize()).doSelectPageInfo(() ->{
            salesWaterReportMapper.listSalesWaterIDTOReport(salesWaterIDTO);
        });
        if(CollectionUtils.isNotEmpty(salesWaterODTOPageInfo.getList())){
            List<String> commodityCodes = salesWaterODTOPageInfo.getList().stream().map(SalesWaterODTO::getCommodityCode).collect(Collectors.toList());
            Set<String> set = new HashSet<>(commodityCodes);
            List<CommodityPackageSpecDTO> taxList = commodityService.getCommodityListByCodes(set);
            Map<String, BigDecimal> map = taxList.stream().collect(Collectors.toMap(CommodityPackageSpecDTO::getCommodityCode, CommodityPackageSpecDTO::getCommodityPackageSpec));
            List<Long> commodityIdList = taxList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<String, Long> commMap = taxList.stream().collect(Collectors.toMap(CommodityPackageSpecDTO::getCommodityCode, CommodityPackageSpecDTO::getCommodityId));
            Map<Long, CommoditySupplierODto> csMap = commoditySupplierClient.queryCommodityDefaultSupplierByCommodityIdList(commodityIdList);

            for(SalesWaterODTO dto:salesWaterODTOPageInfo.getList()){
                CommoditySupplierODto csDto = csMap.get(commMap.get(dto.getCommodityCode()));
                dto.setSupplierName(null != csDto ? csDto.getSupplierName() : "");
                //反算销售份数
                BigDecimal commodityPackageSpec = map.get(dto.getCommodityCode());
                if (null != commodityPackageSpec && null != dto.getQuantity()) {
                    Integer number = new BigDecimal(dto.getQuantity()).divide(commodityPackageSpec,0,BigDecimal.ROUND_UP).intValue();
                    dto.setNumber(number);
                }
            }
        }
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(salesWaterODTOPageInfo, TablePageInfo.class);

        //判断是否有合计的权限
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        CheckUserPermissionIDTO permission = new CheckUserPermissionIDTO();
        permission.setUserId(tokenInfo.getUserId());
        permission.setSystemId(SystemIdNewEnums.品鲜.getCode());
        permission.setPermissionCode("sellingWater:amount");
        Boolean userPermission = sMMUserClient.checkUserPermission(permission);
        if (userPermission) {
            SalesWaterHeaderODTO header = salesWaterReportMapper.listSalesWaterIDTOHeaderReport(salesWaterIDTO);
            if (header == null) {
                header = new SalesWaterHeaderODTO();
            }
            tablePageInfo.setHeader(header);
        }
        return tablePageInfo;
    }

    public TablePageInfo<CashierWaterODTO> listCashierWaterReport(CashierWaterIDTO cashierWaterIDTO) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            TablePageInfo info=new TablePageInfo();
            info.setList(null);
            return info;
        }
        cashierWaterIDTO.setShopIdList(shopIdList);

        PageInfo<CashierWaterODTO> pageInfo = PageHelper.startPage(cashierWaterIDTO.getPageNo(), cashierWaterIDTO.getPageSize()).doSelectPageInfo(() ->{
            cashierWaterReportMapper.listCashierWaterReport(cashierWaterIDTO);
        });

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);

        //判断是否有合计的权限
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        CheckUserPermissionIDTO permission = new CheckUserPermissionIDTO();
        permission.setUserId(tokenInfo.getUserId());
        permission.setSystemId(SystemIdNewEnums.品鲜.getCode());
        permission.setPermissionCode("cashFlow:amount");
        Boolean userPermission = sMMUserClient.checkUserPermission(permission);
        if (userPermission) {
            CashierWaterHeaderODTO header = cashierWaterReportMapper.listCashierWaterReportHeader(cashierWaterIDTO);
            if (header == null) {
                header = new CashierWaterHeaderODTO();
            }
            tablePageInfo.setHeader(header);
        }

        return tablePageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cashierWaterDayReport(String saleTime) {
        // 1. 删除saleTime这天的数据
        cashierWaterReportMapper.deleteCashierWaterDayReportBySaleTime(saleTime);

        // 2. 重新插入
        cashierWaterReportMapper.insertCashierWaterDayReport(saleTime+" 00:00:00",saleTime+" 23:59:59");
    }

    /**
     *查询收银流水日汇总(收银月报使用)
     * @param beginTime
     * @param endTime
     * @param type: 1,对账总表  0，对账明细
     * @return
     */
    public List<CashierWaterDayReportODTO> queryCashierWaterDayReport(Long shopId,String beginTime, String endTime, Integer type) {
        return cashierWaterReportMapper.queryCashierWaterDayReport(shopId,beginTime+" 00:00:00",endTime+" 23:59:59",type);
    }

    public TablePageInfo<CommodityDeleteODTO> signalCommodityDelete(CommodityDeleteIDTO idto) {
        return operationRecordService.signalCommodityDelete(idto);
    }

    public void exportExcelSignalCommodityDelete(CommodityDeleteIDTO idto, HttpServletResponse response) throws IOException {
        TablePageInfo<CommodityDeleteODTO> pageInfo = operationRecordService.signalCommodityDelete(idto);

        List<CommodityDeleteODTO> exportList = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        CommodityDeleteODTO header = (CommodityDeleteODTO) pageInfo.getHeader();
        if (header != null) {
            header.setShopCode("合计");
            exportList.add(0, header);
        }

        String fileName = String.format("单品删除记录_%s", new DateTime().toString(DatePattern.PURE_DATETIME_PATTERN));
        ExcelUtil.setFileNameAndHead(response,  fileName);
        EasyExcel.write(response.getOutputStream(), CommodityDeleteODTO.class)
                .autoCloseStream(Boolean.TRUE).sheet("单品删除记录").doWrite(exportList);
    }

    public TablePageInfo<CommodityDeleteODTO> signalCommodityDeleteTotal(CommodityDeleteIDTO idto) {
        return operationRecordService.signalCommodityDeleteTotal(idto);
    }

    public TablePageInfo<HandDiscountODTO> handDiscountReport(HandDiscountIDTO idto) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return new TablePageInfo();
        }
        if (idto.getShopId() == null) {
            if (SpringUtil.isNotEmpty(shopIdList)) {
                idto.setShopIdList(shopIdList);
            } else {
                return new TablePageInfo();
            }
        }else{
            if(!shopIdList.contains(idto.getShopId())){
                return new TablePageInfo();
            }
        }


        PageInfo<HandDiscountODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            handDiscountMapper.handDiscountReport(idto);
        });

        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            List<Long> shopIds = pageInfo.getList().stream().map(HandDiscountODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));
            pageInfo.getList().forEach(e -> {
                e.setStoreCode(shopMap.get(e.getShopId()));
            });
        }

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        HandDiscountODTO header = handDiscountMapper.handDiscountReportHeader(idto);
        if (header == null) {
            header = new HandDiscountODTO();
        }
        tablePageInfo.setHeader(header);


        return tablePageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertHandDiscountReport(Long shopId, Long userId, String cashDate, BigDecimal promotionPrice) {
        String lockKey = "POS:HANDDISCOUNT:"+ shopId + userId + cashDate;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                // 1. 检查是否存在，不存在插入，存在更新
                Integer count = handDiscountMapper.findCount(shopId, userId, cashDate);
                if (count != null && count > 0) {
                    handDiscountMapper.updateDiscountAmount(shopId, userId, cashDate, promotionPrice);
                } else {
                    handDiscountMapper.insertDiscountAmount(shopId, userId, cashDate, promotionPrice);
                }
            }finally{
                lock.unlock();
            }
        }
    }

    /**
     * pos称重商品价格报表
     * @param idto
     * @return
     */
    public TablePageInfo<WeightPriceODTO> listWeightPriceReport(WeightPriceIDTO idto) {

        QYAssert.isTrue(StringUtils.isNotBlank(idto.getBeginTime()), "请选择销售日期");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getEndTime()), "请选择销售日期");
        idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
        idto.setEndTime(idto.getEndTime()+ " 23:59:59");

        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 30, "收银日期的跨度不能超过31天");

        PageInfo<WeightPriceODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            salesWaterReportMapper.listWeightPriceReport(idto);
        });
        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);

        List<WeightPriceODTO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            WeightPriceODTO header = salesWaterReportMapper.listlistWeightPriceHeaderReport(idto);
            if (header == null) {
                header = new WeightPriceODTO();
            }
            tablePageInfo.setHeader(header);
        }
        return tablePageInfo;
    }

    /**
     *门店自称重高价统计
     * @param idto
     * @return
     */
    public TablePageInfo<WeightOverPriceODTO> listWeightOverPriceReport(WeightPriceIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getBeginTime()), "请选择销售日期");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getEndTime()), "请选择销售日期");
        idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
        idto.setEndTime(idto.getEndTime()+ " 23:59:59");

        int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
        QYAssert.isTrue(diff <= 30, "收银日期的跨度不能超过31天");

        PageInfo<WeightOverPriceODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            salesWaterReportMapper.listWeightOverPriceReport(idto);
        });

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        List<WeightOverPriceODTO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            for(WeightOverPriceODTO overPriceODTO : list){
                overPriceODTO.setTransactionAmount(overPriceODTO.getTransactionAmount() == null ? BigDecimal.ZERO : overPriceODTO.getTransactionAmount());
                overPriceODTO.setRetailAmount(overPriceODTO.getRetailAmount() == null ? BigDecimal.ZERO : overPriceODTO.getRetailAmount());
                overPriceODTO.setOverAmount(overPriceODTO.getOverAmount() == null ? BigDecimal.ZERO : overPriceODTO.getOverAmount());
            }
            WeightOverPriceODTO header = salesWaterReportMapper.listWeightOverPriceHeaderReport(idto);
            if (header == null) {
                header = new WeightOverPriceODTO();
            }
            tablePageInfo.setHeader(header);
        }
        return tablePageInfo;
    }

    /**
     * 门店自称重高价统计(明细)
     * @return
     */
    public List<WeightOverPriceODTO> listWeightOverPriceReportDetail(Long shopId, String beginTime, String endTime) {
        QYAssert.isTrue(shopId != null, "门店id不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(beginTime), "销售日期不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(endTime), "销售日期不能为空");
        beginTime = beginTime + " 00:00:00";
        endTime  = endTime + " 23:59:59";

        List<WeightOverPriceODTO> list = salesWaterReportMapper.listWeightOverPriceDetail(shopId, beginTime, endTime);

        double transactionAmount = list.stream().mapToDouble(item -> item.getTransactionAmount().doubleValue()).sum();
        double retailAmount = list.stream().mapToDouble(item -> item.getRetailAmount().doubleValue()).sum();
        double overAmount = list.stream().mapToDouble(item -> item.getOverAmount().doubleValue()).sum();

        WeightOverPriceODTO weightOverPrice = new WeightOverPriceODTO();
        weightOverPrice.setTransactionAmount(new BigDecimal(transactionAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
        weightOverPrice.setRetailAmount(new BigDecimal(retailAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
        weightOverPrice.setOverAmount(new BigDecimal(overAmount).setScale(2,BigDecimal.ROUND_HALF_UP));

        list.add(0,weightOverPrice);
        return list;
    }

    /**
     * 手动优惠报表明细
     * @param idto
     * @return
     */
    public TablePageInfo<HandDiscountDetailODTO> handDiscountDetailReport(HandDiscountDetailIDTO idto) {
        // 参数校验
        idto.checkParam();
        if(StringUtils.isNotBlank(idto.getBeginTime()) && StringUtils.isNotBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime() + " 00:00:00");
            idto.setEndTime(idto.getEndTime() + " 23:59:59");
        }
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return new TablePageInfo();
        }

        if (!StringUtil.isNullOrEmpty(idto.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(idto.getOrgCode());
            if (CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);
                if (CollectionUtils.isEmpty(shopIdList)) {
                    return new TablePageInfo();
                }
            }else {
                return new TablePageInfo();
            }
        }
        idto.setShopIdList(shopIdList);

        PageInfo<HandDiscountDetailODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            cashierDiscountReportMapper.listHandDiscountDetailReport(idto);
        });

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        List<HandDiscountDetailODTO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList);

            List<Long> shopIds = list.stream().map(HandDiscountDetailODTO::getShopId).collect(Collectors.toList());
            List<ShopODTO> shopODTOList = shopMapper.getStoreListByShopIdList(shopIds);
            Map<Long, String> shopMap = shopODTOList.stream().collect(Collectors.toMap(ShopODTO::getShopId, ShopODTO::getStoreCode));

            for(HandDiscountDetailODTO handDiscountDetail : list){
                String[] barCode = barCodeMap.get(handDiscountDetail.getCommodityId()).split(",");
                handDiscountDetail.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                handDiscountDetail.setBarCodeList(barCodeList);
                handDiscountDetail.setStoreCode(shopMap.get(handDiscountDetail.getShopId()));
            }
            HandDiscountDetailODTO header = cashierDiscountReportMapper.listHandDiscountDetailReportSum(idto);
            header.setHandleRetailPercent(header.getHandleDiscountAmount().divide(header.getRetailAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2)+"%");
            tablePageInfo.setHeader(header);
        }
        return tablePageInfo;
    }

    /**
     * 云超配货>查询大类销售额
     * @param idto
     * @return
     */
    public List<CloudAllocationODTO> queryPosCateSummary(CloudAllocationIDTO idto) {
        return salesWaterReportMapper.queryPosCateSummary(idto);
    }

    /**
     * 订货参考>查询pos销售数量
     * @param idto
     * @return
     */
    public List<PosSaleWaterODTO> queryPosSalesWaterData(PosSaleWaterQueryIDTO idto) {
        QYAssert.notNull(idto.getShopId(), "店铺id不能为空!");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(idto.getCommodityIdList()), "商品ids不能为空!");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getSaleTime()), "销售时间不能为空!");

        return salesWaterReportMapper.queryPosSalesWaterData(idto);
    }

    /**
     * 云集-结算数据
     *提供代销商类型订单数据：
     * 3、POS线下订单，成交金额（包含退货，退货成交金额为负）
     */
    public List<ConsignmentOrderODTO> queryConsignmentOrderSettle(ConsignmentOrderIDTO idto) {
        return salesWaterReportMapper.queryConsignmentOrderSettle(idto);
    }

    public List<InventoryCompPosReportODTO> queryPosReportShopCommoditySumQuantity(InventoryCompPosReportIDTO idto) {
        return salesWaterReportMapper.queryPosReportShopCommoditySumQuantity(idto);
    }

    /***
     * pos销售订单流水原始数据
     * 供大店财务内部往来 使用
     * @param idto
     * @return
     */
    public List<PosSalesOrderODTO> selectShopPosSalesOrderList(PosSalesOrderIDTO idto) {
        QYAssert.notNull(idto.getBusinessType(), "请输入业务类型");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getBusinessDate()), "请输入查询日期");
        QYAssert.notNull(idto.getLimitQuantity(), "请输入查询的条数");
        if(idto.getBusinessType().intValue() != 3 && idto.getBusinessType().intValue() != 5){
            return new ArrayList<>();
        }
        List<PosSalesOrderODTO> posSalesOrderList = salesWaterReportMapper.selectShopPosSalesOrderList(idto);
        if (SpringUtil.isEmpty(posSalesOrderList)) {
            return posSalesOrderList;
        }
        List<CommodityPackageSpecDTO> commodityList = commodityService.getCommodityListByCodes(posSalesOrderList.stream().map(item -> item.getCommodityCode()).collect(Collectors.toSet()));
        if (SpringUtil.isEmpty(commodityList)) {
            return posSalesOrderList;
        }
        Map<String, Long> commodityMap = commodityList.stream().collect(Collectors.toMap(CommodityPackageSpecDTO::getCommodityCode, CommodityPackageSpecDTO::getCommodityId, (v1, v2) -> v2));
        posSalesOrderList.stream().forEach(p -> {
            p.setBusinessType(idto.getBusinessType());
            p.setCommodityId(commodityMap.get(p.getCommodityCode()));
        });
        return posSalesOrderList;
    }
}
