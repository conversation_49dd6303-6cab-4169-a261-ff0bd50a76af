package com.pinshang.qingyun.report.dto.settle;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 */
@Data
public class ShopStockInReportQueryIDTO extends Pagination {

    @ApiModelProperty("组织code")
    private String orgCode;

    @ApiModelProperty("门店ID")
    private Long shopId;


    @ApiModelProperty(value = "结算开始时间 yyyy-MM-dd")
    private String settleTimeBegin;
    @ApiModelProperty(value = "结算结束时间 yyyy-MM-dd")
    private String settleTimeEnd;


    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品一级分类ID")
    private Long cateId1;
    @ApiModelProperty(value = "商品二级分类ID")
    private Long cateId2;
    @ApiModelProperty(value = "商品三级分类ID")
    private Long cateId3;

    @ApiModelProperty("门店id列表")
    private List<Long> shopIdList;

    @ApiModelProperty(value = "1 商品 2 大类  3 中类  4 小类 5 门店")
    private Integer reportType;

    @ApiModelProperty("商品id列表")
    private List<Long> commodityIdList;

    /**门店类型:1-门店，2-鲜食店**/
    private Integer shopType;

    /** 报表类型 1:云超订单取消明细表  2:云超补差退款明细表 3:云超配送失败明细表 */
    public static final class ReportType{
        public static final int COMMODITY = 1;
        public static final int FIRST_CATE = 2;
        public static final int SECOND_CATE = 3;
        public static final int THIRD_CATE = 4;
    }
}
