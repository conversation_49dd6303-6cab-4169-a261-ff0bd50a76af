package com.pinshang.qingyun.report.controller.pos;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.report.dto.TablePageInfo;
import com.pinshang.qingyun.report.dto.pos.*;
import com.pinshang.qingyun.report.service.pos.PosHelpCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

;

/**
 * @ClassName PosHelpCardController
 * <AUTHOR>
 * @Date 2023/2/22 14:42
 * @Description PosHelpCardController
 * @Version 1.0
 */
@Api(value = "posHelpCard")
@RestController
@RequestMapping("posHelpCard")
@Slf4j
public class PosHelpCardController {
    @Autowired
    private PosHelpCardService posHelpCardService;

    @Autowired
    private IRenderService renderService;

    @ApiOperation(value = "帮困卡收银流水", notes = "帮困卡收银流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/helpCardCashierWaterPage")
    @MethodRender
    public TablePageInfo<HelpCardCashierWaterPageODTO> helpCardCashierWaterPage(@RequestBody HelpCardCashierWaterPageIDTO idto){
        return posHelpCardService.helpCardCashierWaterPage(idto);
    }

    @ApiOperation(value = "帮困卡收银流水导出", notes = "帮困卡收银流水导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/helpCardCashierWaterPage")
    public void helpCardCashierWaterExport(HelpCardCashierWaterPageIDTO idto, HttpServletResponse response) throws IOException {

        idto.initExportPage();

        TablePageInfo<HelpCardCashierWaterPageODTO> page = posHelpCardService.helpCardCashierWaterPage(idto);
        List<HelpCardCashierWaterPageODTO> dataList = new ArrayList<>();
        renderService.render(page.getList(), "/export/helpCardCashierWaterPage");
        BigDecimal sum = (BigDecimal) page.getHeader();
        HelpCardCashierWaterPageODTO head = new HelpCardCashierWaterPageODTO();
        head.setAreaName("合计");
        head.setAmount(sum);
        dataList.add(head);
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"帮困卡收银流水导出");
            EasyExcel.write(response.getOutputStream(), HelpCardCashierWaterPageODTO.class).autoCloseStream(Boolean.FALSE).sheet("帮困卡收银流水导出")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("帮困卡收银流水导出异常",e);
        }
    }

    @ApiOperation(value = "帮困卡商品交易流水", notes = "帮困卡商品交易流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/helpCardCommoditySummary")
    @MethodRender
    public TablePageInfo<HelpCardCommoditySummaryODTO> helpCardCommoditySummary(@RequestBody HelpCardCommoditySummaryIDTO idto){
        return posHelpCardService.helpCardCommoditySummary(idto);
    }

    @ApiOperation(value = "帮困卡商品交易汇总导出", notes = "帮困卡商品交易汇总导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/helpCardCommoditySummary")
    public void helpCardCommoditySummaryExport(HelpCardCommoditySummaryIDTO idto, HttpServletResponse response) throws IOException {

        idto.initExportPage();

        TablePageInfo<HelpCardCommoditySummaryODTO> page = posHelpCardService.helpCardCommoditySummary(idto);
        List<HelpCardCommoditySummaryODTO> dataList = new ArrayList<>();
        renderService.render(page.getList(), "/export/helpCardCommoditySummary");
        BigDecimal sum = (BigDecimal) page.getHeader();
        HelpCardCommoditySummaryODTO head = new HelpCardCommoditySummaryODTO();
        head.setAreaName("合计");
        head.setAmount(sum);
        dataList.add(head);
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"帮困卡商品交易汇总导出");
            EasyExcel.write(response.getOutputStream(), HelpCardCommoditySummaryODTO.class).autoCloseStream(Boolean.FALSE).sheet("帮困卡商品交易汇总导出")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("帮困卡商品交易汇总导出",e);
        }
    }

    @ApiOperation(value = "帮困卡门店交易流水", notes = "帮困卡门店交易流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/helpCardShopSummary")
    @MethodRender
    public TablePageInfo<HelpCardShopSummaryODTO> helpCardShopSummary(@RequestBody HelpCardShopSummaryIDTO idto){
        return posHelpCardService.helpCardShopSummary(idto);
    }

    @ApiOperation(value = "帮困卡门店交易汇总导出", notes = "帮困卡门店交易汇总导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/helpCardShopSummary")
    public void helpCardShopSummaryExport(HelpCardShopSummaryIDTO idto, HttpServletResponse response) throws IOException {

        idto.initExportPage();

        TablePageInfo<HelpCardShopSummaryODTO> page = posHelpCardService.helpCardShopSummary(idto);
        List<HelpCardShopSummaryODTO> dataList = new ArrayList<>();
        renderService.render(page.getList(), "/export/helpCardShopSummary");
        BigDecimal sum = (BigDecimal) page.getHeader();
        HelpCardShopSummaryODTO head = new HelpCardShopSummaryODTO();
        head.setAreaName("合计");
        head.setAmount(sum);
        dataList.add(head);
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"帮困卡门店交易汇总导出");
            EasyExcel.write(response.getOutputStream(), HelpCardShopSummaryODTO.class).autoCloseStream(Boolean.FALSE).sheet("帮困卡门店交易汇总导出")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("帮困卡门店交易汇总导出",e);
        }
    }

    @ApiOperation(value = "帮困卡对账汇总", notes = "帮困卡门帮困卡对账汇总店交易流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/helpCardCheckSummary")
    @MethodRender
    public TablePageInfo<HelpCardCheckSummaryODTO> helpCardCheckSummary(@RequestBody HelpCardCheckSummaryIDTO idto){
        return posHelpCardService.helpCardCheckSummary(idto);
    }

    @ApiOperation(value = "帮困卡对账汇总-导出", notes = "帮困卡门帮困卡对账汇总店交易流水-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/helpCardCheckSummary")
    @MethodRender
    public void helpCardCheckSummaryExport(HelpCardCheckSummaryIDTO idto, HttpServletResponse response) throws IOException {

        idto.initExportPage();

        TablePageInfo<HelpCardCheckSummaryODTO> page = posHelpCardService.helpCardCheckSummary(idto);
        List<HelpCardCheckSummaryODTO> dataList = new ArrayList<>();
        renderService.render(page.getList(), "/export/helpCardCheckSummary");
        HelpCardCheckSummaryODTO sum = (HelpCardCheckSummaryODTO) page.getHeader();
        HelpCardCheckSummaryODTO head = new HelpCardCheckSummaryODTO();
        head.setAreaName("合计");
        head.setPxTraderAmount(sum.getPxTraderAmount());
        head.setZfConfirmAmount(sum.getZfConfirmAmount());
        head.setDiffAmount(sum.getDiffAmount());
        dataList.add(head);
        dataList.addAll(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"帮困卡对账汇总导出");
            EasyExcel.write(response.getOutputStream(), HelpCardCheckSummaryODTO.class).autoCloseStream(Boolean.FALSE).sheet("帮困卡对账汇总导出")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("帮困卡对账汇总导出",e);
        }
    }

    @ApiOperation(value = "帮困卡交易流水", notes = "帮困卡交易流水", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/helpCardTradeWaterPage")
    public PageInfo<HelpCardTradeWaterPageODTO> helpCardTradeWaterPage(@RequestBody HelpCardTradeWaterPageIDTO idto){
        return posHelpCardService.helpCardTradeWaterPage(idto);
    }

    @ApiOperation(value = "帮困卡交易流水", notes = "帮困卡交易流水导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/export/helpCardTradeWaterPage")
    public void helpCardTradeWaterPageExport(HelpCardTradeWaterPageIDTO idto, HttpServletResponse response) throws IOException {

        idto.initExportPage();

        PageInfo<HelpCardTradeWaterPageODTO> page = posHelpCardService.helpCardTradeWaterPage(idto);
        List<HelpCardTradeWaterPageODTO> dataList = new ArrayList<>(page.getList());
        try {
            ExcelUtil.setFileNameAndHead( response,"帮困卡交易流水导出");
            EasyExcel.write(response.getOutputStream(), HelpCardTradeWaterPageODTO.class).autoCloseStream(Boolean.FALSE).sheet("帮困卡交易流水导出")
                    .doWrite( dataList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("帮困卡交易流水导出异常",e);
        }
    }
}
