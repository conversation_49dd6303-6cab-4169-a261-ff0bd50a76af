package com.pinshang.qingyun.report.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2020/10/27
 */
@Data
public class BestsellerGoodIDTO extends Pagination {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("大类id")
    private Long cate1;

    @ApiModelProperty("商品id")
    private Long commodityId;


    @ApiModelProperty("条形码")
    private String barCode;

    private String saleMonth;
}
