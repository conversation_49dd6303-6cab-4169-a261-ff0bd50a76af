package com.pinshang.qingyun.report.model.pos;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Entity
@Table(name = "t_pos_report_commodity_quantity_update")
@NoArgsConstructor
public class PosReportCommodityQuantityUpdate {

    private Long id;

    private Long itemId;

    private Long shopId;

    private String shopCode;

    private String shopName;

    private String macCode;

    private Long orderCode;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private String commodityUnit;

    /**
     * 1-增加, 2-减少
     */
    private Integer operateType;

    private BigDecimal quantity;

    private BigDecimal commodityPrice;

    private Long casherId;

    private String casherCode;

    private String casherName;

    private String authorizerCode;

    private String authorizerName;

    private Date operateTime;

    private Long posMacId;

    /**
     * 1-收银pos, 2-自助pos
     */
    private Integer posType;

    private Date createTime;
}
