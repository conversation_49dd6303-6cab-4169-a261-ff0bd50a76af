package com.pinshang.qingyun.report.etl;

import com.pinshang.qingyun.base.druid.DruidProperties;
import lombok.extern.slf4j.Slf4j;
import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.core.exception.KettleException;
import org.pentaho.di.trans.Trans;
import org.pentaho.di.trans.TransMeta;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;

@Service
@Slf4j
public class EtlMethod {

    @Autowired
    private DruidProperties druidProperties;


    @Async
    public void runTransformation(File[] ktrs, String variable){
        for(File f : ktrs){
            runTransformation(f.getAbsolutePath(),variable);
        }
    }


    private void runTransformation(String filename, String variable) {
        try {
            KettleEnvironment.init();
            TransMeta transMeta = new TransMeta(filename);
            Trans trans = new Trans(transMeta);
            trans.setVariable("TIME_STAMP",variable);
            String url = druidProperties.getUrl();
            String[] split = url.split(":");
            String host = split[2].substring(2);
            String port= split[3].substring(0,split[3].indexOf("/"));
            String dataBaseName = split[3].substring(split[3].indexOf("/") + 1 , split[3].indexOf("?"));
            trans.setVariable("host",host);
            trans.setVariable("port",port);
            trans.setVariable("dataBaseName",dataBaseName);
            trans.setVariable("userName",druidProperties.getUsername());
            trans.setVariable("password",druidProperties.getPassword());
            trans.execute(null); // You can pass arguments instead of null.
            trans.waitUntilFinished();
            if ( trans.getErrors() > 0 )
            {
                log.warn("There were errors during transformation execution.");
                throw new RuntimeException( "There were errors during transformation execution." );
            }
        }
        catch ( KettleException e ) {
            log.warn(e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
