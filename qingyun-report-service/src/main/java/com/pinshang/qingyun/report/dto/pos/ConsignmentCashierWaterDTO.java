package com.pinshang.qingyun.report.dto.pos;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsignmentCashierWaterDTO {

    @ExcelProperty("部门")
    private String orgName;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店")
    private String shopName;

    @ApiModelProperty("销售单号")
    @ExcelProperty("销售单号")
    private String orderCode;

    @ApiModelProperty("收银时间")
    @ExcelProperty("收银时间")
    private Date saleTime;

    @ApiModelProperty("收款金额")
    @ExcelProperty("收款金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("收款方式")
    @ExcelProperty("收款方式")
    private String payName;

    @ApiModelProperty("员工姓名")
    @ExcelProperty("收银员")
    private String employeeName;

    @ApiModelProperty("POS机号")
    @ExcelProperty("POS机")
    private String macCode;
}
