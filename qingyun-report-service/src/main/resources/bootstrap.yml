pinshang:
  application-prefix:
  application-name: qingyun-report-service
spring:
  application:
    name: ${application.name.switch:${pinshang.application-prefix}}${pinshang.application-name}
  profiles:
    active: test
  mvc:
    view:
      prefix: /templates/
      suffix: .ftl
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  freemarker:
    cache: false
    request-context-attribute: request
server:
  tomcat:
    uri-encoding: UTF-8
    basedir: ./logs/${spring.application.name}
  port: 9029
  servlet:
    encoding:
      charset: UTF-8
logging:
  file:
    name: ${pinshang.application-name}
    path: ./logs/${spring.application.name}
logstash:
  service: *************:4560
app:
  id: ${pinshang.application-name}
apollo:
  cluster: ${application.name.switch:default}
  bootstrap:
    enabled: true
#-----------------------------------  me  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  cloud:
    config:
      uri: http://localhost:9001/
      username: admin
      password: 111111
  config:
    activate:
      on-profile: me
#-----------------------------------  dev 各环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    config:
      enabled: false
#  cloud:
#    config:
#      uri: http://*************:9001/
#      username: admin
#      password: 111111
apollo:
  meta: http://**************:8080
#-----------------------------------  test  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: test
  cloud:
    config:
      enabled: false
#  cloud:
#    config:
#      uri: http://*************:9001/
#      username: admin
#      password: 111111
apollo:
  meta: http://*************:8080
#-----------------------------------  wg-hd-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: wg-hd-prod
  cloud:
    config:
      enabled: false
#  cloud:
#    config:
#      uri: http://192.168.100.65:9001/
#      username: admin
#      password: qingyun123
apollo:
  meta: http://192.168.100.13:8080
#-----------------------------------  wg-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: wg-prod
  cloud:
    config:
      enabled: false
#  cloud:
#    config:
#      uri: http://192.168.100.35:9001/
#      username: admin
#      password: qingyun123
apollo:
  meta: http://192.168.103.103:8080