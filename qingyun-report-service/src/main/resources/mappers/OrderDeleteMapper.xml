<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.OrderDeleteMapper">

    <resultMap id="OrderDeleteDetailMap" type="com.pinshang.qingyun.report.dto.pos.OrderDeleteDetailODTO">
        <result column="shop_code" property="shopCode" />
        <result column="shop_name" property="shopName" />
        <result column="order_code" property="orderCode" />
        <result column="employeeName" property="employeeName" />
        <result column="createName" property="createName" />
        <result column="orderDeleteTime" property="orderDeleteTime" />
        <result column="orderTotalQuantity" property="orderTotalQuantity" />
        <result column="orderTotalAmount" property="orderTotalAmount" />
        <collection property="items" ofType="com.pinshang.qingyun.report.dto.pos.OrderDeleteDetailODTO$OrderItem">
            <id column="itemId" property="id" />
            <result column="bar_code" property="barCode" />
            <result column="commodity_name" property="commodityName" />
            <result column="quanty" property="quanty" />
            <result column="commodity_unit" property="commodityUnit" />
            <result column="price" property="price" />
            <result column="itemTotalAmount" property="itemTotalAmount" />
        </collection>
    </resultMap>

    <sql id="orderDeleteWhere">
         WHERE 1=1
        <if test="idto.shopId != null and idto.shopId !='' ">
            AND t.shop_id = #{idto.shopId}
        </if>

        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and t.operate_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>

        <if test="idto.operateType != null and idto.operateType !='' ">
            AND t.operate_type = #{idto.operateType}
        </if>

        <if test="idto.operateId != null and idto.operateId !='' ">
            AND t.operate_id = #{idto.operateId}
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size() > 0">
            AND t.shop_id IN
            <foreach collection="idto.shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test = "idto.shopCode != null and idto.shopCode != ''">
            AND t.shop_code = #{idto.shopCode}
        </if>
        <if test="null != idto.posType">
            and t.pos_type = #{idto.posType}
        </if>
        <if test="idto.hourBegin != null and idto.hourBegin != ''">
            AND date_format(t.operate_time,'%k:%i') <![CDATA[ >= ]]> #{idto.hourBegin}
        </if>
        <if test="idto.hourEnd != null and idto.hourEnd != ''">
            AND date_format(t.operate_time,'%k:%i') <![CDATA[ <= ]]> #{idto.hourEnd}
        </if>
        <if test="idto.compareOrderAmount != null and idto.compareOrderAmount != ''">
            AND t.total_amount <![CDATA[ >= ]]> #{idto.compareOrderAmount}
        </if>
    </sql>

    <select id="listOrderDelete"  parameterType="com.pinshang.qingyun.report.dto.pos.OrderDeleteIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.OrderDeleteODTO">
        SELECT
            t.shop_id,
            t.ref_id,
            t.shop_name,
            t.shop_code,
            t.mac_code,
            t.order_code,
            t.quantity,
            t.total_amount,
            t.operate_type,
            (case t.operate_type when 1 then '整单取消' when 2 then '挂单未结算' when 3 then '挂单删除' end) operateTypeName,
            t.order_rest_time,
            t.order_rest_casher_code,
            t.order_rest_casher_name,
            t.order_delete_time,
            t.order_delete_casher_code AS orderDeleteCasherCode,
            t.order_delete_casher_name AS orderDeleteCasherName,
            t.operate_name,
            t.operate_time,
            t.authorizer_code,
            t.authorizer_name,
            CASE t.pos_type
            WHEN 2 THEN '自助POS'
            ELSE '收银POS'
            END AS posTypeName
        FROM t_pos_report_order_delete t
        <include refid="orderDeleteWhere"></include>
        ORDER  BY t.operate_time DESC
    </select>


    <select id="querySumOrderDelete"  parameterType="com.pinshang.qingyun.report.dto.pos.OrderDeleteIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.OrderDeleteODTO">
        SELECT
            sum(t.quantity) quantity,
            sum(t.total_amount) totalAmount
        FROM t_pos_report_order_delete t
        <include refid="orderDeleteWhere"></include>
    </select>




    <sql id="orderCancelWhere">
        WHERE 1=1
        <if test="idto.shopIdList != null">
            AND t.shop_id IN
            <foreach collection="idto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and t.operate_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>

        <if test="idto.casherId != null and idto.casherId !='' ">
            AND t.operate_id = #{idto.casherId}
        </if>
        <if test = "idto.shopCode != null and idto.shopCode != ''">
            AND t.shop_code = #{idto.shopCode}
        </if>
        <if test = "idto.posType != null">
            AND t.pos_type = #{idto.posType}
        </if>
    </sql>

    <select id="listOrderCancel"  parameterType="com.pinshang.qingyun.report.dto.pos.OrderCancelIDTO"  resultType="com.pinshang.qingyun.report.dto.OrderCancelODTO">
        SELECT
            t.shop_id,
            t.shop_name,
            t.shop_code shopCode,
            t.operate_code AS casherCode,
            t.operate_name AS casherName,
            date_format(t.operate_time, '%Y-%m-%d') casherDate,
            sum(t.total_amount) cancelTotalAmount,
            t.pos_type posType,
            CASE  t.pos_type
            WHEN 2 THEN '自助POS'
            ELSE '收银POS'
            END AS posTypeName
        FROM t_pos_report_order_delete t
        <include refid="orderCancelWhere"></include>
        GROUP BY t.operate_date,t.shop_id,t.operate_id
        HAVING cancelTotalAmount >= ${idto.cancelMorethanException}
        ORDER  BY casherDate DESC,cancelTotalAmount DESC
    </select>


    <select id="querySumOrderCancel"  parameterType="com.pinshang.qingyun.report.dto.pos.OrderCancelIDTO"  resultType="com.pinshang.qingyun.report.dto.OrderCancelODTO">
        SELECT sum(tt.cancelTotalAmount) cancelTotalAmount FROM  (
            SELECT
              sum(t.total_amount) cancelTotalAmount
            FROM t_pos_report_order_delete t
            <include refid="orderCancelWhere"></include>
            GROUP BY t.operate_date,t.shop_id,t.operate_id
            HAVING cancelTotalAmount > ${idto.cancelMorethanException}
        ) tt
    </select>

    <select id="orderDeleteDetail" resultMap="OrderDeleteDetailMap">
        select
            tprod.shop_code,
            tprod.shop_name,
            tprod.order_code,
            tprod.operate_code as employeeName,
            tprod.operate_name as createName,
            tprod.operate_time as orderDeleteTime,
            tprod.quantity as orderTotalQuantity,
            tprod.total_amount as orderTotalAmount,
            tprodi.id as itemId,
            tprodi.bar_code,
            tprodi.commodity_name,
            tprodi.quanty,
            tprodi.commodity_unit,
            tprodi.price,
            tprodi.quanty * tprodi.price as itemTotalAmount
        from t_pos_report_order_delete_item tprodi
        left join t_pos_report_order_delete tprod on tprodi.order_delete_id = tprod.id
        where tprod.order_code = #{orderCode}
    </select>

    <select id="listCommodityByOrderCode" resultType="com.pinshang.qingyun.report.dto.pos.AIMonitorCommodityDTO">
        select odi.commodity_name,
               odi.commodity_unit,
               odi.quanty,
               odi.price as salePrice,
               odi.bar_code,
               odi.price*odi.quanty as totalAmount,
               1 as operateType
        from t_pos_report_order_delete_item odi
                 left join t_pos_report_order_delete od on odi.order_delete_id = od.id
        where od.order_code = #{orderCode}
    </select>
</mapper>