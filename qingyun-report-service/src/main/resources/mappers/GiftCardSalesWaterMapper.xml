<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.GiftCardSalesWaterMapper">

    <select id="salesWaterPage" parameterType="com.pinshang.qingyun.report.dto.pos.QueryGiftCardWaterDTO"
    resultType="com.pinshang.qingyun.report.dto.pos.GiftCardSalesWaterPage">
        SELECT a.order_code,a.card_source_type,a.card_no,a.card_sn,a.par_value,
        a.price,a.mac_code,a.sale_time,a.employee_name,b.shop_type,b.shop_name,IFNULL(a.number,1) AS num,
        b.shop_code,c.commodity_code,c.commodity_name,c.commodity_spec,c.commodity_unit_name AS commodityUnit,
        c.bar_code AS barCode,a.shop_id AS shopId
        FROM t_gift_card_sales_water a
        LEFT JOIN t_md_shop b ON a.shop_id = b.id
        LEFT JOIN t_commodity c ON a.commodity_id = c.id
        <include refid="salesWaterCondition"/>
        ORDER BY a.sale_time DESC, a.card_sn ASC
    </select>

    <select id="salesWaterSum" parameterType="com.pinshang.qingyun.report.dto.pos.QueryGiftCardWaterDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.GiftCardSalesWaterPage">
        SELECT sum(ifnull(a.number, 1)) AS num,
               sum(a.par_value) AS parValue,
               sum(a.price) AS price
        FROM t_gift_card_sales_water a
        LEFT JOIN t_md_shop b ON a.shop_id = b.id
        LEFT JOIN t_commodity c ON a.commodity_id = c.id
        <include refid="salesWaterCondition"/>
    </select>

    <sql id = "salesWaterCondition">
        <where>
<!--            <if test="dto.commodityId != null and dto.commodityId != ''">-->
<!--                AND a.commodity_id = #{dto.commodityId}-->
<!--            </if>-->
            AND a.type = #{dto.type}
            <if test="dto.commodityInfo != null and dto.commodityInfo != ''">
                AND (c.commodity_code LIKE concat('%',#{dto.commodityInfo},'%') OR c.commodity_name LIKE concat('%',#{dto.commodityInfo},'%'))
            </if>
            <if test="dto.shopType != null and dto.shopType != ''">
                AND b.shop_type = #{dto.shopType}
            </if>
            <choose>
              <when test="dto.shopId != null and dto.shopId != ''">
                  AND a.shop_id = #{dto.shopId}
              </when>
              <otherwise>
                  AND a.shop_id IN
                  <foreach collection="dto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                      #{id}
                  </foreach>
              </otherwise>
            </choose>
            <if test="dto.dateStart != null and dto.dateStart != '' and dto.dateEnd != null and dto.dateEnd != ''">
                AND a.sale_time BETWEEN #{dto.dateStart} AND #{dto.dateEnd}
            </if>
            <if test="dto.employeeNumber != null and dto.employeeNumber != ''">
                AND a.employee_number = #{dto.employeeNumber}
            </if>
            <if test="dto.cardSourceType != null and dto.cardSourceType != ''">
                AND a.card_source_type = #{dto.cardSourceType}
            </if>
            <if test="dto.orderCode != null and dto.orderCode != ''">
                AND a.order_code like concat('%',#{dto.orderCode},'%')
            </if>
            <if test="dto.cardNo != null and dto.cardNo != ''">
                AND a.card_no = #{dto.cardNo}
            </if>
            <if test="dto.cardSn != null and dto.cardSn != ''">
                AND a.card_sn = #{dto.cardSn}
            </if>
            <if test = "dto.barCode != null and dto.barCode != ''">
                AND a.commodity_id = (select commodity_id from t_commodity_bar_code where bar_code = #{dto.barCode})
            </if>
            <if test = "dto.consignmentId != null and dto.consignmentId != ''">
                AND a.consignment_id = #{dto.consignmentId}
            </if>
            <if test="dto.commodityId != null and dto.commodityId != ''">
                AND a.commodity_id = #{dto.commodityId}
            </if>
        </where>
    </sql>

    <select id="selectConsignmentCommoditySales" resultType="com.pinshang.qingyun.report.dto.pos.ConsignmentCommoditySalesReportODTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.ConsignmentCommoditySalesReportIDTO">
        SELECT
        <if test=" 1 == isWithShop">
            w.shop_id,
            ms.`store_id`,
        </if>
        w.commodity_id,
        SUM(w.price) as salesAmount,
        SUM(w.number) as salesQuantity
        FROM
        t_gift_card_sales_water w
        LEFT JOIN t_md_shop ms ON ms.`id` = w.`shop_id`
        LEFT JOIN t_commodity c ON c.`id` = w.`commodity_id`
        WHERE w.sale_time &gt;= #{beginDate}
        AND w.sale_time &lt;  #{endDate}
        AND w.`type` = 2
        <if test="null != shopId">
        AND w.`shop_id` = #{shopId}
        </if>
        <if test="shopIdList != null and shopIdList.size >0 ">
            AND w.`shop_id` IN
            <foreach collection="shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="null != commodityId">
            AND w.`commodity_id` = #{commodityId}
        </if>
        <if test="null != barCode and '' != barCode">
        AND c.`bar_code` LIKE concat('%', #{barCode}, '%')
        </if>
        <if test="null != consignmentId">
        AND w.`consignment_id` = #{consignmentId}
        </if>
        <if test="cateId1 != null">
            AND c.commodity_first_kind_id =  #{cateId1}
        </if>
        <if test="cateId2 != null">
            AND c.commodity_second_kind_id =  #{cateId2}
        </if>
        <if test="cateId3 != null">
            AND c.commodity_third_kind_id =  #{cateId3}
        </if>
        GROUP BY
        <if test=" 1 == isWithShop">
            w.shop_id,
        </if>
        w.commodity_id
        ORDER BY
        <if test=" 1 == isWithShop">
            w.shop_id,
        </if>
        w.commodity_id
    </select>

        <select id="selectConsignmentShopCommoditySales" resultType="com.pinshang.qingyun.report.dto.pos.ConsignmentShopSalesReportODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.ConsignmentShopSalesReportIDTO">
        SELECT
        w.shop_id,
        ms.`store_id`,
        SUM(w.price) as salesAmount,
        SUM(w.number) as salesQuantity
        FROM
        t_gift_card_sales_water w
        LEFT JOIN t_md_shop ms ON ms.`id` = w.`shop_id`
        WHERE w.sale_time &gt;= #{beginDate}
        AND w.sale_time &lt;  #{endDate}
        AND w.`type` = 2
        <if test="null != shopId">
            AND w.`shop_id` = #{shopId}
        </if>
        <if test="shopIdList != null and shopIdList.size >0 ">
            AND w.`shop_id` IN
            <foreach collection="shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY w.shop_id
        ORDER BY w.shop_id
    </select>

</mapper>