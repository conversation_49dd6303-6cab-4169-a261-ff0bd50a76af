<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.GuestListSummaryReportMapper">

    <select id="guestSummaryReport"  parameterType="com.pinshang.qingyun.report.dto.pos.GuestListSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.GuestListSummaryReportOrderODTO">
      SELECT tt.* from (
        SELECT
            tp.shop_id,
            tp.shop_code,
            tp.shop_name,
            tp.mac_code,
            tp.mac_name,
            IFNULL(SUM(tp.guest_quantity),0) guestQuantity,
            IFNULL(SUM(tp.guest_amount),0) guestAmount,
            tp.sale_time,
            tp.hour_time
        FROM
         <choose>
             <when test="guestListSummaryReportOrderIDTO.summaryType ==0 and guestListSummaryReportOrderIDTO.isCurrentDay == false"> t_pos_report_guest_list_summary_day </when>
             <when test="guestListSummaryReportOrderIDTO.summaryType ==1 and guestListSummaryReportOrderIDTO.isCurrentDay == false"> t_pos_report_guest_list_summary_day </when>
             <when test="guestListSummaryReportOrderIDTO.summaryType ==2 and guestListSummaryReportOrderIDTO.isCurrentDay == false"> t_pos_report_guest_list_summary_day </when>
             <otherwise> t_pos_report_guest_list_summary </otherwise>
         </choose>
             tp
        WHERE 1=1
        <choose>
            <when test="guestListSummaryReportOrderIDTO.shopId != null and guestListSummaryReportOrderIDTO.shopId !='' " > AND tp.shop_id = #{guestListSummaryReportOrderIDTO.shopId} </when>
            <otherwise>
                AND tp.shop_id IN
                <foreach collection="guestListSummaryReportOrderIDTO.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test="guestListSummaryReportOrderIDTO.beginDate !=null and guestListSummaryReportOrderIDTO.endDate != '' and guestListSummaryReportOrderIDTO.beginDate != null and guestListSummaryReportOrderIDTO.endDate != '' ">
            and tp.sale_time BETWEEN #{guestListSummaryReportOrderIDTO.beginDate} and #{guestListSummaryReportOrderIDTO.endDate}
        </if>
        <if test="guestListSummaryReportOrderIDTO.macCode!=null and guestListSummaryReportOrderIDTO.macCode !='' ">
            AND tp.`mac_code` like concat('%',#{guestListSummaryReportOrderIDTO.macCode},'%')
        </if>
        <if test="guestListSummaryReportOrderIDTO.summaryType ==1 ">
            GROUP BY tp.shop_id,tp.mac_id,tp.sale_time
            ORDER BY tp.sale_time,tp.shop_id,tp.mac_id
        </if>
        <if test="guestListSummaryReportOrderIDTO.summaryType ==2 ">
            GROUP BY tp.shop_id,tp.sale_time
            ORDER BY tp.sale_time,tp.shop_id
        </if>
        <if test="guestListSummaryReportOrderIDTO.summaryType ==3 ">
            GROUP BY tp.hour_time
            ORDER BY tp.hour_time
        </if>
      ) tt
    </select>

    <!-- 计算连带率-->
    <select id="getJointRate"  parameterType="com.pinshang.qingyun.report.dto.pos.GuestListSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO">
        SELECT
        tp.shop_id,
        tp.sale_time,
        IFNULL(SUM(tp.tatal_quantity),0) tatalQuantity
        FROM
        t_pos_report_sales_summary tp
        WHERE 1=1
        <choose>
            <when test="jointIDTO.shopId != null and jointIDTO.shopId !='' " > AND tp.shop_id = #{jointIDTO.shopId} </when>
            <otherwise>
                AND tp.shop_id IN
                <foreach collection="jointIDTO.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test="jointIDTO.beginDate !=null and jointIDTO.endDate != '' and jointIDTO.beginDate != null and jointIDTO.endDate != '' ">
            and tp.sale_time BETWEEN #{jointIDTO.beginDate} and #{jointIDTO.endDate}
        </if>
        GROUP BY tp.shop_id,tp.sale_time
    </select>

    <delete id="deleteGuestListSummaryReportByOrderCodes">
        DELETE  FROM  t_pos_report_guest_list_summary WHERE order_code = #{orderCode}
    </delete>

    <delete id="deleteGuestListSummaryReportByTimeRange">
        DELETE  FROM  t_pos_report_guest_list_summary
        WHERE sale_time = #{saleTime} AND  create_time BETWEEN #{beginTime} AND  #{endTime}
        <if test="shopId != null and shopId != 0">
            AND shop_id = #{shopId}
        </if>
    </delete>

    <select id="netSalesList" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
    resultType="com.pinshang.qingyun.report.dto.pos.NetSalesListDTO">
        SELECT
            shop_id,
            <if test="dto.type == 3 or dto.type == 2">
                sale_time,
            </if>
            <if test="dto.type == 3">
                create_id,
            </if>
            SUM(guest_amount) netSales
        FROM t_pos_report_guest_list_summary
        <include refid="netSalesListCondition"/>
        GROUP BY shop_id
                 <if test="dto.type == 3 or dto.type == 2">
                     ,sale_time
                 </if>
                 <if test="dto.type == 3">
                     ,create_id
                 </if>
    </select>

    <sql id = "netSalesListCondition">
       <where>
           <choose>
               <when test="dto.shopId != null and dto.shopId != ''"> AND shop_id = #{dto.shopId} </when>
               <otherwise>
                   AND shop_id IN
                   <foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                       #{shopId}
                   </foreach>
               </otherwise>
           </choose>
           <if test="dto.beginTime != null and dto.beginTime != ''">
               AND sale_time >= #{dto.beginTime}
           </if>
           <if test="dto.endTime != null and dto.endTime != ''">
               AND sale_time <![CDATA[<=]]> #{dto.endTime}
           </if>
           <if test="dto.casherId != null and dto.casherId != ''">
               AND create_id = #{dto.casherId}
           </if>
       </where>
    </sql>

    <select id="netSalesCount" resultType="double">
        select sum(guest_amount)
        from t_pos_report_guest_list_summary
        <where>
          <if test="shopIds != null and shopIds.size > 0">
            and shop_id in
            <foreach collection="shopIds" index="index" item="shopId" open="(" separator="," close=")">
              #{shopId}
            </foreach>
          </if>
          <if test="createIds != null and createIds.size > 0">
              and create_id in
              <foreach collection="createIds" index="index" item="createId" open="(" separator="," close=")">
                  #{createId}
              </foreach>
          </if>
          <choose>
              <when test="saleTimes != null and saleTimes.size > 0">
                  and sale_time in
                  <foreach collection="saleTimes" index="index" item="saleTime" open="(" separator="," close=")">
                      #{saleTime}
                  </foreach>
              </when>
              <otherwise>
                  and sale_time between #{beginTime} and #{endTime}
              </otherwise>
          </choose>
        </where>
    </select>
</mapper>