<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.HelpCardTraderSummaryMapper">
    <select id="helpCardCashierWaterPage" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCashierWaterPageODTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCashierWaterPageIDTO">
        SELECT
        s.`area_id`,
        s.`area_name`,
        s.`shop_id`,
        ms.`store_id`,
        s.`pay_code`,
        s.`order_code`,
        DATE_FORMAT(s.`transaction_date`,'%Y-%m-%d %H:%i:%s') AS transactionDate,
        s.`card_no`,
        sum(s.`amount`) as amount,
        ms.`shop_code`
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms
        ON s.`shop_id` = ms.`id`
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
        <include refid="helpCardCashierWaterPageConditions"/>
        GROUP BY s.`shop_id`,
        s.`area_id`,
        s.`pay_code`,
        DATE_FORMAT(s.`transaction_date`,'%Y-%m-%d') ,
        s.`order_code`,
        s.`card_no`
        ORDER BY s.`transaction_date` DESC
    </select>

    <select id="helpCardCashierWaterSum" resultType="java.math.BigDecimal"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCashierWaterPageIDTO">
        SELECT
        sum(s.`amount`)
        FROM
        t_pos_help_card_trader_summary s
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
        <include refid="helpCardCashierWaterPageConditions"/>
    </select>

    <sql id="helpCardCashierWaterPageConditions">
        <if test="null != payCode and '' != payCode">
            AND s.`pay_code` = #{payCode}
        </if>
        <if test="null != orderCode and '' != orderCode">
            AND s.`order_code` = #{orderCode}
        </if>
        <if test="null != cardNo and '' != cardNo">
            AND s.`card_no` = #{cardNo}
        </if>
    </sql>

    <sql id="helpCardSummaryConditions">
        <if test="beginTime != null and beginTime != ''">
            AND s.`transaction_date` >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND s.`transaction_date` <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="null != shopId">
            AND s.`shop_id` =#{shopId}
        </if>
        <if test="shopIdList != null and shopIdList.size() > 0">
            AND s.`shop_id` in
            <foreach collection="shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="null != areaId and '' != areaId">
            AND s.`area_id` = #{areaId}
        </if>
    </sql>

    <select id="helpCardCommoditySummary" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCommoditySummaryODTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCommoditySummaryIDTO">
        SELECT
        s.`area_id`,
        s.`shop_id`,
        ms.`store_id`,
        s.`area_name`,
        s.`commodity_id`,
        SUM(s.`amount`) AS amount
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms
        ON ms.`id` = s.`shop_id`
        LEFT JOIN t_commodity c
        ON c.`id` = s.`commodity_id`
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
        <if test="null != commodityId">
        AND s.`commodity_id` = #{commodityId}
        </if>
        <if test="null != barCode and '' != barCode">
        AND c.`bar_code` = #{barCode}
        </if>
        GROUP BY s.`shop_id`,
        s.`commodity_id`,
        s.`area_id`
        ORDER BY ms.`shop_code` ASC,
        c.`commodity_code` ASC
    </select>

    <select id="helpCardCommoditySummarySum" resultType="java.math.BigDecimal">
        SELECT
        SUM(s.`amount`) AS amount
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_commodity c
        ON c.`id` = s.`commodity_id`
        WHERE 1=1
        <include refid="helpCardSummaryConditions"/>
        <if test="null != commodityId">
            AND s.`commodity_id` = #{commodityId}
        </if>
        <if test="null != barCode and '' != barCode">
            AND c.`bar_code` = #{barCode}
        </if>
    </select>

    <select id="helpCardShopSummary" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardShopSummaryODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardShopSummaryIDTO">
        SELECT
        s.`area_id`,
        s.`shop_id`,
        ms.`store_id`,
        s.`area_name`,
        SUM(s.`amount`) AS amount
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms
        ON ms.`id` = s.`shop_id`
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
        GROUP BY s.`shop_id`,
        s.`area_id`
        ORDER BY ms.`shop_code` ASC
    </select>

    <select id="helpCardShopSummarySum" resultType="java.math.BigDecimal"
        parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardShopSummaryIDTO">
        SELECT
        SUM(s.`amount`) AS amount
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms
        ON ms.`id` = s.`shop_id`
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
    </select>

    <select id="selectAmountByDate" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardAmountByDate">
        SELECT
        s.`area_id`, s.`area_name`, s.`shop_id`,
        s.`pay_code`, SUM(s.`amount`) as amount
        FROM t_pos_help_card_trader_summary s
        WHERE  s.`transaction_date` >= #{beginTime}
        AND s.`transaction_date` <![CDATA[ <= ]]> #{endTime}
        GROUP BY s.`pay_code`, s.shop_id
    </select>

    <select id="helpCardCheckSummary" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryIDTO">
        SELECT
        s.`area_id`,
        s.`shop_id`,
        ms.`store_id`,
        s.`area_name`,
        s.pay_code,
        SUM(s.`amount`) AS pxTraderAmount
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms
        ON ms.`id` = s.`shop_id`
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
        <if test="null != payCode and '' != payCode">
            AND s.`pay_code` = #{payCode}
        </if>
        group by s.pay_code, s.shop_id
    </select>

    <select id="helpCardCheckSummarySum" resultType="java.math.BigDecimal">
        SELECT
        SUM(s.`amount`) AS pxTraderAmount
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms
        ON ms.`id` = s.`shop_id`
        WHERE 1 = 1
        <include refid="helpCardSummaryConditions"/>
        <if test="null != payCode and '' != payCode">
            AND s.`pay_code` = #{payCode}
        </if>
    </select>

    <select id="selectAmount4CheckItem" resultType="com.pinshang.qingyun.report.model.pos.HelpCardAccountCheckItem">
        SELECT
        s.shop_id,
        s.commodity_id,
        SUM(s.quantity) as quantity,
        SUM(s.amount) as amount,
        st.`id` as storeId
        FROM
        t_pos_help_card_trader_summary s
        LEFT JOIN t_md_shop ms ON s.`shop_id` = ms.`id`
        LEFT JOIN t_store st ON st.`id` = ms.`store_id`
        WHERE  s.`transaction_date` >= #{beginTime}
        AND s.`transaction_date` <![CDATA[ < ]]> #{endTime}
        AND s.area_id =#{areaId}
        GROUP BY  s.shop_id,
        s.commodity_id
    </select>

    <select id="selectPxAmountByStoreIdList" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardPxAmountByStoreIdListODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardPxAmountByStoreIdListIDTO">
        SELECT
        ms.`store_id`,
        SUM(s.`amount`) AS amount
        FROM
        t_pos_help_card_trader_summary s
        INNER JOIN t_md_shop ms
        ON ms.`id` = s.`shop_id`
        WHERE s.`transaction_date` >= #{beginDate}
        AND s.`transaction_date` &lt;= #{endDate}
        AND ms.`store_id` IN
        <foreach collection="storeIdList" item="storeId" open="(" close=")" separator=",">
            #{storeId}
        </foreach>
        GROUP BY s.`shop_id`
    </select>


    <delete id="deleteHelpCardTraderSummaryByOrderCode">
        DELETE  FROM  t_pos_help_card_trader_summary WHERE order_code = #{orderCode}
    </delete>
</mapper>

