<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.HelpCardAccountCheckMapper">
    <select id="page" parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardAccountCheckPageIDTO"
    resultType="com.pinshang.qingyun.report.dto.pos.HelpCardAccountCheckPageODTO">
        SELECT
        id,
        area_id,
        area_name,
        check_date_begin,
        check_date_end,
        check_date_month,
        check_amount,
        px_trader_amount,
        diff_amount,
        `status`,
        create_id,
        create_time,
        update_id,
        update_time
        FROM
        t_pos_help_card_account_check
        WHERE 1=1
        <if test="null != checkDateMonth and '' != checkDateMonth">
         AND check_date_month = #{checkDateMonth}
        </if>
        <if test="null != areaId and '' != areaId">
            AND area_id = #{areaId}
        </if>
        <if test="null != status">
            AND `status` = #{status}
        </if>
        <if test="null != status and 1 ==  status">
            ORDER BY create_time DESC
        </if>
        <if test="null != status and 2 ==  status">
            ORDER BY update_time DESC
        </if>
    </select>

    <select id="traderAmountDetail" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardAccountCheckTraderAmountDetailODTO">
        SELECT
        ts.`shop_id`,
        ms.`store_id`,
        ts.`pay_code`,
        ms.`shop_code`,
        SUM(ts.`amount`) AS pxTraderAmount
        FROM
        t_pos_help_card_trader_summary ts
        INNER JOIN t_pos_help_card_account_check ac
        ON ac.`area_id` = ts.`area_id`
        AND ts.`transaction_date` &lt; ac.`check_date_end`
        AND ts.`transaction_date` >= ac.`check_date_begin`
        LEFT JOIN t_md_shop ms
        ON ms.`id` = ts.`shop_id`
        WHERE ac.`id` =  #{checkId}
        <if test="null != shopId">
        AND ts.`shop_id` = #{shopId}
        </if>
        GROUP BY ts.`shop_id`,
        ts.`pay_code`
        ORDER BY ts.shop_id desc
    </select>

    <select id="traderAmountDetailSum" resultType="java.math.BigDecimal">
        SELECT
        SUM(ts.`amount`) AS pxTraderAmount
        FROM
        t_pos_help_card_trader_summary ts
        INNER JOIN t_pos_help_card_account_check ac
        ON ac.`area_id` = ts.`area_id`
        AND ts.`transaction_date` &lt; ac.`check_date_end`
        AND ts.`transaction_date` >= ac.`check_date_begin`
        WHERE ac.`id` =  #{checkId}
        <if test="null != shopId">
        AND ts.`shop_id` = #{shopId}
        </if>
    </select>

    <select id="selectPxTraderAmount" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardSelectPxTraderAmountIDTO">
        SELECT
        SUM(ts.`amount`)
        FROM
        t_pos_help_card_trader_summary ts
        WHERE
        ts.`area_id` = #{areaId}
        AND ts.`transaction_date` &lt;= #{endDate}
        AND ts.`transaction_date` >= #{beginDate}
    </select>

    <select id="selectUnionDateList" resultType="java.lang.Long">
        SELECT
        id
        FROM
        t_pos_help_card_account_check
        WHERE area_id = #{areaId}
        AND (
        (
        check_date_begin &lt;= #{beginDate}
        AND check_date_end >= #{beginDate}
        )
        OR (
        check_date_begin >= #{beginDate}
        AND check_date_begin &lt;= #{endDate}
        )
        )
        <if test=" null!= id">
            AND id != #{id}
        </if>
        AND status != 3
    </select>

    <select id="selectCheckDetail4Invoice" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceItemODTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceItemIDTO">
        SELECT aci.commodity_id,aci.quantity,aci.amount,ac.`area_id`,ac.`area_name`,ac.`check_date_month`
        FROM
            t_pos_help_card_account_check_item aci left join t_pos_help_card_account_check ac on aci.`check_id` = ac.`id`
        <where>
            <if test="checkIds!= null and checkIds.size() > 0">
                AND aci.check_id IN
                <foreach collection="checkIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="companyId != null">
                AND aci.company_id = #{companyId}
            </if>
        </where>
    </select>


</mapper>