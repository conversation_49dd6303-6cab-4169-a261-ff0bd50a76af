<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.ClearingChargesItemMapper">

    <select id="getHeaderSum" resultType="com.pinshang.qingyun.report.model.ClearingChargesItem">
        SELECT
            <include refid="sumSql"></include>
        FROM
            t_clearing_charges_item
        WHERE is_delete = 0
              and clearing_charges_id = #{id}
    </select>

    <sql id="sumSql">
        sum(cash) as cash,
        sum(pos_cash) as pos_cash,
        sum(mo_ling) as mo_ling,
        sum(bank_card) as bank_card,
        sum(pos_bank_card) as pos_bank_card,
        sum(bank_card_poundage) as bank_card_poundage,
        sum(union_pay) as union_pay,
        sum(pos_union_pay) as pos_union_pay,
        sum(union_poundage) as union_poundage,
        sum(tally_wechat) as tally_wechat,
        sum(pos_tally_wechat) as pos_tally_wechat,
        sum(tally_wechat_poundage) as tally_wechat_poundage,
        sum(tally_ali) as tally_ali,
        sum(pos_tally_ali) as pos_tally_ali,
        sum(tally_ali_poundage) as tally_ali_poundage,
        sum(ok_card_poundage) as ok_card_poundage,
        sum(ok_card) as ok_card,
        sum(pos_ok_card) as pos_ok_card,
        sum(total_amount) as total_amount,
        sum(pos_total_amount) as pos_total_amount,
        sum(total_poundage) as total_poundage
    </sql>

    <select id="selectPaymentSumByType" resultType="java.math.BigDecimal">
        select ifnull(sum(pay_amount),0) from t_pos_report_cashier_water
        where
        create_time >= DATE_FORMAT(#{payDate},'%Y-%m-%d 00:00:00')
        and create_time &lt;= DATE_FORMAT(#{payDate},'%Y-%m-%d 23:59:59')
        and shop_id = #{shopId}
        <if test="types != null">
            and
            pay_type in
            <foreach collection="types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>

    <select id="shopActualSummary" resultType="com.pinshang.qingyun.report.dto.ShopActualSummaryODTO">
        SELECT
            tcci.shop_name as shop_name,
            sum(tcci.cash) as cash,
            sum(tcci.bank_card) as bank_card,
            sum(tcci.union_pay) as union_pay,
            sum(tcci.ok_card) as ok_card,
            sum(tcci.tally_ali) as tally_ali,
            sum(tcci.tally_wechat) as tally_wechat,
            sum(tcci.total_amount)as total_amount
        FROM t_clearing_charges_item tcci
        WHERE tcci.is_delete = 0 AND tcci.import_date_month = #{idto.month}
          <choose>
              <when test="idto.shopId != null and idto.shopId != ''"> AND tcci.shop_id = #{idto.shopId} </when>
              <when test="idto.shopIdList != null and idto.shopIdList.size() > 0">
                  AND tcci.shop_id IN
                  <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                      #{id}
                  </foreach>
              </when>
          </choose>
        group by tcci.shop_id
    </select>

    <select id="shopActualSummaryHeader" resultType="com.pinshang.qingyun.report.dto.ShopActualSummaryODTO">
        SELECT
            IFNULL(SUM(tcci.cash), 0) AS cash,
            IFNULL(SUM(tcci.bank_card), 0) AS bank_card,
            IFNULL(SUM(tcci.union_pay), 0) AS union_pay,
            IFNULL(SUM(tcci.ok_card), 0) AS ok_card,
            IFNULL(SUM(tcci.tally_ali), 0) AS tally_ali,
            IFNULL(SUM(tcci.tally_wechat), 0) AS tally_wechat,
            IFNULL(SUM(tcci.total_amount), 0) AS total_amount
        FROM t_clearing_charges_item tcci
        WHERE tcci.is_delete = 0 AND tcci.import_date_month = #{idto.month}
        <choose>
            <when test="idto.shopId != null and idto.shopId != ''"> AND tcci.shop_id = #{idto.shopId} </when>
            <when test="idto.shopIdList != null and idto.shopIdList.size() > 0">
                AND tcci.shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
        </choose>
    </select>
    <select id="reconciliationOfRurnover" resultType="com.pinshang.qingyun.report.dto.ReconciliationOfRurnoverODTO">
        select import_date,<include refid="sumSql"></include>
        from
          t_clearing_charges_item
        <where>
            <if test="idto.date != null">
                and import_date_month = #{idto.date}
            </if>
            <if test="idto.shopIdList != null and idto.shopIdList.size() > 0">
                AND shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and is_delete = 0
        </where>
        group by import_date
    </select>

    <select id="reconciliationOfRurnoverHearder" resultType="com.pinshang.qingyun.report.dto.ReconciliationOfRurnoverODTO">
        select <include refid="sumSql"></include>
        from
        t_clearing_charges_item
        <where>
            <if test="idto.date != null">
                and import_date_month = #{idto.date}
            </if>
            <if test="idto.shopIdList != null and idto.shopIdList.size() > 0">
                AND shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and is_delete = 0
        </where>
    </select>
    <select id="reconciliationStatement" resultType="com.pinshang.qingyun.report.dto.ReconciliationStatementODTO">
        select shop_name,<include refid="sumSql"></include>
        from
        t_clearing_charges_item
        <where>
            <if test="idto.date != null">
                and import_date_month = #{idto.date}
            </if>
            <if test="idto.shopIdList != null and idto.shopIdList.size() > 0">
                AND shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and is_delete = 0
        </where>
        group by shop_id
    </select>
</mapper>