<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.xd.XdCommodityTaxMapper">

	<select id="findXdCommodityTaxList"  resultType="com.pinshang.qingyun.report.dto.xd.XdCommodityTaxODTO">
		SELECT
		    t.date_time,
			t.shop_id,
			t.commodity_id,
			sum(t.total_sales) totalSales,
			sum(t.total_quanty) totalQuanty,
			sum(t.order_total) orderTotal,
			sum(t.order_quanty) orderQuanty,
		    sum(t.normal_breakage_quantity) normalBreakageQuantity,
		    sum(t.over_normal_breakage_quantity) overNormalBreakageQuantity,
		    IFNULL(c.commodity_package_spec,1) commodityPackageSpec
		FROM
		  t_xd_commodity_tax t
		 LEFT JOIN t_commodity c ON c.id = t.commodity_id
		WHERE
		  t.date_time  BETWEEN #{beginTime}  AND  #{endTime}
		   <if test="shopId != null and shopId != 0 ">
				AND t.shop_id = #{shopId}
		  </if>
		  <if test="commodityId != null and commodityId != 0 ">
				AND t.commodity_id = #{commodityId}
		  </if>
		  group by t.shop_id,t.commodity_id,t.date_time
	</select>

</mapper>