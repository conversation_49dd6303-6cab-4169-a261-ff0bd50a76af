<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.PosOperationRecordMapper">
    <!-- 单品删除记录 -->
    <select id="signalCommodityDelete"  parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO">
        select
            cd.shop_id,
            cd.ref_id,
            cd.shop_name,
            ms.shop_code,
            cd.mac_code,
            cd.order_code,
            date_format(cd.operate_time, '%Y-%m-%d %H:%i:%S') as operate_time,
            cd.commodity_code,
            cd.commodity_name,
            cd.commodity_spec,
            cd.commodity_unit,
            cd.quantity,
            cd.commodity_price,
                CONVERT((cd.commodity_price * cd.quantity), DECIMAL(10, 2)) as commodity_amount,
            cd.casher_code AS casherCode,
            cd.casher_name AS casherName,
            cd.authorizer_code,
            cd.authorizer_name,
            cd.pos_type
        from t_pos_report_commodity_delete cd
        left join t_md_shop ms on ms.id = cd.shop_id
        <include refid="signalCommodityCondition"/>
        order by cd.operate_time desc
    </select>

    <!-- 单品删除记录统计 -->
    <select id="signalCommodityDeleteTotal"  parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO">
        SELECT
        a.shop_name,
        a.shop_id,
        b.shop_code,
        <if test="idto.type == 3">
            a.casher_code,
            a.casher_name,
            a.casher_id,
        </if>
        <if test="idto.type == 3 or idto.type == 2">
            date_format(a.operate_time, '%Y-%m-%d') operate_time,
        </if>
        count(a.quantity) quantity,
        CONVERT(sum(a.commodity_price * a.quantity), DECIMAL(10, 2)) as commodity_amount,
        a.pos_type
        FROM t_pos_report_commodity_delete a
        LEFT JOIN (
            SELECT wd.shop_id,date_format(wd.create_time, '%Y-%m-%d') AS create_time,wd.employee_number
            FROM
            <if test="idto.isCurrentDay == true">
                t_pos_report_cashier_water wd
            </if>
            <if test="idto.isCurrentDay == false">
                t_pos_report_cashier_water_day wd
            </if>
            WHERE wd.create_time BETWEEN #{idto.beginTime} AND #{idto.endTime}
            GROUP BY wd.shop_id,date_format(wd.create_time, '%Y-%m-%d'),wd.employee_number
        ) c ON (a.casher_code = c.employee_number AND c.create_time = date_format(a.operate_time, '%Y-%m-%d') AND c.shop_id = a.shop_id)
        LEFT JOIN t_md_shop b ON a.shop_id = b.id
        <where>
            <choose>
                <when test="idto.shopId != null and idto.shopId != ''"> AND a.shop_id = #{idto.shopId} </when>
                <otherwise>
                    AND a.shop_id IN
                    <foreach collection="idto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                        #{shopId}
                    </foreach></otherwise>
            </choose>

            <if test="idto.beginTime != null and idto.beginTime != ''">
                AND a.operate_time >= #{idto.beginTime}
            </if>
            <if test="idto.endTime != null and idto.endTime != ''">
                AND a.operate_time <![CDATA[<=]]> #{idto.endTime}
            </if>
            <if test="idto.casherId != null and idto.casherId != ''">
                AND a.casher_id = #{idto.casherId}
            </if>
            <if test="null != idto.posType">
                and a.pos_type = #{ idto.posType}
            </if>
        </where>
        GROUP BY
              a.shop_id,a.pos_type
              <if test="idto.type == 3 or idto.type == 2">
                ,date_format(a.operate_time, '%Y-%m-%d')
              </if>
              <if test="idto.type == 3">
                ,a.casher_id
              </if>
        ORDER BY date_format(a.operate_time, '%Y-%m-%d') desc,a.shop_id
    </select>

    <select id="signalCommodityDeleteSum" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO">
        select
            sum(cd.quantity) as quantity,
            CONVERT(sum((cd.commodity_price * cd.quantity)), DECIMAL(10, 2)) as commodity_amount
        from t_pos_report_commodity_delete cd
        left join t_md_shop ms on ms.id = cd.shop_id
        <include refid="signalCommodityCondition"/>
    </select>

<!--    <select id="signalCommodityDeleteTotalSum" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"-->
<!--            resultType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO">-->
<!--        select-->
<!--            count(quantity) as quantity,-->
<!--            CONVERT(sum((commodity_price * quantity)), DECIMAL(10, 2)) as commodity_amount-->
<!--        from t_pos_report_commodity_delete-->
<!--        <include refid="signalCommodityCondition"/>-->
<!--    </select>-->

    <select id="signalCommodityDeleteTotalSum" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO">
        SELECT count(b.quantity) AS quantity,
               CONVERT(sum((b.commodity_price * b.quantity)), DECIMAL(10, 2)) as commodity_amount
        FROM t_pos_report_commodity_delete b
        LEFT JOIN (
            SELECT a.shop_id,date_format(a.create_time, '%Y-%m-%d') AS create_time,a.employee_number
            FROM
            <if test="idto.isCurrentDay == true">
                t_pos_report_cashier_water a
            </if>
            <if test="idto.isCurrentDay == false">
                t_pos_report_cashier_water_day a
            </if>
            WHERE a.create_time BETWEEN #{idto.beginTime} AND #{idto.endTime}
            GROUP BY a.shop_id,date_format(a.create_time, '%Y-%m-%d'),a.employee_number) c
        ON (b.shop_id = c.shop_id AND c.create_time = date_format(b.operate_time, '%Y-%m-%d') AND c.employee_number = b.casher_code)
        <where>
            AND b.operate_time BETWEEN #{idto.beginTime} AND #{idto.endTime}
            <choose>
                <when test="idto.shopId != null and idto.shopId != ''"> AND b.shop_id = #{idto.shopId} </when>
                <otherwise>
                    AND b.shop_id IN
                    <foreach collection="idto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                        #{shopId}
                    </foreach></otherwise>
            </choose>
            <if test="idto.casherId != null and idto.casherId != ''">
                AND b.casher_id = #{idto.casherId}
            </if>
            <if test="idto.orderCode != null and idto.orderCode != ''">
                AND b.order_code = #{idto.orderCode}
            </if>
            <if test="null != idto.posType">
                and b.pos_type = #{ idto.posType}
            </if>
        </where>
    </select>

    <sql id="signalCommodityCondition">
        where 1=1
        <choose>
            <when test="idto.shopId != null and idto.shopId != ''"> AND shop_id = #{idto.shopId} </when>
            <otherwise>
                AND cd.shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach></otherwise>
        </choose>

        <if test="idto.beginTime != null and idto.beginTime != ''">
            AND cd.operate_time >= #{idto.beginTime}
        </if>
        <if test="idto.endTime != null and idto.endTime != ''">
            AND cd.operate_time <![CDATA[<=]]> #{idto.endTime}
        </if>
        <if test="idto.casherId != null and idto.casherId != ''">
            AND cd.casher_id = #{idto.casherId}
        </if>
        <if test="idto.orderCode != null and idto.orderCode != ''">
            AND cd.order_code = #{idto.orderCode}
        </if>
        <if test = "idto.shopCode != null and idto.shopCode != ''">
            AND ms.shop_code = #{idto.shopCode}
        </if>
        <if test="null != idto.posType">
            and cd.pos_type = #{ idto.posType}
        </if>
        <if test="idto.hourBegin != null and idto.hourBegin != ''">
            AND date_format(cd.operate_time,'%k:%i') <![CDATA[ >= ]]> #{idto.hourBegin}
        </if>
        <if test="idto.hourEnd != null and idto.hourEnd != ''">
            AND date_format(cd.operate_time,'%k:%i') <![CDATA[ <= ]]> #{idto.hourEnd}
        </if>
        <if test="idto.compareOrderAmount != null and idto.compareOrderAmount != ''">
            AND CONVERT((cd.commodity_price * cd.quantity), DECIMAL(10, 2)) <![CDATA[ >= ]]> #{idto.compareOrderAmount}
        </if>
    </sql>

</mapper>