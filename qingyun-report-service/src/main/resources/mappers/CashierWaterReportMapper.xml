<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.CashierWaterReportMapper">

    <insert id="insertConsumeRepeat">
        INSERT INTO t_pos_report_unique(order_code) VALUES (#{orderCode})
    </insert>

    <insert id="insertCashierWaterReports" parameterType="List">
        INSERT INTO t_pos_report_cashier_water(shop_id, shop_code, shop_name, order_code,
        create_time, total_amount, sale_type, pay_amount, pay_type, pay_name, employee_number,
        create_name, return_order_code, mac_code, order_total_amount, member_card_no, member_name, third_party_order_id,
        pos_mac_id,pos_type ) VALUES
        <foreach collection="cashierWaterReports" item="report" separator="," >
            (#{report.shopId}, #{report.shopCode},#{report.shopName},#{report.orderCode},
            #{report.createTime},#{report.totalAmount}, #{report.saleType},#{report.payAmount}, #{report.payType}, #{report.payName},
            #{report.employeeNumber},#{report.createName}, #{report.returnOrderCode}, #{report.macCode}, #{report.orderTotalAmount},
            #{report.memberCardNo},#{report.memberName},#{report.thirdPartyOrderId},
            #{report.posMacId},#{report.posType})
        </foreach>
    </insert>



    <select id="listCashierWaterReport" parameterType="com.pinshang.qingyun.report.dto.pos.CashierWaterIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierWaterODTO">
        select
            shop_id,
            shop_code,
            shop_name,
            order_code,
            create_time,
            CONVERT(total_amount, DECIMAL(10, 2)) AS total_amount,
            CONVERT(pay_amount, DECIMAL(10, 2)) AS pay_amount,
            CASE sale_type WHEN 1 THEN '销售' WHEN 2 THEN '退货' END AS sale_type,
            pay_type,
            pay_name,
            member_card_no,
            member_name,
            CASE pos_type
            WHEN 2 THEN mac_code
            ELSE employee_number
            END AS employeeNumber,
            create_name AS createName,
            return_order_code,
            mac_code,
            CASE left(order_code,1) WHEN 8 THEN '联网收银' WHEN 9 THEN '本地收银' WHEN 6 THEN '联网收银' ELSE '' END AS cash_channel,
            third_party_order_id,
            CASE pos_type
            WHEN 2 THEN '自助POS'
            WHEN 3 THEN 'PDA'
            ELSE '收银POS'
            END AS posTypeName
        from t_pos_report_cashier_water
        <include refid="listCashierWaterReportWhere"/>
        order by create_time desc
    </select>

    <select id="listCashierWaterReportHeader" parameterType="com.pinshang.qingyun.report.dto.pos.CashierWaterIDTO"
            resultType="com.pinshang.qingyun.report.service.pos.CashierWaterHeaderODTO">
        select
          sum(pay_amount) as pay_amount
        from t_pos_report_cashier_water
        <include refid="listCashierWaterReportWhere"/>
    </select>

    <sql id="listCashierWaterReportWhere">
        <where>
            <if test="cashierWaterIDTO.shopId == null || cashierWaterIDTO.shopId == ''">
                shop_id IN
                <foreach collection="cashierWaterIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="cashierWaterIDTO.beginTime != null and cashierWaterIDTO.beginTime != ''">
                AND create_time >= #{cashierWaterIDTO.beginTime}
            </if>
            <if test="cashierWaterIDTO.endTime != null and cashierWaterIDTO.endTime != ''">
                AND create_time <![CDATA[ <= ]]> #{cashierWaterIDTO.endTime}
            </if>
            <if test="cashierWaterIDTO.employeeNumber != null and cashierWaterIDTO.employeeNumber != ''">
                AND employee_number = #{cashierWaterIDTO.employeeNumber}
            </if>
            <if test="cashierWaterIDTO.macCode != null and cashierWaterIDTO.macCode != ''">
                AND mac_code = #{cashierWaterIDTO.macCode}
            </if>
            <if test="cashierWaterIDTO.shopId != null and cashierWaterIDTO.shopId != ''">
                AND shop_id = #{cashierWaterIDTO.shopId}
            </if>
            <if test="cashierWaterIDTO.saleType != null and cashierWaterIDTO.saleType != ''">
                AND sale_type = #{cashierWaterIDTO.saleType}
            </if>
            <if test="cashierWaterIDTO.orderCode != null and cashierWaterIDTO.orderCode != ''">
                AND order_code like concat('%', #{cashierWaterIDTO.orderCode}, '%')
            </if>
            <if test="cashierWaterIDTO.payType != null and cashierWaterIDTO.payType != ''">
                AND pay_type = #{cashierWaterIDTO.payType}
            </if>
            <if test="cashierWaterIDTO.beginAmount != null and cashierWaterIDTO.beginAmount != ''">
                AND order_total_amount >= #{cashierWaterIDTO.beginAmount}
            </if>
            <if test="cashierWaterIDTO.endAmount != null and cashierWaterIDTO.endAmount != ''">
                AND order_total_amount <![CDATA[<=]]> #{cashierWaterIDTO.endAmount}
            </if>
            <if test="cashierWaterIDTO.cashChannel != null and cashierWaterIDTO.cashChannel != ''">
                <choose>
                  <when test = "cashierWaterIDTO.cashChannel == 8">
                      AND (order_code LIKE concat(#{cashierWaterIDTO.cashChannel} ,'%')
                      or order_code LIKE concat('6%') )
                  </when>
                  <otherwise>
                      AND order_code LIKE concat(#{cashierWaterIDTO.cashChannel} ,'%')
                  </otherwise>
                </choose>
            </if>
            <if test="cashierWaterIDTO.memberCardNo != null and cashierWaterIDTO.memberCardNo != ''">
                AND member_card_no LIKE concat('%', #{cashierWaterIDTO.memberCardNo} ,'%')
            </if>
            <if test = "cashierWaterIDTO.thirdPartyOrderId != null and cashierWaterIDTO.thirdPartyOrderId != ''">
                AND third_party_order_id = #{cashierWaterIDTO.thirdPartyOrderId}
            </if>
            <if test="null != cashierWaterIDTO.posType">
                and pos_type = #{ cashierWaterIDTO.posType}
            </if>
        </where>
    </sql>

    <delete id="deleteCashierWaterReportByOrderCodes">
        DELETE  FROM  t_pos_report_cashier_water WHERE order_code = #{orderCode}
    </delete>

    <delete id="deleteCashierWaterReportByTimeRange">
        DELETE  FROM  t_pos_report_cashier_water
        WHERE create_time BETWEEN #{beginTime} AND  #{endTime}
        <if test="shopId != null and shopId != 0 ">
            AND shop_id = #{shopId}
        </if>
    </delete>

    <select id="selectReportData" resultType="Integer">
        SELECT count(*) FROM ${tableName} WHERE order_code = #{orderCode}
    </select>

    <select id="getLastDateOrder" resultType="com.pinshang.qingyun.report.model.pos.Payment">
        SELECT
        pp.order_code
        FROM t_pos_report_guest_list_summary pp
        WHERE pp.sale_time=#{lastDate}
        <if test="beginTime !=null and beginTime != '' and endTime != null and endTime != '' ">
            and pp.create_time BETWEEN #{beginTime} and #{endTime}
        </if>
    </select>

    <select id="getLastDiscountOrder" resultType="com.pinshang.qingyun.report.model.pos.Payment">
        SELECT
        DISTINCT
        pp.order_code
        FROM t_pos_report_cashier_discount pp
        WHERE pp.sale_time=#{lastDate}
        <if test="beginTime !=null and beginTime != '' and endTime != null and endTime != '' ">
            and pp.create_time BETWEEN #{beginTime} and #{endTime}
        </if>
    </select>

    <select id="getLastSaleOrder" resultType="com.pinshang.qingyun.report.model.pos.Payment">
        SELECT
             DISTINCT
              pp.order_code
        FROM t_pos_report_sales_summary pp
        WHERE pp.sale_time = #{lastDate}
        <if test="beginTime !=null and beginTime != '' and endTime != null and endTime != '' ">
            and pp.create_time BETWEEN #{beginTime} and #{endTime}
        </if>
    </select>
    <select id="getLastSaleWaterOrder" resultType="com.pinshang.qingyun.report.model.pos.Payment">
        SELECT
            DISTINCT
            pp.order_code
        FROM t_pos_report_sales_water pp
        WHERE pp.sale_time BETWEEN  #{lastBeginDate}  AND  #{lastEndDate}
    </select>
    <select id="getLastCashierWaterOrder" resultType="com.pinshang.qingyun.report.model.pos.Payment">
        SELECT
          DISTINCT
          pp.order_code
        FROM t_pos_report_cashier_water pp
        WHERE pp.create_time BETWEEN  #{lastBeginDate}  AND  #{lastEndDate}
    </select>



    <delete id="deleteCashierWaterDayReportBySaleTime">
        delete from t_pos_report_day_cashier_water where sale_time = #{saleTime}
    </delete>

    <insert id="insertCashierWaterDayReport">
        INSERT INTO t_pos_report_day_cashier_water(shop_id, shop_code, shop_name,sale_time,pay_type,pay_name,pay_amount)
      SELECT t.shop_id,t.shop_code,t.shop_name,
         date_format(t.create_time, '%Y-%m-%d') dateTime,t.pay_type,t.pay_name,IFNULL(sum(t.pay_amount),0) pay_amount
       from t_pos_report_cashier_water t
        WHERE t.create_time BETWEEN #{beginTime} AND #{endTime}
         GROUP BY t.shop_id,dateTime,t.pay_type
         ORDER BY dateTime DESC,t.shop_id
    </insert>


    <select id="queryCashierWaterDayReport" resultType="com.pinshang.qingyun.report.dto.pos.CashierWaterDayReportODTO">
        SELECT
        <if test="type != null and type == 1 ">
          t.shop_id,
          t.shop_code,
          t.shop_name,
        </if>
        <if test="type != null and type == 0 ">
          date_format(t.create_time, '%Y-%m-%d')  saleTime,
        </if>
          t.pay_type,
          t.pay_name,
          SUM(pay_amount) pay_amount
       FROM t_pos_report_cashier_water_day t
       WHERE t.create_time BETWEEN #{beginTime} AND #{endTime}
        <if test="shopId != null and shopId != 0 ">
            AND t.shop_id = #{shopId}
        </if>
        <if test="type != null and type == 1 ">
            GROUP BY t.shop_id,pay_type
        </if>
        <if test="type != null and type == 0 ">
            GROUP BY saleTime,pay_type
        </if>

    </select>



    <select id="getCashierWaterDayAmount" resultType="java.math.BigDecimal">
        SELECT
             IFNULL(SUM(t.pay_amount),0) cashierWaterDayAmount
          FROM t_pos_report_cashier_water_day t
        WHERE t.create_time  BETWEEN #{beginTime} AND #{endTime}
    </select>


    <select id="getGuestListSummaryDayAmount" resultType="java.math.BigDecimal">
          SELECT
            IFNULL(SUM(t.guest_amount),0) guestListSummaryDayAmount
           FROM t_pos_report_guest_list_summary_day t
          WHERE t.sale_time = #{saleTime}

    </select>


    <select id="getSalesSummaryDayAmount" resultType="java.math.BigDecimal">
          SELECT
             IFNULL(SUM(t.tatal_amount),0) salesSummaryDayAmount
           FROM t_pos_report_day_sales_summary t
           WHERE t.sale_time = #{saleTime}
    </select>


    <select id="getLastGuestOrderCount" resultType="java.lang.Long">
        SELECT
         count(DISTINCT pp.order_code)
        FROM t_pos_report_guest_list_summary pp
        WHERE pp.sale_time=#{lastDate}
        <if test="beginTime !=null and beginTime != '' and endTime != null and endTime != '' ">
            and pp.create_time BETWEEN #{beginTime} and #{endTime}
        </if>
    </select>
    <select id="getLastDiscountOrderCount" resultType="java.lang.Long">
        SELECT
           count(DISTINCT pp.order_code)
        FROM t_pos_report_cashier_discount pp
        WHERE pp.sale_time=#{lastDate}
        <if test="beginTime !=null and beginTime != '' and endTime != null and endTime != '' ">
            and pp.create_time BETWEEN #{beginTime} and #{endTime}
        </if>
    </select>
    <select id="getLastSaleOrderCount" resultType="java.lang.Long">
        SELECT
           count(DISTINCT pp.order_code)
        FROM t_pos_report_sales_summary pp
        WHERE pp.sale_time = #{lastDate}
        <if test="beginTime !=null and beginTime != '' and endTime != null and endTime != '' ">
            and pp.create_time BETWEEN #{beginTime} and #{endTime}
        </if>
    </select>
    <select id="getLastSaleWaterOrderCount" resultType="java.lang.Long">
        SELECT
          count(DISTINCT pp.order_code)
        FROM t_pos_report_sales_water pp
        WHERE pp.sale_time BETWEEN  #{lastBeginDate}  AND  #{lastEndDate}
    </select>
    <select id="getLastCashierWaterOrderCount" resultType="java.lang.Long">
        SELECT
            count(DISTINCT pp.order_code)
        FROM t_pos_report_cashier_water pp
        WHERE pp.create_time BETWEEN  #{lastBeginDate}  AND  #{lastEndDate}
    </select>

    <select id="getNetSales" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteODTO">
        select
              a.shop_id,
              a.shop_code,
              a.shop_name,
              <if test="dto.type == 3">
                  CASE a.pos_type
                  WHEN 2 THEN a.pos_mac_id
                  ELSE  b.user_id
                  END AS casherId,

                  CASE a.pos_type
                  WHEN 2 THEN a.mac_code
                  ELSE  b.employee_code
                  END AS casherCode,

                  CASE a.pos_type
                  WHEN 2 THEN a.create_name
                  ELSE b.employee_name
                  END AS casherName,

              </if>
              <if test="dto.type == 3 or dto.type == 2">
                  DATE_FORMAT(a.create_time,'%Y-%m-%d')  operate_time,
              </if>
              sum(a.pay_amount) netSales,
              CASE a.pos_type
              WHEN 2 THEN '自助POS'
              ELSE '收银POS'
              END AS posTypeName,
              a.pos_type,
              a.pos_mac_id AS posMacId
         from
        <if test="dto.isCurrentDay == true">
            t_pos_report_cashier_water a
        </if>
        <if test="dto.isCurrentDay == false">
            t_pos_report_cashier_water_day a
        </if>
        left join t_employee_user b on a.employee_number = b.employee_code
        <include refid="getNetSalesCondition"/>
        group by a.shop_id,a.pos_type
        <if test="dto.type == 3 or dto.type == 2">
            ,DATE_FORMAT(a.create_time,'%Y-%m-%d')
        </if>
        <if test="dto.type == 3">
            ,a.employee_number
        </if>
        order by
        <if test="dto.type == 3 or dto.type == 2">
            a.create_time desc,
        </if>
            a.shop_id asc
    </select>

    <select id="getNetSalesCount" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
    resultType="double">
        select
        sum(a.pay_amount) netSales
        from
        <if test="dto.isCurrentDay == true">
            t_pos_report_cashier_water a
        </if>
        <if test="dto.isCurrentDay == false">
            t_pos_report_cashier_water_day a
        </if>
        left join t_employee_user b on a.employee_number = b.employee_code
        <include refid="getNetSalesCondition"/>
    </select>

    <sql id = "getNetSalesCondition">
        <where>
            <choose>
                <when test="dto.shopId != null and dto.shopId != ''"> AND a.shop_id = #{dto.shopId} </when>
                <otherwise>
                    AND a.shop_id IN
                    <foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                        #{shopId}
                    </foreach></otherwise>
            </choose>
            <if test="dto.beginTime != null and dto.beginTime != '' and dto.endTime != null and dto.endTime != ''">
                and a.create_time BETWEEN #{dto.beginTime} and #{dto.endTime}
            </if>
            <if test="dto.casherId != null and dto.casherId != ''">
                and b.user_id = #{dto.casherId}
            </if>
            <if test = "dto.shopCode != null and dto.shopCode != ''">
                and a.shop_code = #{dto.shopCode}
            </if>
            <if test="null != dto.posType">
                and a.pos_type = #{ dto.posType}
            </if>
        </where>
    </sql>



    <select id="getCashierWaterCurrentDayAmount" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(t.pay_amount),0) cashierWaterDayAmount
        FROM t_pos_report_cashier_water t
        WHERE t.create_time  BETWEEN #{beginTime} AND #{endTime}
    </select>
    <select id="getGuestListSummaryCurrentDayAmount" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(t.guest_amount),0) guestListSummaryDayAmount
        FROM t_pos_report_guest_list_summary t
        WHERE t.sale_time = #{saleTime}
    </select>
    <select id="getSalesSummaryCurrentDayAmount" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(t.tatal_amount),0) salesSummaryDayAmount
        FROM t_pos_report_sales_summary t
        WHERE t.sale_time = #{saleTime}
    </select>


    <update id="batchUpdateCashierWaterOriginOrder" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_pos_report_cashier_water tp
            SET tp.return_order_code =  #{item.orderCode}
            WHERE tp.order_code = #{item.returnCode}
        </foreach>
    </update>

    <select id="getSummaryAmount" parameterType="com.pinshang.qingyun.report.dto.pos.SummaryAmountIDTO"
    resultType="com.pinshang.qingyun.report.dto.pos.SummaryAmountDTO">
        select sum(c.pay_amount) AS amount,
               c.pay_type AS payType,
               c.shop_id AS shopId
        from
        <if test = "true == dto.currentDay">
            t_pos_report_cashier_water c
        </if>
        <if test = "false == dto.currentDay">
            t_pos_report_cashier_water_day c
        </if>
        where c.shop_id in
        <foreach collection="dto.shopIds" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and c.create_time BETWEEN #{dto.startTime} and #{dto.endTime}
        and c.pay_type in
        <foreach collection="dto.payTypes" index="index" item="payType" open="(" separator="," close=")">
            #{payType}
        </foreach>
        group by c.shop_id,c.pay_type
    </select>
</mapper>