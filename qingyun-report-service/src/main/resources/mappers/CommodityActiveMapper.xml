<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.CommodityMapper">

    <select id="findAllFirstCategory" resultType="com.pinshang.qingyun.report.dto.CategoryODTO">
        select id,cate_name
        from t_category
        where cate_level = 1
    </select>

    <select id="findCommodityBarCodeByParam" resultType="com.pinshang.qingyun.report.model.Commodity">
        SELECT
           t.commodity_id commodityId,
           GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCode
        from t_commodity_bar_code t
        where 1=1
        <if test="commodityIdList != null and commodityIdList.size >0 ">
            and t.commodity_id in
            <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
        GROUP BY t.commodity_id
    </select>

    <select id="getCommodityByComodityId" resultType="com.pinshang.qingyun.report.model.Commodity">
        SELECT
        t.id commodityId,
        t.commodity_code,
        t.commodity_name,
        t.commodity_spec,
        t.commodity_unit_name,
        t.bar_code
        from t_commodity t
        where  t.id = #{commodityId}
    </select>

    <select id="findCommodityInfoForHand" resultType="com.pinshang.qingyun.report.dto.CommodityInfoODTO">
        SELECT
            t.id AS commodityId,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_unit_name AS commodityUnit,
            t.origin,
            t.bar_code,
            t.is_weight,
            t.commodity_package_spec
        FROM t_commodity t
        WHERE t.id = (SELECT tc.commodity_id FROM t_commodity_bar_code tc where tc.bar_code = #{barCode})
    </select>

    <select id="findCommodityById" resultType="com.pinshang.qingyun.report.model.Commodity">
        SELECT
            id,
            retail_price,
            cost_price,
            commodity_package_spec,
            is_weight
        FROM t_commodity
        WHERE id = #{commodityId}
    </select>


    <select id="getCommodityListByIds" parameterType="java.util.Set" resultType="com.pinshang.qingyun.report.model.shop.CommodityTax" >
        select
            c.id commodityId,
            c.commodity_first_kind_id commodityFirstId,
            c.commodity_second_kind_id commoditySecondId,
            c.commodity_third_kind_id commodityThirdId,
            IFNULL(c.commodity_package_spec,1) commodityPackageSpec
        from t_commodity c
        WHERE c.id in
        <foreach collection="commodityIdSet" index="index" item="commodityId" open="(" separator="," close=")">
            #{commodityId}
        </foreach>
    </select>

    <select id="getCommodityListByCodes" parameterType="java.util.Set" resultType="com.pinshang.qingyun.report.dto.pos.CommodityPackageSpecDTO" >
        select
            c.id commodityId,
            c.commodity_code commodityCode,
            IFNULL(c.commodity_package_spec,1) commodityPackageSpec
        from t_commodity c
        WHERE c.commodity_code in
        <foreach collection="commodityCodeSet" index="index" item="commodityCode" open="(" separator="," close=")">
            #{commodityCode}
        </foreach>
    </select>


    <select id="getCommodityByCommodityCodes" resultType="com.pinshang.qingyun.report.model.Commodity" >
        select
            c.id commodityId,
            c.commodity_code commodityCode
        from t_commodity c
        WHERE c.commodity_code in
        <foreach collection="commodityCodeList" index="index" item="commodityCode" open="(" separator="," close=")">
            #{commodityCode}
        </foreach>
    </select>

    <select id="findCommodityByIdList" resultType="com.pinshang.qingyun.report.model.Commodity">
        SELECT
           t.id commodityId,
           t.commodity_code,
           t.commodity_name,
           t.commodity_unit_name,
           t.commodity_spec,
           t.is_weight
        from t_commodity t
        where 1=1
        <if test="commodityIdList != null and commodityIdList.size >0 ">
            and t.id in
            <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                #{commodityId}
            </foreach>
        </if>
    </select>

</mapper>