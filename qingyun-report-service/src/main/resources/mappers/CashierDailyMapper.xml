<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.CashierDailyMapper">
    <!--收银日报-->
    <select id="selectCashierDailyList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">

        SELECT
        tprcw.shop_code AS shopCode,
        tprcw.shop_name AS shopName,
        tprcw.employee_number AS saleNumber,
        tprcw.create_name AS saleName,
        <if test="cashierDailyIDTO.searchCriteria == 0">
        tprcw.mac_code,
        case tprcw.sale_type
          when 1 then '销售'
          when 2 then '退货'
        end as sale_type,
        </if>
        <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
            SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
        </foreach>
        SUM(CASE WHEN 1=1 then tprcw.pay_amount else 0 end ) AS total
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null !=cashierDailyIDTO.shopCode and '' != cashierDailyIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyIDTO.shopCode}
        </if>

        <if test="cashierDailyIDTO.startTime != null and cashierDailyIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyIDTO.startTime}
        </if>
        <if test="cashierDailyIDTO.EndTime != null and cashierDailyIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyIDTO.EndTime}
        </if>
        <if test=" null !=  cashierDailyIDTO.saleNumber and '' !=cashierDailyIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierDailyIDTO.saleNumber}
        </if>
        <if test="cashierDailyIDTO.macCode != null and cashierDailyIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierDailyIDTO.macCode}, '%')
        </if>
        <if test="cashierDailyIDTO.saleType != null">
            AND tprcw.sale_type = #{cashierDailyIDTO.saleType}
        </if>
        GROUP BY tprcw.shop_id,tprcw.employee_number <if test="cashierDailyIDTO.searchCriteria == 0"> ,tprcw.mac_code, tprcw.sale_type </if>
        ORDER BY tprcw.shop_id,tprcw.employee_number <if test="cashierDailyIDTO.searchCriteria == 0"> ,tprcw.mac_code, tprcw.sale_type </if>
    </select>
    <select id="selectSUMCashierDailyList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
        <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
            SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
        </foreach>
        SUM(CASE WHEN 1=1 then tprcw.pay_amount else 0 end ) AS total
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null != cashierDailyIDTO.shopCode and '' !=cashierDailyIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyIDTO.shopCode}
        </if>
        <if test="cashierDailyIDTO.startTime != null and cashierDailyIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyIDTO.startTime}
        </if>
        <if test="cashierDailyIDTO.EndTime != null and cashierDailyIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyIDTO.EndTime}
        </if>

        <if test=" null !=  cashierDailyIDTO.saleNumber and '' !=cashierDailyIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierDailyIDTO.saleNumber}
        </if>
        <if test="cashierDailyIDTO.macCode != null and cashierDailyIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierDailyIDTO.macCode}, '%')
        </if>
        <if test="cashierDailyIDTO.saleType != null">
            AND tprcw.sale_type = #{cashierDailyIDTO.saleType}
        </if>
    </select>

    <!--收银日报按门店-->
    <select id="selectCashierDailyListByShop"   parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">
        SELECT
        tprcw.shop_code AS shopCode,
        tprcw.shop_name AS shopName,
        <foreach item="payType" collection="cashierDailyShopIDTO.payTypeList">
            SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
        </foreach>

        SUM(tprcw.pay_amount) AS total
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyShopIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null !=  cashierDailyShopIDTO.shopCode and '' !=cashierDailyShopIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyShopIDTO.shopCode}
        </if>
        <if test="cashierDailyShopIDTO.startTime != null and cashierDailyShopIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyShopIDTO.startTime}
        </if>
        <if test="cashierDailyShopIDTO.EndTime != null and cashierDailyShopIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyShopIDTO.EndTime}
        </if>
        GROUP BY tprcw.shop_id
        ORDER BY tprcw.shop_id
    </select>
    <select id="selectSUMCashierDailyListByShop"   parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
        <foreach item="payType" collection="cashierDailyShopIDTO.payTypeList">
            SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
        </foreach>
        SUM(CASE WHEN 1=1 then tprcw.pay_amount else 0 end ) AS total
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyShopIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null !=  cashierDailyShopIDTO.shopCode and '' != cashierDailyShopIDTO.shopCode">
            AND  tprcw.shop_id = #{cashierDailyShopIDTO.shopCode}
        </if>
        <if test="cashierDailyShopIDTO.startTime != null and cashierDailyShopIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyShopIDTO.startTime}
        </if>
        <if test="cashierDailyShopIDTO.EndTime != null and cashierDailyShopIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyShopIDTO.EndTime}
        </if>
    </select>
    <!--收银日报按日期-->
    <select id="selectCashierDailyListByDate"   parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">
        SELECT
        tprcw.shop_code AS shopCode,
        tprcw.shop_name AS shopName,
        DATE_FORMAT(tprcw.create_time,'%Y-%c-%d') AS createTime,
        <foreach item="payType" collection="cashierDailyDateIDTO.payTypeList">
            SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
        </foreach>
        SUM(tprcw.pay_amount) AS total
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyDateIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null !=cashierDailyDateIDTO.shopCode and '' != cashierDailyDateIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyDateIDTO.shopCode}
        </if>

        <if test="cashierDailyDateIDTO.startTime != null and cashierDailyDateIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyDateIDTO.startTime}
        </if>
        <if test="cashierDailyDateIDTO.EndTime != null and cashierDailyDateIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyDateIDTO.EndTime}
        </if>
        group by tprcw.shop_id,
        year(tprcw.create_time),
        month(tprcw.create_time),
        day(tprcw.create_time)
        ORDER BY tprcw.shop_id ASC ,
        year(tprcw.create_time) DESC ,
        month(tprcw.create_time) DESC,
        day(tprcw.create_time) DESC
    </select>
    <select id="selectSUMCashierDailyListByDate"   parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
        <foreach item="payType" collection="cashierDailyDateIDTO.payTypeList">
            SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
        </foreach>
        SUM(CASE WHEN 1=1 then tprcw.pay_amount else 0 end ) AS total
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyDateIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null != cashierDailyDateIDTO.shopCode and '' != cashierDailyDateIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyDateIDTO.shopCode}
        </if>

        <if test="cashierDailyDateIDTO.startTime != null and cashierDailyDateIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyDateIDTO.startTime}
        </if>
        <if test="cashierDailyDateIDTO.EndTime != null and cashierDailyDateIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyDateIDTO.EndTime}
        </if>
    </select>
    <!--收银日报按付款方式-->
    <select id="selectCashierDailyListByPayment"   parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">
        SELECT
        tprcw.shop_code AS shopCode,
        tprcw.shop_name AS shopName,
        tprcw.employee_number AS saleNumber,
        tprcw.create_name AS saleName,
        (case tprcw.sale_type when 1 then '销售' when 2 then '退货' end ) AS saleType,
        tprcw.pay_name  AS payType,
        SUM(CASE WHEN tprcw.pay_type = tprcw.pay_type then tprcw.pay_amount end ) AS payAmount,
        SUM(CASE WHEN tprcw.pay_type = tprcw.pay_type then tprcw.pay_amount end ) - SUM(CASE WHEN tprcw.pay_type = tprcw.pay_type AND tprcw.sale_type = 2 then tprcw.pay_amount else 0 end ) AS salesAmount,
        SUM(CASE WHEN tprcw.pay_type = tprcw.pay_type AND tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end )AS refundAmount
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyPaymentIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null != cashierDailyPaymentIDTO.shopCode and '' != cashierDailyPaymentIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyPaymentIDTO.shopCode}
        </if>
        <if test="cashierDailyPaymentIDTO.startTime != null and cashierDailyPaymentIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyPaymentIDTO.startTime}
        </if>
        <if test="cashierDailyPaymentIDTO.EndTime != null and cashierDailyPaymentIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyPaymentIDTO.EndTime}
        </if>
        <if test="cashierDailyPaymentIDTO.saleNumber != null and cashierDailyPaymentIDTO.saleNumber != ''">
            AND tprcw.employee_number = #{cashierDailyPaymentIDTO.saleNumber}
        </if>
        GROUP BY tprcw.shop_id,tprcw.pay_type ,tprcw.sale_type,tprcw.employee_number
        ORDER BY tprcw.shop_id ASC,tprcw.employee_number ASC ,ABS(sum(tprcw.pay_amount)) DESC
    </select>
    <select id="selectSUMCashierDailyListByPayment"   parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
        SUM(tprcw.pay_amount) AS payAmount,
        SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount else 0 end ) AS salesAmount,
        SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) else 0 end ) AS refundAmount
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierDailyPaymentIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null != cashierDailyPaymentIDTO.shopCode and '' != cashierDailyPaymentIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyPaymentIDTO.shopCode}
        </if>
        <if test="cashierDailyPaymentIDTO.startTime != null and cashierDailyPaymentIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierDailyPaymentIDTO.startTime}
        </if>
        <if test="cashierDailyPaymentIDTO.EndTime != null and cashierDailyPaymentIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierDailyPaymentIDTO.EndTime}
        </if>
        <if test="cashierDailyPaymentIDTO.saleNumber != null and cashierDailyPaymentIDTO.saleNumber != ''">
            AND tprcw.employee_number = #{cashierDailyPaymentIDTO.saleNumber}
        </if>
    </select>

    <!--收银员对账1-->
    <select id="selectCashierReconciledList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledODTO">
        SELECT
            tprcw.shop_id,
            tprcw.shop_code AS shopCode,
            tprcw.shop_name AS shopName,
            tprcw.employee_number AS saleNumber,
            tprcw.create_name AS saleName,
            (case tprcw.sale_type when 1 then '销售' when 2 then '退货' end ) AS saleType,
            tprcw.pay_name  AS payTypeName,
            tprcw.pay_type  AS payType,
            SUM(tprcw.pay_amount)AS payAmount,
            IFNULL(SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount end ),0) AS salesAmount,
            IFNULL(SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end ),0) AS refundAmount,
            tprcw.mac_code
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierReconciledIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test="cashierReconciledIDTO.startTime != null and cashierReconciledIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierReconciledIDTO.startTime}
        </if>
        <if test="cashierReconciledIDTO.EndTime != null and cashierReconciledIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierReconciledIDTO.EndTime}
        </if>
        <if test=" null !=cashierReconciledIDTO.shopCode and '' != cashierReconciledIDTO.shopCode">
            AND tprcw.shop_id = #{cashierReconciledIDTO.shopCode}
        </if>
        <if test=" null != cashierReconciledIDTO.saleNumber and '' != cashierReconciledIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierReconciledIDTO.saleNumber}
        </if>
        <if test=" null != cashierReconciledIDTO.payType and '' != cashierReconciledIDTO.payType">
            AND tprcw.pay_type = #{cashierReconciledIDTO.payType}
        </if>
        <if test=" 1 == cashierReconciledIDTO.sumOfMoney">
            AND tprcw.pay_type != 'ZF027'
        </if>
        <if test="cashierReconciledIDTO.macCode != null and cashierReconciledIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierReconciledIDTO.macCode}, '%')
        </if>
        GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code,tprcw.pay_type
        ORDER BY tprcw.shop_id ASC,tprcw.employee_number ASC ,ABS(sum(tprcw.pay_amount)) DESC
    </select>
    <select id="selectSUMCashierReconciledList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledSUMODTO">
        SELECT
            SUM(tprcw.pay_amount)AS payAmount,
            IFNULL(SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount end ),0) AS salesAmount,
            IFNULL(SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end ),0) AS refundAmount
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="cashierReconciledIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test="cashierReconciledIDTO.startTime != null and cashierReconciledIDTO.startTime != ''">
            AND tprcw.create_time >= #{cashierReconciledIDTO.startTime}
        </if>
        <if test="cashierReconciledIDTO.EndTime != null and cashierReconciledIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{cashierReconciledIDTO.EndTime}
        </if>
        <if test=" null != cashierReconciledIDTO.shopCode and '' != cashierReconciledIDTO.shopCode">
            AND tprcw.shop_id = #{cashierReconciledIDTO.shopCode}
        </if>
        <if test=" null != cashierReconciledIDTO.saleNumber and '' != cashierReconciledIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierReconciledIDTO.saleNumber}
        </if>
        <if test=" null != cashierReconciledIDTO.payType and '' !=cashierReconciledIDTO.payType">
            AND tprcw.pay_type = #{cashierReconciledIDTO.payType}
        </if>
        <if test=" 1 == cashierReconciledIDTO.sumOfMoney">
            AND tprcw.pay_type != 'ZF027'
        </if>
        <if test="cashierReconciledIDTO.macCode != null and cashierReconciledIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierReconciledIDTO.macCode}, '%')
        </if>
    </select>

    <!-- 收银员对账 统计笔数-->
    <select id="selectCashierWaterCount"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierWaterCountODTO">
        SELECT
            t.shop_id,
            t.employee_number,
            t.mac_code,
            t.pay_type,
            count(DISTINCT t.order_code),
            count(DISTINCT case when sale_type = 1 then order_code end) saleCount,
            count(DISTINCT case when sale_type = 2 then order_code end) returnCount
        FROM t_pos_report_cashier_water t
        WHERE
         t.shop_id IN
        <foreach collection="cashierReconciledIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test="cashierReconciledIDTO.startTime != null and cashierReconciledIDTO.startTime != ''">
            AND t.create_time >= #{cashierReconciledIDTO.startTime}
        </if>
        <if test="cashierReconciledIDTO.EndTime != null and cashierReconciledIDTO.EndTime != ''">
            AND t.create_time <![CDATA[ <= ]]> #{cashierReconciledIDTO.EndTime}
        </if>
        <if test=" null != cashierReconciledIDTO.shopCode and '' != cashierReconciledIDTO.shopCode">
            AND t.shop_id = #{cashierReconciledIDTO.shopCode}
        </if>
        <if test=" null != cashierReconciledIDTO.saleNumber and '' != cashierReconciledIDTO.saleNumber">
            AND t.employee_number = #{cashierReconciledIDTO.saleNumber}
        </if>
        <if test=" null != cashierReconciledIDTO.payType and '' !=cashierReconciledIDTO.payType">
            AND t.pay_type = #{cashierReconciledIDTO.payType}
        </if>
        <if test=" 1 == cashierReconciledIDTO.sumOfMoney">
            AND t.pay_type != 'ZF027'
        </if>
        <if test="cashierReconciledIDTO.macCode != null and cashierReconciledIDTO.macCode != ''">
            AND t.mac_code LIKE concat('%', #{cashierReconciledIDTO.macCode}, '%')
        </if>
        GROUP BY t.shop_id,t.employee_number,t.mac_code,t.pay_type
    </select>


    <!--收银员对账-日对账-->
    <select id="selectDiurnalReconciliationList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledODTO">
        SELECT
        tprcw.shop_code AS shopCode,
        tprcw.shop_name AS shopName,
        DATE_FORMAT(tprcw.create_time,'%Y-%c-%d') AS newDate,
        tprcw.employee_number AS saleNumber,
        tprcw.create_name AS saleName,
        (CASE tprcw.sale_type WHEN 1 THEN '销售' WHEN 2 THEN '退货' END) AS saleType,
        tprcw.pay_name AS payType,
        SUM(tprcw.pay_amount)AS salesAmount
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="diurnalReconciliationIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null != diurnalReconciliationIDTO.shopCode and '' != diurnalReconciliationIDTO.shopCode">
            AND tprcw.shop_id = #{diurnalReconciliationIDTO.shopCode}
        </if>

        <if test="diurnalReconciliationIDTO.startTime != null and diurnalReconciliationIDTO.startTime != ''">
            AND tprcw.create_time >= #{diurnalReconciliationIDTO.startTime}
        </if>
        <if test="diurnalReconciliationIDTO.EndTime != null and diurnalReconciliationIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{diurnalReconciliationIDTO.EndTime}
        </if>

        <if test=" null !=diurnalReconciliationIDTO.saleNumber and '' != diurnalReconciliationIDTO.saleNumber">
            AND tprcw.employee_number = #{diurnalReconciliationIDTO.saleNumber}
        </if>
        <if test=" null != diurnalReconciliationIDTO.saleType and '' != diurnalReconciliationIDTO.saleType">
            AND tprcw.sale_type = #{diurnalReconciliationIDTO.saleType}
        </if>
        <if test=" null != diurnalReconciliationIDTO.payType and '' != diurnalReconciliationIDTO.payType">
            AND tprcw.pay_type = #{diurnalReconciliationIDTO.payType}
        </if>
        <if test=" 1 == diurnalReconciliationIDTO.sumOfMoney">
            AND tprcw.pay_type != 'ZF027'
        </if>
        GROUP BY tprcw.pay_type,tprcw.sale_type,tprcw.shop_id,tprcw.create_name,
        year(tprcw.create_time),
        month(tprcw.create_time),
        day(tprcw.create_time)
        ORDER BY  year(tprcw.create_time) DESC,month(tprcw.create_time) DESC,day(tprcw.create_time) DESC  ,tprcw.shop_code ASC ,tprcw.employee_number ASC ,ABS(sum(tprcw.pay_amount)) DESC
    </select>
    <select id="selectSUMDiurnalReconciliationList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledSUMODTO">
        SELECT
        sum(tprcw.pay_amount) AS salesAmount
        FROM
        t_pos_report_cashier_water_day tprcw
        WHERE
        tprcw.shop_id IN
        <foreach collection="diurnalReconciliationIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null != diurnalReconciliationIDTO.shopCode and '' != diurnalReconciliationIDTO.shopCode">
            AND tprcw.shop_id = #{diurnalReconciliationIDTO.shopCode}
        </if>

        <if test="diurnalReconciliationIDTO.startTime != null and diurnalReconciliationIDTO.startTime != ''">
            AND tprcw.create_time >= #{diurnalReconciliationIDTO.startTime}
        </if>
        <if test="diurnalReconciliationIDTO.EndTime != null and diurnalReconciliationIDTO.EndTime != ''">
            AND tprcw.create_time <![CDATA[ <= ]]> #{diurnalReconciliationIDTO.EndTime}
        </if>
        <if test=" null != diurnalReconciliationIDTO.saleNumber and '' != diurnalReconciliationIDTO.saleNumber">
            AND tprcw.employee_number = #{diurnalReconciliationIDTO.saleNumber}
        </if>
        <if test=" null != diurnalReconciliationIDTO.saleType and '' != diurnalReconciliationIDTO.saleType">
            AND tprcw.sale_type = #{diurnalReconciliationIDTO.saleType}
        </if>
        <if test=" null != diurnalReconciliationIDTO.payType and '' != diurnalReconciliationIDTO.payType">
            AND tprcw.pay_type = #{diurnalReconciliationIDTO.payType}
        </if>
        <if test=" 1 == diurnalReconciliationIDTO.sumOfMoney">
            AND tprcw.pay_type != 'ZF027'
        </if>
    </select>

</mapper>