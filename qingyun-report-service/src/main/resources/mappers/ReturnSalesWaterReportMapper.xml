<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.ReturnSalesWaterReportMapper">
    <insert id="replaceReturnFromSalesBySaleTime" parameterType="java.lang.String">
        REPLACE
        INTO t_pos_report_return_sales_water (
                        id,
                        order_code,
                        sale_time,
                        commodity_code,
                        commodity_name,
                        unit,
                        commodity_spec,
                        transaction_price,
                        quantity,
                        transaction_amount,
                        retail_price,
                        retail_amount,
                        transaction_retail_rate,
                        backgroud_bargain_price,
                        member_price,
                        hand_bargain_end,
                        hand_discount_end,
                        background_promotion_share_end,
                        order_bargain_share_end,
                        order_discount_share_end,
                        bar_code,
                        member_card_no,
                        member_name,
                        order_total_amount,
                        commodity_first_id,
                        commodity_first_kind,
                        commodity_second_id,
                        commodity_second_kind,
                        commodity_third_id,
                        commodity_third_kind,
                        brand_name,
                        employee_number,
                        create_name,
                        op_user_code,
                        op_user_name,
                        return_reason,
                        supplier_name,
                        origin_order_code,
                        promotion_code,
                        shop_id,
                        shop_name,
                        pos_mac_id,
                        mac_code,
                        weight_code,
                        coupon_code,
                        origin_sale_time,
                        transaction_channel,
                        origin_transaction_amount
                        )
        SELECT distinct prsw.id,
                        prsw.order_code,
                        prsw.sale_time,
                        prsw.commodity_code,
                        prsw.commodity_name,
                        prsw.unit,
                        prsw.commodity_spec,
                        prsw.transaction_price,
                        prsw.quantity,
                        prsw.transaction_amount,
                        prsw.retail_price,
                        prsw.retail_amount,
                        prsw.transaction_retail_rate,
                        prsw.backgroud_bargain_price,
                        prsw.member_price,
                        prsw.hand_bargain_end,
                        prsw.hand_discount_end,
                        prsw.background_promotion_share_end,
                        prsw.order_bargain_share_end,
                        prsw.order_discount_share_end,
                        prsw.bar_code,
                        prsw.member_card_no,
                        prsw.member_name,
                        prsw.order_total_amount,
                        prsw.commodity_first_id,
                        prsw.commodity_first_kind,
                        prsw.commodity_second_id,
                        prsw.commodity_second_kind,
                        prsw.commodity_third_id,
                        prsw.commodity_third_kind,
                        prsw.brand_name,
                        prsw.employee_number,
                        prsw.create_name,
                        prsw.op_user_code,
                        prsw.op_user_name,
                        prsw.return_reason,
                        prsw.supplier_name,
                        prsw.origin_order_code,
                        prsw.promotion_code,
                        prsw.shop_id,
                        prsw.shop_name,
                        prsw.pos_mac_id,
                        prsw.mac_code,
                        prsw.weight_code,
                        prsw.coupon_code,
                        prsw1.sale_time,
                        CASE LEFT (prsw.order_code, 1)
            WHEN 8 THEN
            "联网收银"
            WHEN 9 THEN
            "本地收银"
            ELSE
            "未知"
        END
        ,
                        IF (
                            prsw.origin_order_code IS NULL,
                            NULL,
                            prsw.transaction_amount
                        )
        FROM
	        t_pos_report_sales_water prsw
            LEFT JOIN t_pos_report_sales_water prsw1 ON prsw.origin_order_code = prsw1.order_code
		WHERE prsw.sale_type= 2
     and prsw.sale_time  BETWEEN #{start} and #{end}
    </insert>
    <select id="queryList" parameterType="com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterODTO">
        SELECT
        prrsw.shop_name shopName,
        ms.shop_code shopCode,
        prrsw.mac_code macCode,
        prrsw.employee_number employeeNumber,
        prrsw.create_name createName,
        DATE_FORMAT(prrsw.sale_time,'%Y-%m-%d') saleTime,
        prrsw.return_reason returnReason,
        IF (prrsw.origin_order_code IS NULL,"无原单","有原单") originExist,
        prrsw.transaction_amount transactionAmount,
        prrsw.origin_transaction_amount originTransactionAmount,
        prrsw.backgroud_bargain_price backgroudBargainPrice,
        IF(prrsw.hand_bargain_end is null ,prrsw.hand_discount_end,prrsw.hand_bargain_end) handEnd,
        prrsw.order_code orderCode,
        DATE_FORMAT(prrsw.sale_time,'%H:%i:%s') saleTimeHms,
        prrsw.commodity_code commodityCode,
        prrsw.bar_code barCode,
        prrsw.commodity_name commodityName,
        prrsw.commodity_spec commoditySpec,
        prrsw.transaction_price transactionPrice,
        prrsw.quantity quantity,
        prrsw.unit unit,
        prrsw.op_user_code opUserCode,
        prrsw.op_user_name opUserName,
        prrsw.transaction_channel transactionChannel,
        DATE_FORMAT(prrsw.origin_sale_time,'%Y-%m-%d') originSaleTime,
        DATE_FORMAT(prrsw.origin_sale_time,'%H:%i:%s') originSaleTimeHms,
        prrsw.origin_order_code originOrderCode,
        prrsw.member_card_no memberCardNo,
        prrsw.member_name memberName,
        prrsw.commodity_first_kind commodityFirstKind,
        prrsw.commodity_second_kind commoditySecondKind,
        prrsw.commodity_third_kind commodityThirdKind,
        prrsw.brand_name brandName,
        prrsw.supplier_name supplierName
        FROM
        t_pos_report_return_sales_water prrsw left join t_md_shop ms on prrsw.shop_id = ms.id
        where
        prrsw.shop_id IN
        <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="saleTimeBegin != null and saleTimeBegin != '' and saleTimeEnd!= null and saleTimeEnd !=''">
            AND prrsw.sale_time BETWEEN #{saleTimeBegin} and #{saleTimeEnd}
        </if>
        <if test="employeeNumber != null and employeeNumber != ''">
            AND prrsw.employee_number= #{employeeNumber}
        </if>
        <if test="originExist ==1">
            AND  prrsw.origin_order_code is not  NULL
        </if>
        <if test="originExist ==0">
            AND  prrsw.origin_order_code is NULL
        </if>
        <if test="returnReason != null and returnReason != '' ">
            AND prrsw.return_reason =#{returnReason}
        </if>
        <if test="commodityCode != null and commodityCode != '' ">
            AND prrsw.commodity_code =#{commodityCode}
        </if>
        <if test="barCode != null and barCode != '' ">
            AND prrsw.bar_code =#{barCode}
        </if>
        <if test="commodityFirstId != null and commodityFirstId != '' ">
            AND prrsw.commodity_first_id =#{commodityFirstId}
        </if>
        <if test="orderCode != null and orderCode != '' ">
            AND prrsw.order_code =#{orderCode}
        </if>
        <if test="originOrderCode != null and originOrderCode != '' ">
            AND prrsw.origin_order_code =#{originOrderCode}
        </if>
        <if test="priceMin != null and priceMin != '' and priceMax!= null and priceMax !=''">
            AND prrsw.order_total_amount BETWEEN #{priceMin} and #{priceMax}
        </if>
        <if test="hourBegin != null and hourBegin != ''">
            AND date_format(prrsw.sale_time,'%k:%i') <![CDATA[ >= ]]> #{hourBegin}
        </if>
        <if test="hourEnd != null and hourEnd != ''">
            AND date_format(prrsw.sale_time,'%k:%i') <![CDATA[ <= ]]> #{hourEnd}
        </if>
        order by prrsw.sale_time desc ,prrsw.commodity_code
    </select>


    <select id="queryByCommodityCodeList" resultType="com.pinshang.qingyun.report.dto.pos.CommodityBarCodeDTO">
        select c.commodity_code commodityCode,c.id commodityId,cbc.bar_code barCode
        from t_commodity c LEFT JOIN t_commodity_bar_code cbc on c.id =
        cbc.commodity_id
        where c.commodity_code in
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="sumList" parameterType="com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.ReturnSalesWaterODTO">
        SELECT
        "合计" shopName,
        sum(prrsw.transaction_amount) transactionAmount,
        sum(prrsw.quantity) quantity,
        sum(case when prrsw.quantity <![CDATA[ < ]]> 0 then -ceil(prrsw.quantity/b.commodity_package_spec*-1)
        when prrsw.quantity <![CDATA[ > ]]> 0 then ceil(prrsw.quantity/b.commodity_package_spec)  end) as number
        FROM
        t_pos_report_return_sales_water prrsw
        left join t_commodity b on prrsw.commodity_code = b.commodity_code
        where
        prrsw.shop_id IN
        <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="saleTimeBegin != null and saleTimeBegin != '' and saleTimeEnd!= null and saleTimeEnd !=''">
            AND prrsw.sale_time BETWEEN #{saleTimeBegin} and #{saleTimeEnd}
        </if>
        <if test="employeeNumber != null and employeeNumber != ''">
            AND prrsw.employee_number= #{employeeNumber}
        </if>
        <if test="originExist ==1">
            AND  prrsw.origin_order_code is not  NULL
        </if>
        <if test="originExist ==0">
            AND  prrsw.origin_order_code is NULL
        </if>
        <if test="returnReason != null and returnReason != '' ">
            AND prrsw.return_reason =#{returnReason}
        </if>
        <if test="commodityCode != null and commodityCode != '' ">
            AND prrsw.commodity_code =#{commodityCode}
        </if>
        <if test="barCode != null and barCode != '' ">
            AND prrsw.bar_code =#{barCode}
        </if>
        <if test="commodityFirstId != null and commodityFirstId != '' ">
            AND prrsw.commodity_first_id =#{commodityFirstId}
        </if>
        <if test="orderCode != null and orderCode != '' ">
            AND prrsw.order_code =#{orderCode}
        </if>
        <if test="originOrderCode != null and originOrderCode != '' ">
            AND prrsw.origin_order_code =#{originOrderCode}
        </if>
        <if test="priceMin != null and priceMin != '' and priceMax!= null and priceMax !=''">
            AND prrsw.order_total_amount BETWEEN #{priceMin} and #{priceMax}
        </if>
        <if test="hourBegin != null and hourBegin != ''">
            AND date_format(prrsw.sale_time,'%k:%i') <![CDATA[ >= ]]> #{hourBegin}
        </if>
        <if test="hourEnd != null and hourEnd != ''">
            AND date_format(prrsw.sale_time,'%k:%i') <![CDATA[ <= ]]> #{hourEnd}
        </if>


    </select>
</mapper>