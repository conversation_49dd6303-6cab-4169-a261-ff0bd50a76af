<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.shop.ShopMapper">

    <select id="getAllShopList"  resultType="com.pinshang.qingyun.report.model.shop.Shop" >
        select
        t.id id
        from t_md_shop t
    </select>

    <select id="getAllShopListByShopType"  resultType="com.pinshang.qingyun.report.model.shop.Shop" >
        select
          t.id id
        from t_md_shop t
        where 1=1
        <if test="shopType != null ">
            and t.shop_type = #{shopType}
        </if>
    </select>

    <select id="getShopListByStoreCodes"  resultType="com.pinshang.qingyun.report.dto.ShopODTO" >
        SELECT
            s.id storeId,
            s.store_code storeCode,
            md.id shopId
        FROM t_store s
        LEFT JOIN t_md_shop md ON md.store_id = s.id
        where s.store_code IN
        <foreach collection="storeCodeList" index="index" item="storeCode" open="(" separator="," close=")">
            #{storeCode}
        </foreach>
    </select>

    <select id="getShopListByStoreIds"  resultType="com.pinshang.qingyun.report.dto.ShopODTO" >
        SELECT
            s.id storeId,
            s.store_code storeCode,
            s.store_name storeName
        FROM t_store s
        where s.id IN
        <foreach collection="storeIdList" index="index" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
    </select>


    <select id="getStoreListByShopIdList"  resultType="com.pinshang.qingyun.report.dto.ShopODTO" >
        SELECT
            s.id storeId,
            s.store_code storeCode,
            s.store_name,
            md.id shopId,
            md.shop_code,
            md.shop_name,
            md.shop_short_name
        FROM t_store s
        inner JOIN t_md_shop md ON md.store_id = s.id
        where md.id IN
        <foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </select>

    <select id = "shopIdByManagementMode" resultType="Long">
        select id from t_md_shop
        where management_mode = #{managementMode}
    </select>


    <select id = "queryShopIdListByParam" resultType="java.lang.Long">
        select t.id from t_md_shop t
        where 1 = 1
        <if test="shopTypeList != null and shopTypeList.size > 0">
            AND t.shop_type in
            <foreach collection="shopTypeList" index="index" item="shopType" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>

        <if test="managementModeList != null and managementModeList.size > 0">
            AND t.management_mode in
            <foreach collection="managementModeList" index="index" item="managementMode" open="(" separator="," close=")">
                #{managementMode}
            </foreach>
        </if>
    </select>
</mapper>