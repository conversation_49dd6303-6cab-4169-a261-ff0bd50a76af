<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.shop.CommodityShopTaxMapper">

	<select id="getPosCommodityTaxFromOffline" parameterType="java.lang.String" resultType="com.pinshang.qingyun.report.model.shop.CommodityTax">
		SELECT
			t.shop_id,
			t.commodity_id,
			sum(tatal_quantity) totalQuanty,
			sum(tatal_amount) totalSales,
			sum(weight_amount) costTotal
		FROM
		  t_pos_report_sales_summary t
		WHERE
		  t.sale_time=#{toDoDay}
		GROUP BY
		t.shop_id,
		t.commodity_id
	</select>

	<select id="getPosShopTaxFromOffline" parameterType="java.lang.String" resultType="com.pinshang.qingyun.report.dto.CommodityShopTaxODTO">
		SELECT
			t.shop_id,
			sum(tatal_quantity) totalQuanty,
			sum(tatal_amount) totalSales,
			sum(weight_amount) costTotal,
			count(DISTINCT t.order_code) offlineVisitorNumber
		FROM
			t_pos_report_sales_summary t
		WHERE
			t.sale_time = #{toDoDay}
		GROUP BY t.shop_id
	</select>

	<select id="queryOffLineVisitNum" parameterType="java.lang.String" resultType="com.pinshang.qingyun.report.model.shop.CommodityTax">
		SELECT
			t.shop_id,
			t.order_code,
			t.commodity_first_id,
			t.commodity_second_id,
			t.commodity_third_id
		FROM
		t_pos_report_sales_summary t
		WHERE
		t.sale_time = #{day}
	</select>


	<select id="queryOffLineVisitNumByType" parameterType="java.lang.String" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxODTO">
		SELECT
		    <if test="type == 0">
				count(DISTINCT t.order_code) visitNum,
			</if>
			<if test="type == 1">
				t.commodity_first_id,
				count(DISTINCT t.order_code) visitNum,
			</if>
			<if test="type == 2">
				t.commodity_second_id,
				count(DISTINCT t.order_code) visitNum,
			</if>
			t.shop_id
		FROM
		   t_pos_report_sales_summary t
		WHERE
		t.sale_time = #{day}
		<if test="type == 0">
			GROUP BY t.shop_id
		</if>
		<if test="type == 1">
			GROUP BY t.shop_id,t.commodity_first_id
		</if>
		<if test="type == 2">
			GROUP BY t.shop_id,t.commodity_second_id
		</if>
	</select>

	<select id="onToOnLineAnalyzeCode" parameterType="com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO"
			resultType = "com.pinshang.qingyun.report.dto.OnToOffAnalyzeDTO">
	</select>

	<select id="onToOffLineDataAnalyze" parameterType="com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO"
			resultType = "com.pinshang.qingyun.report.dto.OnToOffAnalyzeDTO">
	</select>

	<select id="onToOffAnalyze" parameterType="com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO"
	resultType = "com.pinshang.qingyun.report.dto.OnToOffAnalyzeDTO">
		SELECT ifnull(sum(cft.offline_sales), 0) AS sales, ifnull(sum(cft.offline_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.offline_sales &lt; 0, null, cft.shop_id))) as shopNum,
		       1 as 'time', 2 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE cft.date_time = #{today}
		AND cft.offline_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
		UNION ALL
		SELECT ifnull(sum(cft.offline_sales), 0) AS sales, ifnull(sum(cft.offline_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.offline_sales &lt; 0, null, cft.shop_id))) as shopNum,
		       2 as 'time', 2 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE cft.date_time = #{lastWeek}
		AND cft.offline_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
		UNION ALL
		SELECT ifnull(sum(cft.offline_sales), 0) as sales, ifnull(sum(cft.offline_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.offline_sales &lt; 0, null, cft.shop_id))) as shopNum,
		       3 as 'time', 2 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE  cft.date_time BETWEEN #{monthFirst} AND #{today}
		AND cft.offline_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
		UNION ALL
		SELECT  ifnull(sum(cft.offline_sales),0) as sales, ifnull(sum(cft.offline_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.offline_sales &lt; 0, null, cft.shop_id))) as shopNum,
		        4 as 'time', 2 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE  cft.date_time BETWEEN #{lastMonthFirst} AND #{lastMonthToday}
		AND cft.offline_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
		UNION ALL
        SELECT ifnull(sum(cft.online_sales), 0) as sales, ifnull(sum(cft.online_visitor_number), 0) as visitorNumber,	count(DISTINCT(if(cft.online_sales &lt; 0, null, cft.shop_id))) as shopNum,
               1 as 'time', 1 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE cft.date_time = #{today}
        AND cft.online_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
        UNION ALL
		SELECT ifnull(sum(cft.online_sales), 0) as sales, ifnull(sum(cft.online_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.online_sales &lt; 0, null, cft.shop_id))) as shopNum,
		       2 as 'time', 1 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE cft.date_time = #{lastWeek}
        AND cft.online_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>

		UNION ALL

        SELECT ifnull(sum(cft.online_sales), 0) as sales, ifnull(sum(cft.online_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.online_sales &lt; 0, null, cft.shop_id))) as shopNum,
               3 as 'time', 1 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE  cft.date_time BETWEEN #{monthFirst} AND #{today}
        AND cft.online_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
		UNION ALL
        SELECT  ifnull(sum(cft.online_sales), 0) as sales, ifnull(sum(cft.online_visitor_number), 0) as visitorNumber, count(DISTINCT(if(cft.online_sales &lt; 0, null, cft.shop_id))) as shopNum,
                4 as 'time', 1 as 'channel'
		FROM t_md_commodity_shop_tax cft
		WHERE  cft.date_time BETWEEN #{lastMonthFirst} AND #{lastMonthToday}
        AND cft.online_sales != 0
		AND cft.shop_id IN
		<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
	</select>


	<select id="getPosCommodityTaxFromOfflineByCate"  resultType="com.pinshang.qingyun.report.dto.shop.ShopTaxForJmByCateODTO">
		SELECT
		t.shop_id,
		sum(tatal_quantity) totalQuantity,
		sum(tatal_amount) totalSales,
		<if test="idto.type == 1">
			c.commodity_first_kind_id
		</if>
		<if test="idto.type == 2">
			c.commodity_first_kind_id,
			c.commodity_second_kind_id
		</if>
		<if test="idto.type == 3">
			c.commodity_first_kind_id,
			c.commodity_second_kind_id,
			c.commodity_third_kind_id
		</if>
		<if test="idto.type == 4">
			t.commodity_id,
			c.commodity_first_kind_id,
			c.commodity_second_kind_id,
			c.commodity_third_kind_id
		</if>
		FROM
		 t_pos_report_sales_summary t
		INNER JOIN t_commodity c ON c.id = t.commodity_id
		INNER JOIN t_md_shop ms ON ms.`id` = t.`shop_id`
		WHERE t.sale_time between #{idto.startDate} and #{idto.endDate}
		<if test = "null != idto.shopId">
			AND t.shop_id = #{idto.shopId}
		</if>
		AND ms.`shop_type` = #{idto.shopType}
		<if test="idto.cate1 != null">
			AND c.commodity_first_kind_id = #{idto.cate1}
		</if>
		<if test="idto.cate2 != null">
			AND c.commodity_second_kind_id = #{idto.cate2}
		</if>
		<if test="idto.cate3 != null">
			AND c.commodity_third_kind_id = #{idto.cate3}
		</if>
		<if test="idto.commodityId != null">
			and t.commodity_id = #{idto.commodityId}
		</if>
		<if test="idto.barCode != null and idto.barCode != '' ">
			and t.commodity_id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{idto.barCode} )
		</if>
		GROUP BY
		t.shop_id,
		<if test="idto.type == 1">
			c.commodity_first_kind_id
		</if>
		<if test="idto.type == 2">
			c.commodity_second_kind_id
		</if>
		<if test="idto.type == 3">
			c.commodity_third_kind_id
		</if>
		<if test="idto.type == 4">
			t.commodity_id
		</if>
	</select>
<!--	<select id = "onToOffAnalyze1" parameterType="com.pinshang.qingyun.report.dto.OnToOffAnalyzeIDTO"-->
<!--	resultType = "com.pinshang.qingyun.report.dto.CommodityShopTaxDTO">-->
<!--		select ifnull(sum(cft.offline_sales), 0) as offlineSales, ifnull(sum(cft.offline_visitor_number), 0) as offlineVisitorNumber, ifnull(sum(cft.offline_average_amount), 0) as offlineAverageAmount,-->
<!--		ifnull(sum(cft.online_sales), 0) as onlineSales, ifnull(sum(cft.online_visitor_number), 0) as onlineVisitorNumber, ifnull(sum(cft.online_average_amount), 0) as onlineAverageAmount,-->
<!--		count(DISTINCT(cft.shop_id)) as shopNum, 1 as type-->
<!--		from t_md_commodity_shop_tax cft-->
<!--		where cft.date_time = #{dto.today}-->
<!--		and cft.shop_id in-->
<!--		<foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">-->
<!--			#{shopId}-->
<!--		</foreach>-->
<!--		union ALL-->
<!--		select ifnull(sum(cft.offline_sales), 0) as offlineSales, ifnull(sum(cft.offline_visitor_number), 0) as offlineVisitorNumber, ifnull(sum(cft.offline_average_amount), 0) as offlineAverageAmount,-->
<!--				ifnull(sum(cft.online_sales), 0) as onlineSales, ifnull(sum(cft.online_visitor_number), 0) as onlineVisitorNumber, ifnull(sum(cft.online_average_amount), 0) as onlineAverageAmount,-->
<!--				count(DISTINCT(cft.shop_id)) as shopNum, 2 as 'type'-->
<!--		from t_md_commodity_shop_tax cft-->
<!--		where cft.date_time = #{dto.lastWeek}-->
<!--		and cft.shop_id in-->
<!--		<foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">-->
<!--			#{shopId}-->
<!--		</foreach>-->
<!--		union ALL-->
<!--		select ifnull(sum(cft.offline_sales), 0) as offlineSales, ifnull(sum(cft.offline_visitor_number), 0) as offlineVisitorNumber, ifnull(sum(cft.offline_average_amount), 0) as offline_average_amount,-->
<!--		       ifnull(sum(cft.online_sales), 0) as onlineSales, ifnull(sum(cft.online_visitor_number), 0) as onlineVisitorNumber, ifnull(sum(cft.online_average_amount), 0) as onlineAverageAmount,-->
<!--		       count(DISTINCT(cft.shop_id)) as shopNum, 3 as 'type'-->
<!--		from t_md_commodity_shop_tax cft-->
<!--		where cft.shop_id in-->
<!--		<foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">-->
<!--			#{shopId}-->
<!--		</foreach>-->
<!--		and cft.date_time BETWEEN #{dto.monthFirst} and #{dto.today}-->
<!--		union all-->
<!--		select  ifnull(sum(cft.offline_sales),0) as offlineSales, ifnull(sum(cft.offline_visitor_number), 0) as offlineVisitorNumber, ifnull(sum(cft.offline_average_amount), 0) as offline_average_amount,-->
<!--				ifnull(sum(cft.online_sales), 0) as onlineSales, ifnull(sum(cft.online_visitor_number), 0) as onlineVisitorNumber, ifnull(sum(cft.online_average_amount), 0) as onlineAverageAmount,-->
<!--				count(DISTINCT(cft.shop_id)) as shopNum, 4 as 'type'-->
<!--		from t_md_commodity_shop_tax cft-->
<!--		where cft.shop_id in-->
<!--		<foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">-->
<!--			#{shopId}-->
<!--		</foreach>-->
<!--		and cft.date_time BETWEEN #{dto.lastMonthFirst} and #{dto.lastMonthToday}-->
<!--	</select>-->
</mapper>