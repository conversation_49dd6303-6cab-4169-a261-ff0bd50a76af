<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.XsOrderMapper">

    <select id="selectOrderUserCountWithDay" parameterType="com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportIDTO"
            resultType="com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportODTO">
        SELECT
        count(DISTINCT user_id) AS count,
        DATE(create_time) AS createTime
        FROM
        t_xs_order
        WHERE 1=1
        <if test="enterpriseId !=null">
            AND enterprise_id = #{enterpriseId}
        </if>
         AND source_type=1
        <if test=" shopId != null ">
            AND shop_id = #{shopId}
        </if>
        <if test="startTime !=null and startTime !=''">
            AND create_time <![CDATA[ >= ]]> CONCAT(#{startTime},' 00:00:00')
        </if>
        <if test="endTime !=null and endTime !=''">
            AND create_time <![CDATA[ <= ]]> CONCAT(#{endTime},' 23:59:59')
        </if>
        GROUP BY
        DATE(create_time)
    </select>

    <select id="selectOrderReportList" parameterType="com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportIDTO"
            resultType="com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportODTO">
        SELECT
        COUNT(txo.id) AS count,
        SUM(txo.pay_amount) AS totalAmount,
        DATE(txpb.create_time) AS createTime
        FROM t_xs_order txo
        LEFT JOIN t_xs_pay_bill txpb ON txo.order_code = txpb.bill_code AND txpb.refer_type = 1
        WHERE  txo.source_type=1
        <choose>
            <when test="shopId != null "> AND txo.shop_id = #{shopId} </when>
            <otherwise>
                AND txo.shop_id IN ( SELECT id FROM t_md_shop WHERE shop_type = 2
                <if test=" status != null ">
                    AND status = #{status}
                </if>)
            </otherwise>
        </choose>

        <if test=" payStatus != null "> AND txo.pay_status = #{payStatus} </if>
        <if test=" statusList != null "> AND txo.order_status in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime !=null and startTime !=''">
            AND txpb.create_time <![CDATA[ >= ]]> CONCAT(#{startTime},' 00:00:00')
        </if>
        <if test="endTime !=null and endTime !=''">
            AND txpb.create_time <![CDATA[ <= ]]> CONCAT(#{endTime},' 23:59:59')
        </if>
        GROUP BY DATE(txpb.create_time)
        ORDER BY DATE(txpb.create_time)
    </select>

    <select id="selectOutOrderReportList" parameterType="com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportIDTO"
            resultType="com.pinshang.qingyun.report.dto.xsOrder.XsOrderReportODTO">
        SELECT
        temp.delivery_end_time AS createTime,
        count(*) AS count,
        sum(temp.real_amount) AS totalAmount
        FROM (
        SELECT
        txo.order_code,
        DATE(txo.delivery_end_time) AS delivery_end_time,
        sum(txoi.real_amount) AS real_amount
        FROM t_xs_order txo
        LEFT JOIN t_xs_order_item txoi ON txo.id = txoi.order_id
        WHERE 1=1
        <if test="enterpriseId !=null">
            AND txo.enterprise_id = #{enterpriseId}
        </if>
        AND txo.source_type=1
        <if test=" shopId != null "> AND shop_id = #{shopId} </if>
        <if test=" shopId == null "> AND shop_id IN ( SELECT id FROM t_md_shop WHERE shop_type = 2
            <if test=" status != null "> AND status = #{status} </if>)
        </if>
        AND txo.pay_status = 2
        AND txo.order_status != 1
        <if test="startTime !=null and startTime !=''">
            AND delivery_end_time <![CDATA[ >= ]]> CONCAT(#{startTime},' 00:00:00')
        </if>
        <if test="endTime !=null and endTime !=''">
            AND delivery_end_time <![CDATA[ <= ]]> CONCAT(#{endTime},' 23:59:59')
        </if>
        GROUP BY order_code
        ) temp
        GROUP BY temp.delivery_end_time
        ORDER BY temp.delivery_end_time
    </select>
</mapper>