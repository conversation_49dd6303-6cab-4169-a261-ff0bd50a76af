<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.SalesWaterReportMapper">

    <sql id="listSalesWaterIDTOReportWhere">
        where 1=1
        <if test="salesWaterIDTO.beginTime != null and salesWaterIDTO.beginTime != ''">
            AND sale_time >= #{salesWaterIDTO.beginTime}
        </if>
        <if test="salesWaterIDTO.endTime != null and salesWaterIDTO.endTime != ''">
            AND sale_time <![CDATA[ <= ]]> #{salesWaterIDTO.endTime}
        </if>
        <if test="salesWaterIDTO.shopId != null and salesWaterIDTO.shopId != ''">
            AND shop_id = #{salesWaterIDTO.shopId}
        </if>
        <if test="salesWaterIDTO.shopId == null || salesWaterIDTO.shopId == ''">
            AND shop_id IN
            <foreach collection="salesWaterIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>

        <if test="salesWaterIDTO.employeeNumber != null and salesWaterIDTO.employeeNumber != ''">
            AND employee_number = #{salesWaterIDTO.employeeNumber}
        </if>
        <if test="salesWaterIDTO.macCode != null and salesWaterIDTO.macCode != ''">
            AND mac_code LIKE concat('%', #{salesWaterIDTO.macCode} ,'%')
        </if>
        <if test="salesWaterIDTO.commodityName != null and salesWaterIDTO.commodityName != ''">
            AND commodity_name LIKE concat('%', #{salesWaterIDTO.commodityName}, '%')
        </if>

        <if test="salesWaterIDTO.orderCode != null and salesWaterIDTO.orderCode != ''">
            AND order_code LIKE concat('%', #{salesWaterIDTO.orderCode}, '%')
        </if>
        <if test="salesWaterIDTO.saleType != null and salesWaterIDTO.saleType != ''">
            AND sale_type = #{salesWaterIDTO.saleType}
        </if>
        <if test="salesWaterIDTO.barCode != null and salesWaterIDTO.barCode != ''">
            AND bar_code LIKE concat('%', #{salesWaterIDTO.barCode}, '%')
        </if>
        <if test="salesWaterIDTO.cashChannel != null and salesWaterIDTO.cashChannel != ''">
            <choose>
                <when test = "salesWaterIDTO.cashChannel == 8">
                    AND (order_code LIKE concat(#{salesWaterIDTO.cashChannel} ,'%')
                    or order_code LIKE concat('6%') )
                </when>
                <otherwise>
                    AND order_code LIKE concat(#{salesWaterIDTO.cashChannel} ,'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="salesWaterIDTO.category1 != null and salesWaterIDTO.category1 != ''">
                AND commodity_first_id = #{salesWaterIDTO.category1}
            </when>
            <when test="salesWaterIDTO.category2 != null and salesWaterIDTO.category2 != ''">
                AND commodity_second_id = #{salesWaterIDTO.category2}
            </when>
            <when test="salesWaterIDTO.category3 != null and salesWaterIDTO.category3 != ''">
                AND commodity_third_id = #{salesWaterIDTO.category3}
            </when>
        </choose>
        <choose>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 4"> AND transaction_price = 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 1"> AND backgroud_bargain_price > 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 2"> AND hand_discount_end > 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 3"> AND hand_bargain_end > 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 5 "> AND background_promotion_share_end > 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 6"> AND order_bargain_share_end > 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 7"> AND order_discount_share_end > 0 </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 8"> AND coupon_code IS NOT NULL </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 9"> AND member_price IS NOT NULL </when>
            <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 10"> AND discount_coupon_share_end IS NOT NULL </when>
        </choose>
        <if test="salesWaterIDTO.beginAmount != null and salesWaterIDTO.beginAmount != ''">
            AND order_total_amount >= #{salesWaterIDTO.beginAmount}
        </if>
        <if test="salesWaterIDTO.endAmount != null and salesWaterIDTO.endAmount != ''">
            AND order_total_amount <![CDATA[<=]]> #{salesWaterIDTO.endAmount}
        </if>

        <if test="salesWaterIDTO.memberCardNo != null and salesWaterIDTO.memberCardNo != ''">
            AND member_card_no LIKE concat('%', #{salesWaterIDTO.memberCardNo} ,'%')
        </if>
        <if test="null != salesWaterIDTO.posType">
            and pos_type = #{salesWaterIDTO.posType}
        </if>
        <if test = "null != salesWaterIDTO.couponOfflineSn and salesWaterIDTO.couponOfflineSn != '' ">
            and coupon_offline_sn = #{salesWaterIDTO.couponOfflineSn}
        </if>
        <if test = "null != salesWaterIDTO.stallId">
            and stall_id = #{salesWaterIDTO.stallId}
        </if>
    </sql>

    <select id="listSalesWaterIDTOReport" parameterType="com.pinshang.qingyun.report.dto.pos.SalesWaterIDTO" resultType="com.pinshang.qingyun.report.dto.pos.SalesWaterODTO">
        SELECT
        CASE sale_type
          WHEN '1' THEN '销售'
          WHEN '2' THEN '退货'
        END AS sale_type,
        order_code,
        CASE left(order_code,1) WHEN 8 THEN '联网收银' WHEN 6 THEN '联网收银' WHEN 9 THEN '本地收银' ELSE '' END AS cash_channel,
        sale_time,
        commodity_code,
        commodity_name,
        unit,
        commodity_spec,
        CONVERT(transaction_price, DECIMAL(10, 2)) as transaction_price,
        CONVERT(quantity, DECIMAL(10, 3)) AS quantity,
        CONVERT(transaction_amount, DECIMAL(10, 2)) AS transaction_amount,
        CONVERT(retail_price, DECIMAL(10, 2)) AS retailPrice,
        CONVERT(retail_amount, DECIMAL(10, 2)) AS retail_amount,
        -- transaction_price/retailPrice
        concat(
            abs(
                CONVERT(transaction_price / retail_price * 100, DECIMAL(10, 2))
            ),
            '%'
        ) as transaction_retail_rate,
        CONVERT(backgroud_bargain_price, DECIMAL(10, 2)) AS backgroud_bargain_price,
        CONVERT(member_price, DECIMAL(10, 2)) AS member_price,
        CONVERT(hand_bargain_end, DECIMAL(10, 2)) AS hand_bargain_end,
        CONVERT(hand_discount_end, DECIMAL(10, 2)) AS hand_discount_end,
        CONVERT(background_promotion_share_end, DECIMAL(10, 2)) AS background_promotion_share_end,
        CONVERT(order_bargain_share_end, DECIMAL(10, 2)) AS order_bargain_share_end,
        CONVERT(order_discount_share_end, DECIMAL(10, 2)) AS order_discount_share_end,
        CONVERT(discount_coupon_share_end, DECIMAL(10, 2)) AS discount_coupon_share_end,
        bar_code,
        member_card_no,
        member_name,
        commodity_first_kind,
        commodity_second_kind,
        commodity_third_kind,
        brand_name,
        employee_number,
        create_name,
        op_user_code,
        op_user_name,
        return_reason,
        supplier_name,
        origin_order_code,
        promotion_code,
        discount_coupon_code,
        coupon_offline_sn,
        shop_name,
        mac_code,
        coupon_code,
        CASE pos_type
        WHEN 2 THEN '自助POS'
        WHEN 3 THEN 'PDA'
        ELSE '收银POS'
        END AS posTypeName,
        stall_id
        FROM t_pos_report_sales_water
        <include refid="listSalesWaterIDTOReportWhere"/>
        order by sale_time desc
    </select>


<!--    <select id="listSalesWaterIDTOHeaderReport" parameterType="com.pinshang.qingyun.report.dto.pos.SalesWaterIDTO"-->
<!--        resultType="com.pinshang.qingyun.report.dto.pos.SalesWaterHeaderODTO">-->
<!--        select-->
<!--          sum(quantity) as quantity,-->
<!--          sum(transaction_amount) as transaction_amount,-->
<!--          sum(retail_amount) as retail_amount-->
<!--        from t_pos_report_sales_water-->
<!--        <include refid="listSalesWaterIDTOReportWhere"/>-->
<!--    </select>-->

    <select id="listSalesWaterIDTOHeaderReport" parameterType="com.pinshang.qingyun.report.dto.pos.SalesWaterIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.SalesWaterHeaderODTO">
        select
        sum(a.quantity) as quantity,
        sum(a.transaction_amount) as transaction_amount,
        sum(a.retail_amount) as retail_amount,
        sum(case when a.quantity <![CDATA[ < ]]> 0 then -ceil(a.quantity/b.commodity_package_spec*-1)
                 when a.quantity <![CDATA[ > ]]> 0 then ceil(a.quantity/b.commodity_package_spec)  end) as number
        from t_pos_report_sales_water a
        left join t_commodity b on a.commodity_code = b.commodity_code
        <where>
            <if test="salesWaterIDTO.beginTime != null and salesWaterIDTO.beginTime != ''">
                AND a.sale_time >= #{salesWaterIDTO.beginTime}
            </if>
            <if test="salesWaterIDTO.endTime != null and salesWaterIDTO.endTime != ''">
                AND a.sale_time <![CDATA[ <= ]]> #{salesWaterIDTO.endTime}
            </if>
            <if test="salesWaterIDTO.shopId != null and salesWaterIDTO.shopId != ''">
                AND a.shop_id = #{salesWaterIDTO.shopId}
            </if>
            <if test="salesWaterIDTO.shopId == null || salesWaterIDTO.shopId == ''">
                AND a.shop_id IN
                <foreach collection="salesWaterIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>

            <if test="salesWaterIDTO.employeeNumber != null and salesWaterIDTO.employeeNumber != ''">
                AND a.employee_number = #{salesWaterIDTO.employeeNumber}
            </if>
            <if test="salesWaterIDTO.macCode != null and salesWaterIDTO.macCode != ''">
                AND a.mac_code LIKE concat('%', #{salesWaterIDTO.macCode} ,'%')
            </if>
            <if test="salesWaterIDTO.commodityName != null and salesWaterIDTO.commodityName != ''">
                AND a.commodity_name LIKE concat('%', #{salesWaterIDTO.commodityName}, '%')
            </if>

            <if test="salesWaterIDTO.orderCode != null and salesWaterIDTO.orderCode != ''">
                AND a.order_code LIKE concat('%', #{salesWaterIDTO.orderCode}, '%')
            </if>
            <if test="salesWaterIDTO.saleType != null and salesWaterIDTO.saleType != ''">
                AND a.sale_type = #{salesWaterIDTO.saleType}
            </if>
            <if test="salesWaterIDTO.barCode != null and salesWaterIDTO.barCode != ''">
                AND a.bar_code LIKE concat('%', #{salesWaterIDTO.barCode}, '%')
            </if>
            <if test="salesWaterIDTO.cashChannel != null and salesWaterIDTO.cashChannel != ''">
                AND a.order_code LIKE concat(#{salesWaterIDTO.cashChannel} ,'%')
            </if>
            <choose>
                <when test="salesWaterIDTO.category1 != null and salesWaterIDTO.category1 != ''">
                    AND a.commodity_first_id = #{salesWaterIDTO.category1}
                </when>
                <when test="salesWaterIDTO.category2 != null and salesWaterIDTO.category2 != ''">
                    AND a.commodity_second_id = #{salesWaterIDTO.category2}
                </when>
                <when test="salesWaterIDTO.category3 != null and salesWaterIDTO.category3 != ''">
                    AND a.commodity_third_id = #{salesWaterIDTO.category3}
                </when>
            </choose>
            <choose>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 4"> AND a.transaction_price = 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 1"> AND a.backgroud_bargain_price > 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 2"> AND a.hand_discount_end > 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 3"> AND a.hand_bargain_end > 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 5 "> AND a.background_promotion_share_end > 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 6"> AND a.order_bargain_share_end > 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 7"> AND a.order_discount_share_end > 0 </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 8"> AND a.coupon_code IS NOT NULL </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 9"> AND a.member_price IS NOT NULL </when>
                <when test="salesWaterIDTO.discountType != null and salesWaterIDTO.discountType == 10"> AND a.discount_coupon_share_end IS NOT NULL </when>
            </choose>
            <if test="salesWaterIDTO.beginAmount != null and salesWaterIDTO.beginAmount != ''">
                AND a.order_total_amount >= #{salesWaterIDTO.beginAmount}
            </if>
            <if test="salesWaterIDTO.endAmount != null and salesWaterIDTO.endAmount != ''">
                AND a.order_total_amount <![CDATA[<=]]> #{salesWaterIDTO.endAmount}
            </if>

            <if test="salesWaterIDTO.memberCardNo != null and salesWaterIDTO.memberCardNo != ''">
                AND a.member_card_no LIKE concat('%', #{salesWaterIDTO.memberCardNo} ,'%')
            </if>
            <if test="null != salesWaterIDTO.posType">
                and a.pos_type = #{salesWaterIDTO.posType}
            </if>
            <if test = "null != salesWaterIDTO.couponOfflineSn and salesWaterIDTO.couponOfflineSn != '' ">
                and a.coupon_offline_sn = #{salesWaterIDTO.couponOfflineSn}
            </if>
            <if test = "null != salesWaterIDTO.stallId">
                and a.stall_id = #{salesWaterIDTO.stallId}
            </if>
        </where>
    </select>

    <insert id="insertSalesOfTheWaterReport" parameterType="com.pinshang.qingyun.report.model.pos.ReportSalesWater">
        INSERT INTO t_pos_report_sales_water(sale_type, order_code, sale_time, commodity_code, commodity_name,
        unit, commodity_spec, transaction_price, quantity, transaction_amount, retail_price,
        retail_amount, transaction_retail_rate, backgroud_bargain_price, hand_bargain_end, bar_code,
        hand_discount_end, background_promotion_share_end, order_bargain_share_end, order_discount_share_end,
        commodity_first_id, commodity_first_kind, commodity_second_id, commodity_second_kind, commodity_third_id, commodity_third_kind,
        brand_name, employee_number, create_name, op_user_name, op_user_code, return_reason, supplier_name, origin_order_code,
        promotion_code, shop_id, shop_name, pos_mac_id, mac_code, weight_code, order_total_amount, coupon_code,
        member_card_no, member_name, member_price,pos_type,discount_coupon_code,discount_coupon_share_end,coupon_offline_sn, stall_id
        )VALUES
        <foreach collection="salesWaters" item="report" separator=",">
            (#{report.saleType}, #{report.orderCode}, #{report.saleTime}, #{report.commodityCode}, #{report.commodityName},
            #{report.unit},#{report.commoditySpec},#{report.transactionPrice},#{report.quantity}, #{report.transactionAmount}, #{report.retailPrice},
            #{report.retailAmount}, #{report.transactionRetailRate},#{report.backgroudBargainPrice}, #{report.handBargainEnd},  #{report.barCode},
            #{report.handDiscountEnd} ,#{report.backgroundPromotionShareEnd},#{report.orderBargainShareEnd},#{report.orderDiscountShareEnd},
            #{report.commodityFirstId}, #{report.commodityFirstKind}, #{report.commoditySecondId}, #{report.commoditySecondKind},#{report.commodityThirdId}, #{report.commodityThirdKind},#{report.brandName},#{report.employeeNumber},
            #{report.createName},#{report.opUserName}, #{report.opUserCode}, #{report.returnReason},#{report.supplierName}, #{report.originOrderCode},
            #{report.promotionCode},#{report.shopId},#{report.shopName},#{report.posMacId}, #{report.macCode}, #{report.weightCode}, #{report.orderTotalAmount}, #{report.couponCode}
            ,#{report.memberCardNo}, #{report.memberName}, #{report.memberPrice},#{report.posType},#{report.discountCouponCode},
             #{report.discountCouponShareEnd}, #{report.couponOfflineSn}, #{report.stallId})

        </foreach>
    </insert>

    <delete id="deleteSalesWaterReportByOrderCodes">
        DELETE  FROM  t_pos_report_sales_water WHERE order_code = #{orderCode}
    </delete>

    <delete id="deleteSalesWaterReportByTimeRange">
        DELETE  FROM  t_pos_report_sales_water
        WHERE sale_time BETWEEN #{beginTime} AND  #{endTime}
        <if test="shopId != null and shopId != 0 ">
            AND shop_id = #{shopId}
        </if>
    </delete>





    <sql id="listWeightPriceReportWhere">
        where 1=1
        <if test="vo.beginTime != null and vo.beginTime != '' and vo.endTime != null and vo.endTime != ''">
            AND t.sale_time between #{vo.beginTime} and #{vo.endTime}
        </if>

        <if test="vo.shopId != null and vo.shopId != ''">
            AND t.shop_id = #{vo.shopId}
        </if>

        <if test="vo.commodityId != null ">
            AND t.commodity_id = #{vo.commodityId}
        </if>

        <if test="vo.commodityKey != null and vo.commodityKey != ''">
            AND (tc.commodity_code LIKE concat('%', #{vo.commodityKey}, '%')  or tc.commodity_name LIKE concat('%', #{vo.commodityKey}, '%') )
        </if>

        <if test="vo.commodityPackageId != null">
            AND tc.commodity_package_id = #{vo.commodityPackageId}
        </if>
        <if test="vo.overPercent != null">
            AND abs(
            CONVERT(t.transaction_amount / (t.retail_price*t.quantity) * 100, DECIMAL(10, 2))
            ) >  #{vo.overPercent}
        </if>

    </sql>

    <select id="listWeightPriceReport"  resultType="com.pinshang.qingyun.report.dto.pos.WeightPriceODTO">
        SELECT
           t.sale_type,
           t.shop_id,
           md.shop_name,
           t.order_code,
           t.sale_time,
           t.commodity_id,
           tc.commodity_code,
           tc.commodity_name,
           tc.commodity_package_name,
           t.weight_code,
           t.weight_code_price,
           t.weight_code_amount,
           t.quantity,
           t.transaction_price,
           t.transaction_amount,
           t.retail_price,
           ROUND(t.retail_price*t.quantity,2) retailAmount,
           IF(ROUND(t.transaction_amount - t.retail_price*t.quantity,2) > 0,ROUND(t.transaction_amount - t.retail_price*t.quantity,2),0) overRetailAmount,
           t.promotion_price,
           abs(
            CONVERT(t.transaction_amount / ROUND((t.retail_price*t.quantity),2) * 100, DECIMAL(10, 2))
            )as transactionDivRetail
        FROM t_pos_weight_price t
        left join t_md_shop md on md.id = t.shop_id
        left join t_commodity tc on tc.id = t.commodity_id
        <include refid="listWeightPriceReportWhere"/>
        order by t.sale_time desc
    </select>

    <select id="listlistWeightPriceHeaderReport" resultType="com.pinshang.qingyun.report.dto.pos.WeightPriceODTO">
        select
            sum(t.weight_code_amount) weightCodeAmount,
            sum(t.quantity) quantity,
            sum(t.transaction_amount) transactionAmount,
            sum(ROUND(t.retail_price*t.quantity,2)) retailAmount,
            sum(IF(ROUND(t.transaction_amount - t.retail_price*t.quantity,2) > 0,ROUND(t.transaction_amount - t.retail_price*t.quantity,2),0)) overRetailAmount
        from t_pos_weight_price t
        left join t_commodity tc on tc.id = t.commodity_id
        <include refid="listWeightPriceReportWhere"/>
    </select>


    <select id="getWeightPriceList" resultType="com.pinshang.qingyun.report.dto.pos.WeightPriceODTO">
        select
           CONCAT(t.order_code,'',t.commodity_id) orderCode
        from t_pos_weight_price t
        where  1=1
        and t.order_code IN
        <foreach collection="orderCodeList" index="index" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
        and t.commodity_id IN
        <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
            #{commodityId}
        </foreach>
    </select>


    <select id="listWeightOverPriceReport"  resultType="com.pinshang.qingyun.report.dto.pos.WeightOverPriceODTO">
        SELECT
            md.id shopId,
            md.shop_name,
            tt.transaction_amount,
            tt.retail_amount,
            tt.over_amount
        FROM  t_md_shop md
           LEFT JOIN (
                SELECT
                    t.shop_id,
                    sum(t.transaction_amount) transaction_amount,
                    sum(t.retail_amount) retail_amount,
                    sum(t.over_amount) over_amount
                FROM t_pos_weight_over_price t
                where 1 = 1
                <if test="vo.beginTime != null and vo.beginTime != '' and vo.endTime != null and vo.endTime != ''">
                    AND t.sale_time between #{vo.beginTime} and #{vo.endTime}
                </if>
                <if test="vo.shopId != null and vo.shopId != ''">
                    AND t.shop_id = #{vo.shopId}
                </if>
                GROUP BY t.shop_id
        )tt on tt.shop_id = md.id
        where 1 = 1
        <if test="vo.overAmount != null">
            AND tt.over_amount >  #{vo.overAmount}
        </if>
        <if test="vo.shopId != null and vo.shopId != ''">
            AND md.id = #{vo.shopId}
        </if>
        order by md.shop_code
    </select>
    <select id="listWeightOverPriceHeaderReport"  resultType="com.pinshang.qingyun.report.dto.pos.WeightOverPriceODTO">
        SELECT
            sum(tt.transaction_amount) transactionAmount,
            sum(tt.retail_amount) retailAmount,
            sum(tt.over_amount) overAmount
        from (
            SELECT
                sum(t.transaction_amount) transaction_amount,
                sum(t.retail_amount) retail_amount,
                sum(t.over_amount) over_amount
            FROM t_pos_weight_over_price t
            where 1=1
            <if test="vo.beginTime != null and vo.beginTime != '' and vo.endTime != null and vo.endTime != ''">
                AND t.sale_time between #{vo.beginTime} and #{vo.endTime}
            </if>
            <if test="vo.shopId != null and vo.shopId != ''">
                AND t.shop_id = #{vo.shopId}
            </if>
            GROUP BY t.shop_id
            <if test="vo.overAmount != null">
                HAVING sum(t.over_amount) >  #{vo.overAmount}
            </if>
        ) tt
    </select>

    <select id="listWeightOverPriceDetail"  resultType="com.pinshang.qingyun.report.dto.pos.WeightOverPriceODTO">
        SELECT
            md.shop_name,
            date_format(t.sale_time, '%Y-%m-%d') saleTime,
            sum(t.transaction_amount) transactionAmount,
            sum(t.retail_amount) retailAmount,
            sum(t.over_amount) overAmount
        FROM
        t_pos_weight_over_price t
        LEFT JOIN t_md_shop md on md.id = t.shop_id
        where t.shop_id = #{shopId}
        and t.sale_time BETWEEN #{beginTime} and #{endTime}
        GROUP BY saleTime
        ORDER BY t.sale_time
    </select>


    <select id="selectOriginOrderNUll"  resultType="java.lang.Long">
        SELECT
            tt.order_code
        FROM
            (
                SELECT
                    t.order_code
                FROM
                    t_pos_report_cashier_water t
                WHERE
                    t.sale_type = 2
                  AND (t.return_order_code IS NULL or t.return_order_code = '')
                  AND t.create_time BETWEEN #{beginTime} and #{endTime}

                UNION

                SELECT
                    t.order_code
                FROM
                    t_pos_report_sales_water t
                WHERE
                    t.sale_type = 2
                  AND (t.origin_order_code IS NULL or t.origin_order_code = '')
                  AND t.sale_time BETWEEN #{beginTime} and #{endTime}
            ) tt
    </select>


    <update id="batchUpdateSaleWaterOriginOrder" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_pos_report_sales_water tp
            SET tp.origin_order_code =  #{item.orderCode}
            WHERE tp.order_code = #{item.returnCode}
        </foreach>
    </update>


    <select id="queryPosCateSummary"  resultType="com.pinshang.qingyun.report.dto.pos.CloudAllocationODTO">
        SELECT
            t.shop_id,
            t.commodity_first_id,
            sum(t.tatal_amount) totalAmount
        FROM t_pos_report_third_summary t
        where 1=1
        <if test="vo.beginTime != null and vo.beginTime != '' and vo.endTime != null and vo.endTime != ''">
            and t.sale_time BETWEEN  #{vo.beginTime} and  #{vo.endTime}
        </if>
        <if test="vo.shopIdList != null and vo.shopIdList.size > 0">
            AND t.shop_id IN
            <foreach collection="vo.shopIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.commodityFirstIdList != null and vo.commodityFirstIdList.size > 0">
            AND t.commodity_first_id IN
            <foreach collection="vo.commodityFirstIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t.shop_id,t.commodity_first_id
        ORDER BY t.commodity_first_id,sum(t.tatal_amount) desc
    </select>


    <select id="queryPosSalesWaterData"  resultType="com.pinshang.qingyun.report.dto.pos.PosSaleWaterODTO">
         SELECT
            c.id commodityId,
            SUM(t.quantity) quantity
        from t_pos_report_sales_water t
        LEFT JOIN t_commodity c on c.commodity_code = t.commodity_code
        where t.shop_id =  #{vo.shopId} and t.sale_type = 1
        and t.sale_time BETWEEN CONCAT(#{vo.saleTime},' 00:00:00') and CONCAT(#{vo.saleTime},' 23:59:59')
        AND c.id IN
        <foreach collection="vo.commodityIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY c.id
    </select>

    <select id = "queryConsignmentOrderSettle" resultType="com.pinshang.qingyun.report.dto.pos.ConsignmentOrderODTO">
        SELECT
            md.store_id,
            t.shop_id,
            sum(t.tatal_amount) deliveryAmount
        FROM t_pos_report_sales_summary t
        LEFT JOIN t_md_shop md on md.id = t.shop_id
        where t.sale_time BETWEEN #{dto.beginDate} and #{dto.endDate}
        <if test="dto.storeIdList != null and dto.storeIdList.size() > 0">
            AND md.store_id in
            <foreach collection="dto.storeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and t.consignment_id > 0
        GROUP BY t.shop_id
    </select>

    <select id = "salesWaterList" resultType="com.pinshang.qingyun.report.model.pos.ReportSalesWater"
    parameterType="com.pinshang.qingyun.report.dto.pos.joinShop.AnalysisBySynthesisIDTO">
        SELECT *
        FROM t_pos_report_sales_water a
        WHERE a.sale_time BETWEEN #{dto.startTime} AND #{dto.endTime}
          AND a.shop_id IN
        <foreach collection="dto.shopIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY a.sale_time asc
    </select>
    <select id="queryPosReportShopCommoditySumQuantity"
            resultType="com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportODTO">
        select t.shop_id,t.commodity_code,sum(t.quantity) sumQuantity
        from t_pos_report_sales_water t
        where t.sale_time between concat(#{dto.date},' 00:00:00') and concat(#{dto.date},' 23:59:59')
          and t.commodity_first_kind in
        <foreach collection="dto.commodityFirstKindNameList" separator="," open="(" close=")" item="commodityFirstKindName">
            #{commodityFirstKindName}
        </foreach>
        group by t.shop_id,t.commodity_code
    </select>

    <select id="selectShopPosSalesOrderList" resultType="com.pinshang.qingyun.report.dto.finance.PosSalesOrderODTO">
        SELECT
            s.order_code AS businessCode,
            DATE_FORMAT(s.sale_time,'%Y-%m-%d') AS businessDate,
            s.sale_time AS businessTime,
            s.id AS businessOrderItemId,
            s.quantity,
            s.stall_id,
            s.shop_id,
            s.commodity_code,
            <!-- s.commodity_name, -->
            shop.store_id,
            <!-- shop.management_mode, -->
            #{vo.businessType} AS businessType
        FROM t_pos_report_sales_water s
        LEFT JOIN t_md_shop shop ON shop.id = s.shop_id
        <where>
        <if test="vo.businessDate!=null and vo.businessDate!=''">
            AND s.sale_time BETWEEN STR_TO_DATE(CONCAT(#{vo.businessDate},' ','00:00:00'),'%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(CONCAT(#{vo.businessDate},' ','23:59:59'),'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="vo.businessOrderItemId!=null">
            AND s.id > #{vo.businessOrderItemId}
        </if>
        <choose>
            <when test="vo.businessType == 3">
                AND s.sale_type = 1
            </when>
            <when test="vo.businessType == 5">
                AND s.sale_type = 2
            </when>
        <otherwise>
                AND s.id = -1
        </otherwise>
        </choose>
        AND shop.management_mode = 3
        </where>
        ORDER BY s.id ASC
        LIMIT #{vo.limitQuantity}
    </select>
</mapper>