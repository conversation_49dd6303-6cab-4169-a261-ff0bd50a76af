<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.MemberAccountExecptionMapper">
    <!--收银日报-->
    <select id="listMemeberAccountExecptionUser"  parameterType="com.pinshang.qingyun.report.dto.pos.MemberExceptionUseIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.MemberExceptionUseODTO">
        SELECT
            mae.shop_id,
            mae.shop_name,
            ms.shop_code,
            mae.member_card_no,
            mae.member_name,
                CASE mae.member_type
                    WHEN '1' THEN '普通会员'
                END AS member_type,
            mae.cash_date,
            mae.sale_count,
            mae.sale_total_amount
        FROM t_pos_member_account_execption mae
        LEFT JOIN t_md_shop ms on mae.shop_id = ms.id
        WHERE 1=1
        <if test="idto.shopId != null and idto.shopId != ''">
            AND mae.shop_id = #{idto.shopId}
        </if>
        <if test="idto.beginTime != null and idto.beginTime != ''">
            AND mae.cash_date >= #{idto.beginTime}
        </if>
        <if test="idto.endTime != null and idto.endTime != ''">
            AND mae.cash_date <![CDATA[<=]]> #{idto.endTime}
        </if>
        <if test="idto.cardNo != null and idto.cardNo != ''">
            AND mae.member_card_no LIKE concat('%',#{idto.cardNo},'%')
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
            AND mae.shop_id IN
            <foreach collection="idto.shopIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test = "idto.shopCode != null and idto.shopCode != ''">
            AND ms.shop_code = #{idto.shopCode}
        </if>
        <if test = "idto.removeAbnormalUser != null and idto.removeAbnormalUser == 1 and
                    idto.nonIntegralUserIdList != null and idto.nonIntegralUserIdList.size > 0">
            AND mae.member_card_no NOT IN
            <foreach collection="idto.nonIntegralUserIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findCount" resultType="Integer">
        select
               count(*)
        from t_pos_member_account_execption
        where shop_id = #{shopId} and member_id = #{userId} and cash_date = #{cashDate}
    </select>

    <select id="findMemberAccountExecptionInfo" resultType="com.pinshang.qingyun.report.dto.ExceptionMemberODTO">
        SELECT
            tms.id AS shop_id,
            tms.shop_name,
            txo.user_id AS memberId,
            txu.user_name AS member_card_no,
            txu.nick_name AS member_name,
            txu.member_type,
            #{cashDate} AS cashDate,
            sum(txo.total_amount) AS saleTotalAmount
        FROM t_xs_order txo
        LEFT JOIN t_md_shop tms ON txo.shop_id = tms.id
        LEFT JOIN t_xs_user txu ON txo.user_id = txu.id
        WHERE txo.shop_id = #{shopId} AND txo.user_id = #{userId} AND DATE_FORMAT(txo.create_time, '%Y-%m-%d') = #{cashDate}
          AND txo.integral IS NOT NULL
    </select>

    <update id="updateSaleCount">
        update t_pos_member_account_execption
        set sale_count = #{saleCount}, sale_total_amount = #{saleTotalAmount}
        where shop_id = #{shopId} and member_id = #{userId} and cash_date = #{cashDate}
    </update>

    <insert id="insertExecptionMember">
        insert into t_pos_member_account_execption(shop_id, shop_name, member_id, member_card_no, member_name, member_type
          , sale_count, sale_total_amount, cash_date) values (#{execptionMember.shopId}, #{execptionMember.shopName},
          #{execptionMember.memberId}, #{execptionMember.memberCardNo}, #{execptionMember.memberName},
          #{execptionMember.memberType}, #{execptionMember.saleCount}, #{execptionMember.saleTotalAmount}, #{execptionMember.cashDate})
    </insert>



    <delete id="deleteMemberAccountExecptionBySaleTime">
        DELETE FROM t_pos_member_account_execption where cash_date = #{saleTime}
    </delete>

    <insert id="insertMemberAccountExecptionReport">
        insert into t_pos_member_account_execption(shop_id, shop_name, member_id, member_card_no, member_name, member_type
                                                  , sale_count, sale_total_amount, cash_date)

        SELECT
            t.shop_id,
            t.shop_name,
            t.member_card_no member_id,
            t.member_card_no,
            t.member_name,
            1 member_type,
            COUNT(DISTINCT t.order_code) sale_count,
            sum(t.transaction_amount) sale_total_amount,
            #{saleTime} cash_date
        FROM t_pos_report_sales_water t
        where t.sale_time BETWEEN CONCAT(#{saleTime},' 00:00:00')  AND CONCAT(#{saleTime},' 23:59:59')
          and (t.member_card_no is not null  and t.member_card_no != '')
        GROUP BY t.member_card_no,t.shop_id
        HAVING sale_count >2

    </insert>

    <delete id="deleteHandDiscountBySaleTime">
        DELETE FROM t_pos_report_hand_discount where cash_date = #{saleTime}
    </delete>

    <insert id="insertHandDiscountReport">
        insert into t_pos_report_hand_discount(shop_id, shop_name, casher_id, casher_number, cash_date, discount_total_amount
                                              , create_id, create_time, update_id, update_time)

        SELECT
            t.shop_id,
            t.shop_name,
            eu.user_id casher_id,
            t.cashier_number casher_number,
            #{saleTime} cash_date,
            sum(t.handle_discount_amount) discount_total_amount,
            1 create_id,NOW() create_time,1 update_id,NOW() update_time
        FROM t_pos_report_hand_discount_detail t
            LEFT JOIN t_employee_user eu on eu.employee_code = t.cashier_number
        where t.create_time BETWEEN CONCAT(#{saleTime},' 00:00:00')  AND CONCAT(#{saleTime},' 23:59:59')
        GROUP BY t.shop_id,t.cashier_number

    </insert>


    <delete id="deletePosReportHourSummaryBySaleTime">
        DELETE FROM t_pos_report_hour_summary where sale_time = #{saleTime}
    </delete>

    <insert id="insertPosReportHourSummary">
        Insert into t_pos_report_hour_summary(sale_amount,hour_time,sale_time,create_time,create_id)

        SELECT
            ROUND(sum(tt.guest_amount)/10000,2) saleAmount,
            tt.date_time  dateTime,
            tt.sale_time,NOW(),-1
        from(
                SELECT
                    SUBSTRING(
                            DATE_FORMAT(
                                    concat( date( t.create_time ), ' ', HOUR ( t.create_time ), ':', floor( MINUTE ( t.create_time ) / 30 ) * 30 ),
                                    '%Y-%m-%d %H:%i'
                                ),12,5) AS date_time,
                    t.guest_amount,
                    t.sale_time
                FROM t_pos_report_guest_list_summary t
                where t.sale_time = #{saleTime}
            )tt
        where tt.date_time BETWEEN '05:00' and '23:30'
        GROUP BY tt.date_time
    </insert>

</mapper>