<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.HelpCardCheckDailyMapper">
    <select id="helpCardCheckSummary" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryODTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryIDTO">
        SELECT
        d.area_id,
        d.area_name,
        d.shop_id,
        d.pay_code,
        SUM(d.confirm_amount) AS zfConfirmAmount,
        SUM(d.px_trader_amount) AS pxTraderAmount,
        SUM(d.diff_amount) AS diffAmount,
        ms.store_id
        FROM
        t_pos_help_card_check_daily d
        LEFT JOIN t_md_shop ms
        ON ms.id= d.shop_id
        WHERE 1=1
        <include refid="helpCardCheckSummaryCondition"/>
        GROUP BY d.shop_id,
        d.pay_code
    </select>

    <select id="helpCardCheckSummarySum"  resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckSummaryIDTO">
        SELECT
        SUM(d.confirm_amount) AS zfConfirmAmount,
        SUM(d.px_trader_amount) AS pxTraderAmount,
        SUM(d.diff_amount) AS diffAmount
        FROM
        t_pos_help_card_check_daily d
        WHERE 1=1
        <include refid="helpCardCheckSummaryCondition"/>
    </select>

    <sql id="helpCardCheckSummaryCondition">
        <if test="null != beginTime and '' != beginTime">
        AND d.transaction_date >= #{beginTime}
        </if>
        <if test="null != endTime and '' != endTime">
        AND d.transaction_date &lt; #{endTime}
        </if>
        <if test="null != areaId">
            AND d.area_id = #{areaId}
        </if>
        <if test="null != shopId">
            AND d.shop_id = #{shopId}
        </if>
        <if test="null != payCode and '' != payCode">
            AND d.pay_code = #{payCode}
        </if>
        <if test=" null != isDiff and 1== isDiff">
            AND (d.diff_amount > 0 || d.diff_amount &lt; 0)
        </if>
        <if test="shopIdList != null and shopIdList.size() > 0">
            AND d.`shop_id` in
            <foreach collection="shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>
