<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.HelpCardTransactionFlowMapper">
    <select id="selectAmountByDate" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardAmountByDate">
        SELECT
        s.`pay_code`, SUM(s.`amount`) as amount
        FROM t_pos_help_card_transaction_flow s
        WHERE  s.`transaction_date` >= #{beginTime}
        AND s.`transaction_date` <![CDATA[ <= ]]> #{endTime}
        <if test="null != payCode and '' !=payCode">
            AND s.pay_code = #{payCode}
        </if>
        GROUP BY s.`pay_code`
    </select>

    <select id="selectAmountByDateSum" resultType="java.math.BigDecimal">
        SELECT
        s.`pay_code`, SUM(s.`amount`) as amount
        FROM t_pos_help_card_transaction_flow s
        WHERE  s.`transaction_date` >= #{beginTime}
        AND s.`transaction_date` <![CDATA[ <= ]]> #{endTime}
        <if test="null != payCode and '' !=payCode">
        AND s.pay_code = #{payCode}
        </if>
        GROUP BY s.`pay_code`
    </select>

    <select id="helpCardTradeWaterPage" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardTradeWaterPageODTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardTradeWaterPageIDTO">
        SELECT
        card_no,
        pay_code,
        transaction_date_time as transactionDate,
        amount AS amount
        FROM
        t_pos_help_card_transaction_flow
        WHERE 1 = 1
        AND `transaction_date` >= #{beginTime}
        AND `transaction_date` <![CDATA[ <= ]]> #{endTime}
        <if test = "null != payCode and '' != payCode">
            AND pay_code = #{payCode}
        </if>
        <if test = "null != cardNo and '' != cardNo">
        AND card_no = #{cardNo}
        </if>
        ORDER BY transaction_date_time DESC
    </select>
</mapper>