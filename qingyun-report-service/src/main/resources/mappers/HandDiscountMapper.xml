<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.report.mapper.HandDiscountMapper">

    <select id="handDiscountReport" resultType="com.pinshang.qingyun.report.dto.HandDiscountODTO">
        select
            tprhd.shop_id,
            tprhd.shop_name,
            tru.employee_name as casherName,
            tru.employee_code as casherNumber,
            date_format(tprhd.cash_date, '%Y-%m-%d') as casher_date,
            tprhd.discount_total_amount,
            ms.shop_code
        from t_pos_report_hand_discount tprhd
        left join t_employee_user tru on tprhd.casher_id = tru.user_id
        left join t_md_shop ms on ms.id = tprhd.shop_id
        where 1=1
          <include refid="handDiscountReportCondition"/>
        ORDER BY tprhd.cash_date DESC, tprhd.discount_total_amount DESC
    </select>

    <select id="handDiscountReportHeader" resultType="com.pinshang.qingyun.report.dto.HandDiscountODTO">
        select
            sum(tprhd.discount_total_amount) as discount_total_amount
        from t_pos_report_hand_discount tprhd
        left join t_md_shop ms on ms.id = tprhd.shop_id
        where 1=1
        <include refid="handDiscountReportCondition"/>
    </select>

    <sql id="handDiscountReportCondition">
        <choose>
            <when test=" idto.shopId != null "> AND tprhd.shop_id = #{idto.shopId} </when>
            <when test=" idto.shopId == null ">
                AND tprhd.shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
        </choose>
        <if test="idto.beginTime != null and idto.beginTime != ''">
            AND tprhd.cash_date >= #{idto.beginTime}
        </if>
        <if test="idto.endTime != null and idto.endTime != ''">
            AND tprhd.cash_date <![CDATA[ <= ]]> #{idto.endTime}
        </if>
        <if test="idto.casherId != null">
            AND tprhd.casher_id = #{idto.casherId}
        </if>
        <if test = "idto.shopCode != null and idto.shopCode != ''">
            AND ms.shop_code = #{idto.shopCode}
        </if>
    </sql>

    <select id="findCount" resultType="Integer">
        select
            count(*)
        from t_pos_report_hand_discount
        where shop_id = #{shopId} and casher_id = #{userId} and cash_date = #{cashDate}
    </select>

    <update id="updateDiscountAmount">
        update t_pos_report_hand_discount
        set discount_total_amount = #{promotionPrice}, update_time = now()
        where shop_id = #{shopId} and casher_id =#{userId} and cash_date = #{cashDate}
    </update>

    <insert id="insertDiscountAmount">
        insert into t_pos_report_hand_discount(shop_id, shop_name, casher_id, casher_number, cash_date, discount_total_amount, create_id, create_time, update_id, update_time)
        values (#{shopId},
                (select shop_name from t_md_shop where id = #{shopId}),
                #{userId},
                (select employee_code from t_employee_user where user_id = #{userId}),
                #{cashDate},
                #{promotionPrice},
                #{userId},
                NOW(),
                #{userId},
                NOW()
               )
    </insert>
</mapper>
