<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.SalesSummaryReportMapper">

    <!--  商品销售汇总，商品类别汇总-->
    <select id="commoditySalesSummaryReport"  parameterType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO">
        SELECT tt.* from (
            SELECT
                tp.shop_id,
                tp.shop_code,
                tp.shop_name,
                <if test="idto.summaryType ==1 or idto.summaryType ==2 or idto.summaryType ==3">
                    tp.commodity_first_id commodityFirstId,
                    tp.commodity_first_name commodityFirstName,
                </if>
                <if test="idto.summaryType ==4">
                    tp.commodity_second_id commodityFirstId,
                    tp.commodity_second_name commodityFirstName,
                </if>
                <if test="idto.summaryType ==5">
                    tp.commodity_third_id commodityFirstId,
                    tp.commodity_third_name commodityFirstName,
                </if>
                tp.commodity_id,
                tp.commodity_code,
                tp.commodity_name,
                tp.commodity_spec,
                tp.commodity_unit,
                tp.commodity_price,
                tp.tax_rate,
                tp.sale_time,
                IFNULL(SUM(tp.sale_quantity),0) saleQuantity,
                IFNULL(SUM(tp.return_quantity),0) returnQuantity,
                IFNULL(SUM(tp.give_quantity),0) giveQuantity,
                IFNULL(SUM(tp.sale_amount),0) saleAmount,
                IFNULL(SUM(tp.weight_amount),0) weightAmount,
                IFNULL(SUM(tp.return_amount),0) returnAmount,
                IFNULL(SUM(tp.give_amount),0) giveAmount,
                IFNULL(SUM(tp.discount_amount),0) discountAmount,
                IFNULL(SUM(tp.tatal_quantity),0) tatalQuantity,
                IFNULL(SUM(tp.tatal_amount),0) tatalAmount,
                IFNULL(SUM(tp.no_tax_rate_amount),0) noTaxRateAmount,
                IFNULL(SUM(tp.tax_rate_amount),0) taxRateAmount
            FROM t_pos_report_day_sales_summary tp
            WHERE 1=1
        <if test="idto.shopId != null and idto.shopId !='' " >
            AND tp.shop_id = #{idto.shopId}
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
            AND tp.shop_id IN
            <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and tp.sale_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>
        <if test="idto.cateId1 != null">
            AND tp.commodity_first_id = #{idto.cateId1}
        </if>
        <if test="idto.cateId2 != null">
            AND tp.commodity_second_id = #{idto.cateId2}
        </if>
        <if test="idto.cateId3 != null">
            AND tp.commodity_third_id = #{idto.cateId3}
        </if>
        <if test="idto.commodityId != null">
            AND tp.commodity_id = #{idto.commodityId}
        </if>
        <if test="idto.commodityKey!=null and idto.commodityKey !='' ">
            AND (tp.`commodity_code` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_name` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_aid` like concat('%',#{idto.commodityKey},'%') )
        </if>
        <if test="idto.barCode != null and idto.barCode !=''">
            AND tp.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{idto.barCode} )
        </if>
        <if test="idto.summaryType ==1 ">
            GROUP BY tp.shop_id,tp.commodity_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==2 ">
            GROUP BY tp.commodity_id
        </if>

        <if test="idto.summaryType ==3 and idto.isWithShop==1">
            GROUP BY tp.commodity_first_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==3 and idto.isWithShop==0">
            GROUP BY tp.commodity_first_id
        </if>
        <if test="idto.summaryType ==4 and idto.isWithShop==1 ">
            GROUP BY tp.commodity_second_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==4 and idto.isWithShop==0 ">
            GROUP BY tp.commodity_second_id
        </if>
        <if test="idto.summaryType ==5 and idto.isWithShop==1 ">
            GROUP BY tp.commodity_third_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==5 and idto.isWithShop==0 ">
            GROUP BY tp.commodity_third_id
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==1">
            GROUP BY tp.shop_id
            ORDER BY tatalAmount DESC
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==0">
            GROUP BY tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==7 ">
            GROUP BY tp.shop_id,tp.sale_time
        </if>
      ) tt
    </select>


    <!--  类别汇总，门店汇总-->
    <select id="cateShopSalesSummaryReport"  parameterType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO">
        SELECT tt.* from (
        SELECT
        tp.shop_id,
        tp.shop_code,
        tp.shop_name,
        <if test="idto.summaryType == 1 or idto.summaryType == 2 or idto.summaryType == 3">
            tp.commodity_first_name commodityFirstName,
            tp.commodity_first_code cateCode,
        </if>
        <if test="idto.summaryType == 4">
            tp.commodity_second_name commodityFirstName,
            tp.commodity_second_code cateCode,
        </if>
        <if test="idto.summaryType == 5">
            tp.commodity_third_name commodityFirstName,
            tp.commodity_third_code cateCode,
        </if>
        tp.sale_time,
        SUM(tp.sale_quantity) saleQuantity,
        SUM(tp.return_quantity) returnQuantity,
        SUM(tp.give_quantity) giveQuantity,
        SUM(tp.sale_amount)saleAmount,
        SUM(tp.weight_amount) weightAmount,
        SUM(tp.return_amount) returnAmount,
        SUM(tp.give_amount) giveAmount,
        SUM(tp.discount_amount) discountAmount,
        SUM(tp.tatal_quantity) tatalQuantity,
        SUM(tp.tatal_amount) tatalAmount,
        SUM(tp.no_tax_rate_amount) noTaxRateAmount,
        SUM(tp.tax_rate_amount) taxRateAmount,
        SUM(tp.no_tax_weight_amount) noTaxWeightAmount
        FROM
        t_pos_report_third_summary tp
        WHERE 1=1
        <if test="idto.shopId != null and idto.shopId !='' " >
            AND tp.shop_id = #{idto.shopId}
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
            AND tp.shop_id IN
            <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and tp.sale_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>
        <if test="idto.cateId1 != null">
            AND tp.commodity_first_id = #{idto.cateId1}
        </if>
        <if test="idto.cateId2 != null">
            AND tp.commodity_second_id = #{idto.cateId2}
        </if>
        <if test="idto.cateId3 != null">
            AND tp.commodity_third_id = #{idto.cateId3}
        </if>

        <if test="idto.summaryType ==3 and idto.isWithShop==1">
            GROUP BY tp.commodity_first_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==3 and idto.isWithShop==0">
            GROUP BY tp.commodity_first_id
        </if>
        <if test="idto.summaryType ==4 and idto.isWithShop==1 ">
            GROUP BY tp.commodity_second_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==4 and idto.isWithShop==0 ">
            GROUP BY tp.commodity_second_id
        </if>
        <if test="idto.summaryType ==5 and idto.isWithShop==1 ">
            GROUP BY tp.commodity_third_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==5 and idto.isWithShop==0 ">
            GROUP BY tp.commodity_third_id
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==1">
            GROUP BY tp.shop_id
            ORDER BY tatalAmount DESC
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==0">
            GROUP BY tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==7 ">
            GROUP BY tp.shop_id,tp.sale_time
        </if>
        ) tt
    </select>


    <select id="cateShopSalesDayReport"   resultType="com.pinshang.qingyun.report.model.pos.ThirdSummary">
          SELECT
                tt.shop_id,
                tt.shop_code,
                tt.shop_name,
                tt.commodity_first_id,
                -- tt.commodityFirstCode commodityFirstCode,
                tt.commodity_first_name,
                tt.commodity_second_id,
                -- tt.commoditySecondCode commoditySecondCode,
                tt.commodity_second_name,
                tt.commodity_third_id,
                -- tt.commodityThirdCode commodityThirdCode,
                tt.commodity_third_name,
                SUM(tt.saleQuantity) saleQuantity,
                SUM(tt.returnQuantity) returnQuantity,
                SUM(tt.giveQuantity) giveQuantity,
                SUM(tt.saleAmount) saleAmount,
                SUM(tt.weightAmount) weightAmount,
                SUM(tt.returnAmount) returnAmount,
                SUM(tt.giveAmount) giveAmount,
                SUM(tt.discountAmount) discountAmount,
                SUM(tt.tatalQuantity) tatalQuantity,
                SUM(tt.tatalAmount)tatalAmount,
                SUM(tt.noTaxRateAmount) noTaxRateAmount,
                SUM(tt.taxRateAmount) taxRateAmount,
                SUM(tt.noTaxWeightAmount) noTaxWeightAmount,
                tt.sale_time,
                tt.create_id,
                tt.create_time,
                tt.update_id,
                tt.update_time
            FROM
            (
                SELECT
                    tp.shop_id,
                    tp.shop_code,
                    tp.shop_name,
                    tp.commodity_first_id,
                    -- tc1.cate_code commodityFirstCode,
                    tp.commodity_first_name,
                    tp.commodity_second_id,
                    -- tc2.cate_code commoditySecondCode,
                    tp.commodity_second_name,
                    tp.commodity_third_id,
                    -- tc3.cate_code commodityThirdCode,
                    tp.commodity_third_name,
                    IFNULL(SUM(tp.sale_quantity), 0) saleQuantity,
                    IFNULL(SUM(tp.return_quantity), 0) returnQuantity,
                    IFNULL(SUM(tp.give_quantity), 0) giveQuantity,
                    IFNULL(SUM(tp.sale_amount), 0) saleAmount,
                    IFNULL(SUM(tp.weight_amount), 0) weightAmount,
                    IFNULL(SUM(tp.return_amount), 0) returnAmount,
                    IFNULL(SUM(tp.give_amount), 0) giveAmount,
                    IFNULL(SUM(tp.discount_amount), 0) discountAmount,
                    IFNULL(SUM(tp.tatal_quantity), 0) tatalQuantity,
                    IFNULL(SUM(tp.tatal_amount), 0) tatalAmount,
                    IFNULL(
                        SUM(tp.no_tax_rate_amount),
                        0
                    ) noTaxRateAmount,
                    IFNULL(SUM(tp.tax_rate_amount), 0) taxRateAmount,
                    SUM(tp.weight_amount) / (1 + tp.tax_rate) noTaxWeightAmount,
                    tp.sale_time,
                    tp.create_id,
                    tp.create_time,
                    tp.update_id,
                    tp.update_time
                FROM
                    t_pos_report_sales_summary tp
                -- LEFT JOIN t_category tc1 ON tc1.id = tp.commodity_first_id
                -- LEFT JOIN t_category tc2 ON tc2.id = tp.commodity_second_id
                -- LEFT JOIN t_category tc3 ON tc3.id = tp.commodity_third_id
                WHERE
                    tp.sale_time =#{saleTime}

                GROUP BY
                    tp.shop_id,
                    tp.commodity_id
            ) tt
            GROUP BY
                tt.shop_id,
                tt.commodity_third_id
    </select>

    <!--  畅销品排行-->
    <select id="bestSellerReport" parameterType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO">
        SELECT
            tp.commodity_first_name,
            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_spec,
            tp.commodity_unit,
            IFNULL(SUM(tp.tatal_quantity),0) tatalQuantity,
            IFNULL(SUM(tp.tatal_amount),0) tatalAmount,
            IFNULL(SUM(tp.weight_amount),0) weightAmount,
            IFNULL(SUM(tp.tatal_amount),0)-IFNULL(SUM(tp.weight_amount),0) grossprofitmarginAmount
            FROM
            t_pos_report_sales_summary tp
            WHERE 1=1
            <if test="idto.shopId != null and idto.shopId !='' " >
                AND tp.shop_id = #{idto.shopId}
            </if>
            <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
                AND tp.shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
                and tp.sale_time BETWEEN #{idto.beginDate} and #{idto.endDate}
            </if>
            <if test="idto.cateId1 != null">
                AND tp.commodity_first_id = #{idto.cateId1}
            </if>
            <if test="idto.cateId2 != null">
                AND tp.commodity_second_id = #{idto.cateId2}
            </if>
            <if test="idto.cateId3 != null">
                AND tp.commodity_third_id = #{idto.cateId3}
            </if>
            <if test="idto.barCode != null and idto.barCode !=''">
                AND tp.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{idto.barCode} )
            </if>
            <!--<if test="idto.barCode!=null and idto.barCode !='' ">
                AND tp.commodity_id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode})
            </if>-->
            <if test="idto.commodityKey!=null and idto.commodityKey !='' ">
                AND (tp.`commodity_code` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_name` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_aid` like concat('%',#{idto.commodityKey},'%') )
            </if>
            <if test="idto.isTotal ==0">
                GROUP BY
                tp.commodity_id
            </if>
            <if test="idto.summaryType ==8 ">
                ORDER BY tatalQuantity DESC,commodity_id
            </if>
            <if test="idto.summaryType ==9 ">
                ORDER BY tatalAmount DESC,commodity_id
            </if>
            <if test="idto.summaryType ==10 ">
                ORDER BY grossprofitmarginAmount DESC,commodity_id
            </if>
            limit 100
    </select>

    <!-- 促销销售-->
    <select id="salesPromotionReport" parameterType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO">
         SELECT
            tp.shop_code,
            tp.shop_name,
            tp.commodity_first_name,
            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_spec,
            tp.commodity_unit,
            tp.commodity_price salePrice,
            IFNULL(SUM(tp.tatal_quantity),0) tatalQuantity,
            IFNULL(SUM(tp.tatal_amount),0) tatalAmount,
            IFNULL(SUM(tp.discount_amount),0) discountAmount,
            IFNULL(SUM(tp.give_amount),0) giveAmount,
            tp.promotion_type promotionType,
            tp.promotion_key promotionKey
        FROM
            t_pos_report_sales_summary tp
        WHERE 1=1

        <if test="idto.shopId != null and idto.shopId !='' " >
          AND tp.shop_id = #{idto.shopId}
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
            AND tp.shop_id IN
            <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        AND (tp.promotion_type IS NOT NULL AND tp.promotion_type!='')
        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and tp.sale_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>
        <if test="idto.cateId1 != null">
            AND tp.commodity_first_id = #{idto.cateId1}
        </if>
        <if test="idto.cateId2 != null">
            AND tp.commodity_second_id = #{idto.cateId2}
        </if>
        <if test="idto.cateId3 != null">
            AND tp.commodity_third_id = #{idto.cateId3}
        </if>
        <if test="idto.barCode!=null and idto.barCode !='' ">
            AND tp.commodity_id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{idto.barCode})
        </if>
        <if test="idto.commodityKey!=null and idto.commodityKey !='' ">
            AND (tp.`commodity_code` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_name` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_aid` like concat('%',#{idto.commodityKey},'%') )
        </if>
        <if test="idto.promotionType!=null and idto.promotionType !='' and idto.promotionType != '1-6' ">
            AND tp.`promotion_type` like concat('%',#{idto.promotionType},'%')
        </if>
        <if test="idto.promotionType!=null and idto.promotionType !=''  and idto.promotionType == '1-6'">
            AND (
            find_in_set('1', tp.promotion_type) > 0
            OR find_in_set('2', tp.promotion_type) > 0
            OR find_in_set('3', tp.promotion_type) > 0
            OR find_in_set('4', tp.promotion_type) > 0
            OR find_in_set('5', tp.promotion_type) > 0
            OR find_in_set('6', tp.promotion_type) > 0
            )
        </if>
        <if test="idto.promotionKey!=null and idto.promotionKey !='' ">
            AND tp.`promotion_key` like concat('%',#{idto.promotionKey},'%')
        </if>

        <if test="idto.isTotal ==0">
            GROUP BY
            tp.shop_id,
            tp.commodity_id,tp.promotion_type
        </if>

    </select>

    <delete id="deleteSalesSummaryReportByOrderCodes">
        DELETE  FROM  t_pos_report_sales_summary WHERE order_code = #{orderCode}
    </delete>

    <delete id="deleteSalesSummaryReportByTimeRange">
        DELETE  FROM  t_pos_report_sales_summary
        WHERE sale_time = #{saleTime} AND  create_time BETWEEN #{beginTime} AND  #{endTime}
        <if test="shopId != null and shopId != 0 ">
            AND shop_id = #{shopId}
        </if>
    </delete>

    <delete id="deleteDaySalesSummaryReportBySaleTime">
        delete from t_pos_report_day_sales_summary where sale_time = #{saleTime}
    </delete>

    <insert id="insertPosReportDaySalesSummary">
        INSERT INTO t_pos_report_day_sales_summary (
            shop_id,
            shop_code,
            shop_name,
            commodity_first_id,
            commodity_first_code,
            commodity_first_name,

            commodity_second_id,
            commodity_second_code,
            commodity_second_name,

            commodity_third_id,
            commodity_third_code,
            commodity_third_name,

            commodity_id,
            commodity_code,
            commodity_name,
            commodity_aid,
            commodity_spec,
            commodity_unit,
            commodity_price,
            tax_rate,
            sale_time,

            sale_quantity,
            return_quantity,
            give_quantity,
            sale_amount,
            weight_amount,
            return_amount,
            give_amount,
            discount_amount,
            tatal_quantity,
            tatal_amount,
            no_tax_rate_amount,
            tax_rate_amount,
            consignment_id,

            create_id,
            create_time,
            update_id,
            update_time
        )
        SELECT
            tp.shop_id,
            tp.shop_code,
            tp.shop_name,

            tp.commodity_first_id,
            cate1.cate_code,
            tp.commodity_first_name,

            tp.commodity_second_id,
            cate2.cate_code,
            tp.commodity_second_name,

            tp.commodity_third_id,
            cate3.cate_code,
            tp.commodity_third_name,

            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_aid,
            tp.commodity_spec,
            tp.commodity_unit,
            tp.commodity_price,
            tp.tax_rate,
            tp.sale_time,
            IFNULL(SUM(tp.sale_quantity), 0)      saleQuantity,
            IFNULL(SUM(tp.return_quantity), 0)    returnQuantity,
            IFNULL(SUM(tp.give_quantity), 0)      giveQuantity,
            IFNULL(SUM(tp.sale_amount), 0)        saleAmount,
            IFNULL(SUM(tp.weight_amount), 0)      weightAmount,
            IFNULL(SUM(tp.return_amount), 0)      returnAmount,
            IFNULL(SUM(tp.give_amount), 0)        giveAmount,
            IFNULL(SUM(tp.discount_amount), 0)    discountAmount,
            IFNULL(SUM(tp.tatal_quantity), 0)     tatalQuantity,
            IFNULL(SUM(tp.tatal_amount), 0)       tatalAmount,
            IFNULL(SUM(tp.no_tax_rate_amount), 0) noTaxRateAmount,
            IFNULL(SUM(tp.tax_rate_amount), 0)    taxRateAmount,
            tp.consignment_id consignmentId,
            tp.create_id,
            tp.create_time,
            tp.update_id,
            tp.update_time
        FROM t_pos_report_sales_summary tp
        LEFT JOIN t_category cate1 ON cate1.id = tp.commodity_first_id
        LEFT JOIN t_category cate2 ON cate2.id = tp.commodity_second_id
        LEFT JOIN t_category cate3 ON cate3.id = tp.commodity_third_id
        WHERE tp.sale_time = #{saleTime}
        GROUP BY tp.shop_id, tp.commodity_id
    </insert>

    <delete id="deleteGuestListSummaryDayReportBySaleTime">
        delete from t_pos_report_guest_list_summary_day where sale_time = #{saleTime}
    </delete>

    <insert id="insertGuestListSummaryDayReportBySaleTime">
        insert into t_pos_report_guest_list_summary_day(shop_id, shop_code, shop_name, mac_id, mac_code, mac_name
          , guest_quantity, guest_amount, sale_time, create_id, create_time, update_id, update_time)
        SELECT
             shop_id,
             shop_code,
             shop_name,
             mac_id,
             mac_code,
             mac_name,
             IFNULL(SUM(guest_quantity),0) guest_quantity,
             IFNULL(SUM(guest_amount),0) guest_amount,
             sale_time,
             -1 AS create_id,
             now() as create_time,
             -1 as update_id,
             now() as update_time
         FROM t_pos_report_guest_list_summary
         WHERE sale_time = #{saleTime}
         group by shop_id, sale_time, mac_id
         order by sale_time, shop_id, mac_id
    </insert>

    <delete id="deleteCashierWaterDayReportBySaleTime">
        delete from t_pos_report_cashier_water_day where create_time = concat(#{saleTime}, ' 00:00:00')
    </delete>

    <insert id="insertCashierWaterDayReportBySaleTime">
        insert into t_pos_report_cashier_water_day( shop_id, shop_code, shop_name, create_time, pay_amount
            , sale_type, pay_type, pay_name, employee_number, create_name, mac_code,pos_mac_id, pos_type )
        SELECT
            shop_id,
            shop_code,
            shop_name,
            DATE_FORMAT(create_time, '%Y-%m-%d 00:00:00') as create_time,
            IFNULL(SUM(pay_amount), 0)                    as pay_amount,
            sale_type,
            pay_type,
            pay_name,
            employee_number,
            create_name,
            mac_code,
            pos_mac_id,
            pos_type
        FROM t_pos_report_cashier_water tprcw
        WHERE create_time >= concat(#{saleTime}, ' 00:00:00') and create_time <![CDATA[ <= ]]> concat(#{saleTime}, ' 23:59:59')
        group by shop_id, employee_number, mac_code, sale_type, pay_type, year(create_time), month(create_time), day(create_time)
        order by shop_id, employee_number, mac_code, sale_type, pay_type, year(create_time), month(create_time), day(create_time)
    </insert>


    <select id="comparePosReportAmount" resultType="com.pinshang.qingyun.report.dto.pos.OrderSaleTypeODTO">
        select a.order_code,a.sale_type from (
        select order_code,sum(pay_amount) amount,sale_type  from t_pos_report_cashier_water where create_time BETWEEN #{beginTime} AND #{endTime}
        group by order_code
        ) a,
        (
        select order_code,sum(tatal_amount) amount from t_pos_report_sales_summary where create_time BETWEEN #{beginTime} AND #{endTime}
        group by order_code
        ) b
        where a.order_code = b.order_code and a.amount != b.amount
    </select>

    <select id="queryPosRepeatOrderCode" resultType="com.pinshang.qingyun.report.dto.pos.OrderSaleTypeODTO">
        SELECT
            t.order_code,
            (case when t.guest_amount > 0 then 1 else 2 end) as saleType
        FROM t_pos_report_guest_list_summary t
        WHERE t.sale_time = #{saleTime}
        GROUP BY t.order_code HAVING count(order_code) >1
    </select>

    <delete id="deleteAllPosReportUnique">
       delete from t_pos_report_unique
    </delete>

    <delete id="deleteGuestListSummary10Day">
        delete from t_pos_report_guest_list_summary_10Day where sale_time  <![CDATA[ <= ]]> #{saleTime}
    </delete>

    <select id="singleProductSalesList" resultType="com.pinshang.qingyun.report.dto.pos.joinShop.SingleProductSalesDTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.joinShop.SingleProductSalesIDTO">
        select a.commodity_id as commodityId,
               a.commodity_first_name as commodityFirstName,
               a.commodity_second_name as commoditySecondName,
               a.commodity_third_name as commodityThirdName,
               a.commodity_code as commodityCode,
               a.commodity_name as commodityName,
               a.commodity_unit as commodityUnit,
               ifnull(sum(a.sale_quantity), 0) as sumSaleQuantity,
               ifnull(sum(a.give_quantity), 0) as sumGiveQuantity,
               ifnull(sum(a.return_quantity), 0) as sumReturnQuantity,
               ifnull(sum(a.sale_amount), 0) as sumSaleAmount,
               ifnull(sum(a.return_amount), 0) as sumReturnAmount,
               ifnull(sum(a.commodity_price * a.sale_quantity), 0) as sumCommodityPrice,
               ifnull(sum(a.commodity_price * a.return_quantity), 0) as sumReturnCommodityPrice,
               ifnull(sum(a.give_quantity * a.commodity_price), 0) as sumGiveCommodityPrice
        from
             t_pos_report_sales_summary a
        <include refid="singleProductSalesCondition"></include>
        GROUP BY a.commodity_id
    </select>


    <select id="singleProductSalesSum" resultType="com.pinshang.qingyun.report.dto.pos.joinShop.SingleProductSalesDTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.joinShop.SingleProductSalesIDTO">
        select ifnull(sum(a.sale_quantity), 0) + ifnull(sum(a.give_quantity), 0) - ifnull(sum(a.return_quantity), 0) AS saleQuantity,
               ifnull(sum(a.sale_amount),0) - ifnull(sum(a.return_amount),0) AS saleAmount,
               ifnull(sum(a.commodity_price * a.sale_quantity), 0) + ifnull(sum(a.give_quantity * a.commodity_price), 0) - ifnull(sum(a.commodity_price * a.return_quantity), 0) AS commodityPriceAmount
        from
              t_pos_report_sales_summary a
        <include refid="singleProductSalesCondition"></include>
    </select>

    <sql id = "singleProductSalesCondition">
         <where>
             <if test = "dto.shopId != null and dto.shopId != ''">
                  and a.shop_id = #{dto.shopId}
             </if>
             <if test = "dto.startTime != null and dto.startTime != '' and dto.endTime != null and dto.endTime != ''">
                  and a.create_time <![CDATA[ >= ]]> #{dto.startTime}
                  and a.create_time <![CDATA[ <= ]]> #{dto.endTime}
             </if>
             <if test = "dto.cateId != null and dto.cateId != ''">
                 and (a.commodity_first_id = #{dto.cateId}
                 or a.commodity_second_id = #{dto.cateId}
                 or a.commodity_third_id = #{dto.cateId})
             </if>
             <if test = "dto.commodityId != null and dto.commodityId != ''">
                 and a.commodity_id = #{dto.commodityId}
             </if>
         </where>
    </sql>

    <select id = "salesSummaryList" resultType="com.pinshang.qingyun.report.model.pos.SalesSummaryReport"
            parameterType="com.pinshang.qingyun.report.dto.pos.joinShop.AnalysisBySynthesisIDTO">
        select *
        from
        t_pos_report_sales_summary a
<!--            <if test = "true == dto.currentDay">-->
<!--                t_pos_report_sales_summary a-->
<!--            </if>-->
<!--            <if test = "false == dto.currentDay">-->
<!--                t_pos_report_day_sales_summary a-->
<!--            </if>-->
        where a.create_time BETWEEN #{dto.startTime} AND #{dto.endTime}
        and a.shop_id in
        <foreach collection="dto.shopIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY a.create_time asc
    </select>

</mapper>