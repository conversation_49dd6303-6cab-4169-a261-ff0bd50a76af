<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.CashierDailyGiftCardMapper">

    <sql id="cashierDailySql">
        WHERE 1 = 1
        and tprcw.shop_id IN
        <foreach collection="cashierDailyIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null !=cashierDailyIDTO.shopCode and '' != cashierDailyIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyIDTO.shopCode}
        </if>
        <if test="cashierDailyIDTO.startTime != null and cashierDailyIDTO.startTime != '' and cashierDailyIDTO.EndTime != null and cashierDailyIDTO.EndTime != '' ">
            AND tprcw.create_time between #{cashierDailyIDTO.startTime} and #{cashierDailyIDTO.EndTime}
        </if>
        <if test=" null !=  cashierDailyIDTO.saleNumber and '' !=cashierDailyIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierDailyIDTO.saleNumber}
        </if>
        <if test="cashierDailyIDTO.macCode != null and cashierDailyIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierDailyIDTO.macCode}, '%')
        </if>
        <if test="cashierDailyIDTO.saleType != null">
            AND tprcw.sale_type = #{cashierDailyIDTO.saleType}
        </if>
        <if test="cashierDailyIDTO.saleType != null">
            AND tprcw.sale_type = #{cashierDailyIDTO.saleType}
        </if>
        <if test="null != cashierDailyIDTO.posType">
            and tprcw.pos_type = #{cashierDailyIDTO.posType}
        </if>
    </sql>

    <sql id="cashierDailyGiftSql">
        WHERE 1=1
        and tprcw.shop_id IN
        <foreach collection="cashierDailyIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test=" null !=cashierDailyIDTO.shopCode and '' != cashierDailyIDTO.shopCode">
            AND tprcw.shop_id = #{cashierDailyIDTO.shopCode}
        </if>
        <if test="cashierDailyIDTO.startTime != null and cashierDailyIDTO.startTime != '' and cashierDailyIDTO.EndTime != null and cashierDailyIDTO.EndTime != '' ">
            AND tprcw.sale_time between #{cashierDailyIDTO.startTime} and #{cashierDailyIDTO.EndTime}
        </if>
        <if test=" null !=  cashierDailyIDTO.saleNumber and '' !=cashierDailyIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierDailyIDTO.saleNumber}
        </if>
        <if test="cashierDailyIDTO.macCode != null and cashierDailyIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierDailyIDTO.macCode}, '%')
        </if>
        <if test="cashierDailyIDTO.saleType != null and cashierDailyIDTO.saleType == 1">
            AND 1 = 1
        </if>
        <if test="cashierDailyIDTO.saleType != null and cashierDailyIDTO.saleType == 2">
            AND 1 = 2
        </if>
        <if test="null != cashierDailyIDTO.posType and  cashierDailyIDTO.posType == 1">
            and 1 = 1
        </if>
        <if test="null != cashierDailyIDTO.posType and  cashierDailyIDTO.posType == 2">
            and 1 = 2
        </if>
        <if test="null != cashierDailyIDTO.posType and  cashierDailyIDTO.posType == 3">
            and 1 = 2
        </if>
    </sql>
    <!-- 线下营业款 -->
    <select id="selectCashierDailyList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">
        SELECT
            tprcw.shop_code AS shopCode,
            tprcw.shop_name AS shopName,
            <if test="cashierDailyIDTO.searchCriteria == 0">
                tprcw.employee_number AS saleNumber,
                tprcw.create_name AS saleName,
                tprcw.mac_code,
                case tprcw.sale_type
                when 1 then '销售'
                when 2 then '退货'
                end as sale_type,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 1">
                tprcw.employee_number AS saleNumber,
                tprcw.create_name AS saleName,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 3">
                DATE_FORMAT(tprcw.create_time,'%Y-%m-%d') AS createTime,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                tprcw.employee_number AS saleNumber,
                tprcw.create_name AS saleName,
                (case tprcw.sale_type when 1 then '销售' when 2 then '退货' end ) AS saleType,
                tprcw.pay_name  AS payType,
                SUM(tprcw.pay_amount) AS payAmount,
                SUM(tprcw.pay_amount) - SUM(CASE WHEN tprcw.sale_type = 2 then tprcw.pay_amount else 0 end ) AS salesAmount,
                SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end )AS refundAmount,
            </if>

            <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
            </foreach>
            SUM(tprcw.pay_amount) AS total,
            CASE tprcw.pos_type
            WHEN 2 THEN '自助POS'
            WHEN 3 THEN 'PDA'
            ELSE '收银POS'
            END AS posTypeName
        FROM
            <if test="cashierDailyIDTO.isCurrentDay == true">
                t_pos_report_cashier_water tprcw
            </if>
            <if test="cashierDailyIDTO.isCurrentDay == false">
                t_pos_report_cashier_water_day tprcw
            </if>
            <include refid="cashierDailySql"></include>

            <if test="cashierDailyIDTO.searchCriteria == 0">
                GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code, tprcw.sale_type
                ORDER BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code, tprcw.sale_type
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 1">
                GROUP BY tprcw.shop_id,tprcw.employee_number
                ORDER BY tprcw.shop_id,tprcw.employee_number
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 2">
                GROUP BY tprcw.shop_id, tprcw.pos_type
                ORDER BY tprcw.shop_id
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 3">
                group by tprcw.shop_id,
                year(tprcw.create_time),
                month(tprcw.create_time),
                day(tprcw.create_time)
                ORDER BY tprcw.shop_id ASC ,
                year(tprcw.create_time) DESC ,
                month(tprcw.create_time) DESC,
                day(tprcw.create_time) DESC,
                tprcw.pos_type
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                GROUP BY tprcw.shop_id,tprcw.pay_type,tprcw.sale_type,tprcw.employee_number
                ORDER BY tprcw.shop_id ASC,tprcw.employee_number ASC ,ABS(sum(tprcw.pay_amount)) DESC
            </if>
    </select>

    <!-- 线下营业款 合计 -->
    <select id="selectSUMCashierDailyList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
            <if test="cashierDailyIDTO.searchCriteria == 0 or cashierDailyIDTO.searchCriteria == 1 or cashierDailyIDTO.searchCriteria == 2 or cashierDailyIDTO.searchCriteria == 3">
                <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                    SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
                </foreach>
                SUM(tprcw.pay_amount) AS total
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                SUM(tprcw.pay_amount) AS payAmount,
                SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount else 0 end ) AS salesAmount,
                SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) else 0 end ) AS refundAmount
            </if>
        FROM
            <if test="cashierDailyIDTO.isCurrentDay == true">
                t_pos_report_cashier_water tprcw
            </if>
            <if test="cashierDailyIDTO.isCurrentDay == false">
                t_pos_report_cashier_water_day tprcw
            </if>
        <include refid="cashierDailySql"></include>
    </select>


    <!-- 线下售卡 -->
    <select id="selectCashierDailyGiftCardList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">
        SELECT
            md.shop_code AS shopCode,
            md.shop_name AS shopName,
            <if test="cashierDailyIDTO.searchCriteria == 0">
                tprcw.employee_number AS saleNumber,
                tprcw.employee_name AS saleName,
                tprcw.mac_code,
               '销售' as sale_type,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 1">
                tprcw.employee_number AS saleNumber,
                tprcw.employee_name AS saleName,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 3">
                DATE_FORMAT(tprcw.sale_time,'%Y-%m-%d') AS createTime,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                tprcw.employee_number AS saleNumber,
                tprcw.employee_name AS saleName,
                '销售'  AS saleType,
                tprcw.pay_name  AS payType,
                SUM(tprcw.total_amount) AS payAmount,
                SUM(tprcw.total_amount) AS salesAmount,
                0 AS refundAmount,
            </if>

            <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.total_amount end ) AS #{payType.payTypeCode},
            </foreach>
            SUM(tprcw.total_amount) AS total,
            '收银POS' posTypeName
        FROM
           t_gift_card_cashier_water tprcw
           left join t_md_shop md on md.id = tprcw.shop_id
        <include refid="cashierDailyGiftSql"></include>
        <if test="null != cashierDailyIDTO.moneyType and  cashierDailyIDTO.moneyType == 1">
            and tprcw.type = 1
        </if>
        <if test="null != cashierDailyIDTO.moneyType and  cashierDailyIDTO.moneyType == 3">
            and tprcw.type = 2
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 0">
            GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code
            ORDER BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 1">
            GROUP BY tprcw.shop_id,tprcw.employee_number
            ORDER BY tprcw.shop_id,tprcw.employee_number
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 2">
            GROUP BY tprcw.shop_id
            ORDER BY tprcw.shop_id
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 3">
            group by tprcw.shop_id,
            year(tprcw.sale_time),
            month(tprcw.sale_time),
            day(tprcw.sale_time)
            ORDER BY tprcw.shop_id ASC ,
            year(tprcw.sale_time) DESC ,
            month(tprcw.sale_time) DESC,
            day(tprcw.sale_time) DESC
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 4">
            GROUP BY tprcw.shop_id,tprcw.pay_type,tprcw.employee_number
            ORDER BY tprcw.shop_id ASC,tprcw.employee_number ASC ,ABS(sum(tprcw.total_amount)) DESC
        </if>
    </select>

     <!-- 线下售卡总计 -->
    <select id="selectSUMCashierDailyGiftCardList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
            <if test="cashierDailyIDTO.searchCriteria == 0 or cashierDailyIDTO.searchCriteria == 1 or cashierDailyIDTO.searchCriteria == 2 or cashierDailyIDTO.searchCriteria == 3">
                <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                    SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.total_amount end ) AS #{payType.payTypeCode},
                </foreach>
                SUM(tprcw.total_amount) AS total
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                SUM(tprcw.total_amount) AS payAmount,
                SUM(tprcw.total_amount) AS salesAmount,
                0 AS refundAmount
            </if>
        FROM
          t_gift_card_cashier_water tprcw
        <include refid="cashierDailyGiftSql"></include>
        <if test="null != cashierDailyIDTO.moneyType and  cashierDailyIDTO.moneyType == 1">
            and tprcw.type = 1
        </if>
        <if test="null != cashierDailyIDTO.moneyType and  cashierDailyIDTO.moneyType == 3">
            and tprcw.type = 2
        </if>
    </select>





    <!-- 线下售卡，线下营业款 -->
    <select id="selectCashierDailyAndGiftCardList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailyODTO">
        SELECT
            tt.shopCode,
            tt.shopName,
            <if test="cashierDailyIDTO.searchCriteria == 0">
                tt.saleNumber,
                tt.saleName,
                tt.macCode,
                tt.saleType,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 1">
                tt.saleNumber,
                tt.saleName,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 3">
                tt.createTime,
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                tt.saleNumber,
                tt.saleName,
                tt.saleType,
                tt.payType,
                SUM(tt.payAmount) AS payAmount,
                SUM(tt.salesAmount) AS salesAmount,
                SUM(tt.refundAmount)AS refundAmount,
            </if>

            <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                SUM(${payType.payTypeCode}) AS #{payType.payTypeCode},
            </foreach>
            SUM(tt.payAmount) AS total,
            tt.posTypeName
         FROM (
                SELECT
                    tprcw.shop_id shopId,
                    tprcw.create_time saleTime,
                    tprcw.shop_code AS shopCode,
                    tprcw.shop_name AS shopName,
                    <if test="cashierDailyIDTO.searchCriteria == 0">
                        tprcw.employee_number AS saleNumber,
                        tprcw.create_name AS saleName,
                        tprcw.mac_code macCode,
                        case tprcw.sale_type
                        when 1 then '销售'
                        when 2 then '退货'
                        end as saleType,
                    </if>
                    <if test="cashierDailyIDTO.searchCriteria == 1">
                        tprcw.employee_number AS saleNumber,
                        tprcw.create_name AS saleName,
                    </if>
                    <if test="cashierDailyIDTO.searchCriteria == 3">
                        DATE_FORMAT(tprcw.create_time,'%Y-%m-%d') AS createTime,
                    </if>
                    <if test="cashierDailyIDTO.searchCriteria == 4">
                        tprcw.employee_number AS saleNumber,
                        tprcw.create_name AS saleName,
                        (case tprcw.sale_type when 1 then '销售' when 2 then '退货' end ) AS saleType,
                        tprcw.pay_name  AS payType,
                        SUM(tprcw.pay_amount) - SUM(CASE WHEN tprcw.sale_type = 2 then tprcw.pay_amount else 0 end ) AS salesAmount,
                        SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end )AS refundAmount,
                    </if>

                    <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                        SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
                    </foreach>
                    SUM(tprcw.pay_amount) AS payAmount,
                    CASE tprcw.pos_type
                    WHEN 2 THEN '自助POS'
                    WHEN 3 THEN 'PDA'
                    ELSE '收银POS'
                    END AS posTypeName,
                    tprcw.pos_type
                FROM
                <if test="cashierDailyIDTO.isCurrentDay == true">
                    t_pos_report_cashier_water tprcw
                </if>
                <if test="cashierDailyIDTO.isCurrentDay == false">
                    t_pos_report_cashier_water_day tprcw
                </if>
                 <include refid="cashierDailySql"></include>
                <if test="cashierDailyIDTO.searchCriteria == 0">
                    GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code, tprcw.sale_type
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 1">
                    GROUP BY tprcw.shop_id,tprcw.employee_number
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 2">
                    GROUP BY tprcw.shop_id,tprcw.pos_type
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 3">
                    group by tprcw.shop_id,
                    tprcw.pos_type,
                    year(tprcw.create_time),
                    month(tprcw.create_time),
                    day(tprcw.create_time),
                    tprcw.pos_type
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 4">
                    GROUP BY tprcw.shop_id,tprcw.pay_type,tprcw.sale_type,tprcw.employee_number, tprcw.pos_type
                </if>

                union all


                SELECT
                    md.id shopId,
                    tprcw.sale_time saleTime,
                    md.shop_code AS shopCode,
                    md.shop_name AS shopName,
                    <if test="cashierDailyIDTO.searchCriteria == 0">
                        tprcw.employee_number AS saleNumber,
                        tprcw.employee_name AS saleName,
                        tprcw.mac_code macCode,
                        '销售' as saleType,
                    </if>
                    <if test="cashierDailyIDTO.searchCriteria == 1">
                        tprcw.employee_number AS saleNumber,
                        tprcw.employee_name AS saleName,
                    </if>
                    <if test="cashierDailyIDTO.searchCriteria == 3">
                        DATE_FORMAT(tprcw.sale_time,'%Y-%m-%d') AS createTime,
                    </if>
                    <if test="cashierDailyIDTO.searchCriteria == 4">
                        tprcw.employee_number AS saleNumber,
                        tprcw.employee_name AS saleName,
                        '销售'  AS saleType,
                        tprcw.pay_name  AS payType,
                        SUM(tprcw.total_amount) AS salesAmount,
                        0 AS refundAmount,
                    </if>

                    <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                        SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.total_amount end ) AS #{payType.payTypeCode},
                    </foreach>
                    SUM(tprcw.total_amount) AS payAmount,
                    '收银POS' AS posTypeName,
                    1 AS pos_type
                FROM
                   t_gift_card_cashier_water tprcw
                left join t_md_shop md on md.id = tprcw.shop_id
                <include refid="cashierDailyGiftSql"></include>
                <if test="cashierDailyIDTO.searchCriteria == 0">
                    GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 1">
                    GROUP BY tprcw.shop_id,tprcw.employee_number
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 2">
                    GROUP BY tprcw.shop_id,pos_type
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 3">
                    group by tprcw.shop_id,
                    pos_type,
                    year(tprcw.sale_time),
                    month(tprcw.sale_time),
                    day(tprcw.sale_time)
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 4">
                    GROUP BY tprcw.shop_id,tprcw.pay_type,tprcw.employee_number
                </if>
        ) tt
         where 1 = 1
        <if test="cashierDailyIDTO.searchCriteria == 0">
            GROUP BY tt.shopId,tt.saleNumber,tt.macCode, tt.saleType
            ORDER BY tt.shopId,tt.saleNumber,tt.macCode, tt.saleType
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 1">
            GROUP BY tt.shopId,tt.saleNumber
            ORDER BY tt.shopId,tt.saleNumber
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 2">
            GROUP BY tt.shopId, tt.pos_type
            ORDER BY tt.shopId
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 3">
            group by tt.shopId,
            tt.pos_type,
            year(tt.saleTime),
            month(tt.saleTime),
            day(tt.saleTime)
            ORDER BY tt.shopId ASC ,
            year(tt.saleTime) DESC ,
            month(tt.saleTime) DESC,
            day(tt.saleTime) DESC
        </if>
        <if test="cashierDailyIDTO.searchCriteria == 4">
            GROUP BY tt.shopId,tt.payType,tt.saleType,tt.saleNumber
            ORDER BY tt.shopId ASC,tt.saleNumber ASC ,ABS(sum(tt.payAmount)) DESC
        </if>
    </select>

    <!-- 线下售卡，线下营业款 总计 -->
    <select id="selectSUMCashierDailyAndGiftCardList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDailyIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDailySUMODTO">
        SELECT
           <if test="cashierDailyIDTO.searchCriteria == 0 or cashierDailyIDTO.searchCriteria == 1 or cashierDailyIDTO.searchCriteria == 2 or cashierDailyIDTO.searchCriteria == 3">
                <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                    SUM(${payType.payTypeCode}) AS #{payType.payTypeCode},
                </foreach>
                SUM(tt.total) AS total
            </if>
            <if test="cashierDailyIDTO.searchCriteria == 4">
                SUM(tt.payAmount) AS payAmount,
                SUM(tt.salesAmount) AS salesAmount,
                SUM(tt.refundAmount) AS refundAmount
            </if>
        FROM (
            SELECT
                <if test="cashierDailyIDTO.searchCriteria == 0 or cashierDailyIDTO.searchCriteria == 1 or cashierDailyIDTO.searchCriteria == 2 or cashierDailyIDTO.searchCriteria == 3">
                    <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                        SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.pay_amount end ) AS #{payType.payTypeCode},
                    </foreach>
                    SUM(tprcw.pay_amount) AS total
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 4">
                    SUM(tprcw.pay_amount) AS payAmount,
                    SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount else 0 end ) AS salesAmount,
                    SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) else 0 end ) AS refundAmount
                </if>
            FROM
                <if test="cashierDailyIDTO.isCurrentDay == true">
                    t_pos_report_cashier_water tprcw
                </if>
                <if test="cashierDailyIDTO.isCurrentDay == false">
                    t_pos_report_cashier_water_day tprcw
                </if>
              <include refid="cashierDailySql"></include>

            union all

            SELECT
                <if test="cashierDailyIDTO.searchCriteria == 0 or cashierDailyIDTO.searchCriteria == 1 or cashierDailyIDTO.searchCriteria == 2 or cashierDailyIDTO.searchCriteria == 3">
                    <foreach item="payType" collection="cashierDailyIDTO.payTypeList">
                        SUM(CASE WHEN tprcw.pay_type = #{payType.payTypeCode} then tprcw.total_amount end ) AS #{payType.payTypeCode},
                    </foreach>
                    SUM(tprcw.total_amount) AS total
                </if>
                <if test="cashierDailyIDTO.searchCriteria == 4">
                    SUM(tprcw.total_amount) AS payAmount,
                    SUM(tprcw.total_amount) AS salesAmount,
                    0 AS refundAmount
                </if>
            FROM
              t_gift_card_cashier_water tprcw
             <include refid="cashierDailyGiftSql"></include>

        ) tt
    </select>




    <sql id="cashierReconciledSql">
        WHERE 1 = 1
        and tprcw.shop_id IN
        <foreach collection="cashierReconciledIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test="cashierReconciledIDTO.startTime != null and cashierReconciledIDTO.startTime != '' and cashierReconciledIDTO.EndTime != null and cashierReconciledIDTO.EndTime != '' ">
                AND tprcw.create_time between #{cashierReconciledIDTO.startTime} and #{cashierReconciledIDTO.EndTime}
        </if>
        <if test=" null !=cashierReconciledIDTO.shopCode and '' != cashierReconciledIDTO.shopCode">
            AND tprcw.shop_id = #{cashierReconciledIDTO.shopCode}
        </if>
        <if test=" null != cashierReconciledIDTO.saleNumber and '' != cashierReconciledIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierReconciledIDTO.saleNumber}
        </if>
        <if test=" null != cashierReconciledIDTO.saleType and '' != cashierReconciledIDTO.saleType">
            AND tprcw.sale_type = #{cashierReconciledIDTO.saleType}
        </if>
        <if test=" null != cashierReconciledIDTO.payType and '' != cashierReconciledIDTO.payType">
            AND tprcw.pay_type = #{cashierReconciledIDTO.payType}
        </if>
        <if test=" 1 == cashierReconciledIDTO.sumOfMoney">
            AND tprcw.pay_type != 'ZF027'
        </if>
        <if test="cashierReconciledIDTO.macCode != null and cashierReconciledIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierReconciledIDTO.macCode}, '%')
        </if>
        <if test="null != cashierReconciledIDTO.posType">
            and tprcw.pos_type = #{ cashierReconciledIDTO.posType}
        </if>
    </sql>

    <sql id="cashierReconciledGiftSql">
        WHERE 1 = 1
        and tprcw.shop_id IN
        <foreach collection="cashierReconciledIDTO.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        <if test="cashierReconciledIDTO.startTime != null and cashierReconciledIDTO.startTime != '' and cashierReconciledIDTO.EndTime != null and cashierReconciledIDTO.EndTime != '' ">
                AND tprcw.sale_time between #{cashierReconciledIDTO.startTime} and #{cashierReconciledIDTO.EndTime}
        </if>
        <if test=" null !=cashierReconciledIDTO.shopCode and '' != cashierReconciledIDTO.shopCode">
            AND tprcw.shop_id = #{cashierReconciledIDTO.shopCode}
        </if>
        <if test=" null != cashierReconciledIDTO.saleNumber and '' != cashierReconciledIDTO.saleNumber">
            AND tprcw.employee_number = #{cashierReconciledIDTO.saleNumber}
        </if>
         <if test="cashierReconciledIDTO.saleType != null and cashierReconciledIDTO.saleType == 1">
                AND 1 = 1
        </if>
        <if test="cashierReconciledIDTO.saleType != null and cashierReconciledIDTO.saleType == 2">
            AND 1 = 2
        </if>
        <if test=" null != cashierReconciledIDTO.payType and '' != cashierReconciledIDTO.payType">
            AND tprcw.pay_type = #{cashierReconciledIDTO.payType}
        </if>
        <if test=" 1 == cashierReconciledIDTO.sumOfMoney">
            AND tprcw.pay_type != 'ZF027'
        </if>
        <if test="cashierReconciledIDTO.macCode != null and cashierReconciledIDTO.macCode != ''">
            AND tprcw.mac_code LIKE concat('%', #{cashierReconciledIDTO.macCode}, '%')
        </if>
        <if test="null != cashierReconciledIDTO.posType and  cashierReconciledIDTO.posType == 1">
            and 1 = 1
        </if>
        <if test="null != cashierReconciledIDTO.posType and  cashierReconciledIDTO.posType == 2">
            and 1 = 2
        </if>
    </sql>

    <!-- 收银员对账 -->
    <select id="selectCashierReconciledList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledODTO">
       select
           tt.shop_id,
           tt.shopCode,
           tt.shopName,
           tt.saleNumber,
           tt.saleName,
           tt.saleType,
           tt.payTypeName,
           tt.payType,
           sum(tt.payAmount) payAmount,
           sum(tt.salesAmount) salesAmount,
           sum(tt.refundAmount) refundAmount,
           tt.mac_code,tt.posTypeName
        from (
            SELECT
                tprcw.shop_id,
                tprcw.shop_code AS shopCode,
                tprcw.shop_name AS shopName,
                tprcw.employee_number AS saleNumber,
                tprcw.create_name saleName,
                (case tprcw.sale_type when 1 then '销售' when 2 then '退货' end ) AS saleType,
                tprcw.pay_name  AS payTypeName,
                tprcw.pay_type  AS payType,
                SUM(tprcw.pay_amount)AS payAmount,
                IFNULL(SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount end ),0) AS salesAmount,
                IFNULL(SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end ),0) AS refundAmount,
                tprcw.mac_code,
                CASE tprcw.pos_type
                WHEN 2 THEN '自助POS'
                WHEN 3 THEN 'PDA'
                ELSE '收银POS'
                END AS posTypeName
            FROM
                <if test="cashierReconciledIDTO.isCurrentDay == true">
                    t_pos_report_cashier_water tprcw
                </if>
                <if test="cashierReconciledIDTO.isCurrentDay == false">
                    t_pos_report_cashier_water_day tprcw
                </if>
            <include refid="cashierReconciledSql"/>
            GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code,tprcw.pay_type

            union all

            SELECT
                tprcw.shop_id,
                md.shop_code AS shopCode,
                md.shop_name AS shopName,
                tprcw.employee_number AS saleNumber,
                tprcw.employee_name AS saleName,
                '销售' AS saleType,
                tprcw.pay_name  AS payTypeName,
                tprcw.pay_type  AS payType,
                SUM(tprcw.total_amount)AS payAmount,
                IFNULL(SUM(tprcw.total_amount),0) AS salesAmount,
                0 AS refundAmount,
                tprcw.mac_code,
                '收银POS' AS posTypeName
            FROM
              t_gift_card_cashier_water tprcw left join t_md_shop md on md.id = tprcw.shop_id
             <include refid="cashierReconciledGiftSql"/>
             GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code,tprcw.pay_type
       ) tt
        group by tt.shop_id,tt.saleNumber,tt.mac_code,tt.payType
        ORDER BY tt.shop_id ASC,tt.saleNumber ASC ,ABS(sum(tt.payAmount)) DESC
    </select>

    <select id="selectSUMCashierReconciledList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledSUMODTO">
        select
           sum(tt.payAmount) payAmount,
           sum(tt.salesAmount) salesAmount,
           sum(tt.refundAmount) refundAmount
        from (
            SELECT
                SUM(tprcw.pay_amount)AS payAmount,
                IFNULL(SUM(CASE WHEN tprcw.sale_type = 1 then tprcw.pay_amount end ),0) AS salesAmount,
                IFNULL(SUM(CASE WHEN tprcw.sale_type = 2 then ABS(tprcw.pay_amount) end ),0) AS refundAmount
            FROM
                <if test="cashierReconciledIDTO.isCurrentDay == true">
                    t_pos_report_cashier_water tprcw
                </if>
                <if test="cashierReconciledIDTO.isCurrentDay == false">
                    t_pos_report_cashier_water_day tprcw
                </if>
            <include refid="cashierReconciledSql"/>

            union all

            SELECT
                SUM(tprcw.total_amount)AS payAmount,
                IFNULL(SUM(tprcw.total_amount),0) AS salesAmount,
                0 AS refundAmount
            FROM
             t_gift_card_cashier_water tprcw
             <include refid="cashierReconciledGiftSql"/>
         ) tt
    </select>

     <!-- 收银员对账 统计笔数-->
    <select id="selectCashierWaterCount"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierWaterCountODTO">
        select
              tt.shop_id,
              tt.employee_number,
              tt.mac_code,
              tt.pay_type,
              sum(tt.saleCount) saleCount,
              sum(tt.returnCount) returnCount
          from (

            SELECT
                tprcw.shop_id,
                tprcw.employee_number,
                tprcw.mac_code,
                tprcw.pay_type,
                count(DISTINCT case when sale_type = 1 then order_code end) saleCount,
                count(DISTINCT case when sale_type = 2 then order_code end) returnCount
            FROM t_pos_report_cashier_water tprcw
            <include refid="cashierReconciledSql"/>
            GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code,tprcw.pay_type

            union all

            SELECT
                tprcw.shop_id,
                tprcw.employee_number,
                tprcw.mac_code,
                tprcw.pay_type,
                count(DISTINCT tprcw.order_code) saleCount,
                0 returnCount
            FROM t_gift_card_cashier_water tprcw
            <include refid="cashierReconciledGiftSql"/>
            GROUP BY tprcw.shop_id,tprcw.employee_number,tprcw.mac_code,tprcw.pay_type

       ) tt
       GROUP BY tt.shop_id,tt.employee_number,tt.mac_code,tt.pay_type
    </select>



    <!--收银员对账 -日对账 -->
    <select id="selectDiurnalReconciliationList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledODTO">
        select
            tt.shopCode,
            tt.shopName,
            tt.newDate,
            tt.saleNumber,
            tt.saleName,
            tt.saleType,
            tt.payType,
            sum(tt.salesAmount) salesAmount
        from (
            SELECT
                tprcw.shop_id,
                tprcw.create_time createTime,
                tprcw.shop_code AS shopCode,
                tprcw.shop_name AS shopName,
                DATE_FORMAT(tprcw.create_time,'%Y-%m-%d') AS newDate,
                tprcw.employee_number AS saleNumber,
                tprcw.create_name AS saleName,
                (CASE tprcw.sale_type WHEN 1 THEN '销售' WHEN 2 THEN '退货' END) AS saleType,
                tprcw.pay_name AS payType,
                SUM(tprcw.pay_amount)AS salesAmount
            FROM
                <if test="cashierReconciledIDTO.isCurrentDay == true">
                    t_pos_report_cashier_water tprcw
                </if>
                <if test="cashierReconciledIDTO.isCurrentDay == false">
                    t_pos_report_cashier_water_day tprcw
                </if>
           <include refid="cashierReconciledSql"/>
            GROUP BY tprcw.pay_type,tprcw.sale_type,tprcw.shop_id,tprcw.employee_number,
            year(tprcw.create_time),
            month(tprcw.create_time),
            day(tprcw.create_time)

            union all

            SELECT
                tprcw.shop_id,
                tprcw.sale_time createTime,
                md.shop_code AS shopCode,
                md.shop_name AS shopName,
                DATE_FORMAT(tprcw.sale_time,'%Y-%m-%d') AS newDate,
                tprcw.employee_number AS saleNumber,
                tprcw.employee_name AS saleName,
                '销售'  AS saleType,
                tprcw.pay_name AS payType,
                SUM(tprcw.total_amount)AS salesAmount
            FROM
               t_gift_card_cashier_water tprcw left join t_md_shop md on md.id = tprcw.shop_id
            <include refid="cashierReconciledGiftSql"/>
            GROUP BY tprcw.pay_type,tprcw.shop_id,tprcw.employee_number,
            year(tprcw.sale_time),
            month(tprcw.sale_time),
            day(tprcw.sale_time)
        ) tt
        GROUP BY tt.payType,tt.saleType,tt.shop_id,tt.saleNumber,
            year(tt.createTime),
            month(tt.createTime),
            day(tt.createTime)
        ORDER BY  year(tt.createTime) DESC,month(tt.createTime) DESC,day(tt.createTime) DESC  ,tt.shopCode ASC ,tt.saleNumber ASC ,ABS(sum(tt.salesAmount)) DESC
    </select>
    <select id="selectSUMDiurnalReconciliationList"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierReconciledIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierReconciledSUMODTO">
        select
             sum(tt.salesAmount) salesAmount
          from (
            SELECT
               sum(tprcw.pay_amount) AS salesAmount
            FROM
                <if test="cashierReconciledIDTO.isCurrentDay == true">
                    t_pos_report_cashier_water tprcw
                </if>
                <if test="cashierReconciledIDTO.isCurrentDay == false">
                    t_pos_report_cashier_water_day tprcw
                </if>
            <include refid="cashierReconciledSql"/>

            union all

            SELECT
               sum(tprcw.total_amount) AS salesAmount
            FROM
              t_gift_card_cashier_water tprcw
            <include refid="cashierReconciledGiftSql"/>
        ) tt
    </select>
</mapper>