<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.ReportMapper">
    <!--
        以类别(t_category)为主表
        左连接 当天的今日销售额数据(品鲜POS数据 + 思迅POS数据)
        左连接 上周今日销售额数据(品鲜POS数据 + 思迅POS数据)
    -->
    <select id="categorySalesWeekPercent" parameterType="com.pinshang.qingyun.report.dto.CategorySalesWeekPercentIDTO"
            resultType="com.pinshang.qingyun.report.dto.CategorySalesWeekPercentODTO">
        SELECT
            tc.id AS commodity_first_id,
            tc.cate_name AS firstKindName,
            IFNULL(today.todaySalesMonery, 0) AS todaySalesMonery,
            IFNULL(lastWeek.lastWeekSalesMonery, 0) AS lastWeekSalesMonery
        FROM t_category tc
        LEFT JOIN (
            SELECT
               prss.commodity_first_id,
               sum(prss.offline_sales) AS todaySalesMonery
            FROM t_md_commodity_first_tax prss
            WHERE 1=1
            <if test=" shopId != null "> AND prss.shop_id = #{shopId} </if>
            <if test=" shopId == null ">
                AND prss.shop_id IN
                <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND prss.date_time = #{salesDate}
            GROUP BY prss.commodity_first_id
        ) today ON tc.id = today.commodity_first_id
          LEFT JOIN (
            SELECT
              tprss.commodity_first_id,
              sum(tprss.offline_sales) AS lastWeekSalesMonery
            FROM t_md_commodity_first_tax tprss
            WHERE 1=1
            <if test=" shopId != null "> AND tprss.shop_id = #{shopId} </if>
            <if test=" shopId == null ">
                AND tprss.shop_id IN
                <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND tprss.date_time = DATE_SUB(#{salesDate}, INTERVAL 7 DAY)
            GROUP BY tprss.commodity_first_id
        ) lastWeek
        ON tc.id = lastWeek.commodity_first_id
        WHERE tc.parent_id = 0 AND tc.status = 1 AND (today.todaySalesMonery > 0 or lastWeek.lastWeekSalesMonery > 0)
    </select>


    <select id="shopSalesWeekPercent" parameterType="com.pinshang.qingyun.report.dto.ShopSalesWeekIDTO"
            resultType="com.pinshang.qingyun.report.dto.ShopSalesWeekODTO">
          SELECT
                md.shop_name shopName,
--                 md.id shopId,
                IFNULL(today.saleAmount,0) todaySalesAmount,
                IFNULL(today.visitNum,0) todayVisitNum,
                IFNULL(lastWeek.saleAmount,0) lastWeekSalseAmount,
                IFNULL(lastWeek.visitNum,0) lastWeekVisitNum
            FROM
                t_md_shop md
                LEFT JOIN (
                    SELECT
                        tmd.shop_id,
                        SUM(tmd.offline_sales) saleAmount,
                        SUM(tmd.offline_visitor_number) visitNum
                    FROM
                        t_md_commodity_shop_tax tmd
                    WHERE
                        tmd.offline_sales > 0
                        AND  tmd.date_time = #{today}
                        <choose>
                            <when test="shopId != null and shopId !=''"> AND tmd.shop_id = #{shopId} </when>
                            <otherwise>
                                AND tmd.shop_id IN
                                <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                                    #{id}
                                </foreach>
                            </otherwise>
                        </choose>
                    GROUP BY
                        tmd.shop_id

                ) today ON today.shop_id = md.id
                LEFT JOIN (
                    SELECT
                        tmd.shop_id,
                        SUM(tmd.offline_sales) saleAmount,
                        SUM(tmd.offline_visitor_number) visitNum
                    FROM
                        t_md_commodity_shop_tax tmd
                    WHERE
                        tmd.offline_sales > 0
                        AND  tmd.date_time = #{lastWeek}
                        <choose>
                            <when test="shopId != null and shopId !=''"> AND tmd.shop_id = #{shopId} </when>
                            <otherwise>
                                AND tmd.shop_id IN
                                <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                                    #{id}
                                </foreach>
                            </otherwise>
                        </choose>
                    GROUP BY
                        tmd.shop_id

                ) lastWeek ON lastWeek.shop_id = md.id
            WHERE
                today.saleAmount > 0
            OR lastWeek.saleAmount > 0
    </select>
    <select id="selectFirstCateSum" resultType="com.pinshang.qingyun.report.dto.FirstCateSumODTO">
        SELECT
        sum(total_sales) as sumPrice,
        commodity_first_id as firstId
        FROM
        t_md_commodity_first_tax
        WHERE
        date_time >= #{start}
        <![CDATA[
        and date_time <= #{end}
        ]]>
        GROUP BY commodity_first_id
    </select>

    <select id="getMdCheckCommodityList" resultType="com.pinshang.qingyun.report.dto.mdCheck.DayOrderReportODTO">
        SELECT
            tt.order_time orderTime,
            tt.shop_id,
            tt.shop_code,
            tt.shop_name,
            tt.commodity_id,
            IFNULL(tt.order_quantity,0) orderQuantity,
            IFNULL(tt.delivery_quantity,0) deliveryQuantity,
            IFNULL(tt.order_total_amount,0) orderTotalAmount,
            IFNULL(tt.delivery_total_amount,0) deliveryTotalAmount
        from t_md_check_commodity_month tt
        where tt.order_time between #{start} and #{end}
    </select>

    <insert id="insertMdCheckCommodityList">
        Insert into t_md_check_commodity_month(order_time,shop_id,shop_code,shop_name,
                                               commodity_id,order_quantity,delivery_quantity,
                                                order_total_amount,delivery_total_amount,
                                               create_id,create_time,update_id,update_time)

        SELECT
            DATE_FORMAT(tt.order_time,'%Y-%m-%d') orderTime,
            tt.shopId,
            tt.shop_code,
            tt.shop_name,
            tt.commodity_id,
            IFNULL(sum(tt.orderQuantity),0) orderQuantity,
            IFNULL(sum(tt.deliveryQuantity),0) deliveryQuantity,
            IFNULL(sum(tt.orderTotalPrice),0) orderTotalAmount,
            IFNULL(sum(tt.deliveryTotalPrice),0) deliveryTotalAmount,
            1,now(),1,now()
        from (
                 SELECT
                     md.id shopId,
                     md.shop_code,
                     md.shop_name,
                     t.commodity_id,
                     SUM(t.quantity) orderQuantity,
                     SUM(t.price*t.quantity) orderTotalPrice,
                     sum(t.real_delivery_quantity) deliveryQuantity,
                     sum(t.price*t.real_delivery_quantity) deliveryTotalPrice,
                     t.order_time
                 FROM t_day_order_report t
                          LEFT JOIN t_md_shop md on md.store_id = t.store_id
                 WHERE t.order_time = #{day}
                and t.commodity_id in
                <foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                 GROUP BY t.order_time,t.store_id,t.commodity_id

                 union ALL

                 SELECT
                     md.id shopId,
                     md.shop_code,
                     md.shop_name,
                     t.commodity_id,
                     SUM(t.require_quantity) orderQuantity,
                     SUM(t.price*t.require_quantity) orderTotalPrice,
                     sum(t.real_receive_quantity) deliveryQuantity,
                     sum(t.price*t.real_receive_quantity) deliveryTotalPrice,
                     t.order_time
                 FROM t_day_preorder_report t
                          LEFT JOIN t_md_shop md on md.store_id = t.store_id
                 WHERE t.order_time  = #{day}
                 and t.commodity_id in
                    <foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                 GROUP BY t.order_time,t.store_id,t.commodity_id

             ) tt GROUP BY tt.order_time,tt.shopId,tt.commodity_id

    </insert>

    <insert id="batchInsertMdCheckCommodityList" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_md_check_commodity_month
        (order_time,shop_id,shop_code,shop_name,commodity_id,order_quantity,delivery_quantity,
        order_total_amount,delivery_total_amount,create_id,create_time,update_id,update_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.orderTime}, #{item.shopId}, #{item.shopCode}, #{item.shopName},#{item.commodityId},
            #{item.orderQuantity},#{item.deliveryQuantity},#{item.orderTotalAmount},#{item.deliveryTotalAmount},
            1,now(),1,now())
        </foreach>
    </insert>

    <delete id="deleteMdCheckCommodity">
        DELETE  FROM  t_md_check_commodity_month WHERE order_time = #{day}
    </delete>

    <delete id="deleteMdCheckInfo">
        DELETE  FROM  t_md_check_info_month
        WHERE  plan_begin_date <![CDATA[ >= ]]> #{beginTime}
          and plan_end_date <![CDATA[ <= ]]> #{endTime}
    </delete>

    <insert id="batchInsertMdCheckInfo" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_md_check_info_month
        (shop_id, plan_begin_date, plan_end_date, check_group_id,check_group_name,type,total_plan_aim,day_plan_aim,duration_plan_aim,complete_aim,complete_aim_percent,
        create_id,create_time,update_id,update_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.shopId}, #{item.planBeginDate}, #{item.planEndDate}, #{item.checkGroupId},#{item.checkGroupName},
             #{item.type},#{item.totalPlanAim},#{item.dayPlanAim},#{item.durationPlanAim},#{item.completeAim},#{item.completeAimPercent},
             1,now(),1,now())
        </foreach>
    </insert>



    <select id="getMdCheckGroupList" resultType="com.pinshang.qingyun.report.dto.mdCheck.MdCheckGroupODTO">
        SELECT
            DISTINCT
            t.check_group_id,
            CONCAT(t.plan_begin_date,'~',t.plan_end_date,'  ',t.check_group_name) checkGroupName
        from t_md_check_info_month t
        where t.shop_id = #{shopId}
        and  t.plan_end_date <![CDATA[ >= ]]> #{today}
        order by t.plan_begin_date,t.complete_aim_percent,t.plan_end_date
    </select>

    <select id="getShopCheckPan" resultType="com.pinshang.qingyun.report.dto.mdCheck.MdCheckReportODTO">
        SELECT
            t.plan_begin_date,
            t.plan_end_date,
            t.check_group_id,
            t.check_group_name,
            t.type,
            t.total_plan_aim,
            t.day_plan_aim,
            t.duration_plan_aim,
            t.complete_aim,
            t.complete_aim_percent
        from t_md_check_info_month t
        where t.shop_id = #{shopId}
          and (#{today} between t.plan_begin_date  and t.plan_end_date
           or #{today} <![CDATA[ < ]]> t.plan_begin_date)
        <if test=" checkGroupId != null ">
          AND t.check_group_id= #{checkGroupId}
        </if>
         order by t.plan_begin_date,t.complete_aim_percent,t.plan_end_date
    </select>


    <select id="querySaleCommodityIds" resultType="java.lang.Long">
        SELECT
            DISTINCT t.commodity_id
       FROM t_pos_report_day_sales_summary t
        WHERE t.shop_id = #{shopId}
          AND t.sale_time BETWEEN #{unsalableDay} AND #{yesterDay}
          and t.sale_quantity > 0
    </select>

    <delete id="deleteBestsellerGoodMonth">
        delete from t_pos_report_best_seller_month where sale_month = #{month}
    </delete>

    <insert id="insertBestsellerGoodMonth">

        insert into t_pos_report_best_seller_month
                            (shop_id,shop_code,shop_name,commodity_first_id,commodity_first_code,commodity_first_name,
                              commodity_id,commodity_code,commodity_name,commodity_spec,commodity_unit,is_weight,total_quantity,sale_month,
                              create_id,create_time)

        SELECT
            tp.shop_id,
            tp.shop_code,
            tp.shop_name,
            tp.commodity_first_id,
            tp.commodity_first_code,
            tp.commodity_first_name,
            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_spec,
            tp.commodity_unit,
            tc.is_weight,
            IFNULL(SUM(tp.tatal_quantity),0) tatalQuantity,
            DATE_FORMAT(tp.sale_time,'%Y-%m'),
            1,NOW()
        FROM
            t_pos_report_day_sales_summary tp
                LEFT JOIN t_commodity tc on tc.id = tp.commodity_id
        WHERE  tp.sale_time BETWEEN #{beginTime} AND #{endTime}  and tp.shop_id = #{shopId}
        GROUP BY tp.commodity_id
        ORDER BY tatalQuantity DESC,tp.commodity_id
        LIMIT 500

    </insert>



    <select id="bestsellerGoodMonthReport" resultType="com.pinshang.qingyun.report.dto.BestsellerGoodODTO">
        SELECT
           t.shop_id,
           t.shop_code,
           t.shop_name,
           t.commodity_first_name,
           t.commodity_id,
           t.commodity_code,
           t.commodity_name,
           t.commodity_spec,
           t.commodity_unit commodityUnitName,
           t.is_weight,
           t.total_quantity
        FROM
            t_pos_report_best_seller_month t
        WHERE t.sale_month = #{idto.saleMonth}
        <if test=" idto.shopId != null ">
           AND t.shop_id = #{idto.shopId}
        </if>

        <if test=" idto.cate1 != null ">
            AND t.commodity_first_id = #{idto.cate1}
        </if>

        <if test=" idto.commodityId != null ">
            AND t.commodity_id = #{idto.commodityId}
        </if>

        <if test=" idto.barCode != null and idto.barCode != '' ">
            AND t.commodity_id = (SELECT tc.commodity_id FROM t_commodity_bar_code tc where tc.bar_code = #{idto.barCode})
        </if>
        order by t.total_quantity desc,t.commodity_id
    </select>



    <!--批量插入混合收款-->
    <insert id="batchSaveOrUpdateBlendCollect" parameterType="java.util.List">
        INSERT INTO `t_pos_blend_collect_report`
        (store_id,shop_id,collect_date,collect_type,collect_amount,fee_amount,create_id,create_name,create_time,update_id,update_time)
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.storeId},
            #{item.shopId},
            #{item.collectDate},
            #{item.collectType},
            #{item.collectAmount},
            #{item.feeAmount},
            #{item.createId},
            #{item.createName},
            #{item.createTime},
            #{item.updateId},
            #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        `collect_amount`= VALUES(`collect_amount`),
        `fee_amount`= VALUES(`fee_amount`),
        `create_name`= VALUES(`create_name`),
        `update_id`= VALUES(`update_id`),
        `update_time`= VALUES(`update_time`)
    </insert>


    <sql id="blendCollectSql">
        WHERE 1=1
        <choose>
            <when test="ito.shopId != null and ito.shopId != '' " >
                AND tp.shop_id = #{ito.shopId}
            </when>
            <otherwise>
                AND tp.shop_id IN
                <foreach collection="ito.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test="ito.beginDate != null and ito.endDate != '' and ito.beginDate != null and ito.endDate != '' ">
            and tp.collect_date BETWEEN #{ito.beginDate} and #{ito.endDate}
        </if>
        <if test="ito.collectType != null">
            AND tp.collect_type = #{ito.collectType}
        </if>
    </sql>

    <select id="blendCollectPage"  resultType="com.pinshang.qingyun.report.dto.BlendCollectODTO">
        SELECT
            md.id shopId,
            t.store_code,
            md.shop_code,
            md.shop_name,
            tp.collect_date,
            tp.collect_type,
            tp.collect_amount,
            tp.fee_amount,
            tp.create_name,
            tp.update_time createTime
        FROM
          t_pos_blend_collect_report  tp
          left join  t_store t on  t.id = tp.store_id
          left join  t_md_shop md on md.id = tp.shop_id
        <include refid="blendCollectSql"></include>
        order by tp.collect_date desc,md.shop_code,tp.collect_type
    </select>

    <select id="getBlendCollectSum"  resultType="com.pinshang.qingyun.report.dto.BlendCollectODTO">
        SELECT
            sum(tp.collect_amount) collectAmount,
            sum(tp.fee_amount) feeAmount
        FROM
          t_pos_blend_collect_report  tp
        <include refid="blendCollectSql"></include>
    </select>

    <select id="getBlendCollectShopIdList"  resultType="java.lang.Long">
        SELECT
          distinct tp.shop_id
        FROM
        t_pos_blend_collect_report  tp
        <include refid="blendCollectSql"></include>
    </select>

    <select id="getBlendCollectDateList"  resultType="java.lang.String">
        SELECT
        distinct tp.collect_date
        FROM
        t_pos_blend_collect_report  tp
        <include refid="blendCollectSql"></include>
    </select>

    <select id="getShopDateKey"  resultType="com.pinshang.qingyun.report.dto.BlendCollectODTO">
        SELECT
        DISTINCT tp.shop_id,tp.collect_date
        FROM
        t_pos_blend_collect_report  tp
        <include refid="blendCollectSql"></include>
    </select>

    <select id="getGiftCardCashierWaterList"  resultType="com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterODTO">
        SELECT
            tp.shop_id shopId,
            date_format(tp.sale_time, '%Y-%m-%d') saleTime,
            IFNULL(sum(tp.total_amount),0) totalAmount
        from t_gift_card_cashier_water tp
        where 1=1
        <if test="ito.shopId != null and ito.shopId != '' " >
            AND tp.shop_id = #{ito.shopId}
        </if>
        <!--<if test="ito.shopIdList != null and ito.shopIdList.size() > 0 " >
            AND tp.shop_id IN
            <foreach collection="ito.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>-->
        <if test="ito.beginDate != null and ito.endDate != '' and ito.beginDate != null and ito.endDate != '' ">
            and tp.sale_time BETWEEN #{ito.beginDate} and #{ito.endDate}
        </if>
        AND tp.pay_type IN
        <foreach collection="ito.payTypeList" index="index" item="payType" open="(" separator="," close=")">
            #{payType}
        </foreach>
        AND tp.type = 1
        group by tp.shop_id,saleTime
    </select>
    <select id="checkingExceptionPage"  resultType="com.pinshang.qingyun.report.dto.CheckingExceptionODTO">
        select
            tp.id,
            tp.date,
            tp.shop_id,
            md.shop_code,
            md.shop_name,
            tp.receivable_code,
            tp.receivable_name,
            tp.total_sale,
            tp.poundage,
            tp.amount,
            tp.difference,
            tp.create_id,
            tp.create_time
        from  t_pos_report_receipt_difference tp
            LEFT JOIN t_md_shop md on md.id = tp.shop_id
        WHERE 1=1
        <choose>
            <when test="ito.shopId != null and ito.shopId != '' " >
                AND tp.shop_id = #{ito.shopId}
            </when>
            <otherwise>
                AND tp.shop_id IN
                <foreach collection="ito.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test="ito.beginDate != null and ito.endDate != '' and ito.beginDate != null and ito.endDate != '' ">
            and tp.date BETWEEN #{ito.beginDate} and #{ito.endDate}
        </if>
        <if test="ito.receivableName != null and ito.receivableName != ''">
            and   tp.receivable_name = #{ito.receivableName}
        </if>
        order by tp.date desc,md.shop_code, tp.create_time desc
    </select>


    <select id="shopNegativeStockReport"  resultType="com.pinshang.qingyun.report.dto.shop.ShopNegativeStockODTO">
        select
            tp.shop_id,
            tp.shop_name,
            tp.shop_short_name,
            tp.store_code,
            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_spec,
            tp.commodity_unit,
            case tp.is_weight
            when 1 then '是'
            when 0 then '否'
            end as isWeightName,
            tp.stock_quantity,
            DATE_FORMAT(tp.extract_time, '%Y-%m-%d %H:%i:%S') as extractTimeStr,
            case tp.sell_status
            when 1 then '是'
            when 0 then '否'
            end as sellStatusName,
            (CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' WHEN ms.management_mode=3 THEN '档口分包' ELSE '' END) AS managementModeName
        from  t_shop_negative_stock_report tp
        left join t_md_shop ms on ms.id = tp.shop_id
        WHERE 1=1
        <if test="idto.shopId != null and idto.shopId !='' " >
            AND tp.shop_id = #{idto.shopId}
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
            AND tp.shop_id IN
            <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idto.beginDate != null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and tp.extract_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>
        <if test="idto.sellStatus != null">
            and  tp.sell_status = #{idto.sellStatus}
        </if>
        <if test = "idto.managementMode != null and idto.managementMode != ''">
            and ms.management_mode = #{idto.managementMode}
        </if>
        order by tp.shop_id,tp.commodity_id
    </select>

    <select id="categoryAllSalesWeekPercent" parameterType="com.pinshang.qingyun.report.dto.CategoryAllSalesPercentIDTO"
            resultType="com.pinshang.qingyun.report.dto.CategoryAllSalesPercentODTO">
        SELECT
        tc.id AS commodity_first_id,
        tc.cate_name AS firstKindName,
        IFNULL(today.thisSalesMoneyOffLine, 0) AS thisSalesMoneyOffLine,
        IFNULL(today.thisSalesMoneyOnLine, 0) AS thisSalesMoneyOnLine,
        IFNULL(
        lastWeek.lastSalesMoneyOffLine,
        0
        ) AS lastSalesMoneyOffLine,
        IFNULL(
        lastWeek.lastSalesMoneyOnLine,
        0
        ) AS lastSalesMoneyOnLine
        FROM
        t_category tc
        LEFT JOIN
        (SELECT
        prss.commodity_first_id,
        SUM(prss.offline_sales) AS thisSalesMoneyOffLine,
        SUM(prss.online_sales) AS thisSalesMoneyOnLine
        FROM
        t_md_commodity_first_tax prss FORCE INDEX(idx_date_time)
        WHERE 1 = 1
        <if test=" shopId != null "> AND prss.shop_id = #{shopId} </if>
        <if test=" shopIdList != null and shopIdList.size > 0">
            AND prss.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND prss.date_time = #{salesDate}
        GROUP BY prss.commodity_first_id) today
        ON tc.id = today.commodity_first_id
        LEFT JOIN
        (SELECT
        tprss.commodity_first_id,
        SUM(tprss.offline_sales) AS lastSalesMoneyOffLine,
        SUM(tprss.online_sales) AS lastSalesMoneyOnLine
        FROM
        t_md_commodity_first_tax tprss FORCE INDEX(idx_date_time)
        WHERE 1 = 1
        <if test=" shopId != null "> AND tprss.shop_id = #{shopId} </if>
        <if test=" shopIdList != null and shopIdList.size>0">
            AND tprss.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND tprss.date_time = DATE_SUB(#{salesDate}, INTERVAL 7 DAY)
        GROUP BY tprss.commodity_first_id) lastWeek
        ON tc.id = lastWeek.commodity_first_id
        WHERE tc.parent_id = 0
        AND tc.status = 1
        AND (
        today.thisSalesMoneyOffLine > 0
        OR lastWeek.lastSalesMoneyOffLine > 0
        OR today.thisSalesMoneyOnLine > 0
        OR lastWeek.lastSalesMoneyOnLine > 0
        )
    </select>


    <select id="categoryAllSalesMonthPercent" parameterType="com.pinshang.qingyun.report.dto.CategoryAllSalesPercentIDTO"
            resultType="com.pinshang.qingyun.report.dto.CategoryAllSalesPercentODTO">
        SELECT
        tc.id AS commodity_first_id,
        tc.cate_name AS firstKindName,
        IFNULL(today.thisSalesMoneyOffLine, 0) AS thisSalesMoneyOffLine,
        IFNULL(today.thisSalesMoneyOnLine, 0) AS thisSalesMoneyOnLine,
        IFNULL(lastMonth.lastSalesMoneyOffLine, 0) AS lastSalesMoneyOffLine,
        IFNULL(lastMonth.lastSalesMoneyOnLine, 0) AS lastSalesMoneyOnLine
        FROM t_category tc
        LEFT JOIN (
        SELECT
        prss.commodity_first_id,
        sum(prss.offline_sales) AS thisSalesMoneyOffLine,
        sum(prss.online_sales) AS thisSalesMoneyOnLine
        FROM t_md_commodity_first_tax prss FORCE INDEX(idx_date_time)
        WHERE 1=1
        <if test=" shopId != null "> AND prss.shop_id = #{shopId} </if>
        <if test=" shopIdList != null and shopIdList.size > 0">
            AND prss.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND prss.date_time BETWEEN #{beginDate} and #{endDate}
        GROUP BY prss.commodity_first_id
        ) today ON tc.id = today.commodity_first_id
        LEFT JOIN (
        SELECT
        tprss.commodity_first_id,
        sum(tprss.offline_sales) AS lastSalesMoneyOffLine,
        sum(tprss.online_sales) AS lastSalesMoneyOnLine
        FROM t_md_commodity_first_tax tprss FORCE INDEX(idx_date_time)
        WHERE 1=1
        <if test=" shopId != null "> AND tprss.shop_id = #{shopId} </if>
        <if test=" shopIdList != null and shopIdList.size > 0">
            AND tprss.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND tprss.date_time BETWEEN #{lastMonthBeginDate} and #{lastMonthEndDate}
        GROUP BY tprss.commodity_first_id
        ) lastMonth
        ON tc.id = lastMonth.commodity_first_id
        WHERE tc.parent_id = 0 AND tc.status = 1 AND (today.thisSalesMoneyOffLine > 0 or today.thisSalesMoneyOnLine > 0
        or lastMonth.lastSalesMoneyOffLine > 0 or lastMonth.lastSalesMoneyOnLine > 0)
    </select>
</mapper>