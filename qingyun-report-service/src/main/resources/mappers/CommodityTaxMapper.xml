<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.shop.CommodityTaxMapper">

    <select id="queryShopCommoditySaleList" resultType="com.pinshang.qingyun.report.dto.shop.ShopCommoditySaleStatisticsODTO">
        SELECT
            mct.shop_id AS shopId,mct.commodity_id AS commodityId,sum(mct.online_sales) AS totalQuantity
        FROM
            t_md_commodity_tax mct
        WHERE
            mct.date_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            AND mct.shop_id = #{shopId}
            AND mct.online_sales > 0
        GROUP BY mct.commodity_id
    </select>


    <select id="findMonthSalesQuanty" resultType="com.pinshang.qingyun.report.dto.shop.CommodityOrderSalesODTO" >
        SELECT
            md.date_time saleDate,
            <if test="flag ==1">
                sum(md.total_quanty) salesQuanty
            </if>
            <if test="flag ==0">
                md.total_quanty salesQuanty,
                md.order_quanty orderQuanty
            </if>
        FROM
          t_md_commodity_tax_month md
        WHERE md.shop_id = (SELECT t.id  FROM t_md_shop t WHERE t.store_id = #{storeId})
        AND md.commodity_id = #{commodityId}
        AND md.date_time BETWEEN  #{inBegin} AND  #{yesterday}
    </select>


    <select id="queryTopShopCommoditySale" resultType="java.lang.Long" >
        SELECT tt.commodity_id from (
            SELECT
                t.commodity_id,
                sum(t.offline_sales) offline_sales
            FROM
                t_md_commodity_tax t
            LEFT JOIN t_commodity tc on tc.id = t.commodity_id
            WHERE  tc.logistics_model != 0
              AND t.date_time BETWEEN  #{vo.beginTime} AND  #{vo.endTime}
              and  t.shop_id = #{vo.shopId}
              AND t.commodity_id IN
            <foreach collection="vo.commodityIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            GROUP BY t.commodity_id

        ) tt ORDER BY tt.offline_sales DESC limit 30
    </select>

    <select id = "lastMonthSale" resultType = "com.pinshang.qingyun.report.dto.shop.ShopCommoditySaleStatisticsODTO">
        SELECT mct.offline_sales,mct.shop_id, mct.commodity_id
        FROM t_md_commodity_tax mct
        WHERE mct.offline_sales > 0
        AND mct.date_time BETWEEN #{lastMonthSaleDTO.startTime} AND #{lastMonthSaleDTO.endTime}
        AND mct.shop_id IN
        <foreach collection="lastMonthSaleDTO.shopIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY mct.shop_id, mct.commodity_id
    </select>


    <delete id="deleteCommodityTaxMonth">
        DELETE FROM t_md_commodity_tax_month
        WHERE date_time = #{day}
    </delete>

    <insert id="insertCommodityTaxMonth">
        Insert into t_md_commodity_tax_month(shop_id,commodity_id,total_quanty,
                                             order_quanty,date_time,create_id,create_time)
        SELECT
            t.shop_id,
            t.commodity_id,
            t.total_quanty,
            t.order_quanty,
            t.date_time,
            1,NOW()
        from t_md_commodity_tax t
        where  t.date_time = #{timeStamp}
    </insert>

    <delete id="deleteCommodityTaxMonth30">
        DELETE FROM t_md_commodity_tax_month
        WHERE date_time <![CDATA[ < ]]> #{day}
    </delete>
</mapper>