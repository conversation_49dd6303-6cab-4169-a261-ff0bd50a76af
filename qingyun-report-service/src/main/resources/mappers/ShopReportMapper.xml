<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.ShopReportMapper">

	<select id="getShortDeliveryRealDeliveryAmount" resultType="java.math.BigDecimal" >

       SELECT sum(ROUND(tt.price*tt.deliveryNum,2)) from (

			select
				o.`price`,
				IFNULL(o.real_delivery_quantity,0) deliveryNum
				from t_day_order_report o
				where 1=1
				and o.order_status = 0
				and o.order_time BETWEEN #{beginTime} and #{endTime}
				and DATE_ADD(now(),INTERVAL -1 DAY) > o.order_time

				UNION ALL

				select
				so.`price`,
				IFNULL(so.real_receive_quantity,0) deliveryNum
				from t_day_preorder_report so
				where 1=1
				and so.order_status in (1,2)
				and so.order_time BETWEEN #{beginTime} and #{endTime}
				and DATE_ADD(now(),INTERVAL -1 DAY) > so.order_time

				UNION ALL

				select
				o.`price`,
				o.real_delivery_quantity deliveryNum
				from t_day_order_report o
				where 1=1
				and o.order_status = 0
				and o.order_time BETWEEN #{beginTime} and #{endTime}
				and o.real_delivery_quantity is not null
				and DATE_FORMAT(now(),'%Y-%m-%d') = o.order_time

				UNION ALL

				select
				so.`price`,
				so.real_receive_quantity deliveryNum
				from t_day_preorder_report so
				where 1=1
				and so.order_status in (1,2)
				and so.order_time BETWEEN #{beginTime} and #{endTime}
				and so.real_receive_quantity is not null
				and DATE_FORMAT(now(),'%Y-%m-%d') = so.order_time

		)tt
	</select>

	<select id="getCommodityRealDeliveryAmount" resultType="java.math.BigDecimal" >
         SELECT
		   sum(IFNULL(t.real_delivery_amount,0))
		FROM
		  t_real_delivery_report_day t
		where t.order_time BETWEEN #{beginTime} and #{endTime}
	</select>

	<!-- 短交报表 昨天及昨天前数据-->
	<select id="shortDeliveryBeforeYesterdayReport" resultType="com.pinshang.qingyun.report.dto.shop.ShortDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.ShortDeliveryReportIDto" >
		SELECT tt.* from (
		select
		ms.shop_code,
		ms.shop_name,
		o.order_time delivery_date,
		o.order_code,
		c.commodity_first_kind_name as categoryName,
		c.commodity_second_kind_name as secondCategoryName,
		c.commodity_third_kind_name as thirdCategoryName,
		c.`id` commodityId,
		c.`commodity_code`,
		c.`commodity_name`,
		c.`commodity_spec`,
		o.`commodity_unit_name` commodityUnitName,
		o.quantity orderNum,
		o.`price`,
		IFNULL(o.real_delivery_quantity,0) deliveryNum,
		(case when o.real_delivery_quantity is null then o.quantity else concat(o.quantity-o.real_delivery_quantity,'') end) as differNum,
		(case when o.real_delivery_quantity is null then '100.00%' else concat(FORMAT((o.quantity-o.real_delivery_quantity)/o.quantity*100,2),'%') end) as rate,
		o.factory_name,o.workshop_name,ts.store_code,o.real_name createName,ms.shop_type,
		ts.store_line_group_name
		from t_day_order_report o
		LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
		LEFT join t_store ts on ts.id=o.`store_id`
		LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
		where 1=1
		and o.order_status = 0
		<if test="shopId!=null and shopId != 0 ">
			AND ms.`id` = #{shopId}
		</if>
		<if test="shopType!=null and shopType == 1 ">
			and o.logistics_model in (0,1)
		</if>
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%')  )
		</if>
        <if test="barCode != null and barCode !=''">
            AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
        </if>
		<if test="cateId1 != null">
			AND c.commodity_first_kind_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_kind_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_kind_id = #{cateId3}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND o.order_code = #{orderCode}
		</if>
		<if test="factoryCode != null and factoryCode !=''">
			AND o.factory_code = #{factoryCode}
		</if>
		<if test="differ != null and differ == true">
			and (o.real_delivery_quantity is null or o.quantity != o.real_delivery_quantity)
		</if>
		UNION ALL
		select
		ms.shop_code,
		ms.shop_name,
		so.order_time delivery_date,
		so.order_code,
		c.commodity_first_kind_name as categoryName,
		c.commodity_second_kind_name as secondCategoryName,
		c.commodity_third_kind_name as thirdCategoryName,
		c.`id` commodityId,
		c.`commodity_code`,
		c.`commodity_name`,
		c.`commodity_spec`,
		so.`commodity_unit_name` commodityUnitName,
		so.require_quantity orderNum,
		so.`price`,
		IFNULL(so.real_receive_quantity,0) deliveryNum,
		(case when so.real_receive_quantity is null then so.require_quantity else concat(so.require_quantity-so.real_receive_quantity,'') end) as differNum,
		(case when so.real_receive_quantity is null then '100.00%' else concat(FORMAT((so.require_quantity-so.real_receive_quantity)/so.require_quantity*100,2),'%') end) as rate,
		so.factory_name,so.workshop_name,ts.store_code,so.real_name createName,ms.shop_type,
		ts.store_line_group_name
		from t_day_preorder_report so
		LEFT join `t_md_shop` ms on ms.`store_id` =  so.`store_id`
		LEFT join t_store ts on ts.id=so.`store_id`
		LEFT join `t_commodity` c on c.`id` = so.`commodity_id`
		where 1=1
		and so.order_status in (1,2)
		<if test="shopId!=null and shopId != 0 ">
			AND ms.`id` = #{shopId}
		</if>
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and so.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') )
		</if>
        <if test="barCode != null and barCode !=''">
            AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
        </if>
		<if test="cateId1 != null">
			AND c.commodity_first_kind_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_kind_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_kind_id = #{cateId3}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND so.order_code = #{orderCode}
		</if>
		<if test="factoryCode != null and factoryCode !=''">
			AND so.factory_code = #{factoryCode}
		</if>
		<if test="differ != null and differ == true">
			and (so.real_receive_quantity is null or so.require_quantity != so.real_receive_quantity)
		</if>
		ORDER BY shop_code ASC,delivery_date DESC,commodity_code
		) tt WHERE 1=1
		<if test="shopType != null and shopType != ''">
			and tt.shop_type = #{shopType}
		</if>
	</select>

	<!-- 短交报表今天的数据 -->
	<select id="shortDeliveryTodayReport" resultType="com.pinshang.qingyun.report.dto.shop.ShortDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.ShortDeliveryReportIDto" >
		SELECT tt.* from (
		select
		ms.shop_code,
		ms.shop_name,
		o.order_time delivery_date,
		o.order_code,
		c.commodity_first_kind_name as categoryName,
		c.commodity_second_kind_name as secondCategoryName,
		c.commodity_third_kind_name as thirdCategoryName,
		c.`id` commodityId,
		c.`commodity_code`,
		c.`commodity_name`,
		c.`commodity_spec`,
		o.`commodity_unit_name` commodityUnitName,
		o.quantity orderNum,
		o.`price`,
		o.real_delivery_quantity deliveryNum,
		o.quantity-o.real_delivery_quantity as differNum,
		concat(FORMAT((o.quantity-o.real_delivery_quantity)/o.quantity*100,2),'%') as rate,
		o.factory_name,o.workshop_name,ts.store_code,o.real_name createName,ms.shop_type,
	    ts.store_line_group_name,
		(CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
		from t_day_order_report o
		LEFT join `t_md_shop` ms on ms.`store_id` =  o.`store_id`
		LEFT join t_store ts on ts.id=o.`store_id`
		LEFT join `t_commodity` c on c.`id` = o.`commodity_id`
		where 1=1
		and o.order_status = 0
		<if test="shopId!=null and shopId != 0 ">
			AND ms.`id` = #{shopId}
		</if>
		<if test="shopType!=null and shopType == 1 ">
			and o.logistics_model in (0,1)
		</if>
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%')   )
		</if>
		<if test="barCode != null and barCode !=''">
			AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_kind_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_kind_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_kind_id = #{cateId3}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND o.order_code = #{orderCode}
		</if>
		<if test="factoryCode != null and factoryCode !=''">
			AND o.factory_code = #{factoryCode}
		</if>
		<if test="differ != null and differ == true">
			and o.quantity != o.real_delivery_quantity
		</if>
		<if test = "managementMode != null and managementMode != ''">
		    and ms.management_mode = #{managementMode}
		</if>
		and o.real_delivery_quantity is not null
		UNION ALL
		select
		ms.shop_code,
		ms.shop_name,
		so.order_time delivery_date,
		so.order_code,
		c.commodity_first_kind_name as categoryName,
		c.commodity_second_kind_name as secondCategoryName,
		c.commodity_third_kind_name as thirdCategoryName,
		c.`id` commodityId,
		c.`commodity_code`,
		c.`commodity_name`,
		c.`commodity_spec`,
		so.`commodity_unit_name` commodityUnitName,
		so.require_quantity orderNum,
		so.`price`,
		so.real_receive_quantity deliveryNum,
		so.require_quantity-so.real_receive_quantity as differNum,
		concat(FORMAT((so.require_quantity-so.real_receive_quantity)/so.require_quantity*100,2),'%') as rate,
		so.factory_name,so.workshop_name,ts.store_code,so.real_name createName,ms.shop_type,
		ts.store_line_group_name,
		(CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
		from t_day_preorder_report so
		LEFT join `t_md_shop` ms on ms.`store_id` =  so.`store_id`
		LEFT join t_store ts on ts.id= so.`store_id`
		LEFT join `t_commodity` c on c.`id` = so.`commodity_id`
		where 1=1
		and so.order_status in (1,2)
		<if test="shopId!=null and shopId != 0 ">
			AND ms.`id` = #{shopId}
		</if>
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and so.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') )
		</if>
        <if test="barCode != null and barCode !=''">
            AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
        </if>
		<if test="cateId1 != null">
			AND c.commodity_first_kind_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_kind_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_kind_id = #{cateId3}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND so.order_code = #{orderCode}
		</if>
		<if test="factoryCode != null and factoryCode !=''">
			AND so.factory_code = #{factoryCode}
		</if>
		<if test="differ != null and differ == true">
			and so.require_quantity != so.real_receive_quantity
		</if>
		<if test = "managementMode != null and managementMode != ''">
			and ms.management_mode = #{managementMode}
		</if>
		and so.real_receive_quantity is not null
		ORDER BY shop_code ASC,delivery_date DESC,commodity_code
		) tt WHERE 1=1
		<if test="shopType != null and shopType != ''">
			and tt.shop_type = #{shopType}
		</if>
	</select>

	<select id="getGroupCommodityTaxFromStock" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxODTO" >
		select
		xs.shop_id,
		<if test="collectType == 3">
			c.commodity_first_id as cateId,
		</if>
		<if test="collectType == 4">
			c.commodity_second_id as cateId,
		</if>
		<if test="collectType == 5">
			c.commodity_third_id as cateId,
		</if>
		sum(xs.stock_quantity*xs.weight_price) as stockQuantityPrice,
		ifnull((sum(xs.stock_quantity)/NULLIF(sum(csa.average_sales),0)),null) as stockQuantityDay,
		<![CDATA[
		sum((case when xs.stock_quantity<0 then 1 else 0 end)) as negativeStockQuantity
		]]>
		from t_xs_shop_commodity xs
		INNER JOIN t_commodity c
		on c.id = xs.commodity_id
		LEFT JOIN t_md_commodity_sales_avg csa
		on csa.enterprise_id = xs.enterprise_id and csa.shop_id = xs.shop_id and csa.commodity_id = xs.commodity_id
		<if test="collectType == 1">
			group by xs.shop_id
			order by xs.shop_id
		</if>
		<if test="collectType == 3">
			group by xs.shop_id,c.commodity_first_id
			order by xs.shop_id,c.commodity_first_id
		</if>
		<if test="collectType == 4">
			group by xs.shop_id,c.commodity_second_id
			order by xs.shop_id,c.commodity_second_id
		</if>
		<if test="collectType == 5">
			group by xs.shop_id,c.commodity_third_id
			order by xs.shop_id,c.commodity_third_id
		</if>
	</select>

	<select id="getCommodityTaxFromStock" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxODTO" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportIDto" >
		<![CDATA[
		select
		xs.shop_id,
		xs.commodity_id,
		c.commodity_first_id,
		c.commodity_second_id,
		c.commodity_third_id,
		xs.stock_quantity,
		xs.weight_price,
		xs.stock_quantity*xs.weight_price as stockQuantityPrice,
		(case when xs.stock_quantity/NULLIF(csa.average_sales,0) is null then null when xs.stock_quantity/NULLIF(csa.average_sales,0)<0 then 0 else xs.stock_quantity/NULLIF(csa.average_sales,0) end) as stockQuantityDay
		from t_xs_shop_commodity xs
		INNER JOIN t_commodity c
		on c.id = xs.commodity_id
		LEFT JOIN t_md_commodity_sales_avg csa
		on csa.enterprise_id = xs.enterprise_id and csa.shop_id = xs.shop_id and csa.commodity_id = xs.commodity_id
		where 1=1
		]]>
		<if test="shopId!=null and shopId !='' ">
			AND xs.shop_id = #{shopId}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="barCode!=null and barCode !='' ">
			AND c.id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode})
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		order by xs.shop_id,xs.commodity_id
	</select>

	<select id="commodityTaxReportByShop" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportIDto" >
		select
		ms.shop_name,
		t.shop_id,
		sum(total_sales) as total_sales,
		sum(cost_total) as cost_total,
		sum(online_sales) as online_sales,
		sum(offline_sales) as offline_sales,
		sum(order_total) as order_total,
		sum(sale_return_order_total) as sale_return_order_total,
		sum(stock_adjust_amount_total) as stock_adjust_amount_total,
		sum(offline_visitor_number) as offline_visitor_number,
		sum(offline_average_amount) as offline_average_amount
		from t_md_commodity_shop_tax t
		LEFT JOIN t_md_shop ms on ms.id = t.shop_id
		where 1=1
		<if test="shopId!=null and shopId !='' ">
			AND t.shop_id = #{shopId}
		</if>
		<if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
			<![CDATA[ and t.date_time >= #{beginDate} and t.date_time <= #{endDate} ]]>
		</if>
		group by t.shop_id
		order by t.shop_id
	</select>

	<sql id="commodityTaxReport">
		select
		ms.shop_name,
		t.shop_id,
		<if test="collectType == 2">
			c.id as commodityId,
			c.commodity_code,
			c.commodity_name,
			c.commodity_spec,
		</if>
		<if test="collectType == 3 || collectType == 4 || collectType == 5">
			tc.id as cateId,
			tc.cate_name,
		</if>
		sum(total_sales) as total_sales,
		sum(cost_total) as cost_total,
		sum(online_sales) as online_sales,
		sum(offline_sales) as offline_sales,
		sum(order_total) as order_total,
		sum(sale_return_order_total) as sale_return_order_total,
		sum(stock_adjust_amount_total) as stock_adjust_amount_total
		<if test="collectType == 2">
		    from t_md_commodity_tax t
		</if>
		<if test="collectType == 3">
			from t_md_commodity_first_tax t
		</if>
		<if test="collectType == 4">
			from t_md_commodity_second_tax t
		</if>
		<if test="collectType == 5">
			from t_md_commodity_third_tax t
		</if>
		<if test="collectType == 2">
		   LEFT JOIN t_commodity c on c.id = t.commodity_id
		</if>
		LEFT JOIN t_md_shop ms on ms.id = t.shop_id
		<if test="collectType == 3">
			LEFT JOIN t_category tc on tc.id = t.commodity_first_id
		</if>
		<if test="collectType == 4">
			LEFT JOIN t_category tc on tc.id = t.commodity_second_id
		</if>
		<if test="collectType == 5">
			LEFT JOIN t_category tc on tc.id = t.commodity_third_id
		</if>
		where 1=1
		<if test="shopId!=null and shopId !='' ">
			AND t.shop_id = #{shopId}
		</if>
		<if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
			<![CDATA[ and t.date_time >= #{beginDate} and t.date_time <= #{endDate} ]]>
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') )
		</if>
        <if test="barCode != null and barCode !=''">
            AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
        </if>
		<if test="cateId1 != null">
			AND t.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND t.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND t.commodity_third_id = #{cateId3}
		</if>
		<if test="collectType == 2">
			group by t.shop_id,t.commodity_id
			order by t.shop_id,t.commodity_id
		</if>
		<if test="collectType == 3">
			group by t.shop_id,t.commodity_first_id
			order by t.shop_id,t.commodity_first_id
		</if>
		<if test="collectType == 4">
			group by t.shop_id,t.commodity_second_id
			order by t.shop_id,t.commodity_second_id
		</if>
		<if test="collectType == 5">
			group by t.shop_id,t.commodity_third_id
			order by t.shop_id,t.commodity_third_id
		</if>
	</sql>


	<select id="commodityTaxFirstReport" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportIDto">
		<include refid="commodityTaxReport"></include>
	</select>

	<select id="commodityTaxSecondReport" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportIDto">
		<include refid="commodityTaxReport"></include>
	</select>

	<select id="commodityTaxThirdReport" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportIDto">
		<include refid="commodityTaxReport"></include>
	</select>

	<select id="commodityTaxCommodityReport" resultType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityTaxReportIDto">
		<include refid="commodityTaxReport"></include>
	</select>


	<select id="actualReceiptAnalysisReport" resultType="com.pinshang.qingyun.report.dto.shop.ActualReceiptAnalysisODto" parameterType="com.pinshang.qingyun.report.dto.shop.ActualReceiptAnalysisIDto">
		select
			tmp.id,
			tmp.shop_name,
			tmp.cateName,
			tmp.commodityId,
			tmp.bar_code,
			tmp.commodity_code,
			tmp.commodity_name,
			tmp.commodity_spec,
		    IFNULL(sum(tmp.totalQuantity),0) as totalQuantity,
		    IFNULL(sum(tmp.totalRealReceiveQuantity),0) as totalRealReceiveQuantity,
		    IFNULL(sum(tmp.totalRealDeliveryQuantity),0) as totalRealDeliveryQuantity
		from (
			SELECT
				ms.id,
				ms.shop_name,
				c.commodity_first_kind_name cateName,
				c.id commodityId,
				c.bar_code,
				c.commodity_code,
				c.commodity_name,
				c.commodity_spec,
				sum(o.quantity) as totalQuantity,
				sum(o.real_receive_quantity) as totalRealReceiveQuantity,
				sum(o.real_delivery_quantity) as totalRealDeliveryQuantity
			FROM
			t_day_order_report o
			LEFT JOIN t_md_shop AS ms ON ms.store_id = o.store_id
			LEFT JOIN t_commodity AS c ON o.commodity_id = c.id
			WHERE
			(
			   o.`status` != 0  AND o.`status` != 4
			)
			<if test="shopId != null">
				and ms.id = #{shopId}
			</if>
			<if test="cateId1 != null">
				and c.`commodity_first_kind_id`=#{cateId1}
			</if>
			<if test="cateId2 != null">
				and c.`commodity_second_kind_id`=#{cateId2}
			</if>
			<if test="cateId3 != null">
				and c.`commodity_third_kind_id`=#{cateId3}
			</if>
			<if test="searchWord != null and searchWord != ''">
				and
				(
				c.commodity_code like  concat('%',#{searchWord},'%')
				or
				c.commodity_name like  concat('%',#{searchWord},'%')
				)
			</if>
			<if test="barCode != null and barCode !=''">
				AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
			</if>
			<if test="orderBeginDate != null and orderEndDate != null ">
				and o.order_time between #{orderBeginDate} and #{orderEndDate}
			</if>
			GROUP BY c.id,ms.id

			UNION ALL

			SELECT
				ms.id,
				ms.shop_name,
				c.commodity_first_kind_name cateName,
				c.id commodityId,
				c.bar_code,
				c.commodity_code,
				c.commodity_name,
				c.commodity_spec,
				sum(so.require_quantity) as totalQuantity,
				sum(so.real_receive_quantity) as totalRealReceiveQuantity,
				sum(so.real_receive_quantity) as totalRealDeliveryQuantity
			FROM
			t_day_preorder_report AS so
			LEFT JOIN t_md_shop AS ms ON ms.store_id = so.store_id
			LEFT JOIN t_commodity AS c ON so.commodity_id = c.id
			where
			(
			   so.receive_status != 0 and so.receive_status != 4
			)
			<if test="shopId != null">
				and ms.id = #{shopId}
			</if>
			<if test="cateId1 != null">
				and c.`commodity_first_kind_id`=#{cateId1}
			</if>
			<if test="cateId2 != null">
				and c.`commodity_second_kind_id`=#{cateId2}
			</if>
			<if test="cateId3 != null">
				and c.`commodity_third_kind_id`=#{cateId3}
			</if>
			<if test="searchWord != null and searchWord != ''">
				and
				(
				c.commodity_code like  concat('%',#{searchWord},'%')
				or
				c.commodity_name like  concat('%',#{searchWord},'%')
				)
			</if>
			<if test="barCode != null and barCode !=''">
				AND c.id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
			</if>
			<if test="orderBeginDate != null and orderEndDate != null ">
				and so.order_time between #{orderBeginDate} and #{orderEndDate}
			</if>
			GROUP BY c.id,ms.id
		)  as tmp
		GROUP BY tmp.id,tmp.commodityId
		ORDER BY
		tmp.id
	</select>


	<select id="actualReceiptAnalysisMonthReport" resultType="com.pinshang.qingyun.report.dto.shop.ActualReceiptAnalysisODto" parameterType="com.pinshang.qingyun.report.dto.shop.ActualReceiptAnalysisIDto">
			SELECT
				o.shop_name,
				o.commodity_first_kind_name cateName,
				o.commodity_id commodityId,
				o.commodity_code,
				o.commodity_name,
				o.commodity_spec,
		        IFNULL(sum(o.quantity),0) as totalQuantity,
		        IFNULL(sum(o.real_receive_quantity),0) as totalRealReceiveQuantity,
		        IFNULL(sum(o.real_delivery_quantity),0) as totalRealDeliveryQuantity
			FROM
			  t_day_order_report_month o
			WHERE 1 = 1
			<if test="shopId != null">
				and o.shop_id = #{shopId}
			</if>
			<if test="cateId3 != null">
				and ((o.`commodity_first_kind_id`=#{cateId3}) or (o.`commodity_second_kind_id`=#{cateId3}) or (o.`commodity_third_kind_id`=#{cateId3}))
			</if>
			<if test="searchWord != null and searchWord != ''">
				and
				(
				o.commodity_code like  concat('%',#{searchWord},'%')
				or
				o.commodity_name like  concat('%',#{searchWord},'%')
				)
			</if>
            <if test="barCode != null and barCode !=''">
                AND o.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode} )
            </if>
			<if test="monthBegin != null and monthEnd != null">
				and o.month_time between #{monthBegin} and  #{monthEnd}
			</if>
			GROUP BY o.shop_id,o.commodity_id
		    ORDER BY  o.shop_id
	</select>


	<select id="appOperateReport" resultType="com.pinshang.qingyun.report.dto.shop.AppOperateReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.AppOperateReportIDto">
		SELECT
		id,
		shop_name,
		totalOrderCount,
		realTotalAmount,
		outOrderCount,
		outRealAmount,
		returnNum,
		returnAmount
		FROM t_md_shop AS ms
		LEFT JOIN (
		SELECT
		txo.shop_id,
		count(1) AS totalOrderCount,
		sum(txo.pay_amount) AS realTotalAmount
		FROM t_xs_order txo
		LEFT JOIN t_xs_pay_bill txpb ON txo.order_code = txpb.bill_code AND txpb.refer_type = 1
		WHERE txo.pay_status = 2 AND txo.order_status != 1 AND txo.source_type=1
		<if test="shopId != null "> and txo.shop_id = #{shopId} </if>
		<if test="orderBeginDate != null"> and txpb.create_time >= #{orderBeginDate} </if>
		<if test="orderEndDate != null"> and #{orderEndDate} >= txpb.create_time </if>
		GROUP BY txo.shop_id
		) AS ton ON ton.shop_id = ms.id
		LEFT JOIN (
		SELECT
		txo1.shop_id,
		count(1) AS outOrderCount
		FROM t_xs_order txo1
		LEFT JOIN t_xs_pay_bill txpb ON txo1.order_code = txpb.bill_code AND bill_status = 2 AND refer_type = 1
		WHERE txo1.source_type=1
		<if test="shopId != null "> and txo1.shop_id = #{shopId} </if>
		<if test="orderBeginDate != null"> and txo1.delivery_end_time >= #{orderBeginDate} </if>
		<if test="orderEndDate != null"> and #{orderEndDate} >= txo1.delivery_end_time </if>
		GROUP BY txo1.shop_id
		) AS ooc ON ooc.shop_id = ms.id
		LEFT JOIN (
		SELECT
		shop_id,
		sum(real_amount) AS outRealAmount
		FROM t_xs_order_item as oi
		INNER JOIN t_xs_order as o on o.id = oi.order_id AND o.source_type=1
		WHERE o.pay_status = 2 AND o.order_status != 1
		<if test="shopId != null "> and o.shop_id = #{shopId} </if>
		<if test="orderBeginDate != null"> and o.delivery_end_time >= #{orderBeginDate} </if>
		<if test="orderEndDate != null"> and #{orderEndDate} >= o.delivery_end_time </if>
		GROUP BY o.shop_id
		) AS ooic ON ooic.shop_id = ms.id

		LEFT JOIN (
		SELECT
		count(1) AS returnNum,
		xro.shop_id
		FROM
		t_xs_return_order_item AS xroi
		LEFT JOIN t_xs_return_order AS xro ON xro.id = xroi.return_order_id AND xro.source_type=1
		INNER JOIN t_xs_user as u on u.id = xro.create_id
		WHERE
		xro.`status` = 3
		<!--
            and xro.create_id = 0
         -->
		<if test="shopId != null ">
			and xro.shop_id = #{shopId}
		</if>
		<if test="orderBeginDate != null">
			and xro.update_time >= #{orderBeginDate}
		</if>
		<if test="orderEndDate != null">
			and #{orderEndDate} >= xro.update_time
		</if>
		GROUP BY
		shop_id
		) AS ro ON ro.shop_id = ms.id
		LEFT JOIN (
		SELECT
		sum(refund_amount) AS returnAmount,
		xro.shop_id
		FROM
		t_xs_return_order AS xro
		INNER JOIN t_xs_user as u on u.id = xro.create_id
		WHERE
		xro.`status` = 3  AND xro.source_type=1
		<!--
            and xro.create_id = 0
         -->
		<if test="shopId != null ">
			and xro.shop_id = #{shopId}
		</if>
		<if test="orderBeginDate != null">
			and xro.update_time >= #{orderBeginDate}
		</if>
		<if test="orderEndDate != null">
			and #{orderEndDate} >= xro.update_time
		</if>
		GROUP BY
		shop_id
		) AS ra ON ra.shop_id = ms.id
		<where>
			<if test="shopId != null ">
				and id = #{shopId}
			</if>
		</where>
	</select>



	<sql id="appOperateReportWhere">
		<if test="shopId != null ">
			and shop_id = #{shopId}
		</if>
		<if test="orderBeginDate != null">
			and create_time >= #{orderBeginDate}
		</if>
		<if test="orderEndDate != null">
			and #{orderEndDate} >= create_time
		</if>
	</sql>
	<select id="findOrderUser" resultType="java.lang.Long">
		SELECT
		DISTINCT user_id
		FROM
		t_xs_order
		WHERE
		pay_status = 2 AND source_type=1
		<include refid="appOperateReportWhere"></include>
	</select>



	<select id="commodityOnlineReport" resultType="com.pinshang.qingyun.report.dto.shop.CommodityOnlineReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityReportIDto" >
		select
		ms.shop_name,
		(select ca.cate_name from t_category ca where ca.id = c.commodity_first_id) as cateName1,
		(select ca.cate_name from t_category ca where ca.id = c.commodity_second_id) as cateName2,
		(select ca.cate_name from t_category ca where ca.id = c.commodity_third_id) as cateName3,
		o.shop_id,
		c.id as commodity_id,
		c.commodity_code,
		c.commodity_name,
		c.commodity_spec,
		(SELECT brand_china_name FROM t_brand WHERE id = c.brand_id) AS brandName,
		sum(o.quantity) as salesQuantity,
		sum(o.quantity * o.price) as sales_amount,
		sum(o.quantity * o.weight_price) as costTotal,
		count(DISTINCT o.order_user_id) AS customerCount
		from t_day_xs_order_report o
		INNER JOIN t_commodity c on c.id = o.commodity_id
		INNER JOIN t_md_shop ms on ms.id = o.shop_id
		where 1=1 AND o.source_type=1
		<if test="shopId!=null and shopId !='' ">
			AND o.shop_id = #{shopId}
		</if>
		<if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
			<![CDATA[ and o.bill_time >= #{beginDate} and o.bill_time <= #{endDate} ]]>
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="barCode!=null and barCode !='' ">
			AND c.id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode})
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		GROUP BY o.shop_id,o.commodity_id
		order by o.shop_id,o.commodity_id
	</select>

	<select id="commodityOnlineReport2" resultType="com.pinshang.qingyun.report.dto.shop.CommodityOnlineReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityReportIDto" >
		SELECT
		txo.shop_id,
		c.id AS commodity_id,
		sum(txoi.real_quantity) as real_quantity,
		sum(txoi.real_amount) as real_amount
		FROM t_xs_order txo
		LEFT JOIN t_xs_order_item txoi ON txo.id = txoi.order_id
		LEFT JOIN t_xs_shop_commodity txsc ON (txo.shop_id = txsc.shop_id AND txoi.commodity_id = txsc.commodity_id)
		LEFT JOIN t_commodity c ON txoi.commodity_id = c.id
		where txo.source_type=1 AND txoi.real_quantity IS NOT NULL AND txo.delivery_end_time IS NOT NULL
		<if test="shopId!=null and shopId !='' ">
			AND txo.shop_id = #{shopId}
		</if>
		<if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
			<![CDATA[ and txo.delivery_end_time >= #{beginDate} and txo.delivery_end_time <= #{endDate} ]]>
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="barCode!=null and barCode !='' ">
			AND c.id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode})
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		GROUP BY  txo.shop_id, txoi.commodity_id
		ORDER BY txo.shop_id, txoi.commodity_id

	</select>

	<select id="commodityOnlineReportReturn" resultType="com.pinshang.qingyun.report.dto.shop.CommodityOnlineReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityReportIDto" >
		select
		o.shop_id,
		oi.commodity_id,
		sum(oi.commodity_number) as returnQuantity,
		sum(oi.real_amount) as returnAmount
		from t_xs_return_order o
		INNER JOIN t_xs_return_order_item oi on oi.return_order_id = o.id
		INNER JOIN t_commodity c on c.id = oi.commodity_id
		INNER JOIN t_md_shop ms on ms.id = o.shop_id
		where 1=1 AND o.source_type=1
		and o.status in (2,3)
		<if test="shopId!=null and shopId !='' ">
			AND o.shop_id = #{shopId}
		</if>
		<if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
			<![CDATA[ and o.update_time >= #{beginDate} and o.update_time <= #{endDate} ]]>
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			AND (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="barCode!=null and barCode !='' ">
			AND (c.`bar_code` like concat('%',#{barCode},'%')  )
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		GROUP BY o.shop_id,oi.commodity_id
		order by o.shop_id,oi.commodity_id
	</select>



	<select id="commodityStockReport" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityStockReportIDto" resultType="com.pinshang.qingyun.report.dto.shop.CommodityStockReportODto">
		SELECT
		tms.shop_name,
		(SELECT ca.cate_name FROM t_category ca WHERE ca.id = tc.commodity_first_id) as commodity_first_kind,
		(SELECT ca.cate_name FROM t_category ca WHERE ca.id = tc.commodity_second_id) as commodity_second_kind,
		(SELECT ca.cate_name FROM t_category ca WHERE ca.id = tc.commodity_third_id) as commodity_third_kind,
		tc.bar_code,
		bar_codes,
		tc.commodity_code,
		tc.commodity_name,
		tc.commodity_spec,
		(SELECT brand_china_name FROM t_brand WHERE id = tc.brand_id) AS brand_name,
		CASE tc.commodity_state
		WHEN 0 THEN '停用'
		WHEN 1 THEN '启用'
		END AS commodity_state,
		CASE txsc.app_status
		WHEN 0 THEN '上架'
		WHEN 1 THEN '下架'
		END AS app_status,
		txsc.weight_price,
		txsc.stock_quantity,
		txsc.weight_price * txsc.stock_quantity AS stock_price,
		txsc.month_init_stock,
		IFNULL(
		(SELECT tppml.commodity_price FROM t_product_price_model_list tppml
		WHERE tppml.commodity_id = txsc.commodity_id AND tppml.product_price_model_id =
		(SELECT tss.product_price_model_id FROM t_store_settlement tss WHERE tss.store_id = (SELECT s.store_id FROM t_md_shop s WHERE s.id = txsc.shop_id))),
		0
		) AS commodity_price,
		IFNULL(t_commodity_quantity.quantity, 0) AS currentMonthInStorageQuantity
		FROM t_xs_shop_commodity txsc
		LEFT JOIN t_md_shop tms ON txsc.shop_id = tms.id
		LEFT JOIN t_commodity tc ON txsc.commodity_id = tc.id
		LEFT JOIN ( SELECT GROUP_CONCAT(bar_code ORDER BY default_state desc) as bar_codes, commodity_id FROM t_commodity_bar_code group by commodity_id) AS cbc on cbc.commodity_id = tc.id
		LEFT JOIN (
		SELECT tmpi.commodity_id, sum(tmpi.real_receive_quantity) AS quantity
		FROM t_md_preorder tmp
		INNER JOIN t_md_preorder_item tmpi ON tmp.id = tmpi.preorder_id
		WHERE 1=1
		<if test="shopId!=null and shopId !='' "> AND tmp.store_id = (SELECT shop.store_id FROM t_md_shop shop WHERE shop.id = #{shopId}) </if>
		AND receiver_time >= #{previous30DayStart}
		AND receiver_time <![CDATA[<]]> #{currentDateTimeStart}
		AND receive_status IN (1, 2, 3)
		GROUP BY tmpi.commodity_id
		UNION ALL
		SELECT tsoi.commodity_id, sum(tsoi.quantity) AS quantity
		FROM t_md_receive_order tmro
		INNER JOIN t_sub_order tso ON tmro.sub_order_id = tso.id
		INNER JOIN t_sub_order_item tsoi ON tso.id = tsoi.sub_order_id
		INNER JOIN t_order o ON tso.order_id = o.id
		WHERE 1=1
		<if test="shopId!=null and shopId !='' "> AND o.store_id = (SELECT shop.store_id FROM t_md_shop shop WHERE shop.id = #{shopId}) </if>
		AND tmro.receive_time >= #{previous30DayStart}
		AND tmro.receive_time <![CDATA[<]]> #{currentDateTimeStart}
		GROUP BY tsoi.commodity_id
		) AS t_commodity_quantity ON txsc.commodity_id = t_commodity_quantity.commodity_id
		WHERE 1 = 1
		<if test="shopId!=null">
			AND txsc.shop_id = #{shopId}
		</if>
		<if test="commodityState != null">
			AND tc.commodity_state = #{commodityState}
		</if>
		<if test="appStatus !=null">
			AND txsc.app_status = #{appStatus}
		</if>
		<if test="cateId1 != null">
			AND tc.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND tc.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND tc.commodity_third_id = #{cateId3}
		</if>
		<if test="codeNameAid!=null and codeNameAid !='' ">
			AND (tc.`commodity_code` LIKE concat('%',#{codeNameAid},'%')
			OR tc.`commodity_name` LIKE concat('%',#{codeNameAid},'%')
			OR tc.`commodity_aid` LIKE concat('%',#{codeNameAid},'%'))
		</if>
		<if test="barCode != null and barCode != ''">
			AND tc.id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{barCode})
		</if>
	</select>
	<select id="cateUserReport" resultType="com.pinshang.qingyun.report.dto.shop.CateUserReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.CateUserReportIDto" >
		SELECT
		tt.shop_name shopName,
		tt.cate_name cateName,
		count(DISTINCT tt.user_id) userCount,
		ifnull(sum(tt.quantity), 0) saleCount,
		ifnull(sum(tt.total), 0) saleAmount
		FROM (
		SELECT
		tso.order_code,
		tso.shop_id,
		tms.id shopId,
		tms.shop_name,
		tso.order_user_id user_id,
		tso.receive_time,
		tso.commodity_id,
		tso.quantity,
		tso.price * tso.quantity AS total,
		tc.cate_name,
		tc.id
		FROM t_day_xs_order_report tso
		INNER JOIN t_md_shop tms ON tms.id = tso.shop_id
		INNER JOIN t_commodity tcom ON tcom.id = tso.commodity_id
		<if test="groupType == 1 ">
			INNER JOIN t_category tc ON tc.id = tcom.commodity_first_id -- 当是1类这样
		</if>
		<if test="groupType == 2 ">
			INNER JOIN t_category tc ON tc.id = tcom.commodity_second_id -- 当是2类这样
		</if>
		where tso.pay_status = 2 AND tso.order_status != 1 AND tso.source_type=1
		<if test="shopId!=null and shopId !='' "> AND tso.shop_id = #{shopId} </if>
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and tso.bill_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="cateId!=null and cateId !='' "> AND tc.id = #{cateId} </if>
		) tt
		GROUP BY
		tt.shopId,tt.id

	</select>

	<select id="commodityDetailReport" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityDetailReportIDto"
			resultType="com.pinshang.qingyun.report.dto.shop.CommodityDetailReportODto">
		SELECT
		CASE sc.app_status
		WHEN 0 THEN '上架'
		WHEN 1 THEN '下架'
		END AS app_status,
		sc.stock_quantity,
		sc.weight_price,
		(SELECT cate.cate_name FROM t_category cate WHERE cate.id = c.commodity_first_id) AS commodity_first_kind,
		(SELECT cate.cate_name FROM t_category cate WHERE cate.id = c.commodity_second_id) AS commodity_second_kind,
		(SELECT cate.cate_name FROM t_category cate WHERE cate.id = c.commodity_third_id) AS commodity_third_kind,
		c.id as commodity_id,
		c.bar_code,
		bar_codes,
		c.commodity_code,
		c.commodity_name,
		c.commodity_spec,
		c.commodity_sub_name,
		CASE c.commodity_state
		WHEN 0 THEN '停用'
		WHEN 1 THEN '启用'
		END AS commodity_state,
		(SELECT option_name FROM t_dictionary WHERE id = c.commodity_package_id) AS commodity_package_kind,
		CASE c.is_weight
		WHEN 0 THEN '不称重'
		WHEN 1 THEN '称重'
		END AS is_weight,
		c.sell_weight,
		(SELECT option_name FROM t_dictionary WHERE id = c.commodity_unit_id) AS commodity_unit,
		(SELECT option_name FROM t_dictionary WHERE id = c.tax_rate_id) AS tax_rate,
		CASE c.commodity_is_instant
		WHEN 0 THEN '否'
		WHEN 1 THEN '是'
		END AS commodity_is_instant,
		c.storage_condition,
		c.quality_days,
		c.origin,
		CASE c.logistics_model
		WHEN 0 THEN '直送'
		WHEN 1 THEN '配送'
		WHEN 2 THEN '直通'
		END AS logistics_model,
		c.sales_box_capacity,
		c.box_capacity,
		IF(c.default_pic_url IS NOT NULL, '有', '无') AS default_pic_url,
		IF(c.img_text_pic_url IS NOT NULL, '有', '无') AS img_text_pic_url,
		CASE
		WHEN c.pic_url1 IS NOT NULL AND c.pic_url2 IS NOT NULL AND c.pic_url3 IS NOT NULL THEN 3
		WHEN (c.pic_url1 IS NOT NULL AND c.pic_url2 IS NOT NULL) || (c.pic_url1 IS NOT NULL AND c.pic_url3 IS NOT NULL) || (c.pic_url2 IS NOT NULL AND c.pic_url3 IS NOT NULL) THEN 2
		WHEN c.pic_url1 IS NOT NULL || c.pic_url2 IS NOT NULL || c.pic_url3 IS NOT NULL THEN 1
		ELSE 0
		END AS picQuantity,
		ms.shop_name,
		ms.id as shop_id,
		(SELECT brand_china_name FROM t_brand WHERE id = c.brand_id) AS brand_name,
		(SELECT tcp.base_price FROM t_commodity_price tcp WHERE tcp.commodity_id = sc.commodity_id ORDER BY valid_time DESC LIMIT 1) as base_price,
		xpl.commodity_price,
		(SELECT tppml.commodity_price FROM t_product_price_model_list tppml
		WHERE tppml.commodity_id = sc.commodity_id AND tppml.product_price_model_id =
		(
		SELECT tppm.id FROM t_product_price_model tppm WHERE tppm.price_model_state = 1
		AND tppm.id = (SELECT tss.product_price_model_id FROM t_store_settlement tss WHERE tss.store_id = ms.store_id)
		)
		) AS purchasePrice
		FROM t_xs_shop_commodity sc
		INNER JOIN t_md_shop ms ON sc.shop_id = ms.id
		INNER JOIN t_commodity c ON sc.commodity_id = c.id
		left join ( SELECT GROUP_CONCAT(bar_code ORDER BY default_state desc) as bar_codes, commodity_id FROM t_commodity_bar_code group by commodity_id) AS cbc on cbc.commodity_id = c.id
		LEFT JOIN (select xps.refer_id as shop_id,xpl.commodity_id,xpl.commodity_price from t_xs_promotion xp
		LEFT JOIN t_xs_promotion_list xpl on xpl.promotion_id = xp.id
		LEFT JOIN t_xs_promotion_scope xps ON xps.promotion_id = xp.id
		where xp.`status` = 1
		AND SYSDATE() BETWEEN xp.start_time AND xp.end_time
		AND xps.scope_type = 1
		) xpl
		ON xpl.shop_id = sc.shop_id and xpl.commodity_id = sc.commodity_id
		WHERE sc.enterprise_id = #{enterpriseId}
		<if test="shopId != null"> AND sc.shop_id = #{shopId} </if>
		<choose>
			<when test="cateId1 != null"> AND c.commodity_first_id = #{cateId1} </when>
			<when test="cateId2 != null"> AND c.commodity_second_id = #{cateId2} </when>
			<when test="cateId3 != null"> AND c.commodity_third_id = #{cateId3} </when>
		</choose>

		<if test="isWeight != null">
			AND c.is_weight = #{isWeight}
		</if>
		<if test="appStatus != null">
			AND sc.app_status = #{appStatus}
		</if>
		<if test="barCode != null and barCode != ''">
			AND c.id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{barCode})
		</if>
		<if test="codeNameAid != null and codeNameAid != ''">
			AND (c.commodity_code = #{codeNameAid}
			OR c.commodity_name LIKE '%${codeNameAid}%'
			OR c.commodity_aid = #{codeNameAid} )
		</if>
	</select>

	<select id="commodityDetailReport2" parameterType="com.pinshang.qingyun.report.dto.shop.CommodityDetailReportIDto"
			resultType="com.pinshang.qingyun.report.dto.shop.CommodityDetailReportODto">
		select
		o.shop_id,
		oi.commodity_id,
		<![CDATA[
			sum((case when p.create_time>= #{previous7DayStart} and p.create_time <= #{currentDateTimeStart} then oi.quantity else 0 end)) as previous7DayQuantity,
			sum((case when p.create_time>= #{previous30DayStart} and p.create_time <= #{currentDateTimeStart} then oi.quantity else 0 end)) as previous30DayQuantity,
			sum((case when p.create_time>= #{newYearStart} and p.create_time <= #{currentDateTimeStart} then oi.quantity else 0 end)) as yearToTodayQuantity
			]]>
		from t_xs_order o
		LEFT JOIN t_xs_pay_bill p on p.bill_code = o.order_code and p.refer_type = 1 and p.bill_status = 2
		LEFT JOIN t_xs_order_item oi on oi.order_id = o.id
		LEFT JOIN t_commodity c ON oi.commodity_id = c.id
		<if test="appStatus != null">
			LEFT JOIN t_xs_shop_commodity sc ON sc.shop_id = o.shop_id and sc.commodity_id = oi.commodity_id
		</if>
		WHERE o.source_type = 1 and o.pay_status = 2 AND o.enterprise_id = #{enterpriseId}
		<if test="shopId != null"> AND o.shop_id = #{shopId} </if>
		<choose>
			<when test="cateId1 != null"> AND c.commodity_first_id = #{cateId1} </when>
			<when test="cateId2 != null"> AND c.commodity_second_id = #{cateId2} </when>
			<when test="cateId3 != null"> AND c.commodity_third_id = #{cateId3} </when>
		</choose>

		<if test="isWeight != null">
			AND c.is_weight = #{isWeight}
		</if>
		<if test="appStatus != null">
			AND sc.app_status = #{appStatus}
		</if>
		<if test="barCode != null and barCode != ''">
			AND c.id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{barCode})
		</if>
		<if test="codeNameAid != null and codeNameAid != ''">
			AND (c.commodity_code = #{codeNameAid}
			OR c.commodity_name LIKE '%${codeNameAid}%'
			OR c.commodity_aid = #{codeNameAid} )
		</if>
		group by o.shop_id, oi.commodity_id
	</select>
	<select id="getAllShopNameList" resultType="com.pinshang.qingyun.report.dto.pos.ShopODTO">
		select id,shop_short_name as shop_name,shop_area,longitude,latitude,shop_status,shop_code
		from t_md_shop
	</select>

    <sql id="orderSalesAnalysisReportSql">
	  SELECT tt.* from (
         SELECT
			sum(cft.order_total) AS order_total,
			subcft.all_order_total,
			sum(cft.offline_sales) AS offline_sales,
			subcft.all_offline_sales,
			md.shop_name,
			cate.cate_name,
			sum(cft.sale_return_order_total) as sale_return_order_total,
		    md.id shop_id
        FROM
        <if test="summaryType == 1">
            t_md_commodity_first_tax AS cft
        </if>
        <if test="summaryType == 2">
            t_md_commodity_second_tax AS cft
        </if>
        LEFT JOIN (
			SELECT
				sum(order_total) AS all_order_total,
				sum(offline_sales) AS all_offline_sales,
				shop_id AS sub_shop_id
			FROM
			<if test="summaryType == 1">
				t_md_commodity_first_tax AS cft
			</if>
			<if test="summaryType == 2">
				t_md_commodity_second_tax AS cft
			</if>
			WHERE
			date_time >= #{beginDate}
			<![CDATA[
				AND date_time <= #{endDate}
				]]>
			<if test="shopId != null">
				and  cft.shop_id = #{shopId}
			</if>
			GROUP BY shop_id
        ) AS subcft ON subcft.sub_shop_id = cft.shop_id
        left join t_md_shop as md on cft.shop_id = md.id
        left join t_category as cate on cate.id =
        <if test="summaryType == 1">
            cft.commodity_first_id
        </if>
        <if test="summaryType == 2">
            cft.commodity_second_id
        </if>
        WHERE
        cft.date_time >= #{beginDate}
        <![CDATA[
		AND cft.date_time <= #{endDate}
		]]>
		<if test="shopId != null">
			and  cft.shop_id = #{shopId}
		</if>
        GROUP BY
        shop_id,
        <if test="summaryType == 1">
            commodity_first_id
        </if>
        <if test="summaryType == 2">
            commodity_second_id
        </if>
        HAVING (sum(cft.offline_sales) != 0 or sum(cft.order_total) != 0 or sum(cft.sale_return_order_total) != 0)
        ORDER BY
        md.shop_code
	 ) tt WHERE 1=1
		<if test="shopId == null">
			and tt.shop_id IN
			<foreach collection="shopIds" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

    </sql>
	<select id="orderSalesAnalysisReport"
            resultType="com.pinshang.qingyun.report.dto.shop.OrderSalesAnalysisODTO">
		<include refid="orderSalesAnalysisReportSql"></include>
	</select>

	<select id="orderSalesAnalysisFirstCateReport"
			resultType="com.pinshang.qingyun.report.dto.shop.OrderSalesAnalysisODTO">
		<include refid="orderSalesAnalysisReportSql"></include>
	</select>

	<select id="orderSalesAnalysisSecondCateReport"
			resultType="com.pinshang.qingyun.report.dto.shop.OrderSalesAnalysisODTO">
		<include refid="orderSalesAnalysisReportSql"></include>
	</select>

    <select id="orderSalesAnalysisReportSum"
            resultType="com.pinshang.qingyun.report.dto.shop.OrderSalesAnalysisODTO">
        select sum(s.order_total) as order_total,sum(s.offline_sales) as offline_sales,sum(s.sale_return_order_total) as sale_return_order_total
          from (<include refid="orderSalesAnalysisReportSql"></include>) as s
    </select>
	<select id="cateAverageAmountReport"
			resultType="com.pinshang.qingyun.report.dto.shop.CateAverageAmountODTO">
		<include refid="cateAverageAmountSql"></include>
	</select>

	<!-- 类别客单周同比 - 大类 -->
	<select id="cateAverageAmountFirstReport"
			resultType="com.pinshang.qingyun.report.dto.shop.CateAverageAmountBiODTO">
	</select>
	<!-- 类别客单周同比 - 中类 -->
	<select id="cateAverageAmountSecondReport"
			resultType="com.pinshang.qingyun.report.dto.shop.CateAverageAmountBiODTO">
	</select>
	<!-- 类别客单周同比 - sum -->
	<select id="cateAverageAmountSum" resultType="com.pinshang.qingyun.report.dto.shop.CateAverageAmountODTO">
		select sum(s.offline_visitor_number) as offline_visitor_number,
				sum(s.offline_sales) as offline_average_amount,
				sum(s.lastweek_offline_visitor_number) as lastweek_offline_visitor_number,
				sum(s.lastweek_offline_sales) as lastweek_offline_average_amount
			from (<include refid="cateAverageAmountSql"></include>) as s

	</select>

	<sql id="cateAverageAmountSql">
		SELECT
			ms.shop_name,
			c.cate_name,
			offline_visitor_number,
			offline_average_amount,
			offline_sales,
			lastweek.lastweek_offline_visitor_number,
			lastweek.lastweek_offline_average_amount,
			lastweek.lastweek_offline_sales
		FROM
			t_category AS c
		LEFT JOIN (
			SELECT
				offline_visitor_number,
				offline_average_amount,
				offline_sales,
				shop_id,
			<if test="summaryType == 1">
				commodity_first_id
			</if>
			<if test="summaryType == 2">
				commodity_second_id
			</if>
			FROM
			<if test="summaryType == 1">
				t_md_commodity_first_tax
			</if>
			<if test="summaryType == 2">
				t_md_commodity_second_tax
			</if>
			WHERE
				date_time = #{date}
			AND shop_id IN
			<foreach collection="shopIds" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			<if test="summaryType == 1">
				group by shop_id,commodity_first_id
			</if>
			<if test="summaryType == 2">
				group by shop_id,commodity_second_id
			</if>
		) AS cft ON cft.
			<if test="summaryType == 1">
				commodity_first_id
			</if>
			<if test="summaryType == 2">
				commodity_second_id
			</if>
		= c.id
		LEFT JOIN (
			SELECT
				offline_visitor_number AS lastweek_offline_visitor_number,
				offline_average_amount AS lastweek_offline_average_amount,
				offline_sales as lastweek_offline_sales,
				shop_id,
			<if test="summaryType == 1">
				commodity_first_id
			</if>
			<if test="summaryType == 2">
				commodity_second_id
			</if>
			FROM
			<if test="summaryType == 1">
				t_md_commodity_first_tax
			</if>
			<if test="summaryType == 2">
				t_md_commodity_second_tax
			</if>
			WHERE
				date_time = #{lastweek}
			AND shop_id IN
			<foreach collection="shopIds" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			<if test="summaryType == 1">
				group by shop_id,commodity_first_id
			</if>
			<if test="summaryType == 2">
				group by shop_id,commodity_second_id
			</if>
		) AS lastweek ON lastweek.
		<if test="summaryType == 1">
			commodity_first_id
		</if>
		<if test="summaryType == 2">
			commodity_second_id
		</if>
		= c.id
		LEFT JOIN t_md_shop AS ms ON (
			ms.id = cft.shop_id
			OR ms.id = lastweek.shop_id
		)
		WHERE
		 (
			offline_visitor_number > 0
			OR lastweek_offline_visitor_number > 0
			OR offline_average_amount > 0
			OR lastweek_offline_average_amount > 0
		)
		AND (
			offline_visitor_number IS NOT NULL
			OR lastweek_offline_visitor_number IS NOT NULL
		)
		ORDER BY
			ms.shop_code
	</sql>



	<sql id="realDeliveryReportWhere">

		<if test="vo.commodityKey != null and vo.commodityKey !='' ">
			AND (t.`commodity_code` like concat('%',#{vo.commodityKey},'%') or t.`commodity_name` like concat('%',#{vo.commodityKey},'%')  )
		</if>
		<if test="vo.commodityId != null">
			AND t.commodity_id =  #{vo.commodityId}
		</if>
        <if test="vo.barCode != null and vo.barCode !=''">
            AND t.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
        </if>
		<if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
			and t.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>
		<if test="vo.categoryId != null">
			AND t.commodity_first_id = #{vo.categoryId}
		</if>
		<if test="vo.factoryCode != null and vo.factoryCode != ''">
			AND t.factory_code = #{vo.factoryCode}
		</if>
		<if test="vo.workshopCodeOrName != null and vo.workshopCodeOrName != ''">
			AND (t.`workshop_code` like concat('%',#{vo.workshopCodeOrName},'%') or t.`workshop_name` like concat('%',#{vo.workshopCodeOrName},'%')  )
		</if>
		<if test="vo.flowshopCodeOrName != null and vo.flowshopCodeOrName !=''">
			AND (t.`flowshop_code` like concat('%',#{vo.flowshopCodeOrName},'%') or t.`flowshop_name` like concat('%',#{vo.flowshopCodeOrName},'%')  )
		</if>
		<if test="vo.differ != null and vo.differ == true">
			and (t.real_delivery_quantity is null or t.quantity != t.real_delivery_quantity)
		</if>
	</sql>

	<select id="realDeliveryReport" resultType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
			t.commodity_id,
			t.commodity_code,
			t.commodity_name,
			t.commodity_spec,
			t.commodity_first_name,
			t.commodity_unit_name,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum,
			t.factory_name,
			t.workshop_name,
			t.flowshop_name
		FROM
		  t_real_delivery_report_day t
		where 1=1
		<include refid="realDeliveryReportWhere"></include>
		GROUP BY t.commodity_id
		ORDER  BY  t.factory_name,t.flowshop_name,t.workshop_name,t.commodity_code
	</select>

	<select id="realTotalDeliveryReport" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
		   sum(IFNULL(t.real_delivery_amount,0))
		FROM
		  t_real_delivery_report_day t
		where 1=1
		<include refid="realDeliveryReportWhere"></include>
	</select>


	<select id="thirdRealDeliveryReport" resultType="com.pinshang.qingyun.report.dto.shop.ThirdRealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
		    t.shop_id,
			t.store_code storeCode,
			t.shop_name shopName,
			<if test="vo.groupCate != null and vo.groupCate == 1">
				t.commodity_first_name commodityFirstName,
			</if>
			<if test="vo.groupCate != null and vo.groupCate == 2">
				t.commodity_first_name commodityFirstName,
		        t.commodity_second_name commoditySecondName,
			</if>
			<if test="vo.groupCate != null and vo.groupCate == 3">
				t.commodity_first_name commodityFirstName,
				t.commodity_second_name commoditySecondName,
		        t.commodity_third_name  commodityThirdName,
			</if>
			SUM(t.order_amount) orderAmount,
			SUM(t.real_amount) realAmount,
			SUM(t.return_amount) returnAmount,
		    (CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
		FROM
		   t_day_third_report t
		INNER JOIN t_md_shop ms on ms.id = t.shop_id
		WHERE 1 = 1
		<if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
			and t.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>
		<if test="vo.cate1 != null">
			AND t.commodity_first_id = #{vo.cate1}
		</if>
		<if test="vo.cate2 != null">
			AND t.commodity_second_id = #{vo.cate2}
		</if>
		<if test="vo.cate3 != null">
			AND t.commodity_third_id = #{vo.cate3}
		</if>
		<if test="vo.shopId != null">
			AND t.shop_id = #{vo.shopId}
		</if>
		<if test = "vo.shopIdList != null and vo.shopIdList.size > 0">
			AND t.shop_id in
			<foreach collection="vo.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
				#{shopId}
			</foreach>
		</if>
		<if test="vo.storeId != null">
			AND t.store_id = #{vo.storeId}
		</if>
<!--		<if test = "vo.managementMode != null and vo.managementMode != '' ">-->
<!--            AND ms.management_mode = #{vo.managementMode}-->
<!--		</if>-->
		<if test = "vo.shopType != null and vo.shopType != '' ">
		    AND ms.shop_type = #{vo.shopType}
		</if>
		<if test="vo.groupCate != null and vo.groupCate == 1">
			GROUP BY t.shop_id,t.commodity_first_id
		</if>
		<if test="vo.groupCate != null and vo.groupCate == 2">
			GROUP BY t.shop_id,t.commodity_second_id
		</if>
		<if test="vo.groupCate != null and vo.groupCate == 3">
			GROUP BY t.shop_id,t.commodity_third_id
		</if>
		ORDER BY t.shop_id
	</select>

	<sql id="thirdRealDeliveryReportSql">
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and t.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="cate1 != null">
			AND t.commodity_first_id = #{cate1}
		</if>
		<if test="cate2 != null">
			AND t.commodity_second_id = #{cate2}
		</if>
		<if test="cate3 != null">
			AND t.commodity_third_id = #{cate3}
		</if>
		<if test="shopId != null">
			AND t.shop_id = #{shopId}
		</if>
		<if test = "shopIdList != null and shopIdList.size > 0">
			AND t.shop_id in
			<foreach collection="shopIdList" index="index" item="shopId" open="(" separator="," close=")">
				#{shopId}
			</foreach>
		</if>
		<if test="storeId != null">
			AND t.store_id = #{storeId}
		</if>

		<if test = "shopType != null and shopType != '' ">
			AND ms.shop_type = #{shopType}
		</if>
	</sql>
	<select id="thirdRealDeliveryFirstReport" resultType="com.pinshang.qingyun.report.dto.shop.ThirdRealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
			t.shop_id,
			t.store_code storeCode,
			t.shop_name shopName,

			t.commodity_first_name commodityFirstName,

			SUM(t.order_amount) orderAmount,
			SUM(t.real_amount) realAmount,
			SUM(t.return_amount) returnAmount,
			(CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
		FROM
		t_day_third_report t
		INNER JOIN t_md_shop ms on ms.id = t.shop_id
		WHERE 1 = 1
		<include refid="thirdRealDeliveryReportSql"/>
		GROUP BY t.shop_id,t.commodity_first_id
		ORDER BY t.shop_id
	</select>
	<select id="thirdRealDeliverySecondReport" resultType="com.pinshang.qingyun.report.dto.shop.ThirdRealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
			t.shop_id,
			t.store_code storeCode,
			t.shop_name shopName,

			t.commodity_first_name commodityFirstName,
			t.commodity_second_name commoditySecondName,

			SUM(t.order_amount) orderAmount,
			SUM(t.real_amount) realAmount,
			SUM(t.return_amount) returnAmount,
			(CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
		FROM
		t_day_third_report t
		INNER JOIN t_md_shop ms on ms.id = t.shop_id
		WHERE 1 = 1
		<include refid="thirdRealDeliveryReportSql"/>
		GROUP BY t.shop_id,t.commodity_second_id
		ORDER BY t.shop_id
	</select>
	<select id="thirdRealDeliveryThirdReport" resultType="com.pinshang.qingyun.report.dto.shop.ThirdRealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
			t.shop_id,
			t.store_code storeCode,
			t.shop_name shopName,
			t.commodity_first_name commodityFirstName,
			t.commodity_second_name commoditySecondName,
			t.commodity_third_name  commodityThirdName,

			SUM(t.order_amount) orderAmount,
			SUM(t.real_amount) realAmount,
			SUM(t.return_amount) returnAmount,
			(CASE WHEN ms.management_mode=1 THEN '直营' WHEN ms.management_mode=2 THEN '外包' ELSE '' END) AS managementModeName
		FROM
		t_day_third_report t
		INNER JOIN t_md_shop ms on ms.id = t.shop_id
		WHERE 1 = 1
		<include refid="thirdRealDeliveryReportSql"/>
		GROUP BY t.shop_id,t.commodity_third_id
		ORDER BY t.shop_id
	</select>
	<select id="thirdRealDeliverySumReport" resultType="com.pinshang.qingyun.report.dto.shop.ThirdRealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
			SUM(t.order_amount) orderAmount,
			SUM(t.real_amount) realAmount,
			SUM(t.return_amount) returnAmount
		FROM
		  t_day_third_report t
		WHERE 1 = 1
		<include refid="thirdRealDeliveryReportSql"/>
	</select>

	<delete id="deleteOrderReportMonth">
		DELETE  FROM  t_day_order_report_month WHERE month_time = #{month}
	</delete>

	<insert id="insertOrderReportMonth">

        insert into t_day_order_report_month(month_time,store_id,shop_id,shop_code,shop_name,commodity_id,commodity_code,commodity_name,
					commodity_spec,commodity_first_kind_id,commodity_first_kind_name,commodity_second_kind_id,commodity_second_kind_name,commodity_third_kind_id,
					commodity_third_kind_name,quantity,real_receive_quantity,real_delivery_quantity,
					workshop_code,workshop_name,factory_code,factory_name,commodity_unit_name,create_id,create_time)

			SELECT
			   date_format(t.order_time, '%Y-%m') month_time,
			   t.store_id,
			   ms.id,
			   ms.shop_code,
			   ms.shop_name,
			   t.commodity_id,
			   c.commodity_code,
			   c.commodity_name,
			   c.commodity_spec,
			   c.commodity_first_kind_id,
			   c.commodity_first_kind_name,
			   c.commodity_second_kind_id,
			   c.commodity_second_kind_name,
			   c.commodity_third_kind_id,
			   c.commodity_third_kind_name,
			   SUM(t.quantity),
			   SUM(t.real_receive_quantity),
			   SUM(t.real_delivery_quantity),
			   t.workshop_code,
			   t.workshop_name,
			   t.factory_code,
			   t.factory_name,
			   t.commodity_unit_name,
			   1 create_id,
			   now() create_time
			   FROM
				 t_day_order_report t
			   LEFT JOIN t_md_shop ms ON ms.store_id = t.store_id
			   LEFT JOIN t_commodity c ON c.id = t.commodity_id
			   WHERE t.order_time BETWEEN #{beginTime} AND #{endTime}
			   AND t.`status` != 0  AND t.`status` != 4
			   GROUP BY t.store_id,t.commodity_id


			   UNION ALL

			   SELECT
				 date_format(t.order_time, '%Y-%m') month_time,
				 t.store_id,
				 ms.id,
				 ms.shop_code,
				 ms.shop_name,
				 t.commodity_id,
				 c.commodity_code,
			     c.commodity_name,
			     c.commodity_spec,
			     c.commodity_first_kind_id,
				 c.commodity_first_kind_name,
				 c.commodity_second_kind_id,
				 c.commodity_second_kind_name,
				 c.commodity_third_kind_id,
				 c.commodity_third_kind_name,
				 SUM(t.require_quantity),
				 SUM(t.real_receive_quantity),
				 SUM(t.real_receive_quantity),
				 t.workshop_code,
				 t.workshop_name,
				 t.factory_code,
				 t.factory_name,
				 t.commodity_unit_name,
				 1 create_id,
				 now() create_time
				FROM
				   t_day_preorder_report t
				LEFT JOIN t_md_shop ms ON ms.store_id = t.store_id
				LEFT JOIN t_commodity c ON c.id = t.commodity_id
				WHERE t.order_time BETWEEN #{beginTime} AND #{endTime}
				and t.receive_status != 0 and t.receive_status != 4
				GROUP BY t.store_id,t.commodity_id

	</insert>





	<sql id="realDeliveryStoreTypeReportWhere">
		<if test="vo.storeTypeId != null">
			AND t.store_type_id =  #{vo.storeTypeId}
		</if>
		<if test="vo.commodityKey != null and vo.commodityKey !='' ">
			AND (t.`commodity_code` like concat('%',#{vo.commodityKey},'%') or t.`commodity_name` like concat('%',#{vo.commodityKey},'%')  )
		</if>
		<if test="vo.commodityId != null">
			AND t.commodity_id =  #{vo.commodityId}
		</if>
		<if test="vo.barCode != null and vo.barCode !=''">
			AND t.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
		</if>
		<if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
			and t.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>
		<if test="vo.categoryId != null">
			AND t.commodity_first_id = #{vo.categoryId}
		</if>
		<if test="vo.factoryCode != null and vo.factoryCode != ''">
			AND t.factory_code = #{vo.factoryCode}
		</if>
		<if test="vo.workshopCodeOrName != null and vo.workshopCodeOrName != ''">
			AND (t.`workshop_code` like concat('%',#{vo.workshopCodeOrName},'%') or t.`workshop_name` like concat('%',#{vo.workshopCodeOrName},'%')  )
		</if>
		<if test="vo.flowshopCodeOrName != null and vo.flowshopCodeOrName !=''">
			AND (t.`flowshop_code` like concat('%',#{vo.flowshopCodeOrName},'%') or t.`flowshop_name` like concat('%',#{vo.flowshopCodeOrName},'%')  )
		</if>
		<if test="vo.warehouseCodeOrName != null and vo.warehouseCodeOrName !=''">
			AND (t.`warehouse_code` like concat('%',#{vo.warehouseCodeOrName},'%') or t.`warehouse_name` like concat('%',#{vo.warehouseCodeOrName},'%')  )
		</if>
	</sql>

	<select id="realDeliveryStoreTypeReport" resultType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
		    t.store_type_name storeTypeName,
			t.commodity_id,
			t.commodity_code,
			t.commodity_name,
			t.commodity_spec,
			t.commodity_first_name,
			t.commodity_unit_name,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum,
			t.factory_name,
			t.workshop_name,
			t.flowshop_name,
			t.warehouse_name
		FROM
		  t_real_delivery_storetype_report_day t
		where 1=1
		<include refid="realDeliveryStoreTypeReportWhere"></include>
		GROUP BY t.store_type_id,t.commodity_id
		ORDER  BY  t.store_type_id,t.commodity_id
	</select>

	<select id="realTotalDeliveryStoreTypeReport" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.report.dto.shop.RealDeliveryReportIDto" >
		SELECT
		 sum(IFNULL(t.real_delivery_amount,0))
		FROM
		  t_real_delivery_storetype_report_day t
		where 1=1
		<include refid="realDeliveryStoreTypeReportWhere"></include>
	</select>



	<select id="shopOrderGoodReport" resultType="com.pinshang.qingyun.report.dto.shop.ShopOrderGoodReportODto" parameterType="com.pinshang.qingyun.report.dto.shop.ShopOrderGoodReportIDto" >
		SELECT
		    t.shop_type,
		    t.org_name,
		    t.shop_code,
		    t.shop_name,

			t.commodity_id,
			t.commodity_code,
			t.commodity_name,
			t.commodity_spec,
			t.commodity_first_name,
			t.commodity_second_name,
			t.commodity_third_name,
			t.commodity_unit_name,

			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum,
			t.factory_name,
			t.workshop_name
		FROM
		  t_order_good_report_day t
		where 1=1
		<if test="vo.shopType != null ">
			AND t.shop_type = #{vo.shopType}
		</if>
		<if test="vo.shopId != null">
			AND t.shop_id =  #{vo.shopId}
		</if>
		<if test="vo.beginDate !=null and vo.endDate != '' and vo.beginDate != null and vo.endDate != '' ">
			and t.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>
		<if test="vo.cate1 != null">
			AND t.commodity_first_id =  #{vo.cate1}
		</if>
		<if test="vo.cate2 != null">
			AND t.commodity_second_id =  #{vo.cate2}
		</if>
		<if test="vo.cate3 != null">
			AND t.commodity_third_id =  #{vo.cate3}
		</if>

		<if test="vo.commodityIdList != null and vo.commodityIdList.size >0 ">
			and t.commodity_id in
			<foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="vo.barCode != null and vo.barCode !=''">
			AND t.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{vo.barCode} )
		</if>

		<if test="vo.factoryId != null ">
			AND t.factory_id = #{vo.factoryId}
		</if>

		<if test="vo.differ != null and vo.differ == true">
			and t.quantity != t.real_delivery_quantity
		</if>
		GROUP BY t.shop_id,t.commodity_id
		ORDER  BY  t.shop_code,t.commodity_code
	</select>


	<delete id="deleteDayOrderReportByCodes">
		DELETE FROM t_day_order_report
		WHERE order_code in
		<foreach collection="orderCodeList" item="orderCode" open="(" close=")" separator=",">
			#{orderCode}
		</foreach>
	</delete>

	<insert id="batchInsertDayOrderReport" parameterType="List">
		INSERT INTO t_day_order_report(order_code, order_status, order_time, store_id,
		commodity_id, logistics_model, quantity, total_price, price, real_delivery_quantity, real_receive_quantity,
		create_id, create_time, update_id, update_time, order_item_id, status,workshop_code,workshop_name,factory_code,
		factory_name,commodity_unit_name,real_name) VALUES
		<foreach collection="orderInfoList" item="report" separator="," >
			(#{report.orderCode}, #{report.orderStatus},#{report.orderTime},#{report.storeId},
			#{report.commodityId},#{report.logisticsModel}, #{report.quantity},#{report.totalPrice}, #{report.price}, #{report.realDeliveryQuantity},
			#{report.realReceiveQuantity},#{report.createId},#{report.createTime},#{report.updateId},#{report.updateTime},
			#{report.orderItemId},#{report.status}, #{report.workshopCode}, #{report.workshopName}, #{report.factoryCode},
			#{report.factoryName},#{report.commodityUnitName},#{report.realName})
		</foreach>
	</insert>


	<select id="getShortOrderTimeList" resultType="java.lang.String"  >
		SELECT DISTINCT tt.order_time from (
		    SELECT
		   		t.order_time
			FROM
			  t_day_order_report t
			WHERE order_code in
			<foreach collection="orderCodeList" item="orderCode" open="(" close=")" separator=",">
				#{orderCode}
			</foreach>
			limit 10
		)tt
	</select>
</mapper>