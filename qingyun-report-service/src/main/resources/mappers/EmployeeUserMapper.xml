<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.EmployeeUserMapper">

    <select id="getEmployeeUserByUserId" resultType="com.pinshang.qingyun.report.model.EmployeeUser">
        SELECT
            employee_id employeeId,
            employee_code employeeCode,
            employee_name employeeName,
            employee_state employeeState,
            employee_account_state employeeAccountState,
            user_id userId
        FROM t_employee_user
        WHERE user_id = #{userId}
    </select>
</mapper>