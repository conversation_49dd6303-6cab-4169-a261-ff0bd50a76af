<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.CashierDiscountReportMapper">

    <select id="cashierDiscountReport"  parameterType="com.pinshang.qingyun.report.dto.pos.CashierDiscountReportOrderIDTO" resultType="com.pinshang.qingyun.report.dto.pos.CashierDiscountReportOrderODTO">

        SELECT
            tp.sale_time,
            tp.shop_code,
            tp.shop_name,
            tp.cashier_no,
            tp.cashier_name,
            SUM(tp.real_total_amount) realTotalAmount,
            SUM(tp.total_amount) totalAmount,
            SUM(tp.discount_amount) discountAmount
        FROM
            t_pos_report_cashier_discount tp
        WHERE 1=1
        <choose>
            <when test="idto.shopId != null and idto.shopId !='' " > AND tp.shop_id = #{idto.shopId} </when>
            <otherwise>
                AND tp.shop_id IN
                <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and tp.sale_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>
        <if test="idto.cashierKey!=null and idto.cashierKey !='' ">
            AND (tp.`cashier_no` like concat('%',#{idto.cashierKey},'%') or tp.`cashier_name` like concat('%',#{idto.cashierKey},'%')  )
        </if>
        <if test="idto.isTotal ==0">
            GROUP BY tp.shop_id,tp.sale_time,tp.cashier_no
        </if>
          ORDER BY tp.sale_time
    </select>

    <delete id="deleteCashierDiscountReportByOrderCodes">
        DELETE  FROM  t_pos_report_cashier_discount WHERE order_code = #{orderCode}
    </delete>

    <delete id="deleteCashierDiscountReportByTimeRange">
        DELETE  FROM  t_pos_report_cashier_discount
        WHERE sale_time = #{saleTime} AND  create_time BETWEEN #{beginTime} AND  #{endTime}
        <if test="shopId != null and shopId != 0 ">
            AND shop_id = #{shopId}
        </if>
    </delete>




    <sql id="listHandDiscountDetailWhere">
        WHERE 1=1
        <choose>
            <when test="vo.shopId != null" > AND tp.shop_id = #{vo.shopId} </when>
            <otherwise>
                AND tp.shop_id IN
                <foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        <if test="vo.beginTime != null and vo.beginTime != '' and vo.endTime != null and vo.endTime != '' ">
            and tp.create_time BETWEEN #{vo.beginTime} and #{vo.endTime}
        </if>
        <if test="vo.employeeNumber != null and vo.employeeNumber != ''">
            AND tp.cashier_number = #{vo.employeeNumber}
        </if>
        <if test="vo.cateId1 != null ">
            AND tp.commodity_first_id = #{vo.cateId1}
        </if>
        <if test="vo.handleDiscountType != null ">
            AND tp.handle_discount_type = #{vo.handleDiscountType}
        </if>
        <if test="vo.commodityId != null ">
            AND tp.commodity_id = #{vo.commodityId}
        </if>
        <if test="vo.barCode != null and vo.barCode != ''">
            AND tp.commodity_id = (select commodity_id from t_commodity_bar_code where bar_code = #{vo.barCode} )
        </if>
        <if test = "vo.shopCode != null and vo.shopCode != ''">
            AND tp.shop_code = #{vo.shopCode}
        </if>
        <if test="vo.hourBegin != null and vo.hourBegin != ''">
            AND date_format(tp.create_time,'%k:%i') <![CDATA[ >= ]]> #{vo.hourBegin}
        </if>
        <if test="vo.hourEnd != null and vo.hourEnd != ''">
            AND date_format(tp.create_time,'%k:%i') <![CDATA[ <= ]]> #{vo.hourEnd}
        </if>
        <if test="vo.compareOrderAmount != null and vo.compareOrderAmount != ''">
            AND tp.handle_discount_amount <![CDATA[ >= ]]> #{vo.compareOrderAmount}
        </if>
    </sql>

    <select id="listHandDiscountDetailReport"  resultType="com.pinshang.qingyun.report.dto.pos.HandDiscountDetailODTO">
        SELECT
            tp.shop_id,
            tp.shop_code,
            tp.shop_name,
            tp.cashier_number,
            tp.cashier_name,
            tp.create_time cashierTime,
            tp.order_code,
            tp.commodity_first_name,
            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_spec,
            tp.commodity_unit,
            tp.retail_price,
            tp.retail_amount,
            tp.quantity,
            tp.background_discount_amount,
            tp.handle_discount_amount,
            tp.transaction_amount,
            tp.handle_retail_percent,
            tp.handle_discount_type,
            tp.op_user_code,
            tp.op_user_name
        FROM
          t_pos_report_hand_discount_detail tp
        <include refid="listHandDiscountDetailWhere"/>
        order by tp.create_time desc,tp.id
    </select>


    <select id="listHandDiscountDetailReportSum"  resultType="com.pinshang.qingyun.report.dto.pos.HandDiscountDetailODTO">
        SELECT
            sum(tp.retail_amount) retailAmount,
            sum(tp.quantity) quantity,
            sum(tp.handle_discount_amount) handleDiscountAmount,
            sum(tp.background_discount_amount) backgroundDiscountAmount,
            sum(tp.transaction_amount) transactionAmount
        FROM
          t_pos_report_hand_discount_detail tp
        <include refid="listHandDiscountDetailWhere"/>

    </select>
</mapper>