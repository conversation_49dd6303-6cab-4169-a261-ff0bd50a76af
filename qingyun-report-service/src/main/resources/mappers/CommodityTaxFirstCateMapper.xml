<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.shop.CommodityTaxFirstCateMapper">
    <insert id="batchInsertOnlineSales" parameterType="com.pinshang.qingyun.report.model.shop.CommodityFirstCateTax">
        INSERT INTO t_md_commodity_first_tax(shop_id, commodity_first_id, online_sales , total_sales, offline_sales, total_quanty, order_total, order_quanty, cost_total, sale_return_order_total, stock_adjust_amount_total, offline_visitor_number, offline_average_amount, date_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.shopId}, #{item.commodityFirstId}, #{item.onlineSales}, #{item.totalSales}, #{item.offlineSales}, #{item.totalQuanty}, #{item.orderTotal}, #{item.orderQuanty}, #{item.costTotal}, #{item.saleReturnOrderTotal}, #{item.stockAdjustAmountTotal}, #{item.offlineVisitorNumber}, #{item.offlineAverageAmount}, #{item.dateTime} )
        </foreach>
    </insert>

    <update id="batchUpdateOnlineSales" parameterType="com.pinshang.qingyun.report.model.shop.CommodityFirstCateTax">
        <foreach collection="list" item="item" separator=";">
        UPDATE t_md_commodity_first_tax SET online_sales = #{item.onlineSales}, total_sales = #{item.totalSales} WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>