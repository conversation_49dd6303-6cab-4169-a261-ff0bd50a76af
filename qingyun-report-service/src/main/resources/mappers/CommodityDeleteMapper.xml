<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.CommodityDeleteMapper">

    <select id="listCommodityByOrderCode" resultType="com.pinshang.qingyun.report.dto.pos.AIMonitorCommodityDTO">
        select cd.commodity_name,
               cd.commodity_unit,
               cd.quantity,
               cd.commodity_price as salePrice,
               c.bar_code,
               cd.commodity_price*cd.quantity as totalAmount,
               c.is_weight,
               1 as 'operateType'
        from t_pos_report_commodity_delete cd
        left join t_commodity c on cd.commodity_code = c.commodity_code
        where cd.order_code = #{orderCode}
    </select>

</mapper>