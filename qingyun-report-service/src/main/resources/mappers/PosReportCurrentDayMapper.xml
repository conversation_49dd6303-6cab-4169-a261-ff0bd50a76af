<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.PosReportCurrentDayMapper">

    <!--  商品销售汇总，商品类别汇总 11 -->
    <select id="commoditySalesSummaryReport"  parameterType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderIDTO"  resultType="com.pinshang.qingyun.report.dto.pos.SalesSummaryReportOrderODTO">
        SELECT tt.* from (
          SELECT
            tp.shop_id,
            tp.shop_code,
            tp.shop_name,
            tp.stall_id,
            <if test="idto.summaryType ==3">
                tp.commodity_first_id commodityFirstId,
                tp.commodity_first_name commodityFirstName,
            </if>
            <if test="idto.summaryType ==4">
                tp.commodity_first_id commodityFirstId,
                tp.commodity_first_name commodityFirstName,
                tp.commodity_second_id commoditySecondId,
                tp.commodity_second_name commoditySecondName,
            </if>
            <if test="idto.summaryType ==1 or idto.summaryType ==2 or idto.summaryType ==5">
                tp.commodity_first_id commodityFirstId,
                tp.commodity_first_name commodityFirstName,
                tp.commodity_second_id commoditySecondId,
                tp.commodity_second_name commoditySecondName,
                tp.commodity_third_id commodityThirdId,
                tp.commodity_third_name commodityThirdName,
            </if>
            tp.commodity_id,
            tp.commodity_code,
            tp.commodity_name,
            tp.commodity_spec,
            tp.commodity_unit,
            tp.commodity_price,
            tp.tax_rate,
            tp.sale_time,
            IFNULL(SUM(tp.sale_quantity),0) saleQuantity,
            IFNULL(SUM(tp.return_quantity),0) returnQuantity,
            IFNULL(SUM(tp.give_quantity),0) giveQuantity,
            IFNULL(SUM(tp.sale_amount),0) saleAmount,
            IFNULL(SUM(tp.weight_amount),0) weightAmount,
            IFNULL(SUM(tp.return_amount),0) returnAmount,
            IFNULL(SUM(tp.give_amount),0) giveAmount,
            IFNULL(SUM(tp.discount_amount),0) discountAmount,
            IFNULL(SUM(tp.tatal_quantity),0) tatalQuantity,
            IFNULL(SUM(tp.tatal_amount),0) tatalAmount,
            IFNULL(SUM(tp.no_tax_rate_amount),0) noTaxRateAmount,
            IFNULL(SUM(tp.tax_rate_amount),0) taxRateAmount,
            IFNULL(SUM(tp.weight_amount) / (1 + tp.tax_rate),0) noTaxWeightAmount
        FROM t_pos_report_sales_summary tp
        WHERE 1=1
        <if test="idto.shopId != null and idto.shopId !='' " >
            AND tp.shop_id = #{idto.shopId}
        </if>
        <if test="idto.shopIdList != null and idto.shopIdList.size > 0">
            AND tp.shop_id IN
            <foreach collection="idto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idto.beginDate !=null and idto.endDate != '' and idto.beginDate != null and idto.endDate != '' ">
            and tp.sale_time BETWEEN #{idto.beginDate} and #{idto.endDate}
        </if>
        <if test="idto.cateId1 != null">
            AND tp.commodity_first_id = #{idto.cateId1}
        </if>
        <if test="idto.cateId2 != null">
            AND tp.commodity_second_id = #{idto.cateId2}
        </if>
        <if test="idto.cateId3 != null">
            AND tp.commodity_third_id = #{idto.cateId3}
        </if>
        <if test="idto.commodityId != null">
            AND tp.commodity_id = #{idto.commodityId}
        </if>
        <if test="idto.commodityKey!=null and idto.commodityKey !='' ">
            AND (tp.`commodity_code` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_name` like concat('%',#{idto.commodityKey},'%') or tp.`commodity_aid` like concat('%',#{idto.commodityKey},'%') )
        </if>
        <if test="idto.barCode != null and idto.barCode !=''">
            AND tp.commodity_id =  (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{idto.barCode} )
        </if>
        <if test="idto.consignmentId != null and idto.consignmentId != ''">
            AND tp.consignment_id = #{idto.consignmentId}
        </if>
        <if test="idto.stallId != null and idto.stallId !='' " >
            AND tp.stall_id = #{idto.stallId}
        </if>
        <if test="idto.stallList != null and idto.stallList.size > 0">
            AND tp.stall_id IN
            <foreach collection="idto.stallList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="idto.summaryType ==1 and idto.queryType != 1">
            GROUP BY tp.shop_id,tp.commodity_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==1 and idto.queryType == 1">
            GROUP BY tp.stall_id,tp.commodity_id
            ORDER BY tp.stall_id
        </if>
        <if test="idto.summaryType ==2 ">
            GROUP BY tp.commodity_id
        </if>

        <if test="idto.summaryType ==3 and idto.isWithShop==1">
            GROUP BY tp.commodity_first_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==3 and idto.isWithShop==0">
            GROUP BY tp.commodity_first_id
        </if>
        <if test="idto.summaryType ==4 and idto.isWithShop==1 ">
            GROUP BY tp.commodity_second_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==4 and idto.isWithShop==0 ">
            GROUP BY tp.commodity_second_id
        </if>
        <if test="idto.summaryType ==5 and idto.isWithShop==1 ">
            GROUP BY tp.commodity_third_id,tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==5 and idto.isWithShop==0 ">
            GROUP BY tp.commodity_third_id
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==1 and idto.queryType != 1 ">
            GROUP BY tp.shop_id
            ORDER BY tatalAmount DESC
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==1 and idto.queryType == 1">
            GROUP BY tp.stall_id
            ORDER BY tatalAmount DESC
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==0 and idto.queryType != 1">
            GROUP BY tp.shop_id
            ORDER BY tp.shop_id
        </if>
        <if test="idto.summaryType ==6 and idto.isSortByAmount==0 and idto.queryType == 1">
            GROUP BY tp.stall_id
            ORDER BY tp.stall_id
        </if>
        <if test="idto.summaryType ==7 ">
            GROUP BY tp.shop_id,tp.sale_time
        </if>
        <if test = "idto.summaryType == 11">
            GROUP BY tp.shop_id, tp.stall_id
        </if>
        ) tt
    </select>


    <select id="queryFirstCateWeekSaleAmount"   resultType="java.math.BigDecimal">
        SELECT
         IFNULL(SUM(tp.tatal_amount),0)
        FROM t_pos_report_day_sales_summary tp
        WHERE tp.shop_id = #{shopId}
          and tp.sale_time BETWEEN #{beginDate} and #{endDate}
          AND tp.commodity_first_id = (SELECT t.id FROM t_category t where t.parent_id = 0 and  t.cate_name = '香烟')
    </select>

</mapper>