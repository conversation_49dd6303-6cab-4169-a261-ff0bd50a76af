<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.FixDayReportMapper">

    <select id="getThirdSummaryList"  resultType="com.pinshang.qingyun.report.model.pos.ThirdSummary">
        SELECT
            tp.id,
            tp.shop_id,
            tp.commodity_third_id,
            tp.sale_quantity,
            tp.return_quantity,
            tp.give_quantity,
            tp.sale_amount,
            tp.weight_amount,
            tp.return_amount,
            tp.give_amount,
            tp.discount_amount,
            tp.tatal_quantity,
            tp.tatal_amount,
            tp.no_tax_rate_amount,
            tp.tax_rate_amount,
            tp.no_tax_weight_amount
        FROM
          t_pos_report_third_summary tp
        WHERE  tp.sale_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.commodity_third_id = #{commodityThirdId}
    </select>
    <update id="batchUpdateThirdSummary" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_pos_report_third_summary tp
            SET tp.sale_quantity = tp.sale_quantity + #{item.saleQuantity},
                tp.return_quantity = tp.return_quantity + #{item.returnQuantity},
                tp.give_quantity = tp.give_quantity + #{item.giveQuantity},
                tp.sale_amount = tp.sale_amount + #{item.saleAmount},
                tp.weight_amount = tp.weight_amount + #{item.weightAmount},
                tp.return_amount = tp.return_amount + #{item.returnAmount},
                tp.give_amount = tp.give_amount + #{item.giveAmount},
                tp.discount_amount = tp.discount_amount + #{item.discountAmount},
                tp.tatal_quantity = tp.tatal_quantity + #{item.tatalQuantity},
                tp.tatal_amount = tp.tatal_amount + #{item.tatalAmount},
                tp.no_tax_rate_amount = tp.no_tax_rate_amount + #{item.noTaxRateAmount},
                tp.tax_rate_amount = tp.tax_rate_amount + #{item.taxRateAmount},
                tp.no_tax_weight_amount = tp.no_tax_weight_amount + #{item.noTaxWeightAmount}
            WHERE tp.id = #{item.id}
        </foreach>
    </update>


    <select id="getDaySaleSummary"  resultType="com.pinshang.qingyun.report.model.pos.DaySaleSummary">
        SELECT
            tp.id,
            tp.shop_id,
            tp.commodity_id,
            tp.sale_quantity,
            tp.return_quantity,
            tp.give_quantity,
            tp.sale_amount,
            tp.weight_amount,
            tp.return_amount,
            tp.give_amount,
            tp.discount_amount,
            tp.tatal_quantity,
            tp.tatal_amount,
            tp.no_tax_rate_amount,
            tp.tax_rate_amount
        FROM
           t_pos_report_day_sales_summary tp
        WHERE  tp.sale_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.commodity_id = #{commodityId}
    </select>
    <update id="batchUpdateDaySummary" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_pos_report_day_sales_summary tp
            SET tp.sale_quantity = tp.sale_quantity + #{item.saleQuantity},
                tp.return_quantity = tp.return_quantity + #{item.returnQuantity},
                tp.give_quantity = tp.give_quantity + #{item.giveQuantity},
                tp.sale_amount = tp.sale_amount + #{item.saleAmount},
                tp.weight_amount = tp.weight_amount + #{item.weightAmount},
                tp.return_amount = tp.return_amount + #{item.returnAmount},
                tp.give_amount = tp.give_amount + #{item.giveAmount},
                tp.discount_amount = tp.discount_amount + #{item.discountAmount},
                tp.tatal_quantity = tp.tatal_quantity + #{item.tatalQuantity},
                tp.tatal_amount = tp.tatal_amount + #{item.tatalAmount},
                tp.no_tax_rate_amount = tp.no_tax_rate_amount + #{item.noTaxRateAmount},
                tp.tax_rate_amount = tp.tax_rate_amount + #{item.taxRateAmount}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>


    <select id="getDayGuestListSummary"  resultType="com.pinshang.qingyun.report.model.pos.GuestListSummaryDay">
        SELECT
            tp.id,
            tp.shop_id,
            tp.mac_id,
            tp.guest_quantity,
            tp.guest_amount
        FROM
           t_pos_report_guest_list_summary_day tp
        WHERE  tp.sale_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.mac_id = #{macId}
    </select>
    <update id="batchUpdateDayGuestListSummary" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_pos_report_guest_list_summary_day tp
            SET tp.guest_quantity = tp.guest_quantity + #{item.guestQuantity},
                tp.guest_amount = tp.guest_amount + #{item.guestAmount}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>


    <select id="getDayCashierWater"  resultType="com.pinshang.qingyun.report.model.pos.CashierWaterDay">
        SELECT
            tp.id,
            tp.shop_id,
            tp.pay_amount
        FROM
           t_pos_report_cashier_water_day tp
        WHERE  tp.create_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.employee_number = #{employeeNumber}
         AND tp.mac_code = #{macCode}
         AND tp.sale_type = #{saleType}
         AND tp.pay_type = #{payType}
    </select>
    <update id="batchUpdateDayCashierWater" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_pos_report_cashier_water_day tp
            SET tp.pay_amount = tp.pay_amount + #{item.payAmount}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>






    <!--  商品总表 -->
    <select id="getCommodityTax"  resultType="com.pinshang.qingyun.report.model.shop.CommodityTax">
        SELECT
            tp.id,
            tp.offline_sales,
            tp.total_sales,
            tp.total_quanty,
            tp.cost_total
        FROM
           t_md_commodity_tax tp
        WHERE  tp.date_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.commodity_id = #{commodityId}
    </select>
    <select id="getCommodityThirdCateTax"  resultType="com.pinshang.qingyun.report.model.shop.CommodityThirdCateTax">
         SELECT
            tp.id,
            tp.offline_sales,
            tp.total_sales,
            tp.total_quanty,
            tp.cost_total
        FROM
           t_md_commodity_third_tax tp
        WHERE  tp.date_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.commodity_third_id = #{commodityThirdId}
    </select>
    <select id="getCommoditySecondCateTax"  resultType="com.pinshang.qingyun.report.model.shop.CommoditySecondCateTax">
         SELECT
            tp.id,
            tp.offline_sales,
            tp.total_sales,
            tp.total_quanty,
            tp.cost_total,
            tp.offline_visitor_number
        FROM
           t_md_commodity_second_tax tp
        WHERE  tp.date_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.commodity_second_id = #{commoditySecondId}
    </select>
    <select id="getCommodityFirstCateTax"  resultType="com.pinshang.qingyun.report.model.shop.CommodityFirstCateTax">
         SELECT
            tp.id,
            tp.offline_sales,
            tp.total_sales,
            tp.total_quanty,
            tp.cost_total,
            tp.offline_visitor_number
        FROM
           t_md_commodity_first_tax tp
        WHERE  tp.date_time = #{saleTime}
         and tp.shop_id = #{shopId}
         AND tp.commodity_first_id = #{commodityFirstId}
    </select>
    <select id="getCommodityShopTax"  resultType="com.pinshang.qingyun.report.model.shop.CommodityShopTax">
         SELECT
            tp.id,
            tp.offline_sales,
            tp.total_sales,
            tp.total_quanty,
            tp.cost_total,
            tp.offline_cost,
            tp.offline_visitor_number
        FROM
           t_md_commodity_shop_tax tp
        WHERE  tp.date_time = #{saleTime}
         and tp.shop_id = #{shopId}
    </select>




    <update id="batchUpdateCommodityTax" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_md_commodity_tax tp
            SET tp.offline_sales = tp.offline_sales + #{item.offlineSales},
            tp.total_sales = tp.total_sales + #{item.totalSales},
            tp.total_quanty = tp.total_quanty + #{item.totalQuanty},
            tp.cost_total = tp.cost_total + #{item.costTotal}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>
    <update id="batchUpdateCommodityThirdTax" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_md_commodity_third_tax tp
            SET tp.offline_sales = tp.offline_sales + #{item.offlineSales},
                tp.total_sales = tp.total_sales + #{item.totalSales},
                tp.total_quanty = tp.total_quanty + #{item.totalQuanty},
                tp.cost_total = tp.cost_total + #{item.costTotal}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>
    <update id="batchUpdateCommoditySecondTax" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_md_commodity_second_tax tp
            SET tp.offline_sales =  #{item.offlineSales},
            tp.total_sales = #{item.totalSales},
            tp.total_quanty =  #{item.totalQuanty},
            tp.cost_total =  #{item.costTotal},
            tp.offline_visitor_number = #{item.offlineVisitorNumber},
            tp.offline_average_amount = #{item.offlineAverageAmount}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>
    <update id="batchUpdateCommodityFirstTax" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_md_commodity_first_tax tp
            SET tp.offline_sales =  #{item.offlineSales},
            tp.total_sales =  #{item.totalSales},
            tp.total_quanty =  #{item.totalQuanty},
            tp.cost_total =  #{item.costTotal},
            tp.offline_visitor_number = #{item.offlineVisitorNumber},
            tp.offline_average_amount = #{item.offlineAverageAmount}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>
    <update id="batchUpdateCommodityShopTax" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";"
                 open="" close="">
            UPDATE t_md_commodity_shop_tax tp
            SET tp.offline_sales =  #{item.offlineSales},
            tp.total_sales =  #{item.totalSales},
            tp.total_quanty = #{item.totalQuanty},
            tp.cost_total =  #{item.costTotal},
            tp.offline_cost =  #{item.offlineCost},
            tp.offline_visitor_number = #{item.offlineVisitorNumber},
            tp.offline_average_amount = #{item.offlineAverageAmount}
            WHERE  tp.id = #{item.id}
        </foreach>
    </update>

</mapper>