<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.report.mapper.CloudCommodityReportMapper">

    <sql id="realDeliveryReportSql">
         SELECT
            t.shop_id,
            t.store_code,
            t.shop_name,
            t.commodity_factory_name,
            t.commodity_workshop_name,
            t.commodity_flowshop_name,
            t.commodity_id,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_unit_name,
            sum(t.quantity) quantity,
            sum(t.amount) amount,
            sum(t.real_amount) realAmount,
            sum(t.weight_amount) weightAmount,
            sum(t.package_quantity) packageQuantity,
            sum(t.package_amount) packageAmount,
            t.commodity_first_kind_name,
            t.commodity_second_kind_name,
            t.commodity_third_kind_name,
            t.tax_rate,
            t.to_shop_date as orderTime
          FROM
                t_cloud_commodity_real_delivery_report t
            where 1=1
            <if test="vo.orderTimeBegin != null and vo.orderTimeBegin != '' and vo.orderTimeEnd != null and vo.orderTimeEnd != '' ">
                and t.to_shop_date BETWEEN #{vo.orderTimeBegin} and #{vo.orderTimeEnd}
            </if>
            <if test="vo.deliveryBatch != null">
                AND t.delivery_batch = #{vo.deliveryBatch}
            </if>
            <if test="vo.commodityFactoryId != null">
                AND t.commodity_factory_id = #{vo.commodityFactoryId}
            </if>
            <if test="vo.commodityWorkshopId != null">
                AND t.commodity_workshop_id = #{vo.commodityWorkshopId}
            </if>
            <if test="vo.commodityFlowshopId != null">
                AND t.commodity_flowshop_id = #{vo.commodityFlowshopId}
            </if>
            <if test="vo.commodityId != null">
                AND t.commodity_id = #{vo.commodityId}
            </if>
            <if test="vo.commodityIdList != null and vo.commodityIdList.size > 0">
                AND t.commodity_id in
                <foreach collection="vo.commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                    #{commodityId}
                </foreach>
            </if>
            <if test="vo.cateId1 != null">
                AND t.commodity_first_kind_id = #{vo.cateId1}
            </if>
            <if test="vo.cateId2 != null">
                AND t.commodity_second_kind_id = #{vo.cateId2}
            </if>
            <if test="vo.cateId3 != null">
                AND t.commodity_third_kind_id = #{vo.cateId3}
            </if>
            <if test="vo.factoryIdList != null and vo.factoryIdList.size() > 0">
                AND t.commodity_factory_id IN
                <foreach collection="vo.factoryIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.shopIdList != null and vo.shopIdList.size() > 0">
                AND t.shop_id IN
                <foreach collection="vo.shopIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.shopId != null">
                AND t.shop_id = #{vo.shopId}
            </if>
            <if test="vo.isWithShop == 1">
                GROUP BY t.shop_id,t.commodity_id
            </if>
            <if test="vo.isWithShop == 0">
                GROUP BY t.commodity_id
            </if>

    </sql>

    <select id="realDeliveryReport2" resultType="com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportODTO">
        <include refid="realDeliveryReportSql"></include>
    </select>
    <select id="realDeliveryReport2Sum" resultType="com.pinshang.qingyun.report.dto.cloud.CloudCommodityReportODTO">
        select
            sum(tt.realAmount) realAmount,
            sum(tt.packageAmount) packageAmount,
            sum(tt.weightAmount) weightAmount
       from (
          <include refid="realDeliveryReportSql"></include>
        ) tt
    </select>


    <delete id="deleteCloudCommodityReportSummary">
        DELETE  FROM  t_cloud_commodity_real_delivery_report WHERE to_shop_date = #{orderTime}
    </delete>

    <insert id="batchInsertCloudCommodityReportSummary" parameterType="List">
        INSERT INTO t_cloud_commodity_real_delivery_report(shop_id, shop_code, shop_name,store_id, store_code,store_name,
        to_shop_date, order_code, order_status,delivery_batch, commodity_id,commodity_code,commodity_name,commodity_spec,
        commodity_first_kind_id, commodity_first_kind_name, commodity_second_kind_id, commodity_second_kind_name, commodity_third_kind_id, commodity_third_kind_name,
        commodity_unit_name,tax_rate, commodity_factory_id, commodity_factory_name, commodity_factory_code, commodity_workshop_id,
        commodity_workshop_name,commodity_workshop_code,commodity_flowshop_id,commodity_flowshop_name,
        quantity,amount,real_amount,weight_amount,package_quantity,package_amount,create_id,create_time) VALUES
        <foreach collection="summaryList" item="vo" separator="," >
            (#{vo.shopId}, #{vo.shopCode},#{vo.shopName},#{vo.storeId},#{vo.storeCode},#{vo.storeName},
             #{vo.toShopDate}, #{vo.orderCode},#{vo.orderStatus},#{vo.deliveryBatch},#{vo.commodityId},
             #{vo.commodityCode},#{vo.commodityName}, #{vo.commoditySpec},#{vo.commodityFirstKindId}, #{vo.commodityFirstKindName},
             #{vo.commoditySecondKindId},#{vo.commoditySecondKindName},#{vo.commodityThirdKindId},#{vo.commodityThirdKindName},
             #{vo.commodityUnitName}, #{vo.taxRate},#{vo.commodityFactoryId}, #{vo.commodityFactoryName}, #{vo.commodityFactoryCode},
             #{vo.commodityWorkshopId},#{vo.commodityWorkshopName},#{vo.commodityWorkshopCode},#{vo.commodityFlowshopId},#{vo.commodityFlowshopName},
            #{vo.quantity},#{vo.amount},#{vo.realAmount},#{vo.weightAmount},
            #{vo.packageQuantity},#{vo.packageAmount},1,now())
        </foreach>
    </insert>



    <delete id="deleteCloudCommodityInfoReport">
        DELETE  FROM  t_cloud_commodity_report WHERE to_shop_date = #{orderTime}
    </delete>

    <insert id="batchInsertCloudCommodityInfoReport" parameterType="List">
        INSERT INTO t_cloud_commodity_report(shop_id, shop_code, shop_name,store_id, store_code,store_name,
        order_code,source_type, order_status, pay_status, to_shop_date, refer_type, bill_time,
        commodity_id, commodity_code,commodity_name, commodity_spec, commodity_unit_name, tax_rate,
        commodity_first_kind_id, commodity_first_kind_name,commodity_second_kind_id,commodity_second_kind_name,commodity_third_kind_id,commodity_third_kind_name,
        quantity,amount,real_amount,weight_amount,package_quantity,package_amount,real_diff_amount,create_id,create_time) VALUES
        <foreach collection="summaryList" item="vo" separator="," >
            (#{vo.shopId}, #{vo.shopCode},#{vo.shopName},#{vo.storeId},#{vo.storeCode},#{vo.storeName},
            #{vo.orderCode},#{vo.sourceType}, #{vo.orderStatus},#{vo.payStatus}, #{vo.toShopDate},#{vo.referType},#{vo.billTime},
            #{vo.commodityId},#{vo.commodityCode},#{vo.commodityName},#{vo.commoditySpec},#{vo.commodityUnitName},#{vo.taxRate},
            #{vo.commodityFirstKindId},#{vo.commodityFirstKindName}, #{vo.commoditySecondKindId},#{vo.commoditySecondKindName}, #{vo.commodityThirdKindId}, #{vo.commodityThirdKindName},
            #{vo.quantity},#{vo.amount},#{vo.realAmount},#{vo.weightAmount},
            #{vo.packageQuantity},#{vo.packageAmount},#{vo.realDiffAmount},1,now())
        </foreach>
    </insert>




    <sql id="cloudCommodityReportSql">
        SELECT
            t.shop_id,
            t.store_code,
            t.shop_name,
            t.order_code,
            ( CASE
            WHEN t.source_type = 8 THEN '云超小程序'
            WHEN t.source_type = 9 THEN '云超APP'
            ELSE '' END
            ) AS sourceType,

            t.commodity_id,
            t.commodity_code,
            t.commodity_name,
            t.commodity_spec,
            t.commodity_unit_name,
            t.tax_rate,
            t.commodity_first_kind_name,
            t.commodity_second_kind_name,
            t.commodity_third_kind_name,

            t.to_shop_date,
            t.bill_time,

            t.quantity,
            t.amount,
            t.real_amount,
            t.weight_amount,
            t.package_quantity,
            t.package_amount,
            t.real_diff_amount
        FROM
            t_cloud_commodity_report t
        where 1=1
        <if test="vo.orderTimeBegin != null and vo.orderTimeBegin != '' and vo.orderTimeEnd != null and vo.orderTimeEnd != '' ">
            and t.bill_time BETWEEN #{vo.orderTimeBegin} and #{vo.orderTimeEnd}
        </if>
        <if test="vo.shopIdList != null and vo.shopIdList.size() > 0">
            AND t.shop_id IN
            <foreach collection="vo.shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.shopId != null">
            AND t.shop_id = #{vo.shopId}
        </if>
        <if test="vo.sourceType != null">
            AND t.source_type = #{vo.sourceType}
        </if>
        <if test="vo.commodityId != null">
            AND t.commodity_id = #{vo.commodityId}
        </if>
        <if test="vo.orderCode != null and vo.orderCode != '' ">
            AND t.order_code = #{vo.orderCode}
        </if>

        <if test="vo.cateId1 != null">
            AND t.commodity_first_kind_id = #{vo.cateId1}
        </if>
        <if test="vo.cateId2 != null">
            AND t.commodity_second_kind_id = #{vo.cateId2}
        </if>
        <if test="vo.cateId3 != null">
            AND t.commodity_third_kind_id = #{vo.cateId3}
        </if>

        <if test="vo.reportType == 1">
            AND t.pay_status = 2 and t.order_status = 0 and t.refer_type = 2
        </if>

        <if test="vo.reportType == 2">
            AND t.order_status = 6 and t.refer_type = 4 and t.real_diff_amount > 0
        </if>

        <if test="vo.reportType == 3">
            AND t.order_status = 7
        </if>
        order by t.bill_time desc,t.shop_id
    </sql>

    <select id="cloudCommodityReport" resultType="com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportODTO">
        <include refid="cloudCommodityReportSql"></include>
    </select>
    <select id="cloudCommodityReportSum" resultType="com.pinshang.qingyun.report.dto.cloud.CloudCommodityInfoReportODTO">
        select
            sum(tt.quantity) quantity,
            sum(tt.amount) amount,
            sum(tt.real_amount) realAmount,
            sum(tt.weight_amount) weightAmount,
            sum(tt.package_quantity) packageQuantity,
            sum(tt.package_amount) packageAmount,
            sum(tt.real_diff_amount) realDiffAmount
        from (
            <include refid="cloudCommodityReportSql"></include>
        ) tt
    </select>


    <select id="getCloudInReport" resultType="com.pinshang.qingyun.report.model.settle.StockInCommodity">
        SELECT
            t.shop_id,
            t.commodity_id,
            sum(t.package_quantity) cloudInQuantity,
            sum(t.weight_amount) cloudInAmount
        FROM
          t_cloud_commodity_report t
        where  t.bill_time BETWEEN  CONCAT(#{day},' 00:00:00') and CONCAT(#{day},' 23:59:59')
        and ((t.pay_status = 2 and t.order_status = 0 and t.refer_type = 2) or t.order_status = 7 )
        group by t.shop_id,t.commodity_id
    </select>
</mapper>