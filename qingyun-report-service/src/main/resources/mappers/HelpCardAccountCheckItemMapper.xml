<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.HelpCardAccountCheckItemMapper">

    <select id="selectCheckDetail4InvoiceByCompany" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceIDTO">
        SELECT
        ac.id,
        ac.`area_id`,
        ac.`area_name`,
        aci.`company_id`,
        ac.`check_date_month`,
        ac.`check_date_begin`,
        ac.`check_date_end`,
        SUM(aci.`amount`)  AS pxAmount,
        ac.check_amount AS totalAmount,
        ac.px_trader_amount AS pxTotalAmount
        FROM t_pos_help_card_account_check ac
        INNER JOIN t_pos_help_card_account_check_item aci ON aci.`check_id` = ac.`id`
        WHERE
         ac.`status` = 2
        <if test="null != checkDateMonth">
            AND ac.`check_date_month` = #{checkDateMonth}
        </if>
        <if test="null != companyId">
            AND aci.`company_id` = #{companyId}
        </if>
        <if test="null!= idList and idList.size() > 0">
            AND ac.`id` IN
            <foreach collection="idList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!= notIdList and notIdList.size() > 0">
            AND ac.`id` NOT IN
            <foreach collection="notIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!= areaIdList and areaIdList.size() > 0">
            AND ac.`area_id` IN
            <foreach collection="areaIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by aci.check_id,  aci.`company_id`
        ORDER BY aci.create_time DESC
    </select>

    <select id="selectCheckDetail4InvoiceByArea" resultType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceODTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.HelpCardCheckDetail4InvoiceIDTO">
        SELECT
        ac.id,
        ac.`area_id`,
        ac.`area_name`,
        ac.`check_date_month`,
        ac.`check_date_begin`,
        ac.`check_date_end`,
        ac.`check_amount` AS totalAmount
        FROM t_pos_help_card_account_check ac
        WHERE
        ac.`status` = 2
        <if test="null != checkDateMonth">
            AND ac.`check_date_month` = #{checkDateMonth}
        </if>
        <if test="null!= idList and idList.size() > 0">
            AND ac.`id` IN
            <foreach collection="idList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!= notIdList and notIdList.size() > 0">
            AND ac.`id` NOT IN
            <foreach collection="notIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!= areaIdList and areaIdList.size() > 0">
            AND ac.`area_id` IN
            <foreach collection="areaIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY ac.create_time DESC
    </select>

</mapper>
