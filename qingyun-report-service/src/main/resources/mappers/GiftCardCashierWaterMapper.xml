<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.GiftCardCashierWaterMapper">

    <select id="cashierWaterPage" parameterType="com.pinshang.qingyun.report.dto.pos.QueryGiftCardWaterDTO"
    resultType="com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterPage">
        SELECT a.card_source_type,a.total_amount,a.order_code,a.pay_name,
               a.mac_code,a.sale_time,a.employee_name,b.shop_type,b.shop_name,
               a.shop_id AS shopId
        FROM t_gift_card_cashier_water a
        LEFT JOIN t_md_shop b ON a.shop_id = b.id
        <include refid="cashierWaterCondition"/>
        ORDER BY a.sale_time DESC
    </select>

    <select id="cashierWaterSum" parameterType="com.pinshang.qingyun.report.dto.pos.QueryGiftCardWaterDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.GiftCardCashierWaterPage">
        SELECT sum(a.total_amount) AS totalAmount
        FROM t_gift_card_cashier_water a
        LEFT JOIN t_md_shop b ON a.shop_id = b.id
        <include refid="cashierWaterCondition"/>
    </select>

    <sql id = "cashierWaterCondition">
        <where>
            <if test="dto.type != null">
                AND a.type = #{dto.type}
            </if>
            <if test="dto.shopType != null and dto.shopType != ''">
                AND b.shop_type = #{dto.shopType}
            </if>
            <choose>
                <when test="dto.shopId != null and dto.shopId != ''">
                    AND a.shop_id = #{dto.shopId}
                </when>
                <otherwise>
                    AND a.shop_id IN
                    <foreach collection="dto.shopIdList" index="index" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
            <if test="dto.dateStart != null and dto.dateStart != '' and dto.dateEnd != null and dto.dateEnd != ''">
                AND a.sale_time BETWEEN #{dto.dateStart} AND #{dto.dateEnd}
            </if>
            <if test="dto.employeeNumber != null and dto.employeeNumber != ''">
                AND a.employee_number = #{dto.employeeNumber}
            </if>
            <if test="dto.cardSourceType != null and dto.cardSourceType != ''">
                AND a.card_source_type = #{dto.cardSourceType}
            </if>
            <if test="dto.orderCode != null and dto.orderCode != ''">
                AND a.order_code like concat('%',#{dto.orderCode},'%')
            </if>
            <if test="dto.payType != null and dto.payType != ''">
                AND a.pay_type = #{dto.payType}
            </if>
        </where>
    </sql>

</mapper>