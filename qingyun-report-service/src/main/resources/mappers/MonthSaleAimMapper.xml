<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.shop.MonthSaleAimMapper">

    <select id="countMonthSaleAimByShopSaleTime" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM  t_month_sale_aim t
        WHERE  t.shop_id = #{shopId}  AND  t.sale_time = #{saleTime}
    </select>

    <select id="listMonthSaleAimPage" resultType="com.pinshang.qingyun.report.dto.shop.MonthSaleAimODTO" parameterType="com.pinshang.qingyun.report.dto.shop.MonthSaleAimPageIDTO" >
        SELECT
        t.id,
        t.shop_id,
        s.shop_name,
        t.shop_code,
        t.sale_time,
        t.aim_sale_amount,
        t.remark
        FROM t_month_sale_aim t
        LEFT JOIN t_md_shop s on s.id = t.shop_id
        WHERE t.shop_id IN
        <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
            and t.sale_time BETWEEN #{beginDate} and #{endDate}
        </if>
        ORDER  BY t.sale_time DESC,t.shop_id
    </select>

    <select id="queryMonthKeyListRepeat" resultType="java.lang.String">
        SELECT
        CONCAT(t.shop_code,t.sale_time)
        FROM  t_month_sale_aim t
        WHERE CONCAT(t.shop_code,t.sale_time) in
        <foreach collection="keyList" item="keyl" open="(" close=")" separator=",">
            #{keyl}
        </foreach>
    </select>
</mapper>