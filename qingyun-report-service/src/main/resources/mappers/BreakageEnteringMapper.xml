<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.BreakageEnteringMapper">

    <insert id="saveBreakageEnting">
        INSERT INTO t_pos_report_breakage_entering(
               shop_id,
               commodity_id,
               cost_price,
               retail_price,
               breakage,
               breakage_num,
               monitor_location_id,
               monitor_location_name,
               reason_id,
               reason_name,
               make_date,
               expire_date,
               consignment_id,
               create_id,
               create_name,
               create_time,
               stock_area_id,
               breakage_channel,
               refer_code,
               goods_allocation_code,
               stall_id
        )
        VALUES (
                #{idto.shopId},
                #{idto.commodityId},
                #{commodity.costPrice},
                #{commodity.retailPrice},
                #{idto.breakage},
                #{idto.breakageNum},
                #{idto.monitorLocationId},
                #{idto.monitorLocationName},
                #{idto.reasonId},
                #{idto.reasonName},
                #{idto.makeDate},
                #{idto.expireDate},
                #{idto.consignmentId},
                #{idto.createId},
                #{idto.createName},
                NOW(),
                #{idto.stockAreaId},
                #{idto.breakageChannel},
                #{idto.referCode},
                #{idto.goodsAllocationCode},
                #{idto.stallId}
               )
    </insert>



    <select id="queryTotalTimesForPass24Hours" resultType="Long">
        SELECT count(*) FROM t_pos_report_breakage_entering
        WHERE shop_id = #{shopId}
        AND create_time >= DATE_SUB(now(), INTERVAL 24 HOUR)
        <if test="null != consignmentId">
            AND consignment_id = #{consignmentId}
        </if>
    </select>
    <select id="breakagedCommodityForPass24Hours" resultType="com.pinshang.qingyun.report.dto.BreakagedCommodityODTO">
        SELECT
            tc.commodity_name,
            tc.commodity_spec,
            tc.commodity_unit_name AS commodityUnit,
            tprbe.breakage_num,
            tprbe.reason_name,
            tprbe.create_name,
            tprbe.create_time AS createDate
        FROM t_pos_report_breakage_entering tprbe
        LEFT JOIN t_commodity tc ON tprbe.commodity_id = tc.id
        WHERE shop_id = #{shopId} AND tprbe.create_time >= DATE_SUB(now(), INTERVAL 24 HOUR)
        AND tprbe.consignment_id = #{consignmentId}
        ORDER BY tprbe.create_time DESC
    </select>

    <select id="breakageEnteringList" resultType="com.pinshang.qingyun.report.dto.BreakageEnteringODTO">
        SELECT
            tms.shop_name,
            tc.commodity_code,
            tc.bar_code,
            concat(tc.commodity_name, ', ', tc.commodity_spec) AS commodityNameAndSpec,
            tc.commodity_unit_name,
            tprbe.stall_id,
            tprbe.goods_allocation_code,
            tprbe.stock_area_id,
            tprbe.breakage AS breakage,
            tprbe.breakage_num,
            CONVERT(tprbe.retail_price, DECIMAL(10, 2)) AS retail_price,
            CONVERT(tprbe.breakage_num * tprbe.retail_price, DECIMAL(10, 2)) AS breakageXretailPrice,
            CONVERT(tprbe.cost_price, DECIMAL(10, 2)) AS cost_price,
            CONVERT(tprbe.breakage_num * tprbe.cost_price, DECIMAL(10, 2)) AS breakageXcostPrice,
            tprbe.create_name,
            DATE_FORMAT(tprbe.create_time,'%Y-%m-%d %H:%i:%s') AS create_time,
            tprbe.monitor_location_name,
            tprbe.reason_name,
            DATE_FORMAT(tprbe.make_date,'%Y-%m-%d') AS make_date,
            DATE_FORMAT(tprbe.expire_date,'%Y-%m-%d') AS expire_date,
            CASE tprbe.breakage_channel
            WHEN 1 THEN '掌上门店PDA'
            WHEN 2 THEN '餐饮POS'
            ELSE '掌上门店PDA'
            END
            AS breakageChannelName,
            tprbe.refer_code AS referCode
        FROM t_pos_report_breakage_entering tprbe
        LEFT JOIN t_md_shop tms ON tprbe.shop_id = tms.id
        LEFT JOIN t_commodity tc ON tprbe.commodity_id = tc.id
        WHERE <include refid="breakageEnteringListCondition"/>
        ORDER BY tprbe.create_time DESC
    </select>

    <select id="breakageEnteringListSum" resultType="com.pinshang.qingyun.report.dto.BreakageEnteringODTO">
        select
            sum(temp.breakageXretailPrice) as breakageXretailPrice,
            sum(temp.breakageXcostPrice) as breakageXcostPrice
        from (
            SELECT
                CONVERT(tprbe.breakage_num * tprbe.retail_price, DECIMAL(10, 2)) AS breakageXretailPrice,
                CONVERT(tprbe.breakage_num * tprbe.cost_price, DECIMAL(10, 2)) AS breakageXcostPrice
            FROM t_pos_report_breakage_entering tprbe
            LEFT JOIN t_commodity tc ON tprbe.commodity_id = tc.id
            WHERE <include refid="breakageEnteringListCondition"/>
        ) temp
    </select>

    <sql id="breakageEnteringListCondition">
        1=1
        <if test="idto.shopId != null"> AND tprbe.shop_id = #{idto.shopId} </if>
        <if test="idto.beginTime != null and idto.beginTime != ''"> AND tprbe.create_time >= #{idto.beginTime} </if>
        <if test="idto.endTime != null and idto.endTime != ''"> AND tprbe.create_time <![CDATA[ <= ]]> #{idto.endTime} </if>
        <if test="idto.commodityName != null and idto.commodityName != ''"> AND tc.commodity_name LIKE concat('%', #{idto.commodityName}, '%') </if>
        <if test="idto.reasonId != null and idto.reasonId != ''"> AND tprbe.reason_id = #{idto.reasonId} </if>
        <if test="idto.category1 != null and idto.category1 != ''"> AND tc.commodity_first_kind_id = #{idto.category1} </if>
        <if test="idto.category2 != null and idto.category2 != ''"> AND tc.commodity_second_kind_id = #{idto.category2} </if>
        <if test="idto.category3 != null and idto.category3 != ''"> AND tc.commodity_third_kind_id = #{idto.category3} </if>
        <if test="idto.breakageChannel != null and idto.breakageChannel ==1 "> AND (tprbe.breakage_channel = #{idto.breakageChannel} OR tprbe.breakage_channel IS NULL)</if>
        <if test="idto.breakageChannel != null and idto.breakageChannel ==2 "> AND tprbe.breakage_channel = #{idto.breakageChannel} </if>
        <if test="idto.referCode != null and idto.referCode != ''"> AND tprbe.refer_code like concat('%',#{idto.referCode}, '%') </if>
        <if test="idto.consignmentId != null and idto.consignmentId != ''"> AND tprbe.consignment_id = #{idto.consignmentId}</if>
        <if test="idto.stallId != null and idto.stallId != ''"> AND tprbe.stall_id = #{idto.stallId}</if>
        <if test="idto.stallIdList != null and idto.stallIdList.size > 0">
            AND tprbe.stall_id IN
            <foreach collection="idto.stallIdList" index="index" item="stallId" open="(" separator="," close=")">
                #{stallId}
            </foreach>
        </if>
    </sql>


    <select id="breakageEnteringDayList" resultType="com.pinshang.qingyun.report.dto.BreakageEnteringDayODTO">
        SELECT
            t.shop_code,
            t.shop_name,
            sum(t.breakage_cost_price) breakageCostPrice
        FROM t_pos_report_breakage_entering_day t
        <include refid="breakageEnteringListDayCondition"/>
        group by t.shop_id
        order by breakageCostPrice desc
    </select>


    <select id="breakageEnteringListDaySum" resultType="com.pinshang.qingyun.report.dto.BreakageEnteringDayODTO">
        select
          sum(t.breakage_cost_price) as breakageCostPrice
        from t_pos_report_breakage_entering_day t
        <include refid="breakageEnteringListDayCondition"/>
    </select>

    <sql id="breakageEnteringListDayCondition">
        where 1=1
        <if test="idto.beginTime != null and idto.beginTime != '' and idto.endTime != null and idto.endTime != ''">
          AND t.breakage_date between  #{idto.beginTime}  and #{idto.endTime}
        </if>
    </sql>

    <delete id="deleteBreakageEnteringDay">
        DELETE  FROM  t_pos_report_breakage_entering_day WHERE breakage_date = #{dateTime}
    </delete>

    <insert id="insertBreakageEnteringDay">
        Insert into t_pos_report_breakage_entering_day(shop_id,shop_code,shop_name,
                                                       breakage_cost_price,breakage_date,create_id,create_time)
        SELECT
            t.shop_id,
            md.shop_code,
            md.shop_name,
            sum(CONVERT(t.breakage_num * t.cost_price, DECIMAL(10, 2))) AS breakageCostPrice,
            DATE_FORMAT(t.create_time,'%Y-%m-%d'),
            1,NOW()
        from t_pos_report_breakage_entering t
                 LEFT JOIN t_md_shop md on md.id = t.shop_id
        where 1=1
          and t.create_time BETWEEN #{beginDateTime} and #{endDateTime}
        GROUP BY t.shop_id
    </insert>

    <select id="selectBreakageRecordList" resultType="com.pinshang.qingyun.report.dto.finance.ShopBreakageRecordODTO">
        SELECT
            e.id AS businessCode,
            e.id AS businessOrderItemId,
            e.shop_id,
            e.stall_id,
            e.commodity_id,
            e.breakage_num AS quantity,
            DATE_FORMAT(e.create_time,'%Y-%m-%d') AS businessDate,
            e.create_time AS businessTime,
            md.store_id
        FROM
            t_pos_report_breakage_entering e
            LEFT JOIN t_md_shop md ON e.shop_id = md.id
        <where>
            e.create_time BETWEEN STR_TO_DATE(CONCAT(#{vo.businessDate},' ','00:00:00'),'%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(CONCAT(#{vo.businessDate},' ','23:59:59'),'%Y-%m-%d %H:%i:%s')
        <if test="vo.businessOrderItemId!=null">
            AND e.id > #{vo.businessOrderItemId}
        </if>
            AND md.management_mode = 3
        </where>
        ORDER BY e.id ASC
        LIMIT #{vo.limitQuantity}
    </select>
</mapper>