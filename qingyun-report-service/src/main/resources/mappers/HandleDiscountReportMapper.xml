<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.HandleDiscountMapper">

    <delete id="deleteHandleDiscountReportByOrderCodes">
        DELETE  FROM  t_pos_report_hand_discount_detail WHERE order_code = #{orderCode}
    </delete>

    <delete id="deleteHandleDiscountReportByTimeRange">
        DELETE  FROM  t_pos_report_hand_discount_detail
        WHERE  create_time BETWEEN #{beginTime} AND  #{endTime}
        <if test="shopId != null and shopId != 0 ">
            AND shop_id = #{shopId}
        </if>
    </delete>

</mapper>