<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.report.mapper.settle.StockInCommodityMapper">

    <sql id="shopStockInReportSql">
         SELECT
               t.shop_id,
               <if test="vo.reportType == 1">
                   t.commodity_id,
                   tc.commodity_code,
                   tc.commodity_name,
                   tc.commodity_spec,
                   tc.commodity_unit_name,
                   t.commodity_first_id,
                   t.commodity_second_id,
                   t.commodity_third_id,
               </if>
                <if test="vo.reportType == 2">
                    cate1.cate_name  commodityFirstCateName,
                </if>
                <if test="vo.reportType == 3">
                    cate1.cate_name  commodityFirstCateName,
                    cate2.cate_name  commoditySecondCateName,
                </if>
                <if test="vo.reportType == 4">
                    cate1.cate_name  commodityFirstCateName,
                    cate2.cate_name  commoditySecondCateName,
                    cate3.cate_name  commodityThirdCateName,
                </if>
                sum(t.order_quantity) order_quantity,
                sum(t.order_amount) order_amount,
                sum(t.delivery_quantity) delivery_quantity,
                sum(t.delivery_amount) delivery_amount,
                sum(t.allot_in_quantity) allot_in_quantity,
                sum(t.allot_in_amount) allot_in_amount,
                sum(t.allot_out_quantity) allot_out_quantity,
                sum(t.allot_out_amount) allot_out_amount,
                sum(t.return_quantity) return_quantity,
                sum(t.return_amount) return_amount,
                sum(t.cloud_in_quantity) cloud_in_quantity,
                sum(t.cloud_in_amount) cloud_in_amount,
                sum((t.delivery_quantity + t.allot_in_quantity - t.allot_out_quantity - t.return_quantity + t.cloud_in_quantity)) totalQuantity,
                sum((t.delivery_amount + t.allot_in_amount - t.allot_out_amount - t.return_amount + t.cloud_in_amount)) totalAmount
          FROM
            <if test="vo.reportType == 1">
                t_md_stock_in_commodity t
                left join t_commodity tc on tc.id = t.commodity_id

            </if>
            <if test="vo.reportType == 2">
                t_md_stock_in_first t
                left join t_category cate1 on cate1.id = t.commodity_first_id
            </if>
            <if test="vo.reportType == 3">
                t_md_stock_in_second t
                left join t_category cate1 on cate1.id = t.commodity_first_id
                left join t_category cate2 on cate2.id = t.commodity_second_id
            </if>
            <if test="vo.reportType == 4">
                t_md_stock_in_third t
                left join t_category cate1 on cate1.id = t.commodity_first_id
                left join t_category cate2 on cate2.id = t.commodity_second_id
                left join t_category cate3 on cate3.id = t.commodity_third_id
            </if>
            <if test="vo.reportType == 5">
                t_md_stock_in_shop t
            </if>
            inner join t_md_shop ms on t.shop_id = ms.id
            where 1=1
            <if test="vo.settleTimeBegin != null and vo.settleTimeBegin != '' and vo.settleTimeEnd != null and vo.settleTimeEnd != '' ">
                and t.date_time BETWEEN #{vo.settleTimeBegin} and #{vo.settleTimeEnd}
            </if>

            <if test="vo.commodityId != null">
                AND t.commodity_id = #{vo.commodityId}
            </if>
            <if test="null != vo.commodityIdList and vo.commodityIdList.size > 0">
                AND t.commodity_id IN
                <foreach collection="vo.commodityIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.cateId1 != null">
                AND t.commodity_first_id = #{vo.cateId1}
            </if>
            <if test="vo.cateId2 != null">
                AND t.commodity_second_id = #{vo.cateId2}
            </if>
            <if test="vo.cateId3 != null">
                AND t.commodity_third_id = #{vo.cateId3}
            </if>
            <if test="vo.shopIdList != null and vo.shopIdList.size() > 0">
                AND t.shop_id IN
                <foreach collection="vo.shopIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.shopId != null">
                AND t.shop_id = #{vo.shopId}
            </if>
            <if test="vo.shopType!=null">
                AND ms.shop_type = #{vo.shopType}
            </if>
            <if test="vo.reportType == 1">
                group by t.shop_id,t.commodity_id
                order by t.shop_id,tc.commodity_code
            </if>
            <if test="vo.reportType == 2">
                group by t.shop_id,t.commodity_first_id
                order by t.shop_id,t.commodity_first_id
            </if>
            <if test="vo.reportType == 3">
                group by t.shop_id,t.commodity_first_id,t.commodity_second_id
                order by t.shop_id,t.commodity_first_id,t.commodity_second_id
            </if>
            <if test="vo.reportType == 4">
                group by t.shop_id,t.commodity_first_id,t.commodity_third_id
                order by t.shop_id,t.commodity_first_id,t.commodity_third_id
            </if>
            <if test="vo.reportType == 5">
                group by t.shop_id
                order by t.shop_id
            </if>

    </sql>

    <select id="shopStockInReport" resultType="com.pinshang.qingyun.report.dto.settle.ShopStockInReportODTO">
        <include refid="shopStockInReportSql"></include>
    </select>
    <select id="shopStockInReportSum" resultType="com.pinshang.qingyun.report.dto.settle.ShopStockInReportODTO">
        select
            sum(tt.order_quantity) orderQuantity,
            sum(tt.order_amount) orderAmount,
            sum(tt.delivery_quantity) deliveryQuantity,
            sum(tt.delivery_amount) deliveryAmount,
            sum(tt.allot_in_quantity) allotInQuantity,
            sum(tt.allot_in_amount) allotInAmount,
            sum(tt.allot_out_quantity) allotOutQuantity,
            sum(tt.allot_out_amount) allotOutAmount,
            sum(tt.return_quantity) returnQuantity,
            sum(tt.return_amount) returnAmount,
            sum(tt.cloud_in_quantity) cloudInQuantity,
            sum(tt.cloud_in_amount) cloudInAmount,
            sum(tt.totalQuantity) totalQuantity,
            sum(tt.totalAmount) totalAmount
       from (
          <include refid="shopStockInReportSql"></include>
        ) tt
    </select>

</mapper>