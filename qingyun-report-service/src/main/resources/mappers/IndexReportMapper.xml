<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.IndexReportMapper">
    <select id="selectSalesDataHistory" resultType="com.pinshang.qingyun.report.dto.index.SalesDataEntry">
        SELECT
        -- 鲜到app销售
        IFNULL(SUM(if(trtrbd.source_type=3 and trtrbd.order_type = 0,trtrbd.sales,0)),0) AS appletsSales,
        IFNULL(SUM(if(trtrbd.source_type=3 and trtrbd.order_type = 0,trtrbd.cost,0)),0) AS appletsCost,
        IFNULL(SUM(if(trtrbd.source_type=1 and trtrbd.order_type = 0,trtrbd.sales,0)),0) AS appSales,
        IFNULL(SUM(if(trtrbd.source_type=1 and trtrbd.order_type = 0,trtrbd.cost,0)),0) AS appCost,

        -- 前置仓销售
        IFNULL(SUM(if(trtrbd.source_type=3 and trtrbd.order_type = 5,trtrbd.sales,0)),0) AS warehouseSales,
        IFNULL(SUM(if(trtrbd.source_type=3 and trtrbd.order_type = 5,trtrbd.cost,0)),0) AS warehouseCost,
        IFNULL(SUM(if(trtrbd.source_type=1 and trtrbd.order_type = 5,trtrbd.sales,0)),0) AS warehouseAppSales,
        IFNULL(SUM(if(trtrbd.source_type=1 and trtrbd.order_type = 5,trtrbd.cost,0)),0) AS warehouseAppCost,

        -- 团购销售
        IFNULL(SUM(if(trtrbd.order_type=1 and (trtrbd.source_type=3 or trtrbd.source_type=10),trtrbd.sales,0)),0) AS groupSales,
        IFNULL(SUM(if(trtrbd.order_type=1 and (trtrbd.source_type=3 or trtrbd.source_type=10),trtrbd.cost,0)),0) AS groupCost,

        -- 云超销售
        IFNULL(SUM(if(trtrbd.source_type = 8,trtrbd.sales,0)),0) AS cloudSales,
        IFNULL(SUM(if(trtrbd.source_type = 8,trtrbd.cost,0)),0) AS cloudCost,
        IFNULL(SUM(if(trtrbd.source_type = 9,trtrbd.sales,0)),0) AS cloudAppSales,
        IFNULL(SUM(if(trtrbd.source_type = 9,trtrbd.cost,0)),0) AS cloudAppCost,

        -- 第三方
        IFNULL(SUM(if(trtrbd.source_type=4,trtrbd.sales,0)),0) AS elmSales,
        IFNULL(SUM(if(trtrbd.source_type=4,trtrbd.cost,0)),0) AS elmCost,
        IFNULL(SUM(if(trtrbd.source_type=7,trtrbd.sales,0)),0) AS jddjSales,
        IFNULL(SUM(if(trtrbd.source_type=7,trtrbd.cost,0)),0) AS jddjCost,

        -- 清美团团
        IFNULL(SUM(if(trtrbd.source_type=11,trtrbd.sales,0)),0) AS quickSales,
        IFNULL(SUM(if(trtrbd.source_type=11,trtrbd.cost,0)),0) AS quickCost,

        IFNULL(SUM(trtrbd.quantity),0) AS visitorNumber
        FROM
        t_real_time_report_by_date trtrbd
        WHERE
        trtrbd.date_time BETWEEN DATE_FORMAT(#{beginDate},'%Y-%m-%d') AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        <if test="sourceType == 1">
            AND (( trtrbd.order_type = 0 AND trtrbd.source_type IN (1,3))
            OR(trtrbd.order_type = 5 AND trtrbd.source_type IN (1,3))
            OR(trtrbd.order_type = 1 AND trtrbd.source_type IN (10,3))
            OR(trtrbd.source_type IN(4,7,8,9,11)))
        </if>
        <if test="sourceType == 2">
            AND trtrbd.`order_type` = 0
            AND trtrbd.`source_type` IN (1,3)
        </if>
        <if test="sourceType == 4">
            AND trtrbd.`order_type` = 1
            AND trtrbd.`source_type` IN (10,3)
        </if>
        <if test="sourceType == 5">
            AND trtrbd.`order_type` = 2
            AND trtrbd.`source_type` IN (8,9)
        </if>
        <if test="sourceType == 6">
            AND trtrbd.`order_type` IN (0,3)
            AND trtrbd.`source_type` IN (4,7)
        </if>
        <if test="sourceType == 7">
            AND trtrbd.`order_type` = 5
            AND trtrbd.`source_type` IN (1,3)
        </if>
        <if test="sourceType == 8">
            AND trtrbd.`order_type` = 4
            AND trtrbd.`source_type` = 11
        </if>
        <if test=" shopId !=null and shopId != 0 ">
            AND trtrbd.shop_id = #{shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and  trtrbd.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectOffLineSalesDataHistory" resultType="com.pinshang.qingyun.report.dto.index.SalesDataEntry">
        SELECT
        IFNULL(SUM(tmcst.offline_sales),0) AS posSales,
        IFNULL(SUM(tmcst.offline_cost),0) AS posCost,
        IFNULL(SUM(tmcst.offline_visitor_number),0) AS visitorNumber
        FROM
        t_md_commodity_shop_tax tmcst
        WHERE
        tmcst.date_time BETWEEN DATE_FORMAT(#{beginDate},'%Y-%m-%d') AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        AND tmcst.total_sales != 0
        <if test="shopId !=null and shopId != 0 ">
            AND tmcst.shop_id = #{shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and  tmcst.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
    </select>

    <!--    曲线图-->
    <select id="selectDataGraphicsHistory" resultType="com.pinshang.qingyun.report.dto.index.YearOnYearDataEntry">
        SELECT
        SUM(trtrbd.sales) AS salesVolume,
        trtrbd.date_time AS date
        FROM
        t_real_time_report_by_date trtrbd
        WHERE
        trtrbd.date_time BETWEEN DATE_FORMAT(#{beginDate},'%Y-%m-%d') AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        <if test="sourceType == 1">
            AND (( trtrbd.order_type = 0 AND trtrbd.source_type IN (1,3))
            OR(trtrbd.order_type = 5 AND trtrbd.source_type IN (1,3))
            OR(trtrbd.order_type = 1 AND trtrbd.source_type IN (10,3))
            OR(trtrbd.source_type IN(4,7,8,9,11)))
        </if>
        <if test="sourceType == 2">
            AND trtrbd.`order_type` = 0
            AND trtrbd.`source_type` IN (1,3)
        </if>
        <if test="sourceType == 4">
            AND trtrbd.`order_type` = 1
            AND trtrbd.`source_type` IN (10,3)
        </if>
        <if test="sourceType == 5">
            AND trtrbd.`order_type` = 2
            AND trtrbd.`source_type` IN (8,9)
        </if>
        <if test="sourceType == 6">
            AND trtrbd.`order_type` IN (0,3)
            AND trtrbd.`source_type` IN (4,7)
        </if>
        <if test="sourceType == 7">
            AND trtrbd.`order_type` = 5
            AND trtrbd.`source_type` IN (1,3)
        </if>
        <if test="sourceType == 8">
            AND trtrbd.`order_type` = 4
            AND trtrbd.`source_type` = 11
        </if>
        <if test="shopId !=null and shopId != 0 ">
            AND trtrbd.shop_id = #{shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and  trtrbd.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
        GROUP BY trtrbd.date_time
    </select>

    <select id="selectSelectOffLineDataGraphicsHistory" resultType="com.pinshang.qingyun.report.dto.index.YearOnYearDataEntry">
        SELECT
        SUM(tmcst.offline_sales) AS salesVolume,
        tmcst.date_time AS date
        FROM t_md_commodity_shop_tax tmcst
        WHERE
        tmcst.date_time BETWEEN DATE_FORMAT(#{beginDate},'%Y-%m-%d') AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        AND tmcst.total_sales != 0
        <if test="shopId !=null and shopId != 0 ">
            AND tmcst.shop_id = #{shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and  tmcst.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
        GROUP BY tmcst.date_time
    </select>

    <select id="selectOffLineHistoryStoreSale" resultType="com.pinshang.qingyun.report.dto.index.StoreSaleEntry">
        SELECT
        tmcst.shop_id AS shopId,
        tms.shop_name AS shopName,
        SUM(tmcst.offline_sales) AS shopSale
        FROM
        t_md_commodity_shop_tax tmcst
        LEFT JOIN t_md_shop tms ON tmcst.shop_id = tms.id
        WHERE
        1=1
        AND tmcst.offline_sales != 0
        AND tmcst.date_time BETWEEN  DATE_FORMAT(#{beginDate},'%Y-%m-%d') AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        <if test="null != shopId and 0 != shopId ">
            AND tmcst.shop_id =#{shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and tmcst.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
        GROUP BY tmcst.shop_id
    </select>

    <!--    门店排序-->
    <select id="selectHistoryStoreSale" resultType="com.pinshang.qingyun.report.dto.index.StoreSaleEntry">
        SELECT
        trtrbd.shop_id AS shopId,
        tms.shop_name AS shopName,
        SUM(trtrbd.sales) AS shopSale
        FROM
        t_real_time_report_by_date trtrbd
        LEFT JOIN t_md_shop tms ON trtrbd.shop_id = tms.id
        WHERE
        1=1
        AND trtrbd.date_time BETWEEN  DATE_FORMAT(#{beginDate},'%Y-%m-%d') AND DATE_FORMAT(#{endDate},'%Y-%m-%d')
        <if test="sourceType == 1">
            AND (( trtrbd.order_type = 0 AND trtrbd.source_type IN (1,3))
            OR(trtrbd.order_type = 5 AND trtrbd.source_type IN (1,3))
            OR(trtrbd.order_type = 1 AND trtrbd.source_type IN (10,3))
            OR(trtrbd.source_type IN(4,7,8,9,11)))
        </if>
        <if test="sourceType == 2">
            AND trtrbd.`order_type` = 0
            AND trtrbd.`source_type` IN (1,3)
        </if>
        <if test="sourceType == 4">
            AND trtrbd.`order_type` = 1
            AND trtrbd.`source_type` IN (10,3)
        </if>
        <if test="sourceType == 5">
            AND trtrbd.`order_type` = 2
            AND trtrbd.`source_type` IN (8,9)
        </if>
        <if test="sourceType == 6">
            AND trtrbd.`order_type` IN (0,3)
            AND trtrbd.`source_type` IN (4,7)
        </if>
        <if test="sourceType == 7">
            AND trtrbd.`order_type` = 5
            AND trtrbd.`source_type` IN (1,3)
        </if>
        <if test="sourceType == 8">
            AND trtrbd.`order_type` = 4
            AND trtrbd.`source_type` = 11
        </if>
        <if test="null != shopId and 0 != shopId ">
            AND trtrbd.shop_id =#{ shopId}
        </if>
        <if test="shopId == 0 ">
            <choose>
                <when test="shopIdList != null and shopIdList.size >0">
                    and trtrbd.shop_id IN
                    <foreach collection="shopIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and  1 = 2
                </otherwise>
            </choose>
        </if>
        GROUP BY trtrbd.shop_id
    </select>
    <select id="compensateIndex" resultType="com.pinshang.qingyun.report.dto.index.IndexEntry">
        SELECT
        tprss.shop_id AS shopId,
        tms.shop_name AS shopName,
        IFNULL(SUM(tprss.tatal_amount), 0) AS totalSales
        FROM
        t_pos_report_sales_summary tprss
        LEFT JOIN t_md_shop tms ON tms.id = tprss.shop_id
        <where>
            <if test="null != beginTime and null != endTime">
                AND tprss.create_time <![CDATA[ >=]]> DATE_FORMAT(#{beginTime},'%Y-%m-%d %T')  AND tprss.create_time <![CDATA[ < ]]> DATE_FORMAT(#{endTime},'%Y-%m-%d %T')
            </if>
            GROUP BY tprss.shop_id
        </where>
    </select>
    <select id="selectOffHistorySaleByTimeSlot" resultType="com.pinshang.qingyun.report.dto.index.IndexEntry">
        SELECT
        tprss.shop_id AS shopId,
        tms.shop_name AS shopName,
        tprss.tatal_amount AS totalSales,
        tprss.create_time AS dateTime
        FROM
        t_pos_report_sales_summary tprss
        LEFT JOIN t_md_shop tms ON tms.id = tprss.shop_id
        <where>
            <if test="null != beginTime and null != endTime">
                AND tprss.create_time BETWEEN DATE_FORMAT(#{beginTime},'%Y-%m-%d %T') AND DATE_FORMAT(#{endTime},'%Y-%m-%d %T')
            </if>
        </where>
    </select>
    <select id="countCommodityShopTaxByTime" resultType="java.lang.Long">
        SELECT count(1) FROM t_md_commodity_shop_tax t where t.date_time = #{dateTime}
    </select>

    <select id="countRealTimeReportByTime" resultType="java.lang.Long">
        SELECT count(1) FROM t_real_time_report_by_date t where t.date_time = #{dateTime}
    </select>

</mapper>