<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.shop.DaySaleAimMapper">

    <select id="countDaySaleAimByShopSaleTime" resultType="java.lang.Integer">
        SELECT
            count(1)
            FROM  t_day_sale_aim t
            WHERE  t.shop_id = #{shopId}  AND  t.sale_time = #{saleTime}
    </select>

    <select id="listDaySaleAimPage" resultType="com.pinshang.qingyun.report.dto.shop.DaySaleAimODTO" parameterType="com.pinshang.qingyun.report.dto.shop.DaySaleAimPageIDTO" >
        SELECT
        t.id,
        t.shop_id,
        s.shop_name,
        t.shop_code,
        t.sale_time,
        t.aim_sale_amount,
        t.remark
        FROM t_day_sale_aim t
        LEFT JOIN t_md_shop s on s.id = t.shop_id
        WHERE t.shop_id IN
        <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
            and t.sale_time BETWEEN #{beginDate} and #{endDate}
        </if>
        ORDER  BY t.sale_time DESC,t.shop_id
    </select>

    <select id="daySalesCompletionRatePage"
            resultType="com.pinshang.qingyun.report.dto.SalesCompletionRateODTO">
        SELECT dsa.shop_id,dsa.sale_time,dsa.shop_code,s.shop_name,dsa.aim_sale_amount,ifnull(mcst.offline_sales,0) as offline_sales
        FROM t_day_sale_aim AS dsa
        LEFT JOIN t_md_shop s on s.id = dsa.shop_id
        LEFT JOIN t_md_commodity_shop_tax AS mcst ON mcst.shop_id = dsa.shop_id AND dsa.sale_time = mcst.date_time
        <where>
            <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
                dsa.sale_time BETWEEN #{beginDate} and #{endDate}
            </if>
            AND dsa.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY dsa.sale_time DESC , dsa.shop_code
    </select>

    <select id="monthSalesCompletionRatePage"
            resultType="com.pinshang.qingyun.report.dto.SalesCompletionRateODTO">
        SELECT dsa.shop_id,dsa.sale_time,dsa.shop_code,s.shop_name,dsa.aim_sale_amount, ifnull(sum(mcst.offline_sales),0) as offline_sales
        FROM t_month_sale_aim AS dsa
        LEFT JOIN t_md_shop s on s.id = dsa.shop_id
        LEFT JOIN t_md_commodity_shop_tax AS mcst ON mcst.shop_id = dsa.shop_id and left(mcst.date_time,7) = dsa.sale_time
        <where>
            <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
                dsa.sale_time BETWEEN #{beginDate} and #{endDate}
            </if>
            AND dsa.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        GROUP BY dsa.shop_code,dsa.sale_time
        ORDER BY dsa.sale_time DESC , dsa.shop_code
    </select>

    <select id="daySalesCompletionRateSum"
            resultType="com.pinshang.qingyun.report.dto.DaySalesCompletionRateSum">
        SELECT sum(mcst.offline_sales) AS offline_sales_sum,SUM(dsa.aim_sale_amount) AS aim_sale_amount_sum
        FROM t_day_sale_aim AS dsa
        LEFT JOIN t_md_commodity_shop_tax AS mcst ON mcst.shop_id = dsa.shop_id AND dsa.sale_time = mcst.date_time
        <where>
            <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
                dsa.sale_time BETWEEN #{beginDate} and #{endDate}
            </if>
            AND dsa.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>


    <select id="monthSalesCompletionRateSum"
            resultType="com.pinshang.qingyun.report.dto.DaySalesCompletionRateSum">
        SELECT sum(mcst.offline_sales) AS offline_sales_sum,SUM(dsa.aim_sale_amount) AS aim_sale_amount_sum
        FROM t_month_sale_aim AS dsa
        LEFT JOIN
        (
        SELECT SUM(offline_sales) AS offline_sales,
        LEFT(date_time,7) AS date_time,
        shop_id
        FROM t_md_commodity_shop_tax
        WHERE
        shop_id IN
        <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
            and date_time   BETWEEN #{beginDate} and concat(#{endDate},'-31')
        </if>

        GROUP BY
        LEFT(date_time,7),shop_id
        )
        AS mcst ON mcst.shop_id = dsa.shop_id AND left(mcst.date_time,7) = dsa.sale_time
        <where>
            <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
                dsa.sale_time BETWEEN #{beginDate} and #{endDate}
            </if>
            AND dsa.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>

    </select>


    <select id="queryDayKeyListRepeat" resultType="java.lang.String">
        SELECT
           CONCAT(t.shop_code,t.sale_time)
          FROM  t_day_sale_aim t
        WHERE CONCAT(t.shop_code,t.sale_time) in
        <foreach collection="keyList" item="keyl" open="(" close=")" separator=",">
            #{keyl}
        </foreach>
    </select>
    <select id="getDaySaleAimShopIdList" resultType="java.lang.Long">
        select shop_id from t_day_sale_aim as dsa
        <where>
            <if test="beginDate !=null and beginDate != '' and endDate != null and endDate != '' ">
                dsa.sale_time BETWEEN #{beginDate} and #{endDate}
            </if>
            AND dsa.shop_id IN
            <foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>