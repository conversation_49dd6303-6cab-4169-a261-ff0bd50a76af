<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.report.mapper.pos.PosReportCommodityQuantityUpdateMapper">

    <select id = "list" resultType="com.pinshang.qingyun.report.dto.pos.CommodityQuantityUpdateDTO"
    parameterType="com.pinshang.qingyun.report.dto.pos.CommodityQuantityUpdateIDTO">
        select qu.shop_id, qu.shop_code, qu.shop_name,qu.mac_code,qu.order_code, qu.commodity_code, qu.commodity_name, qu.commodity_spec, qu.commodity_unit, qu.operate_type, qu.quantity, qu.commodity_price,
               qu.casher_id, qu.casher_code, qu.casher_name, qu.authorizer_code, qu.authorizer_name, qu.operate_time, qu.create_time, qu.pos_mac_id, qu.pos_type,
               qu.commodity_price * qu.quantity as totalPrice
        from t_pos_report_commodity_quantity_update qu
        <include refid="listWhere"></include>
        order by qu.operate_time desc
    </select>

    <select id = "sum" resultType="com.pinshang.qingyun.report.dto.pos.CommodityQuantityUpdateDTO"
            parameterType="com.pinshang.qingyun.report.dto.pos.CommodityQuantityUpdateIDTO">
        select sum(qu.commodity_price * qu.quantity) as totalPrice
        from t_pos_report_commodity_quantity_update qu
        <include refid="listWhere"></include>
    </select>

    <sql id="listWhere">
        <where>
            <if test = "dto.shopIdList != null and dto.shopIdList.size > 0">
                AND qu.shop_id in
                <foreach collection="dto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
<!--            <if test = "dto.shopId != null and dto.shopId != '' ">-->
<!--                AND qu.shop_id = #{dto.shopId}-->
<!--            </if>-->
            <if test = "dto.shopCode != null and dto.shopCode != ''">
                AND qu.shop_code = #{dto.shopCode}
            </if>
            <if test = "dto.startOperateDate != null and dto.startOperateDate != ''
            and dto.endOperateDate != null and dto.endOperateDate != ''" >
                AND qu.operate_time between #{dto.startOperateDate} and #{dto.endOperateDate}
            </if>
            <if test = "dto.orderCode != null and dto.orderCode != ''">
                AND qu.order_code = #{dto.orderCode}
            </if>
            <if test="dto.hourBegin != null and dto.hourBegin != ''">
                AND date_format(qu.operate_time,'%k:%i') <![CDATA[ >= ]]> #{dto.hourBegin}
            </if>
            <if test="dto.hourEnd != null and dto.hourEnd != ''">
                AND date_format(qu.operate_time,'%k:%i') <![CDATA[ <= ]]> #{dto.hourEnd}
            </if>
            <if test="dto.compareOrderAmount != null and dto.compareOrderAmount != ''">
                AND ( qu.commodity_price * qu.quantity ) <![CDATA[ >= ]]> #{dto.compareOrderAmount}
            </if>
        </where>
    </sql>

    <select id = "report" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
    resultType="com.pinshang.qingyun.report.dto.pos.CommodityQuantityUpdateReportDTO">
        select qu.shop_id,
               qu.shop_code,
               qu.shop_name,
               qu.operate_type,
            <if test="idto.type == 3">
                qu.casher_id,
                qu.casher_code,
                qu.casher_name,
            </if>
            <if test="idto.type == 3 or idto.type == 2">
                date_format(qu.operate_time, '%Y-%m-%d') operateTime,
            </if>
            CONVERT(sum(qu.commodity_price * qu.quantity), DECIMAL(10, 2)) as amount,
            count(*) num
        from t_pos_report_commodity_quantity_update qu
        <include refid="reportCondition"></include>
        group by qu.operate_type, qu.shop_id
        <if test="idto.type == 3 or idto.type == 2">
            ,date_format(qu.operate_time, '%Y-%m-%d')
        </if>
        <if test="idto.type == 3">
            ,qu.casher_id
        </if>

    </select>

    <select id = "reportSum" parameterType="com.pinshang.qingyun.report.dto.pos.CommodityDeleteIDTO"
            resultType="com.pinshang.qingyun.report.dto.pos.CommodityQuantityUpdateReportDTO">
        select qu.operate_type,
               CONVERT(sum(qu.commodity_price * qu.quantity), DECIMAL(10, 2)) as amount,
               count(*) num
        from t_pos_report_commodity_quantity_update qu
        <include refid="reportCondition"></include>
        group by qu.operate_type
    </select>

    <sql id = "reportCondition">
        <where>
            <if test = "idto.shopCode != null and idto.shopCode != ''">
                and qu.shop_code = #{idto.shopCode}
            </if>
            <choose>
                <when test="idto.shopId != null and idto.shopId != ''"> AND qu.shop_id = #{idto.shopId} </when>
                <otherwise>
                    AND qu.shop_id IN
                    <foreach collection="idto.shopIdList" index="index" item="shopId" open="(" separator="," close=")">
                        #{shopId}
                    </foreach></otherwise>
            </choose>
            <if test="idto.beginTime != null and idto.beginTime != ''
                      and idto.endTime != null and idto.endTime != ''">
                and qu.operate_time BETWEEN #{idto.beginTime} and #{idto.endTime}
            </if>
        </where>
    </sql>

    <select id="updateCommodityInfo" resultType="com.pinshang.qingyun.report.dto.pos.AIMonitorCommodityDTO">
        select qu.item_id as itemId,
               sum(qu.quantity) as quantity
        from t_pos_report_commodity_quantity_update qu
        where qu.order_code = #{orderCode}
          and qu.operate_type = 2
        group by qu.item_id
    </select>

</mapper>