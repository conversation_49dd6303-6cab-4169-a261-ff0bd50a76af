<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<include resource="conf/pinshang-log-base.xml" />
	<jmxConfigurator/>
	<!--<logger name="com.pinshang.qingyun.ApplicationCoreAggregationService" level="DEBUG"/>-->
	<springProfile name="me">
		<!--<logger name="com.pinshang.demo.ApplicationPermissionServiceervice" level="INFO"/>-->
		<logger name="org.apache.kafka.clients.consumer.internals.ConsumerCoordinator" level="INFO"/>
		<logger name="org.apache.kafka.clients.NetworkClient" level="INFO"/>
		<logger name="org.apache.kafka.common.network.Selector" level="INFO"/>
		<root level="debug">
<!--			<appender-ref ref="ASYNC_APPENDER" />-->
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="CatAppender" />
			<!--<appender-ref ref="FILE_ERROR" />-->
			<!--<appender-ref ref="FILE_INFO" />-->
			<!--<appender-ref ref="FILE_DEBUG" />-->
			<!--<appender-ref ref="FILE_WARN" />-->
		</root>
	</springProfile>
	<springProfile name="dev">
		<root level="info">
<!--			<appender-ref ref="ASYNC_APPENDER" />-->
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="CatAppender" />
<!--			<appender-ref ref="FILE_DEBUG" />-->
			<!--<appender-ref ref="FILE_ERROR" />-->
			<!--<appender-ref ref="FILE_INFO" />-->
			<!--<appender-ref ref="FILE_DEBUG" />-->
			<!--<appender-ref ref="FILE_WARN" />-->
		</root>
	</springProfile>
	<springProfile name="test">
		<root level="info">
<!--			<appender-ref ref="ASYNC_APPENDER" />-->
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="CatAppender" />
			<!--<appender-ref ref="FILE_ERROR" />-->
			<!--<appender-ref ref="FILE_INFO" />-->
			<!--<appender-ref ref="FILE_DEBUG" />-->
			<!--<appender-ref ref="FILE_WARN" />-->
		</root>
	</springProfile>
	<springProfile name="wg-hd-prod">
		<root level="info">
<!--			<appender-ref ref="ASYNC_APPENDER" />-->
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="CatAppender" />
			<!--<appender-ref ref="FILE_ERROR" />-->
			<!--<appender-ref ref="FILE_INFO" />-->
			<!--<appender-ref ref="FILE_DEBUG" />-->
			<!--<appender-ref ref="FILE_WARN" />-->
		</root>
	</springProfile>
	<springProfile name="wg-prod">
		<logger name="org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainer" level="INFO"/>
		<logger name="org.springframework.cloud.config.client.ConfigServicePropertySourceLocator" level="INFO"/>
		<logger name="com.netflix.discovery.DiscoveryClient" level="INFO"/>
		<root level="warn">
<!--			<appender-ref ref="ASYNC_APPENDER" />-->
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="CatAppender" />
			<!--<appender-ref ref="FILE_ERROR" />-->
			<!--<appender-ref ref="FILE_INFO" />-->
			<!--<appender-ref ref="FILE_DEBUG" />-->
			<!--<appender-ref ref="FILE_WARN" />-->
		</root>
	</springProfile>

</configuration>
